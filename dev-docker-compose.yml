version: '3'
services:
  kuber-backend-v2:
    image: kuber-backend-v2:rev-1
    ports:
      - 3000:3000
    volumes:
      - /home/<USER>/kuber-v2-deploy/kuber-volumes/backend/.env:/usr/src/app/.env
    logging:
      driver: "json-file"
      options:
        "max-file": "10"
        "max-size": "10m"
  kuber-frontend-v2:
    image: kuber-frontend-v2:rev-1
    ports:
      - 3001:80
    logging:
      driver: "json-file"
      options:
        "max-file": "10"
        "max-size": "10m"
  kuber-db-v2:
    image: mysql:5.7
    restart: always
    environment:
      MYSQL_DATABASE: 'db'
      # So you don't have to use root, but you can if you like
      MYSQL_USER: 'newkuber'
      # You can use whatever password you like
      MYSQL_PASSWORD: 'rocker44kuber'
      # Password for root access
      MYSQL_ROOT_PASSWORD: 'admin'
    ports:
      - '3002:3306'
    expose:
      # Opens port 3306 on the container
      - '3002'
      # Where our data will be persisted
    volumes:
      - /home/<USER>/kuber-v2-deploy/kuber-volumes/mysql:/var/lib/mysql
      - /home/<USER>/kuber-v2-deploy/kuber-volumes/inital-backup/init.sql:/docker-entrypoint-initdb.d/setup.sql
    logging:
      driver: "json-file"
      options:
        "max-file": "10"
        "max-size": "10m"   