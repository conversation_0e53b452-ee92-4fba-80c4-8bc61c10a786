import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
const { REQUEST, LOGIN_USER } = actionTypes

//write sagas function

function* handleLoginUserData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.loginUserEndpoint,
      data.data,
    )
    yield sendPayload(apiResponse, LOGIN_USER)
  } catch (e) {
    yield sendPayloadFailure(e, LOGIN_USER)
  }
}

export const sagas = {
  //watcher come here
  watchLoginUser: takeLatest(actionTypes.LOGIN_USER[REQUEST], handleLoginUserData),
}
