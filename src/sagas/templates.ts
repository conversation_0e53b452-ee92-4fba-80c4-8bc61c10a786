import { call, put, takeLatest } from 'redux-saga/effects'
import { toast } from 'react-toastify'
import { delay } from 'redux-saga/effects'

import { sendPayload, sendPayloadFailure } from './helper'
import * as RestAPI from '../services/rest'
import {
  FETCH_TEMPLATES,
  CREATE_TEMPLATE,
  UPDATE_TEMPLATE,
  DELETE_TEMPLATE,
  SEARCH_TEMPLATES,
  FETCH_TEMPLATE_BY_ID,
  ASSIGN_TEMPLATE,
  GET_ASSIGNED_TEMPLATE,
  EDIT_TEMPLATE_VALUES,
  GET_KPI_TEMPLATES,
  LOAD_TEMPLATE_OR_OPTIONS,
} from '../actions/actiontypes'
import { 
  fetchTemplatesSuccess,
  fetchTemplatesFailure,
  createTemplateSuccess,
  createTemplateFailure,
  updateTemplateSuccess,
  updateTemplateFailure,
  deleteTemplateSuccess,
  deleteTemplateFailure,
  searchTemplatesSuccess,
  searchTemplatesFailure,
  fetchTemplateByIdSuccess,
  fetchTemplateByIdFailure,
  assignTemplateSuccess,
  assignTemplateFailure,
  getAssignedTemplateSuccess,
  getAssignedTemplateFailure,
  editTemplateValuesSuccess,
  editTemplateValuesFailure,
  getKPITemplatesSuccess,
  getKPITemplatesFailure,
  loadTemplateOrOptionsSuccess,
  loadTemplateOrOptionsFailure,
} from '../actions'

// Helper function to decode JWT token and extract user info
const getUserInfoFromToken = () => {
  try {
    const token = localStorage.getItem('token')
    if (!token) return { userId: 1, tenantId: 152 }
    
    // Simple JWT decode (without verification - since it's already validated by backend)
    const base64Payload = token.split('.')[1]
    const decodedPayload = JSON.parse(atob(base64Payload))
    
    return {
      userId: decodedPayload.userId || 1,
      tenantId: decodedPayload.tenantId || 152
    }
  } catch (error) {
    console.warn('Failed to decode JWT token:', error)
    return { userId: 1, tenantId: 152 } // fallback values
  }
}

// Helper function to get user info with multiple fallback sources
const getUserInfo = () => {
  // Try userContext first (if it exists)
  try {
    const userContext = JSON.parse(localStorage.getItem('userContext') || '{}')
    if (userContext.id) {
      const userId = parseInt(userContext.id, 10)
      const tenantId = userContext.tenant_id || userContext.tenantId || 152
      return { userId, tenantId }
    }
  } catch (error) {
    console.warn('Failed to parse userContext:', error)
  }
  
  // Try JWT token as fallback
  return getUserInfoFromToken()
}

// API Service functions for templates (using REST API service)
const templateApi = {
  getTemplates: async () => {
    const response = await RestAPI.GET('api/templates')
    return response.data
  },

  getTemplate: async (id: string) => {
    const response = await RestAPI.GET(`api/templates/${id}`)
    return response.data
  },

  createTemplate: async (templateData: any) => {
    const { userId, tenantId } = getUserInfo()
    
    const payload = {
      ...templateData,
      tenant_id: tenantId,
      created_by: userId,
      updated_by: userId
    }
    
    console.log('Creating template with data:', JSON.stringify(payload, null, 2))
    console.log('User info:', { userId, tenantId })
    
    const response = await RestAPI.POST('api/templates', payload)
    return response.data
  },

  updateTemplate: async (id: string, templateData: any) => {
    const { userId } = getUserInfo()
    
    const response = await RestAPI.PUT(`api/templates/${id}`, {
      ...templateData,
      updated_by: userId
    })
    return response.data
  },

  deleteTemplate: async (id: string) => {
    await RestAPI.DELETE(`api/templates/${id}`)
    return { id }
  },

  searchTemplates: async (query: string) => {
    const response = await RestAPI.GET(`api/templates/search?q=${encodeURIComponent(query)}`)
    return response.data
  },
};

// Saga functions
function* handleFetchTemplates(): Generator<any, void, any> {
  try {
    console.log('Template Saga: handleFetchTemplates called')
    console.log('Template Saga: Making API call to fetch templates')
    const apiResponse = yield call(templateApi.getTemplates);
    console.log('Template Saga: API response received:', apiResponse)
    
    // Since we're using fetch() directly, we need to wrap the response to match sendPayload expectations
    // Other sagas use RestAPI which returns { data: { data: ... } }, so we need to simulate that format
    const wrappedResponse = {
      data: {
        data: apiResponse
      }
    };
    
    yield sendPayload(wrappedResponse, FETCH_TEMPLATES)
    
    if (apiResponse && apiResponse.length === 0) {
      toast.info('No templates found');
    } else if (apiResponse && apiResponse.length > 0) {
      console.log(`Template Saga: Successfully fetched ${apiResponse.length} templates`)
    }
  } catch (error: any) {
    console.error('Template Saga: Error in handleFetchTemplates:', error)
    yield sendPayloadFailure(error, FETCH_TEMPLATES);
  }
}

function* handleCreateTemplate(action: any): Generator<any, void, any> {
  try {
    const { data } = action;
    const apiResponse = yield call(templateApi.createTemplate, data);
    const wrappedResponse = {
      data: {
        data: apiResponse
      }
    };
    yield sendPayload(wrappedResponse, CREATE_TEMPLATE)
    toast.success('Template created successfully!');
  } catch (error: any) {
    yield sendPayloadFailure(error, CREATE_TEMPLATE);
  }
}

function* handleUpdateTemplate(action: any): Generator<any, void, any> {
  try {
    const { data } = action;
    const { id, templateData } = data;
    const apiResponse = yield call(templateApi.updateTemplate, id, templateData);
    const wrappedResponse = {
      data: {
        data: apiResponse
      }
    };
    yield sendPayload(wrappedResponse, UPDATE_TEMPLATE)
    toast.success('Template updated successfully!');
  } catch (error: any) {
    yield sendPayloadFailure(error, UPDATE_TEMPLATE);
  }
}

function* handleDeleteTemplate(action: any): Generator<any, void, any> {
  try {
    const { data } = action;
    const { id } = data;
    yield call(templateApi.deleteTemplate, id);
    const wrappedResponse = {
      data: {
        data: { id }
      }
    };
    yield sendPayload(wrappedResponse, DELETE_TEMPLATE)
    toast.success('Template deleted successfully!');
  } catch (error: any) {
    yield sendPayloadFailure(error, DELETE_TEMPLATE);
  }
}

function* handleSearchTemplates(action: any): Generator<any, void, any> {
  try {
    const { data } = action;
    const { query } = data;
    const apiResponse = yield call(templateApi.searchTemplates, query);
    const wrappedResponse = {
      data: {
        data: apiResponse
      }
    };
    yield sendPayload(wrappedResponse, SEARCH_TEMPLATES)
  } catch (error: any) {
    yield sendPayloadFailure(error, SEARCH_TEMPLATES);
  }
}

function* handleFetchTemplateById(action: any): Generator<any, void, any> {
  try {
    const { data } = action;
    const { id } = data;
    const apiResponse = yield call(templateApi.getTemplate, id);
    const wrappedResponse = {
      data: {
        data: apiResponse
      }
    };
    yield sendPayload(wrappedResponse, FETCH_TEMPLATE_BY_ID)
  } catch (error: any) {
    yield sendPayloadFailure(error, FETCH_TEMPLATE_BY_ID);
  }
}



// DR Section KPI Template Management Sagas
function* handleAssignTemplate(action: any): Generator<any, void, any> {
  try {
    console.log('Assign Template Saga: Starting with data:', action.data)
    
    const response = yield call(RestAPI.POST, 'template/assign', action.data)
    console.log('Assign Template Saga: Success:', response.data)
    
    yield put(assignTemplateSuccess(response.data))
    
    // Note: Auto-refresh removed to prevent duplicate API calls
    // The component will handle refreshing via loadTemplateOrOptions action
    
    toast.success('Template assigned successfully!')
  } catch (error: any) {
    console.error('Assign Template Saga: Error:', error)
    yield put(assignTemplateFailure(error.response?.data?.message || error.message))
    toast.error(error.response?.data?.message || error.message || 'Failed to assign template')
  }
}

function* handleGetAssignedTemplate(action: any): Generator<any, void, any> {
  try {
    console.log('Get Assigned Template Saga: Starting with data:', action.data)
    
    const { userId, tId } = action.data
    const response = yield call(RestAPI.GET, `template?userId=${userId}&tId=${tId}`)
    console.log('Get Assigned Template Saga: Success:', response.data)
    
    yield put(getAssignedTemplateSuccess(response.data.data))
  } catch (error: any) {
    console.error('Get Assigned Template Saga: Error:', error)
    if (error.response?.status === 404) {
      // No template assigned - this is not an error, just return null
      yield put(getAssignedTemplateSuccess(null))
    } else {
      yield put(getAssignedTemplateFailure(error.response?.data?.message || error.message))
      toast.error(error.response?.data?.message || error.message || 'Failed to get assigned template')
    }
  }
}

function* handleEditTemplateValues(action: any): Generator<any, void, any> {
  try {
    console.log('Edit Template Values Saga: Starting with data:', action.data)
    console.log('Edit Template Values Saga: Calling PUT /template/categories/subcategories')
    
    const response = yield call(RestAPI.PUT, 'template/categories/subcategories', action.data)
    console.log('Edit Template Values Saga: PUT request successful:', response.data)
    
    // Wrap response to match reducer expectations
    const wrappedResponse = {
      data: response.data.data || response.data
    }
    yield put(editTemplateValuesSuccess(wrappedResponse))
    console.log('Edit Template Values Saga: Success action dispatched')
    
    // Note: Auto-refresh removed to prevent duplicate API calls
    // The component will handle refreshing via loadTemplateOrOptions action
    
    toast.success('KPI value updated successfully!')
  } catch (error: any) {
    console.error('Edit Template Values Saga: Error:', error)
    yield put(editTemplateValuesFailure(error.response?.data?.message || error.message))
    toast.error(error.response?.data?.message || error.message || 'Failed to update KPI value')
  }
}

function* handleGetKPITemplates(): Generator<any, void, any> {
  try {
    console.log('Get KPI Templates Saga: Starting')
    
    const response = yield call(RestAPI.GET, 'api/templates')
    console.log('Get KPI Templates Saga: Success:', response.data)
    
    yield put(getKPITemplatesSuccess(response.data.data))
  } catch (error: any) {
    console.error('Get KPI Templates Saga: Error:', error)
    yield put(getKPITemplatesFailure(error.response?.data?.message || error.message))
    toast.error(error.response?.data?.message || error.message || 'Failed to fetch templates')
  }
}

// Intelligent Template Loading Saga
function* handleLoadTemplateOrOptions(action: any): Generator<any, void, any> {
  try {
    console.log('Load Template Or Options Saga: ACTION RECEIVED! ')
    console.log('Load Template Or Options Saga: This proves the timeout worked and action was dispatched!')
    console.log('Load Template Or Options Saga: Full action:', JSON.stringify(action, null, 2))
    
    if (!action.data) {
      console.error(' Load Template Or Options Saga: No action.data found!')
      yield put(loadTemplateOrOptionsFailure('No action data provided'))
      return
    }
    
    const { userId, tId } = action.data
    console.log('Load Template Or Options Saga: Extracted userId:', userId, 'tId:', tId)
    console.log('Load Template Or Options Saga: About to make API call to refresh template data')
    
    // First, try to get assigned template
    try {
      console.log('Load Template Or Options Saga: Checking for assigned template...')
      console.log('Load Template Or Options Saga: Calling GET /template?userId=' + userId + '&tId=' + tId)
      const assignedResponse = yield call(RestAPI.GET, `template?userId=${userId}&tId=${tId}`)
      console.log('Load Template Or Options Saga: GET request successful - Found assigned template:', assignedResponse.data)
      
      // Template is assigned, return it
      yield put(loadTemplateOrOptionsSuccess({
        type: 'assigned',
        data: assignedResponse.data.data
      }))
      console.log('Load Template Or Options Saga: Updated template data loaded successfully')
      
    } catch (error: any) {
      console.log('Load Template Or Options Saga: No assigned template found, fetching available templates...')
      
      if (error.response?.status === 404) {
        // No template assigned, fetch available templates
        try {
          const templatesResponse = yield call(RestAPI.GET, 'api/templates')
          console.log('Load Template Or Options Saga: Fetched available templates:', templatesResponse.data)
          
          yield put(loadTemplateOrOptionsSuccess({
            type: 'options',
            data: templatesResponse.data
          }))
          
        } catch (templatesError: any) {
          console.error('Load Template Or Options Saga: Error fetching templates:', templatesError)
          yield put(loadTemplateOrOptionsFailure(templatesError.response?.data?.message || templatesError.message))
          toast.error('Failed to fetch available templates')
        }
      } else {
        // Some other error occurred
        console.error('Load Template Or Options Saga: Unexpected error:', error)
        yield put(loadTemplateOrOptionsFailure(error.response?.data?.message || error.message))
        toast.error(error.response?.data?.message || error.message || 'Failed to check template assignment')
      }
    }
  } catch (error: any) {
    console.error('Load Template Or Options Saga: Unexpected error:', error)
    yield put(loadTemplateOrOptionsFailure(error.message))
    toast.error('An unexpected error occurred while loading template data')
  }
}

// Export sagas object to match pattern used by other saga files
export const sagas = {
  watchFetchTemplates: takeLatest(FETCH_TEMPLATES.REQUEST, handleFetchTemplates),
  watchCreateTemplate: takeLatest(CREATE_TEMPLATE.REQUEST, handleCreateTemplate),
  watchUpdateTemplate: takeLatest(UPDATE_TEMPLATE.REQUEST, handleUpdateTemplate),
  watchDeleteTemplate: takeLatest(DELETE_TEMPLATE.REQUEST, handleDeleteTemplate),
  watchSearchTemplates: takeLatest(SEARCH_TEMPLATES.REQUEST, handleSearchTemplates),
  watchFetchTemplateById: takeLatest(FETCH_TEMPLATE_BY_ID.REQUEST, handleFetchTemplateById),
  watchAssignTemplate: takeLatest(ASSIGN_TEMPLATE.REQUEST, handleAssignTemplate),
  watchGetAssignedTemplate: takeLatest(GET_ASSIGNED_TEMPLATE.REQUEST, handleGetAssignedTemplate),
  watchEditTemplateValues: takeLatest(EDIT_TEMPLATE_VALUES.REQUEST, handleEditTemplateValues),
  watchGetKPITemplates: takeLatest(GET_KPI_TEMPLATES.REQUEST, handleGetKPITemplates),
  watchLoadTemplateOrOptions: takeLatest(LOAD_TEMPLATE_OR_OPTIONS.REQUEST, handleLoadTemplateOrOptions),
} 