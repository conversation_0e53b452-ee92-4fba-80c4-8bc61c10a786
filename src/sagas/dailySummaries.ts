import { call, put, takeLatest } from 'redux-saga/effects';
import { getDailySummariesService, getMonthlySummariesEndPoint, getWeeklySummariesEndPoint } from '../services';
import { GET_DAILY_SUMMARIES, MONTHLY_SUMMARIES, WEEKLY_SUMMARIES } from '../actions/actiontypes';
import { actionTypes } from '../actions';

const { REQUEST } = actionTypes;

function* getDailySummariesSaga(action: any): Generator<any, void, any> {
  try {
    const response = yield call(getDailySummariesService, action.data.data);
    
    const summariesData = {
      response: response?.data || response
    };
    
    yield put({
      type: GET_DAILY_SUMMARIES.SUCCESS,
      payload: summariesData
    });

  } catch (error) {
    yield put({
      type: GET_DAILY_SUMMARIES.FAILURE,
      payload: error
    });
  }
}

function* getMonthlySummariesSaga(action: any): Generator<any, void, any> {
  try {
    const response = yield call(getMonthlySummariesEndPoint, action.data.data);
    
    const monthlySummariesData = {
      response: response?.data || response
    };
    
    yield put({
      type: MONTHLY_SUMMARIES.SUCCESS,
      payload: monthlySummariesData
    });

  } catch (error) {
    yield put({
      type: MONTHLY_SUMMARIES.FAILURE,
      payload: error
    });
  }
}

function* getWeeklySummariesSaga(action: any): Generator<any, void, any> {
  try {
    const response = yield call(getWeeklySummariesEndPoint, action.data.data);
    
    const weeklySummariesData = {
      response: response?.data || response
    };
    
    yield put({
      type: WEEKLY_SUMMARIES.SUCCESS,
      payload: weeklySummariesData
    });

  } catch (error) {
    yield put({
      type: WEEKLY_SUMMARIES.FAILURE,
      payload: error
    });
  }
}

export const sagas = {
  watchGetDailySummaries: takeLatest(GET_DAILY_SUMMARIES[REQUEST], getDailySummariesSaga),
  watchGetMonthlySummaries: takeLatest(MONTHLY_SUMMARIES[REQUEST], getMonthlySummariesSaga),
  watchWeeklySummaries: takeLatest(WEEKLY_SUMMARIES.REQUEST, getWeeklySummariesSaga)
};
