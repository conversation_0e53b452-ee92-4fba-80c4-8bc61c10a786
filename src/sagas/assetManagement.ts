import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import { ADD_ASSET_MAKE, ADD_ASSET_STATUS, ADD_NEW_ASSET, ADD_TECHN<PERSON>OGIES, ASSET_ABBERATION, ASSET_AB<PERSON>EVATION_COUNT, ASSET_CATEGORY, ASSET_CATEGORY_DELETE, ASSET_CATEGORY_UPDATE, ASSET_LOCATION, ASSET_MAKE, ASSET_MAKE_DATA, ASSET_MAKE_DELETE, ASSET_OS, ASSET_OS_DATA, ASSET_OS_UPDATE, ASSET_STATUS, ASSET_STATUS_COUNT, ASSET_STATUS_DATA, ASSET_STATUS_DELETE, ASSET_TECHNOLOGIES, ASSETS_DATA, ASSIGN_ASSET, ASSIGN_ASSET_REPORT, DELETE_ASSET_OS, DELETE_T<PERSON>H<PERSON><PERSON>OGIES, DOWNLOAD_ASSETS_QR, DOWNLOAD_QR, EMPLOYEE_ASSETS_COUNT, EMPLOYEE_ASSETS_DETAILS, FETCH_ASSETS, FETCH_ASSETS_DATA_AS_EXCEL, FETCH_EMP_ASSET_DATA, FETCH_EMP_DATA, FETCH_NUMBER_OF_ASSETS, TECHNOLOGIES_DATA, UNALLOCATE_ASSET } from '../actions/actiontypes'

const { REQUEST } = actionTypes

  function* handleAssetsData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetsData,
        data.data,
      )
      yield sendPayload(apiResponse, ASSETS_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, ASSETS_DATA)
    }
  }

  function* handleNumberOfAssetsData() {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getNumberOfAssets
      )
      yield sendPayload(apiResponse, FETCH_NUMBER_OF_ASSETS)
    } catch (e) {
      yield sendPayloadFailure(e, FETCH_NUMBER_OF_ASSETS)
    }
  }

  function* handleEmpData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getEmpData,
        data.data,
      )
      yield sendPayload(apiResponse, FETCH_EMP_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, FETCH_EMP_DATA)
    }
  }

  function* handleWorkEmpData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getEmpAssetData,
        data.data,
      )
      yield sendPayload(apiResponse, FETCH_EMP_ASSET_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, FETCH_EMP_ASSET_DATA)
    }
  }


  function* handleAllAssetsList(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAllAssetsList,
        data.data,
      )
      yield sendPayload(apiResponse, FETCH_ASSETS)
    } catch (e) {
      yield sendPayloadFailure(e, FETCH_ASSETS)
    }
  }

  function* handleAssignAsset(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.updateAssignAsset,
        data.data,
      )
      yield sendPayload(apiResponse, ASSIGN_ASSET)
    } catch (e) {
      yield sendPayloadFailure(e, ASSIGN_ASSET)
    }
  }

  function* handleEmployeeAssets(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getEmployeeAssets,
        data.data,
      )
      yield sendPayload(apiResponse, EMPLOYEE_ASSETS_DETAILS)
    } catch (e) {
      yield sendPayloadFailure(e, EMPLOYEE_ASSETS_DETAILS)
    }
  }

function* handleUnallocateAsset(data: any) {

    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.updateUnallocateAsset,
        data.data,
      )
      yield sendPayload(apiResponse, UNALLOCATE_ASSET)
    } catch (e) {
      yield sendPayloadFailure(e, UNALLOCATE_ASSET)
    }
  }

  function* handleAddNewAsset(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.updateAddNewAsset,
        data.data,
      )
      yield sendPayload(apiResponse, ADD_NEW_ASSET)
    } catch (e) {
      yield sendPayloadFailure(e, ADD_NEW_ASSET)
    }
  }

  function* handleAssetsDataAsExcel(data: any) {
    try {
      const apiResponse: Generator<String, number, string> = yield call(
        ApiService.getAssetsDataAsExcel,
        data.data,
      )
      yield sendPayload(apiResponse, FETCH_ASSETS_DATA_AS_EXCEL)
    } catch (e) {
      yield sendPayloadFailure(e, FETCH_ASSETS_DATA_AS_EXCEL)
    }
  }

  function* handleAssetsAbberation(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetAbiberation,
        data,
      )
      yield sendPayload(apiResponse, ASSET_ABBERATION)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_ABBERATION)
    }
  }

  function* handleAssetLocation(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetLocation,
        data,
      )
      yield sendPayload(apiResponse, ASSET_LOCATION)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_LOCATION)
    }
  }

  function* handleAssetMake(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetMake,
        data,
      )
      yield sendPayload(apiResponse, ASSET_MAKE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_MAKE)
    }
  }

  function* handleAssetStatus(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetStatus,
        data,
      )
      yield sendPayload(apiResponse, ASSET_STATUS)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_STATUS)
    }
  }

  function* handleAssetOS(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetOS,
        data,
      )
      yield sendPayload(apiResponse, ASSET_OS)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_OS)
    }
  }

  function* handleAssetTechnologies(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetTechnologies,
        data,
      )
      yield sendPayload(apiResponse, ASSET_TECHNOLOGIES)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_TECHNOLOGIES)
    }
  }

  function* handleAssetCategoryData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetCategoryData,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_CATEGORY)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_CATEGORY)
    }
  }

  function* handleAssetOSData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetOSData,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_OS_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_OS_DATA)
    }
  }

  function* handleAssetOSUpdate(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.updateAssetOs,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_OS_UPDATE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_OS_UPDATE)
    }
  }

  function* handleAssetCategoryUpdate(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.updateAssetCategory,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_CATEGORY_UPDATE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_CATEGORY_UPDATE)
    }
  }

  function* handleAssetCategoryDelete(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.deleteAssetCategory,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_CATEGORY_DELETE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_CATEGORY_DELETE)
    }
  }

  function* handleAssetMakeData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetMakeData,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_MAKE_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_MAKE_DATA)
    }
  }

  function* handleAddAssetMake(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.addAssetMake,
        data.data,
      )
      yield sendPayload(apiResponse, ADD_ASSET_MAKE)
    } catch (e) {
      yield sendPayloadFailure(e, ADD_ASSET_MAKE)
    }
  }

  function* handleAssetMakeDelete(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.deleteAssetMake,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_MAKE_DELETE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_MAKE_DELETE)
    }
  }

  function* handleAssetStatusData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetStatusData,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_STATUS_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_STATUS_DATA)
    }
  }

  function* handleAddAssetStatus(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.addAssetStatus,
        data.data,
      )
      yield sendPayload(apiResponse, ADD_ASSET_STATUS)
    } catch (e) {
      yield sendPayloadFailure(e, ADD_ASSET_STATUS)
    }
  }

  function* handleAssetStatusDelete(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.deleteAssetStatus,
        data.data,
      )
      yield sendPayload(apiResponse, ASSET_STATUS_DELETE)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_STATUS_DELETE)
    }
  }

  function* handleTechnologiesData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getTechnologiesData,
        data.data,
      )
      yield sendPayload(apiResponse, TECHNOLOGIES_DATA)
    } catch (e) {
      yield sendPayloadFailure(e, TECHNOLOGIES_DATA)
    }
  }

  function* handleAddTechnologies(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.addTechnologies,
        data.data,
      )
      yield sendPayload(apiResponse, ADD_TECHNOLOGIES)
    } catch (e) {
      yield sendPayloadFailure(e, ADD_TECHNOLOGIES)
    }
  }

  function* handleTechnologiesDelete(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.deleteTechnologies,
        data.data,
      )
      yield sendPayload(apiResponse, DELETE_TECHNOLOGIES)
    } catch (e) {
      yield sendPayloadFailure(e, DELETE_TECHNOLOGIES)
    }
  }

  function* handleAssetOsDelete(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.deleteAssetOs,
        data.data,
      )
      yield sendPayload(apiResponse, DELETE_ASSET_OS)
    } catch (e) {
      yield sendPayloadFailure(e, DELETE_ASSET_OS)
    }
  }

  function* handleEmployeeAssetCountData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getEmployeeAssetCountData,
        data.data,
      )
      yield sendPayload(apiResponse, EMPLOYEE_ASSETS_COUNT)
    } catch (e) {
      yield sendPayloadFailure(e, EMPLOYEE_ASSETS_COUNT)
    }
  }

  function* handleAssetAbbreationCountData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetAbbrevationCountData,
        data,
      )
      yield sendPayload(apiResponse, ASSET_ABBREVATION_COUNT)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_ABBREVATION_COUNT)
    }
  }


  function* handleAssetStatusCountData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssetStatusCountData,
        data,
      )
      yield sendPayload(apiResponse, ASSET_STATUS_COUNT)
    } catch (e) {
      yield sendPayloadFailure(e, ASSET_STATUS_COUNT)
    }
  }
 
  function* handleAssignAssetReportData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getAssignAssetReportData,
        data,
      )
      yield sendPayload(apiResponse, ASSIGN_ASSET_REPORT)
    } catch (e) {
      yield sendPayloadFailure(e, ASSIGN_ASSET_REPORT)
    }
  }

  function* handleDownloadAssetsQRData(data: any) {
    try {
      const apiResponse: Generator<string, number, string> = yield call(
        ApiService.getDownloadAssetsQrData,
        data,
      )
      yield sendPayload(apiResponse, DOWNLOAD_ASSETS_QR)
    } catch (e) {
      yield sendPayloadFailure(e, DOWNLOAD_ASSETS_QR)
    }
  }

  function* handleDownloadQR(data: any) {
    try {
      
      const apiResponse: Generator<String, number, string> = yield call(
        ApiService.downloadQR,
        data.data,
      )
      yield sendPayload(apiResponse, DOWNLOAD_QR)
    } catch (e) {
      yield sendPayloadFailure(e, DOWNLOAD_QR)
    }
  }

export const sagas = {
    handleAssetsData: takeLatest(ASSETS_DATA[REQUEST], handleAssetsData),
    handleNumberOfAssetsData: takeLatest(FETCH_NUMBER_OF_ASSETS[REQUEST], handleNumberOfAssetsData),
    handleEmpData: takeLatest(FETCH_EMP_DATA[REQUEST], handleEmpData),
    handleAllAssetsList: takeLatest(FETCH_ASSETS[REQUEST], handleAllAssetsList), 
    handleAssignAsset: takeLatest(ASSIGN_ASSET[REQUEST], handleAssignAsset),
    handleEmployeeAssets: takeLatest(EMPLOYEE_ASSETS_DETAILS[REQUEST], handleEmployeeAssets),
    handleUnallocateAsset: takeLatest(UNALLOCATE_ASSET[REQUEST], handleUnallocateAsset),
    handleAddNewAsset: takeLatest(ADD_NEW_ASSET[REQUEST], handleAddNewAsset),
    handleWorkEmpData: takeLatest(FETCH_EMP_ASSET_DATA[REQUEST], handleWorkEmpData),
    handleAssetsDataAsExcel: takeLatest(FETCH_ASSETS_DATA_AS_EXCEL[REQUEST], handleAssetsDataAsExcel),
    handleAssetsAbberation: takeLatest(ASSET_ABBERATION[REQUEST], handleAssetsAbberation),
    handleAssetLocation: takeLatest(ASSET_LOCATION[REQUEST], handleAssetLocation),
    handleAssetMake: takeLatest(ASSET_MAKE[REQUEST], handleAssetMake),
    handleAssetStatus: takeLatest(ASSET_STATUS[REQUEST], handleAssetStatus),
    handleAssetOS: takeLatest(ASSET_OS[REQUEST], handleAssetOS),
    handleAssetTechnologies: takeLatest(ASSET_TECHNOLOGIES[REQUEST], handleAssetTechnologies),
    handleAssetCategoryData: takeLatest(ASSET_CATEGORY[REQUEST], handleAssetCategoryData),
    handleAssetOSData: takeLatest(ASSET_OS_DATA[REQUEST], handleAssetOSData),
    handleAssetOSUpdate: takeLatest(ASSET_OS_UPDATE[REQUEST], handleAssetOSUpdate),
    handleAssetCategoryUpdate: takeLatest(ASSET_CATEGORY_UPDATE[REQUEST], handleAssetCategoryUpdate),
    handleAssetCategoryDelete: takeLatest(ASSET_CATEGORY_DELETE[REQUEST], handleAssetCategoryDelete),
    handleAssetMakeData: takeLatest(ASSET_MAKE_DATA[REQUEST], handleAssetMakeData),
    handleAddAssetMake: takeLatest(ADD_ASSET_MAKE[REQUEST], handleAddAssetMake),
    handleAssetMakeDelete: takeLatest(ASSET_MAKE_DELETE[REQUEST], handleAssetMakeDelete),
    handleAssetStatusData: takeLatest(ASSET_STATUS_DATA[REQUEST], handleAssetStatusData),
    handleAddAssetStatus: takeLatest(ADD_ASSET_STATUS[REQUEST], handleAddAssetStatus),
    handleAssetStatusDelete: takeLatest(ASSET_STATUS_DELETE[REQUEST], handleAssetStatusDelete),
    handleTechnologiesData: takeLatest(TECHNOLOGIES_DATA[REQUEST], handleTechnologiesData),
    handleAddTechnologies: takeLatest(ADD_TECHNOLOGIES[REQUEST], handleAddTechnologies),
    handleTechnologiesDelete: takeLatest(DELETE_TECHNOLOGIES[REQUEST], handleTechnologiesDelete),
    handleAssetOsDelete: takeLatest(DELETE_ASSET_OS[REQUEST],handleAssetOsDelete),
    handleEmployeeAssetCountData: takeLatest(EMPLOYEE_ASSETS_COUNT[REQUEST], handleEmployeeAssetCountData),
    handleAssetAbbreationCountData: takeLatest(ASSET_ABBREVATION_COUNT[REQUEST], handleAssetAbbreationCountData),
    handleAssetStatusCountData: takeLatest(ASSET_STATUS_COUNT[REQUEST], handleAssetStatusCountData),
    handleAssignAssetReportData: takeLatest(ASSIGN_ASSET_REPORT[REQUEST], handleAssignAssetReportData),
    handleDownloadAssetsQRData: takeLatest(DOWNLOAD_ASSETS_QR[REQUEST], handleDownloadAssetsQRData),
    handleDownloadQR: takeLatest(DOWNLOAD_QR[REQUEST], handleDownloadQR),
}