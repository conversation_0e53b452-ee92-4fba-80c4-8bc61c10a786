import { call, takeLatest } from 'redux-saga/effects'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import {
  FETCH_POSITIONS,
  FETCH_RECRUITERS,
  <PERSON><PERSON><PERSON>_ROUND,
  <PERSON><PERSON><PERSON>_TAGS,
  REQUE<PERSON>,
  <PERSON><PERSON><PERSON>_TEMPLATE_DETAILS,
  DELETE_RECRUITMENT_TEMPLATE,
  <PERSON>ETCH_COLLEGE_DETAILS,
  DELETE_COLLEGE,
  <PERSON>ETCH_ADDQUALIFICATION,
  FETCH_BATCHES,
  <PERSON><PERSON><PERSON>_TAG_CANDIDATES,
  FETCH_BLOCK_DOMAIN_DATA,
  CREATE_STATIC_DATA,
  <PERSON>ETCH_TPO_DETAILS,
  <PERSON><PERSON><PERSON>_EMAIL_TEMPLATES,
  <PERSON>ETCH_STATIC_DATA,
  FETCH_INTERVIEWER,
  FETCH_ROUNDCANDIDATES,
  <PERSON>ETCH_TAGCANDIDATES,
  ADD_REJECTED_SUBJECT,
  ADD_MULTIPLE_CANDIDATES,
  ADD_JOB_EXPERIENCE,
  <PERSON><PERSON><PERSON>_DATE_TIME_BY_ROUND,
  <PERSON><PERSON><PERSON>_TEMPLATE_BY_ROUND,
  FETCH_CANDIDATE_BY_FILTERS,
  DELETE_CANDIDATE_BY_ID,
  SENDMAIL_CANDIDATE_BY_IDS,
  VIEW_ATTACHMENTS_CANDIDATE,
  FETCH_VIDEO_URL,
  FETCH_INTERVIEWER_PANNEL,
  FETCH_BLOCKEDSUBJECTS,
  FETCH_EXPERIENCES,
  FETCH_APPLICANTS_QUALIFICATION,
  FETCH_UNAPPROVED_CANDIDATE,
  FETCH_SPAMING,
  DELETE_UNAPPROVED_CANDIDATE,
  DELETE_EXPECTED_JOINERS,
  ADD_DRIVE,
  EDIT_ORG,
  FETCH_INTERVIEWER_WORK,
  CANDIDATE_COUNT_BY_ROUND,
  FETCH_JOINED_CANDIDATES,
  FETCH_CANDIDATEBYID,
  ADD_MANAGEQUALIFICATION,
  ADD_MANAGEBATCHES,
  DELETE_QUALIFICATION,
  DELETE_BATCH,
  EDIT_BATCH,
  EDIT_QUALIFICATION,
  FETCH_REPRESENTATIVE_TYPE,
  ADD_ORG_DETAILS,
  ADD_USER_DETAILS,
  ADD_DATE_TIME_DETAILS,
  DELETE_ORG_DETAILS,
  DELETE_USER_DETAILS,
  DELETE_DATE_TIME_DETAILS,
  EDIT_ORG_DETAILS,
  EDIT_USER_DETAILS,
  ADD_EMAIL_TEMPLATE_DATA,
  FETCH_ROUNDS_FOR_TEMPLATE,
  EDIT_TEMPLATE,
  EDIT_CANDIDATE_INLINE,
  FETCH_INSTITUTE_DETAILS,
  ADD_JOB_POSITION,
  ADD_INST_DETAILS,
  EDIT_INST_DETAILS,
  SUB_INST_DETAILS,
  DEL_INST_DETAILS,
  ROUND_FEEDBACK_DROPDOWN,
  FETCH_JOB_EXPERIENCE,
  ADD_STATIC_DATA,
  ADD_INTERVIEWER_DATA,
  EDIT_STATIC_DATA,
  DELETE_INTERVIEWER_DATA,
  DELETE_STATIC_DATA,
  EDIT_INTERVIEWER_DATA,
  FETCH_EXPECTED_JOINERS,
  ADD_EXPECTED_JOINERS,
  EDIT_EXPECTED_JOINERS,
  DELETE_BLOCKED_BODY,
  GET_BLOCKED_BODY,
  ADD_BLOCKED_BODY,
  ADD_REJECTED_BODY,
  DELETE_REJECTED_BODY,
  EDIT_REJECTED_BODY,
  FETCH_NEW_TAG,
  FETCH_EDIT_TAG,
  FETCH_DELETE_TAG,
  FETCH_ROUND_TYPE,
  FETCH_ADD_ROUND,
  FETCH_ROUNDS_BY_TYPE,
  DELETE_ROUNDS,
  EDIT_ROUNDS,
  DELETE_TPO,
  EDIT_TPO,
  ADD_TPO,
  ADD_COMMENT_PLANFORTHEDAY,
  ORGANISATION_DETAILS_BY_TYPE,
  FETCH_FEEDBACK,
  FETCH_CANDIDATEPOSITION,
  ADD_BLOCK_DOMAIN_DATA,
  DELETE_BLOCK_DOMAIN_DATA,
  EDIT_BLOCK_DOMAIN_DATA,
  ADD_CANDIDATE_FEEDBACK,
  DELETE_ATTACHMENT,
  DELETE_CANDIDATE_FEEDBACK,
  EDIT_CANDIDATE,
  FETCH_BATCH_DROPDOWN,
  EDIT_DATE_TIME,
  EDIT_REJECTED_SUBJECT,
  EDIT_JOB_EXPERIENCE,
  DELETE_JOB_EXPERIENCE,
  DELETE_REJECTED_SUBJECT,
  EDIT_POSITION_DETAILS,
  SUB_POSITION_DETAILS,
  DELETE_POSITION_DETAILS,
  ADD_FEEDBACK,
  DELETE_FEEDBACK,
  ADD_CANDIDATE,
  ADD_RESUME,
  EDIT_CANDIDATEFORM,
  VIEW_RECRUITER_CALLS,
  VIEW_CANDIDATE_FEEDBACK,
  VIEW_CANDIDATE_TAGS,
  FETCH_SEND_REMAINDER,
  FETCH_COOLING_OFF_PERIOD,
  ADD_UPLOAD_RESUME,
  ADD_UPLOAD_ASSIGNMENT,
  EDIT_FEEDBACK,
  SUB_EDIT_FEEDBACK,
  DELETE_MULTIPLE_APPLICANTS,
  APPLICANTS_REJECT,
  APPLICANTS_APPROVE,
  FETCH_APPLICANTS_RESUME,
  FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID,
  TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID,
  VIEW_CANDIDATE_LOGS,
} from '../actions/actiontypes'
import { FETCH_DATEANDTIME, FETCH_ORGANISATIONDETAILS, FETCH_USER } from '../actions/actiontypes'
import { ApiResponse, payloadType } from './Types'
import { CollegesPayload } from 'components/Types'
import { editBatch } from 'actions'
import { toast } from 'react-toastify'

function* handleGetRoundsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getRoundEndPoint)
    yield sendPayload(apiResponse, FETCH_ROUND)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ROUND)
  }
}

function* handlePositionsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getPositionEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_POSITIONS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_POSITIONS)
  }
}

function* handleGetTagsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getTagEndPoint)
    yield sendPayload(apiResponse, FETCH_TAGS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TAGS)
  }
}

function* handleGetRecruitmentsData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getRecruitmentEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_RECRUITERS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_RECRUITERS)
  }
}

function* handleGetTpoDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getTpoDetailsEndPoint,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_TPO_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TPO_DETAILS)
  }
}

function* handleGetRoundType() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEndRoundTypesPointEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_ROUND_TYPE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ROUND_TYPE)
  }
}
function* handleGetExperiencesData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getExperienceEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_EXPERIENCES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EXPERIENCES)
  }
}
function* handleGetBlockedSubjectsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getBlockedSubjectEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_BLOCKEDSUBJECTS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_BLOCKEDSUBJECTS)
  }
}

function* handleGetCandidateRoundsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getCandidateRoundEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_ROUNDCANDIDATES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ROUNDCANDIDATES)
  }
}
function* handleGetCandidateTagsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getCandidateTagEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_TAGCANDIDATES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TAGCANDIDATES)
  }
}

function* handleGetDateTimeByRoundData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDateTimeByRoundEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_DATE_TIME_BY_ROUND)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_DATE_TIME_BY_ROUND)
  }
}

function* handleGetTemplateByRound(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getTemplateByRoundEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_TEMPLATE_BY_ROUND)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TEMPLATE_BY_ROUND)
  }
}

function* handleGetCandidateByFilters(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getCandidatesByFiltersEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_CANDIDATE_BY_FILTERS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_CANDIDATE_BY_FILTERS)
  }
}

function* handleDeleteCandidateById(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteCandidateByIdEndPOint,
      data.data,
    )
    yield sendPayload(apiResponse, DELETE_CANDIDATE_BY_ID)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_CANDIDATE_BY_ID)
  }
}

function* getTemplateDetails() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getAllTemplates)
    yield sendPayload(apiResponse, FETCH_TEMPLATE_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TEMPLATE_DETAILS)
  }
}
function* handleBatches() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getBatchesEndPoint)
    yield sendPayload(apiResponse, FETCH_BATCHES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_BATCHES)
  }
}

function* handleAddQualification() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddQualificationEndPOint,
    )
    yield sendPayload(apiResponse, FETCH_ADDQUALIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ADDQUALIFICATION)
  }
}

function* deleteRecruitmentTemplate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postDeleteTemplate,
      data,
    )
    yield sendPayload(apiResponse, DELETE_RECRUITMENT_TEMPLATE)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_RECRUITMENT_TEMPLATE)
  }
}

function* deleteCollege(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postDeleteCollege,
      data,
    )
    yield sendPayload(apiResponse, DELETE_COLLEGE)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_COLLEGE)
  }
}

function* getCollegeDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllColleges,
      data,
    )
    yield sendPayload(apiResponse, FETCH_COLLEGE_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_COLLEGE_DETAILS)
  }
}

function* handleGetExpectedJoinerData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getExpectedJoinerEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_EXPECTED_JOINERS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EXPECTED_JOINERS)
  }
}

function* handleGetFeedback() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getFeedbackEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_FEEDBACK)
  }
}

function* handleGetCandidatePosition() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getCandidatePositionEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_CANDIDATEPOSITION)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_CANDIDATEPOSITION)
  }
}

function* handleAddJobPosition(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(ApiService.AddJobPositionEndPoint, data.data)
    yield sendPayload(apiResponse, ADD_JOB_POSITION)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_JOB_POSITION)
  }
}

function* handleFetchTagCandidates() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewTagsCandidatesCountEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_TAG_CANDIDATES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_TAG_CANDIDATES)
  }
}

function* handleGetBlockDomainsData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getBlockDataEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_BLOCK_DOMAIN_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_BLOCK_DOMAIN_DATA)
  }
}

function* handleAddMultipleCandidates(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addMultipleCandidatesEndPoint,
      payload,
    )
    if (apiResponse) toast.success('Candidate uploaded successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, ADD_MULTIPLE_CANDIDATES)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_MULTIPLE_CANDIDATES)
  }
}

function* handleAddRejectedSubjects(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addRejectedSubjectsEndPoint,
      data.data,
    )
    if (apiResponse) toast.success('Subject added successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, ADD_REJECTED_SUBJECT)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_REJECTED_SUBJECT)
  }
}

function* handleAddJobExperiences(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addJobExperiencesEndPoint,
      data.data,
    )
    if (apiResponse) toast.success('Experience added successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, ADD_JOB_EXPERIENCE)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_JOB_EXPERIENCE)
  }
}

function* handleAddExpectedJoiners(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addExpectedJoinersEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_EXPECTED_JOINERS)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_EXPECTED_JOINERS)
  }
}

function* getEmailTemplates() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEmailTemplatesEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_EMAIL_TEMPLATES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EMAIL_TEMPLATES)
  }
}
function* handleStaticData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getStaticData)
    yield sendPayload(apiResponse, FETCH_STATIC_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_STATIC_DATA)
  }
}
function* handleGetRepresentativeType() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getRepresentativeTypeEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_REPRESENTATIVE_TYPE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_REPRESENTATIVE_TYPE)
  }
}

function* handleInterviewer(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getInterviewer,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_INTERVIEWER)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_INTERVIEWER)
  }
}

function* handleDelInstDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(ApiService.DelInstDetailsEndPoint, data.data)
    yield sendPayload(apiResponse, DEL_INST_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, DEL_INST_DETAILS)
  }
}

function* handleroundfeedbackDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(
      ApiService.roundfeedbackDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ROUND_FEEDBACK_DROPDOWN)
  } catch (e) {
    yield sendPayloadFailure(e, ROUND_FEEDBACK_DROPDOWN)
  }
}

function* handleCreateStaticData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getInterviewer,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_STATIC_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_STATIC_DATA)
  }
}

function* handleGetRoundsByType(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getRoundsByTypeEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_ROUNDS_BY_TYPE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ROUNDS_BY_TYPE)
  }
}

function* handleDeleteExpectedJoiners(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.fetchDeleteExpectedJoinersEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_EXPECTED_JOINERS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_EXPECTED_JOINERS)
  }
}

function* handleSendmailCandidateByIds(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.sendmailCandidateEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, SENDMAIL_CANDIDATE_BY_IDS)
  } catch (e) {
    yield sendPayloadFailure(e, SENDMAIL_CANDIDATE_BY_IDS)
  }
}

function* handleViewAttachmentsCandidate(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getViewAttachmentsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, VIEW_ATTACHMENTS_CANDIDATE)
  } catch (e) {
    yield sendPayloadFailure(e, VIEW_ATTACHMENTS_CANDIDATE)
  }
}

function* handleGetVideoUrl(data: payloadType): Generator<unknown, void, ApiResponse> {
  try {
    const apiResponse: ApiResponse = yield call(ApiService.getVideoUrlEndPoint, data.data)

    const customizedResponse = {
      data: {
        data: apiResponse.data,
      },
      status: apiResponse.status,
      statusText: apiResponse.statusText,
      headers: apiResponse.headers,
      config: apiResponse.config,
      request: apiResponse.request,
    }

    yield sendPayload(customizedResponse, FETCH_VIDEO_URL)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_VIDEO_URL)
  }
}

function* handleGetInterviewerPannel() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getInterviewerPannelEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_INTERVIEWER_PANNEL)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_INTERVIEWER_PANNEL)
  }
}
function* handleCandidateByID(action: any) {
  try {
    const { id } = action
    const apiResponse: Generator<string> = yield call(ApiService.getCandidateByIDEndPOint, {
      id: id,
    })
    yield sendPayload(apiResponse, FETCH_CANDIDATEBYID)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_CANDIDATEBYID)
  }
}

function* handleaddManageQualification(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addManageQualificationEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_MANAGEQUALIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_MANAGEQUALIFICATION)
  }
}

function* handleaddManageBatches(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addManageBatchesEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_MANAGEBATCHES)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_MANAGEBATCHES)
  }
}

function* deleteQualification(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteQualificationEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_QUALIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_QUALIFICATION)
  }
}

function* deleteBatch(data: any) {
  try {
    const apiResponse: Generator<number> = yield call(ApiService.deleteBatchEndPoint, data)
    yield sendPayload(apiResponse, DELETE_BATCH)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_BATCH)
  }
}

function* handleEditBatch(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editBatchEndPoint,
      payload,
    )
    if (apiResponse) {
      toast.success('Batch Edited Succesfully')
    }
    yield sendPayload(apiResponse, EDIT_BATCH)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_BATCH)
  }
}

function* handleEditQualification(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editQualificationEndPoint,
      payload,
    )
    yield sendPayload(apiResponse, EDIT_QUALIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_QUALIFICATION)
  }
}

function* getQualification() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllQualification,
    )
    yield sendPayload(apiResponse, FETCH_APPLICANTS_QUALIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_APPLICANTS_QUALIFICATION)
  }
}

function* handleGetUnApprovedCandidate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllUnapprovedCandidate,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_UNAPPROVED_CANDIDATE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_UNAPPROVED_CANDIDATE)
  }
}

function* handleGetSpaming(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllSpaming,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_SPAMING)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_SPAMING)
  }
}

function* handleDeleteUnapprovedCandidate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteAllUnapprovedCandidate,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_SPAMING)

    yield sendPayload(apiResponse, FETCH_UNAPPROVED_CANDIDATE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_UNAPPROVED_CANDIDATE)
  }
}

function* handleNewTag(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllNewTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_NEW_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_NEW_TAG)
  }
}

function* handledit(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllEditTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_EDIT_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EDIT_TAG)
  }
}

function* handleDelete(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllDeleteTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_DELETE_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_DELETE_TAG)
  }
}

function* handlePostDriveDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postDriveData,
      data,
    )
    yield sendPayload(apiResponse, ADD_DRIVE)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_DRIVE)
  }
}

function* handleAddBlockDomains(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addBlockDomainsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_BLOCK_DOMAIN_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_BLOCK_DOMAIN_DATA)
  }
}

function* handleDeleteBlockDomains(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteBlockDomainsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_BLOCK_DOMAIN_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_BLOCK_DOMAIN_DATA)
  }
}
function* handleEditBlockDomains(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editBlockDomainsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, EDIT_BLOCK_DOMAIN_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_BLOCK_DOMAIN_DATA)
  }
}

function* handleAddFeedbackCandidate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addFeedbackCandidateEndPoint,
      data,
    )
    yield sendPayload(apiResponse, ADD_CANDIDATE_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_CANDIDATE_FEEDBACK)
  }
}

function* handleDeleteAttachment(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteAttachmentEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_ATTACHMENT)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_ATTACHMENT)
  }
}

function* handleDeleteCandidateFeedback(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteCandidateFeedbackEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_CANDIDATE_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_CANDIDATE_FEEDBACK)
  }
}

function* handleEditCandidate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editCandidateEndPoint,
      data,
    )
    yield sendPayload(apiResponse, EDIT_CANDIDATE)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_CANDIDATE)
  }
}

function* handlePostOrgDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.postOrgData, data)
    yield sendPayload(apiResponse, EDIT_ORG)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_ORG)
  }
}

function* handleAddOrganisationDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddOrganisationDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_ORG_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_ORG_DETAILS)
  }
}

function* handleAddUserDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddUserDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_USER_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_USER_DETAILS)
  }
}

function* handleAddDateTimeDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddDateTimeDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_DATE_TIME_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_DATE_TIME_DETAILS)
  }
}

function* handleDeleteOrgDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDeleteOrgDetailsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_ORG_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_ORG_DETAILS)
  }
}

function* handleDeleteUserDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDeleteUserDetailsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_USER_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_USER_DETAILS)
  }
}

function* handleDeleteDateTimeDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDeleteDateTimeDetailsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_DATE_TIME_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_DATE_TIME_DETAILS)
  }
}

function* handleEditOrgDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditOrgDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_ORG_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_ORG_DETAILS)
  }
}

function* handleEditUserDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditUserDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_USER_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_USER_DETAILS)
  }
}
function* handleAddEmailTemplateData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postEmailTemplateData,
      data,
    )
    yield sendPayload(apiResponse, ADD_EMAIL_TEMPLATE_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_EMAIL_TEMPLATE_DATA)
  }
}

function* handleRoundsForTemplate() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getRoundsForTemplate,
    )
    yield sendPayload(apiResponse, FETCH_ROUNDS_FOR_TEMPLATE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ROUNDS_FOR_TEMPLATE)
  }
}

function* handlePostTemplateDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postTemplateData,
      data,
    )
    yield sendPayload(apiResponse, EDIT_TEMPLATE)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_TEMPLATE)
  }
}

function* handleInterviewerWork(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postInterviewerWork,
      data,
    )
    yield sendPayload(apiResponse, FETCH_INTERVIEWER_WORK)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_INTERVIEWER_WORK)
  }
}

function* handleCandidateCount(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postCandidateCount,
      data,
    )
    yield sendPayload(apiResponse, CANDIDATE_COUNT_BY_ROUND)
  } catch (e) {
    yield sendPayloadFailure(e, CANDIDATE_COUNT_BY_ROUND)
  }
}

function* handleJoinedCandidates(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postJoinedCandidates,
      data,
    )
    yield sendPayload(apiResponse, FETCH_JOINED_CANDIDATES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_JOINED_CANDIDATES)
  }
}

function* handleEditCandidateInline(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditCandidateInlineEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_CANDIDATE_INLINE)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_CANDIDATE_INLINE)
  }
}
function* handleEditRejectedSubjects(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editRejectedSubjectsEndPoint,
      payload,
    )
    if (apiResponse) toast.success('Subject updated successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, EDIT_REJECTED_SUBJECT)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_REJECTED_SUBJECT)
  }
}

function* handleJobExperience() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getJobExperience)
    yield sendPayload(apiResponse, FETCH_JOB_EXPERIENCE)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_JOB_EXPERIENCE)
  }
}
function* handleViewRecruitersCalls(data: payloadType) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewRecruitersCallsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, VIEW_RECRUITER_CALLS)
  } catch (e) {
    yield sendPayloadFailure(e, VIEW_RECRUITER_CALLS)
  }
}

function* handleAddStaticData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postStaticData,
      data,
    )
    yield sendPayload(apiResponse, ADD_STATIC_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_STATIC_DATA)
  }
}
function* handleAddInterviewerData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postInterviewerData,
      data,
    )
    yield sendPayload(apiResponse, ADD_INTERVIEWER_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_INTERVIEWER_DATA)
  }
}

function* handleEditStaticData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postEditStaticData,
      data,
    )
    yield sendPayload(apiResponse, EDIT_STATIC_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_STATIC_DATA)
  }
}
function* handleEditInterviewerData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postEditInterviewerData,
      data,
    )
    yield sendPayload(apiResponse, EDIT_INTERVIEWER_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_INTERVIEWER_DATA)
  }
}

function* handleDeleteStaticData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postDeleteStaticData,
      data,
    )
    yield sendPayload(apiResponse, DELETE_STATIC_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_STATIC_DATA)
  }
}
function* handleDeleteInterviewerData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.postDeleteInterviewerData,
      data,
    )
    yield sendPayload(apiResponse, DELETE_INTERVIEWER_DATA)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_INTERVIEWER_DATA)
  }
}

function* handleGetDateandTime() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDateandTimeEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_DATEANDTIME)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_DATEANDTIME)
  }
}
function* handleGetOrganisationDetails() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getOrganisationDetailsEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_ORGANISATIONDETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ORGANISATIONDETAILS)
  }
}

function* handleGetUser(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getUserEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_USER)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_USER)
  }
}

function* handleGetInstituteDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(
      ApiService.getInstituteDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_INSTITUTE_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_INSTITUTE_DETAILS)
  }
}

function* handleAddInstDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(ApiService.AddInstDetailsEndPoint, data.data)
    yield sendPayload(apiResponse, ADD_INST_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_INST_DETAILS)
  }
}

function* handleSubInstDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(ApiService.SubInstDetailsEndPoint, data.data)
    yield sendPayload(apiResponse, SUB_INST_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, SUB_INST_DETAILS)
  }
}

function* handleEditInstDetails(data: any) {
  try {
    const apiResponse: Generator<string> = yield call(ApiService.EditInstDetailsEndPoint, data.data)
    yield sendPayload(apiResponse, EDIT_INST_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_INST_DETAILS)
  }
}
function* handleEditExpectedJoiners(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editExpectedJoinersEndPoint,
      data.data,
    )

    yield sendPayload(apiResponse, EDIT_EXPECTED_JOINERS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_EXPECTED_JOINERS)
  }
}

function* handleAddBlockedBody(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addBlockedBodyEndPoint,
      data.data,
    )

    yield sendPayload(apiResponse, ADD_BLOCKED_BODY)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_BLOCKED_BODY)
  }
}

function* handleGetBlockedBody() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getBlockedBodyEndPoint,
    )

    yield sendPayload(apiResponse, GET_BLOCKED_BODY)
  } catch (e) {
    yield sendPayloadFailure(e, GET_BLOCKED_BODY)
  }
}

function* addRejectedBody(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addRejectedBodyEndPoint,
      data.data,
    )

    yield sendPayload(apiResponse, ADD_REJECTED_BODY)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_REJECTED_BODY)
  }
}

function* handleDeleteRejectedBody(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteRejectedBodyEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_REJECTED_BODY)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_REJECTED_BODY)
  }
}

function* handleEditRejectedBody(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editRejectedBodyEndPoint,
      payload,
    )
    yield sendPayload(apiResponse, EDIT_REJECTED_BODY)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_REJECTED_BODY)
  }
}

function* handleTagNew(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllNewTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_NEW_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_NEW_TAG)
  }
}

function* handleditTag(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllEditTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_EDIT_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EDIT_TAG)
  }
}

function* handleDeleteTag(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAllDeleteTag,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_DELETE_TAG)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_DELETE_TAG)
  }
}

function* handleAddRound(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddRoundsEndPoint,
      data.data,
    )

    yield sendPayload(apiResponse, FETCH_ADD_ROUND)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ADD_ROUND)
  }
}

function* handleDeleteRounds(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteRoundsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_ROUNDS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_ROUNDS)
  }
}

function* handleDeleteTpo(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteTpoEndPoint,
      data,
    )
    yield sendPayload(apiResponse, DELETE_TPO)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_TPO)
  }
}

function* handleEditRounds(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditRoundsEndPoint,
      data,
    )
    yield sendPayload(apiResponse, EDIT_ROUNDS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_ROUNDS)
  }
}

function* handleEditTpo(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditTpoEndPoint,
      data,
    )
    yield sendPayload(apiResponse, EDIT_TPO)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_TPO)
  }
}

function* handleAddTpo(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddTpoEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_TPO)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_TPO)
  }
}

function* viewOrganisationDetailsByType(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewOrganisationDetailsByType,
      data.data,
    )
    yield sendPayload(apiResponse, ORGANISATION_DETAILS_BY_TYPE)
  } catch (e) {
    yield sendPayloadFailure(e, ORGANISATION_DETAILS_BY_TYPE)
  }
}
function* handleGetBatchDropdownData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getBatchDropdownEndPoint,
    )
    yield sendPayload(apiResponse, FETCH_BATCH_DROPDOWN)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_BATCH_DROPDOWN)
  }
}
function* handleEditDateTime(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditDateTimeEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_DATE_TIME)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_DATE_TIME)
  }
}

function* handleEditJobExperiences(payload: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editJobExperiencesEndPoint,
      payload,
    )
    if (apiResponse) toast.success('Experience updated successfully!', { position: 'top-right' })

    yield sendPayload(apiResponse, EDIT_JOB_EXPERIENCE)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_JOB_EXPERIENCE)
  }
}
function* handleDeleteJobExperiences(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteJobExperiencesEndPoint,
      data,
    )
    if (apiResponse) toast.success('Experience deleted successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, DELETE_JOB_EXPERIENCE)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_JOB_EXPERIENCE)
  }
}
function* handleDeleteRejectedSubjects(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.deleteRejectedSubjectsEndPoint,
      data,
    )
    if (apiResponse) toast.success('Subject deleted successfully!', { position: 'top-right' })
    yield sendPayload(apiResponse, DELETE_REJECTED_SUBJECT)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_REJECTED_SUBJECT)
  }
}
function* handleEditPositionDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditPositionDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_POSITION_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_POSITION_DETAILS)
  }
}

function* handleSubmitPositionDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getSubmitPositionDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, SUB_POSITION_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, SUB_POSITION_DETAILS)
  }
}

function* handleDeletePositionDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDeletePositionDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, DELETE_POSITION_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_POSITION_DETAILS)
  }
}

function* handleAddFeedbackDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAddFeedbackDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_FEEDBACK)
  }
}

function* handleDeleteFeedbackDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDeleteFeedbackDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, DELETE_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_FEEDBACK)
  }
}

function* handleaddCandidate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addCandidateEndPoint,
      data,
    )
    yield sendPayload(apiResponse, ADD_CANDIDATE)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_CANDIDATE)
  }
}

function* handleeditCandidateForm(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editCandidateFormEndPoint,
      data,
    )
    if (apiResponse) {
      toast.success('Candidate Updated Succesfully')
    }
    yield sendPayload(apiResponse, EDIT_CANDIDATEFORM)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_CANDIDATEFORM)
  }
}

function* addResume(action: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addResumeEndPoint,
      action,
    )
    yield sendPayload(apiResponse, ADD_RESUME)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_RESUME)
  }
}

function* handleViewCandidateFeedback(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewCandidateFeedbackEndPoint,
      data,
    )
    yield sendPayload(apiResponse, VIEW_CANDIDATE_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, VIEW_CANDIDATE_FEEDBACK)
  }
}

function* handleViewCandidateTags(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewCandidateTagsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, VIEW_CANDIDATE_TAGS)
  } catch (e) {
    yield sendPayloadFailure(e, VIEW_CANDIDATE_TAGS)
  }
}

function* handleSendRemainders() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getSendRemainder)
    yield sendPayload(apiResponse, FETCH_SEND_REMAINDER)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_SEND_REMAINDER)
  }
}

function* handleCoolingOffPeriod() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getCoolingOffPeriod,
    )
    yield sendPayload(apiResponse, FETCH_COOLING_OFF_PERIOD)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_COOLING_OFF_PERIOD)
  }
}

function* handleaddUploadResume(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addUploadResumeEndPoint,
      data,
    )
    yield sendPayload(apiResponse, ADD_UPLOAD_RESUME)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_UPLOAD_RESUME)
  }
}

function* handleaddUploadAssignment(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addUploadAssignmentEndPoint,
      data,
    )
    yield sendPayload(apiResponse, ADD_UPLOAD_ASSIGNMENT)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_UPLOAD_ASSIGNMENT)
  }
}
function* handleEditFeedbackDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getEditFeedbackDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_FEEDBACK)
  }
}
function* handlSubEditFeedbackDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getSubEditFeedbackDetailsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, SUB_EDIT_FEEDBACK)
  } catch (e) {
    yield sendPayloadFailure(e, SUB_EDIT_FEEDBACK)
  }
}

function* viewApprovedApplicants(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getApprovedApplicants,
      data.data,
    )
    yield sendPayload(apiResponse, APPLICANTS_APPROVE)
  } catch (e) {
    yield sendPayloadFailure(e, APPLICANTS_APPROVE)
  }
}

function* viewRejectedApplicants(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getRejectedApplicants,
      data.data,
    )
    yield sendPayload(apiResponse, APPLICANTS_REJECT)
  } catch (e) {
    yield sendPayloadFailure(e, APPLICANTS_REJECT)
  }
}

function* viewMultipleDeleteApplicants(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getMultipleDeleteApplicants,
      data.data,
    )
    yield sendPayload(apiResponse, DELETE_MULTIPLE_APPLICANTS)
  } catch (e) {
    yield sendPayloadFailure(e, DELETE_MULTIPLE_APPLICANTS)
  }
}

function* viewApplicantsResume(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getApplicantsResume,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_APPLICANTS_RESUME)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_APPLICANTS_RESUME)
  }
}

function* getShorcutKeysByCandidateId(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getShorcutKeysByCandidateId,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID)
  }
}

function* toggleShortcutKeyToCandidateId(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.toggleShortcutKeyToCandidateId,
      data.data,
    )
    yield sendPayload(apiResponse, TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID)
  } catch (e) {
    yield sendPayloadFailure(e, TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID)
  }
}

function* handleViewCandidateLogs(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.viewCandidateLogsEndPoint,
      data.data,
    )
    yield sendPayload(apiResponse, VIEW_CANDIDATE_LOGS)
  } catch (e) {
    yield sendPayloadFailure(e, VIEW_CANDIDATE_LOGS)
  }
}

export const sagas = {
  watchGetRoundData: takeLatest(FETCH_ROUND[REQUEST], handleGetRoundsData),
  watchGetRecruitemntData: takeLatest(FETCH_RECRUITERS[REQUEST], handleGetRecruitmentsData),
  watchGetPositionData: takeLatest(FETCH_POSITIONS[REQUEST], handlePositionsData),
  watchGetTagData: takeLatest(FETCH_TAGS[REQUEST], handleGetTagsData),
  watchTemplateDetails: takeLatest(FETCH_TEMPLATE_DETAILS[REQUEST], getTemplateDetails),
  watchDeleteRecruitmentTemplate: takeLatest(
    DELETE_RECRUITMENT_TEMPLATE[REQUEST],
    deleteRecruitmentTemplate,
  ),
  watchDeleteCollege: takeLatest(DELETE_COLLEGE[REQUEST], deleteCollege),
  watchCollegeDetails: takeLatest(FETCH_COLLEGE_DETAILS[REQUEST], getCollegeDetails),
  watchGetBatchesData: takeLatest(FETCH_BATCHES[REQUEST], handleBatches),
  watchGetAddQualificationData: takeLatest(FETCH_ADDQUALIFICATION[REQUEST], handleAddQualification),
  watchGetExpectedJoinerData: takeLatest(
    FETCH_EXPECTED_JOINERS[REQUEST],
    handleGetExpectedJoinerData,
  ),
  watchGetFeedbackData: takeLatest(FETCH_FEEDBACK[REQUEST], handleGetFeedback),
  watchGetCandidatePosition: takeLatest(
    FETCH_CANDIDATEPOSITION[REQUEST],
    handleGetCandidatePosition,
  ),
  watchGetTPOData: takeLatest(FETCH_TPO_DETAILS[REQUEST], handleGetTpoDetails),
  watchGetRoundType: takeLatest(FETCH_ROUND_TYPE[REQUEST], handleGetRoundType),

  watchGetScheduleData: takeLatest(FETCH_DATEANDTIME[REQUEST], handleGetDateandTime),
  watchGetOrganisationData: takeLatest(
    FETCH_ORGANISATIONDETAILS[REQUEST],
    handleGetOrganisationDetails,
  ),
  watchFetchTagCandidates: takeLatest(FETCH_TAG_CANDIDATES[REQUEST], handleFetchTagCandidates),
  watchGetBLockDomainData: takeLatest(FETCH_BLOCK_DOMAIN_DATA[REQUEST], handleGetBlockDomainsData),
  watchEmailTemplateDetails: takeLatest(FETCH_EMAIL_TEMPLATES[REQUEST], getTemplateDetails),
  watchStaticData: takeLatest(FETCH_STATIC_DATA[REQUEST], handleStaticData),
  watchInterviewer: takeLatest(FETCH_INTERVIEWER[REQUEST], handleInterviewer),
  watchCreateStaticData: takeLatest(CREATE_STATIC_DATA[REQUEST], handleCreateStaticData),
  watchGetExperienceData: takeLatest(FETCH_EXPERIENCES[REQUEST], handleGetExperiencesData),
  watchGetBlockedSubjectData: takeLatest(
    FETCH_BLOCKEDSUBJECTS[REQUEST],
    handleGetBlockedSubjectsData,
  ),
  watchQualification: takeLatest(FETCH_APPLICANTS_QUALIFICATION[REQUEST], getQualification),
  watchUnapprovedCandidate: takeLatest(
    FETCH_UNAPPROVED_CANDIDATE[REQUEST],
    handleGetUnApprovedCandidate,
  ),
  watchSpaming: takeLatest(FETCH_SPAMING[REQUEST], handleGetSpaming),

  watchGetCandidateRoundData: takeLatest(
    FETCH_ROUNDCANDIDATES[REQUEST],
    handleGetCandidateRoundsData,
  ),
  watchGetCandidateTagData: takeLatest(FETCH_TAGCANDIDATES[REQUEST], handleGetCandidateTagsData),
  watchAddMultipleCandidates: takeLatest(
    ADD_MULTIPLE_CANDIDATES[REQUEST],
    handleAddMultipleCandidates,
  ),
  watchAddRejectedSubjects: takeLatest(ADD_REJECTED_SUBJECT[REQUEST], handleAddRejectedSubjects),
  watchAddJobExperiences: takeLatest(ADD_JOB_EXPERIENCE[REQUEST], handleAddJobExperiences),

  watchGetRecruitmentData: takeLatest(FETCH_RECRUITERS[REQUEST], handleGetRecruitmentsData),
  watchGetDateTimeByRound: takeLatest(
    FETCH_DATE_TIME_BY_ROUND[REQUEST],
    handleGetDateTimeByRoundData,
  ),
  watchGetTemplateByRound: takeLatest(FETCH_TEMPLATE_BY_ROUND[REQUEST], handleGetTemplateByRound),
  watchGetCandidateByFilters: takeLatest(
    FETCH_CANDIDATE_BY_FILTERS[REQUEST],
    handleGetCandidateByFilters,
  ),
  watchDeleteCandidateById: takeLatest(DELETE_CANDIDATE_BY_ID[REQUEST], handleDeleteCandidateById),
  watchSendmailCandidateByIds: takeLatest(
    SENDMAIL_CANDIDATE_BY_IDS[REQUEST],
    handleSendmailCandidateByIds,
  ),
  watchViewAttachmentsCandidate: takeLatest(
    VIEW_ATTACHMENTS_CANDIDATE[REQUEST],
    handleViewAttachmentsCandidate,
  ),

  watchGetVideoUrl: takeLatest(FETCH_VIDEO_URL[REQUEST], handleGetVideoUrl),
  watchGetInterviewerPannel: takeLatest(
    FETCH_INTERVIEWER_PANNEL[REQUEST],
    handleGetInterviewerPannel,
  ),
  watchDeleteUnapprovedCandidate: takeLatest(
    DELETE_UNAPPROVED_CANDIDATE[REQUEST],
    handleDeleteUnapprovedCandidate,
  ),
  watchGetRoundsByType: takeLatest(FETCH_ROUNDS_BY_TYPE[REQUEST], handleGetRoundsByType),
  watchDeleteExpectedJoiners: takeLatest(
    DELETE_EXPECTED_JOINERS[REQUEST],
    handleDeleteExpectedJoiners,
  ),
  watchDriveDetails: takeLatest(ADD_DRIVE[REQUEST], handlePostDriveDetails),
  watchOrgDetails: takeLatest(EDIT_ORG[REQUEST], handlePostOrgDetails),
  watchGetCandidateByID: takeLatest(FETCH_CANDIDATEBYID[REQUEST], handleCandidateByID),
  watchAddManageQualification: takeLatest(
    ADD_MANAGEQUALIFICATION[REQUEST],
    handleaddManageQualification,
  ),
  watchAddManageBatches: takeLatest(ADD_MANAGEBATCHES[REQUEST], handleaddManageBatches),
  watchDeleteQualification: takeLatest(DELETE_QUALIFICATION[REQUEST], deleteQualification),
  watchDeleteBatch: takeLatest(DELETE_BATCH[REQUEST], deleteBatch),
  watchEditBatch: takeLatest(EDIT_BATCH[REQUEST], handleEditBatch),
  watchEditQualification: takeLatest(EDIT_QUALIFICATION[REQUEST], handleEditQualification),
  watchGetRepresentativeType: takeLatest(
    FETCH_REPRESENTATIVE_TYPE[REQUEST],
    handleGetRepresentativeType,
  ),
  watchGetInterviewer: takeLatest(FETCH_INTERVIEWER[REQUEST], handleInterviewer),
  watchAddOrganisationDetails: takeLatest(ADD_ORG_DETAILS[REQUEST], handleAddOrganisationDetails),
  watchAddUserDetails: takeLatest(ADD_USER_DETAILS[REQUEST], handleAddUserDetails),
  watchAddDateTimeDetails: takeLatest(ADD_DATE_TIME_DETAILS[REQUEST], handleAddDateTimeDetails),
  watchDeleteOrgDetails: takeLatest(DELETE_ORG_DETAILS[REQUEST], handleDeleteOrgDetails),
  watchDeleteUserDetails: takeLatest(DELETE_USER_DETAILS[REQUEST], handleDeleteUserDetails),
  watchDeleteDateTimeDetails: takeLatest(
    DELETE_DATE_TIME_DETAILS[REQUEST],
    handleDeleteDateTimeDetails,
  ),
  watchEditOrgDetails: takeLatest(EDIT_ORG_DETAILS[REQUEST], handleEditOrgDetails),
  watchEditUserDetails: takeLatest(EDIT_USER_DETAILS[REQUEST], handleEditUserDetails),
  watchRoundsForTemplate: takeLatest(FETCH_ROUNDS_FOR_TEMPLATE[REQUEST], handleRoundsForTemplate),
  watchAddEmailTemplateData: takeLatest(
    ADD_EMAIL_TEMPLATE_DATA[REQUEST],
    handleAddEmailTemplateData,
  ),
  watchEditTemplateDetails: takeLatest(EDIT_TEMPLATE[REQUEST], handlePostTemplateDetails),
  watchInterviewerWork: takeLatest(FETCH_INTERVIEWER_WORK[REQUEST], handleInterviewerWork),
  watchCandidateCount: takeLatest(CANDIDATE_COUNT_BY_ROUND[REQUEST], handleCandidateCount),
  watchJoinedCandidate: takeLatest(FETCH_JOINED_CANDIDATES[REQUEST], handleJoinedCandidates),
  watchEditCandidateInline: takeLatest(EDIT_CANDIDATE_INLINE[REQUEST], handleEditCandidateInline),
  watchGetInstituteDetails: takeLatest(FETCH_INSTITUTE_DETAILS[REQUEST], handleGetInstituteDetails),
  watchAddJobPosition: takeLatest(ADD_JOB_POSITION[REQUEST], handleAddJobPosition),
  watchAddInstDetails: takeLatest(ADD_INST_DETAILS[REQUEST], handleAddInstDetails),
  watchEditInstDetails: takeLatest(EDIT_INST_DETAILS[REQUEST], handleEditInstDetails),
  watchSubInstDetails: takeLatest(SUB_INST_DETAILS[REQUEST], handleSubInstDetails),
  watchDelInstDetails: takeLatest(DEL_INST_DETAILS[REQUEST], handleDelInstDetails),
  watchroundfeedbackDetails: takeLatest(
    ROUND_FEEDBACK_DROPDOWN[REQUEST],
    handleroundfeedbackDetails,
  ),
  watchAddExpectedJoiners: takeLatest(ADD_EXPECTED_JOINERS[REQUEST], handleAddExpectedJoiners),
  watchEditExpectedJoiners: takeLatest(EDIT_EXPECTED_JOINERS[REQUEST], handleEditExpectedJoiners),
  watchAddBlockedBody: takeLatest(ADD_EXPECTED_JOINERS[REQUEST], handleAddBlockedBody),
  watchGetBlockedBody: takeLatest(GET_BLOCKED_BODY[REQUEST], handleGetBlockedBody),
  watchAddRejectedBody: takeLatest(ADD_REJECTED_BODY[REQUEST], addRejectedBody),
  watchDeleteRejectedBody: takeLatest(DELETE_REJECTED_BODY[REQUEST], handleDeleteRejectedBody),
  watchEditRejectedBody: takeLatest(EDIT_REJECTED_BODY[REQUEST], handleEditRejectedBody),
  watchAddBlockDomains: takeLatest(ADD_BLOCK_DOMAIN_DATA[REQUEST], handleAddBlockDomains),
  watchDeleteBlockDomains: takeLatest(DELETE_BLOCK_DOMAIN_DATA[REQUEST], handleDeleteBlockDomains),
  watchEditBlockDomains: takeLatest(EDIT_BLOCK_DOMAIN_DATA[REQUEST], handleEditBlockDomains),

  watchAddFeedbackcandidate: takeLatest(
    ADD_CANDIDATE_FEEDBACK[REQUEST],
    handleAddFeedbackCandidate,
  ),
  watchDeleteAttachment: takeLatest(DELETE_ATTACHMENT[REQUEST], handleDeleteAttachment),
  watchDeleteCandidateFeedback: takeLatest(
    DELETE_CANDIDATE_FEEDBACK[REQUEST],
    handleDeleteCandidateFeedback,
  ),

  watchEditCandidate: takeLatest(EDIT_CANDIDATE[REQUEST], handleEditCandidate),
  watchGetBatchDropdownData: takeLatest(FETCH_BATCH_DROPDOWN[REQUEST], handleGetBatchDropdownData),
  watchJobExperience: takeLatest(FETCH_JOB_EXPERIENCE[REQUEST], handleJobExperience),
  watchAddStaticData: takeLatest(ADD_STATIC_DATA[REQUEST], handleAddStaticData),
  watchAddInterviewerData: takeLatest(ADD_INTERVIEWER_DATA[REQUEST], handleAddInterviewerData),
  watchEditStaticData: takeLatest(EDIT_STATIC_DATA[REQUEST], handleEditStaticData),
  watchEditInterviewerData: takeLatest(EDIT_INTERVIEWER_DATA[REQUEST], handleEditInterviewerData),
  watchDeleteStaticData: takeLatest(DELETE_STATIC_DATA[REQUEST], handleDeleteStaticData),
  watchDeleteInterviewerData: takeLatest(
    DELETE_INTERVIEWER_DATA[REQUEST],
    handleDeleteInterviewerData,
  ),

  watchSendRemainder: takeLatest(FETCH_SEND_REMAINDER[REQUEST], handleSendRemainders),
  watchCoolingOffPeriod: takeLatest(FETCH_COOLING_OFF_PERIOD[REQUEST], handleCoolingOffPeriod),

  // watchDateTimeByRound: takeLatest(
  //   DATETIME_BY_ROUND[REQUEST],
  //   handleTemplateByRound,
  // ),

  // watchTemplateByRound: takeLatest(
  //   TEMPLATE_BY_ROUND[REQUEST],
  //   handleDeleteInterviewerData,
  // ),

  watchGetUser: takeLatest(FETCH_USER[REQUEST], handleGetUser),
  watchAddTag: takeLatest(FETCH_NEW_TAG[REQUEST], handleNewTag),
  watchEditTag: takeLatest(FETCH_EDIT_TAG[REQUEST], handledit),
  watchDeleteTag: takeLatest(FETCH_DELETE_TAG[REQUEST], handleDelete),
  watchGetAddRound: takeLatest(FETCH_ADD_ROUND[REQUEST], handleAddRound),
  watchDeleteRounds: takeLatest(DELETE_ROUNDS[REQUEST], handleDeleteRounds),
  watchEditRounds: takeLatest(EDIT_ROUNDS[REQUEST], handleEditRounds),
  watchEditTpo: takeLatest(EDIT_TPO[REQUEST], handleEditTpo),
  watchDeleteTpo: takeLatest(DELETE_TPO[REQUEST], handleDeleteTpo),
  watchAddTpo: takeLatest(ADD_TPO[REQUEST], handleAddTpo),
  watchviewOrganisationDetailsByType: takeLatest(
    ORGANISATION_DETAILS_BY_TYPE[REQUEST],
    viewOrganisationDetailsByType,
  ),

  watchEditDateTimeDetails: takeLatest(EDIT_DATE_TIME[REQUEST], handleEditDateTime),
  watchEditRejectedSubjects: takeLatest(EDIT_REJECTED_SUBJECT[REQUEST], handleEditRejectedSubjects),
  watchEditJobExperiences: takeLatest(EDIT_JOB_EXPERIENCE[REQUEST], handleEditJobExperiences),
  watchDeleteJobExperiences: takeLatest(DELETE_JOB_EXPERIENCE[REQUEST], handleDeleteJobExperiences),
  watchDeleteRejectedSubjects: takeLatest(
    DELETE_REJECTED_SUBJECT[REQUEST],
    handleDeleteRejectedSubjects,
  ),
  watcheditpositionDetails: takeLatest(EDIT_POSITION_DETAILS[REQUEST], handleEditPositionDetails),
  watchsubmitpositionDetails: takeLatest(
    SUB_POSITION_DETAILS[REQUEST],
    handleSubmitPositionDetails,
  ),
  watchdeletepositionDetails: takeLatest(
    DELETE_POSITION_DETAILS[REQUEST],
    handleDeletePositionDetails,
  ),
  watchAddFeedbackDetails: takeLatest(ADD_FEEDBACK[REQUEST], handleAddFeedbackDetails),
  watchDeleteFeedbackDetails: takeLatest(DELETE_FEEDBACK[REQUEST], handleDeleteFeedbackDetails),
  watchAddCandidate: takeLatest(ADD_CANDIDATE[REQUEST], handleaddCandidate),
  watchEditCandidateForm: takeLatest(EDIT_CANDIDATEFORM[REQUEST], handleeditCandidateForm),
  watchAddResume: takeLatest(ADD_RESUME[REQUEST], addResume),
  watchViewRecruitersCalls: takeLatest(VIEW_RECRUITER_CALLS[REQUEST], handleViewRecruitersCalls),
  watchviewcandidateFeedback: takeLatest(
    VIEW_CANDIDATE_FEEDBACK[REQUEST],
    handleViewCandidateFeedback,
  ),
  watchviewcandidateTags: takeLatest(VIEW_CANDIDATE_TAGS[REQUEST], handleViewCandidateTags),
  watchDeleteCandidateFeedbackData: takeLatest(
    DELETE_CANDIDATE_FEEDBACK[REQUEST],
    handleDeleteCandidateFeedback,
  ),
  watchaddUploadResume: takeLatest(ADD_UPLOAD_RESUME[REQUEST], handleaddUploadResume),
  watchaddUploadAssignment: takeLatest(ADD_UPLOAD_ASSIGNMENT[REQUEST], handleaddUploadAssignment),
  watchEditFeedbackDetails: takeLatest(EDIT_FEEDBACK[REQUEST], handleEditFeedbackDetails),
  watchSubEditFeedbackDetails: takeLatest(SUB_EDIT_FEEDBACK[REQUEST], handlSubEditFeedbackDetails),
  watchApproveApplicants: takeLatest(APPLICANTS_APPROVE[REQUEST], viewApprovedApplicants),
  watchRejectAppliants: takeLatest(APPLICANTS_REJECT[REQUEST], viewRejectedApplicants),
  watchMultipleApplicants: takeLatest(
    DELETE_MULTIPLE_APPLICANTS[REQUEST],
    viewMultipleDeleteApplicants,
  ),
  watchApplicantsResume: takeLatest(FETCH_APPLICANTS_RESUME[REQUEST], viewApplicantsResume),
  watchgetShorcutKeysByCandidateId: takeLatest(
    FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID[REQUEST],
    getShorcutKeysByCandidateId,
  ),
  watchToggleShortcutKeyToCandidateId: takeLatest(
    TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID[REQUEST],
    toggleShortcutKeyToCandidateId,
  ),
  watchViewCandidateLogs: takeLatest(VIEW_CANDIDATE_LOGS[REQUEST],handleViewCandidateLogs,),
}
