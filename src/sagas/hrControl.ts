import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import {
  CREATE_EMP_DESIGNATIONS,
  CREATE_HOLIDAY,
  CREATE_LEAVE_TYPE,
  CREATE_LEAVES,
  CREATE_QAULIFICATION,
  CREATE_TIMING,
  EDIT_DESIGNATIONS,
  FETCH_LEAVE_TYPES,
  GET_CATEGORY,
  GET_EMP_DESIGNATIONS,
  GET_LEAVE_INFO,
} from '../actions/actiontypes'
const { REQUEST, LOGIN_USER } = actionTypes

//write sagas function

function* handleHrControlData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getDesignationApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_EMP_DESIGNATIONS)
  } catch (e) {
    yield sendPayloadFailure(e, GET_EMP_DESIGNATIONS)
  }
}

function* handleCreateLeavesData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addCreateLeavesApi,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_LEAVES)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_LEAVES)
  }
}

function* handleCreateDesignationData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addDesignationApi,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_EMP_DESIGNATIONS)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_EMP_DESIGNATIONS)
  }
}

function* handleCreateLeaveTypeData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addLeaveTypeApi,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_LEAVE_TYPE)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_LEAVE_TYPE)
  }
}

function* handleLeavesData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesInfo,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_INFO)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_INFO)
  }
}

function* handleLeaveTypesData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesTypes,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_LEAVE_TYPES)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_LEAVE_TYPES)
  }
}

function* handleLeaveCategoryData() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesCategoryAPI,
    )
    yield sendPayload(apiResponse, GET_CATEGORY)
  } catch (e) {
    yield sendPayloadFailure(e, GET_CATEGORY)
  }
}

function* handleEditDesignationData(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.editDesignationApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, EDIT_DESIGNATIONS)
  } catch (e) {
    yield sendPayloadFailure(e, EDIT_DESIGNATIONS)
  }
}

function* handleCreateHolidayApiCall(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.createHolidayApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_HOLIDAY)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_HOLIDAY)
  }
}

function* handleCreateTimingApiCall(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.createTimingApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_TIMING)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_TIMING)
  }
}

function* handleCreateQualificationApiCall(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.createQualificationApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_QAULIFICATION)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_QAULIFICATION)
  }
}

export const sagas = {
  //watcher come here
  watchHrControlUser: takeLatest(actionTypes.GET_EMP_DESIGNATIONS[REQUEST], handleHrControlData),
  watchCreateDesignationUser: takeLatest(
    actionTypes.CREATE_EMP_DESIGNATIONS[REQUEST],
    handleCreateDesignationData,
  ),
  watchEditDesignationUser: takeLatest(
    actionTypes.EDIT_DESIGNATIONS[REQUEST],
    handleEditDesignationData,
  ),

  watchCreateHolidayApiCall: takeLatest(
    actionTypes.CREATE_HOLIDAY[REQUEST],
    handleCreateHolidayApiCall,
  ),

  watchCreateTimingApiCall: takeLatest(
    actionTypes.CREATE_TIMING[REQUEST],
    handleCreateTimingApiCall,
  ),

  watchCreateQualificationApiCall: takeLatest(
    actionTypes.CREATE_QAULIFICATION[REQUEST],
    handleCreateQualificationApiCall,
  ),
  watchCreateLeavesApiCall: takeLatest(actionTypes.CREATE_LEAVES[REQUEST], handleCreateLeavesData),

  watchGetLeavesApiCall: takeLatest(actionTypes.GET_LEAVE_INFO[REQUEST], handleLeavesData),

  watchFetchLeavesTypesApiCall: takeLatest(
    actionTypes.FETCH_LEAVE_TYPES[REQUEST],
    handleLeaveTypesData,
  ),
  watchFetchCategoryLeaveApiCall: takeLatest(
    actionTypes.GET_CATEGORY[REQUEST],
    handleLeaveCategoryData,
  ),

  watchCreateLeaveLypeApiCall: takeLatest(
    actionTypes.CREATE_LEAVE_TYPE[REQUEST],
    handleCreateLeaveTypeData,
  ),
  
}
