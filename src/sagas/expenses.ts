import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import {
  CREATE_EXPENSE,
  DOWNLOAD_ATTACHMENTS,
  EXPENSE_STATUS_UPDATE,
  PERTICULAR_EXPENSE,
  REIMBURSEMENT_REQUESTS,
} from '../actions/actiontypes'
const { REQUEST } = actionTypes

function* handleGetReimbursementRequests(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getReimbursementRequestsAPI,
      data.data,
    )
    yield sendPayload(apiResponse, REIMBURSEMENT_REQUESTS)
  } catch (e) {
    yield sendPayloadFailure(e, REIMBURSEMENT_REQUESTS)
  }
}

function* handlePerticularExpense(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getPerticularExpenseAPI,
      data.data,
    )
    yield sendPayload(apiResponse, PERTICULAR_EXPENSE)
  } catch (e) {
    yield sendPayloadFailure(e, PERTICULAR_EXPENSE)
  }
}

function* handleExpenseStatusUpdate(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getExpenseStatusUpdateAPI,
      data.data,
    )
    yield sendPayload(apiResponse, EXPENSE_STATUS_UPDATE)
  } catch (e) {
    yield sendPayloadFailure(e, EXPENSE_STATUS_UPDATE)
  }
}

function* handleCreateExpense(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.createExpense,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_EXPENSE)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_EXPENSE)
  }
}

function* handleDownloadAttachments(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.downloadAttachments,
      data.data,
    )
    yield sendPayload(apiResponse, DOWNLOAD_ATTACHMENTS)
  } catch (e) {
    yield sendPayloadFailure(e, DOWNLOAD_ATTACHMENTS)
  }
}

export const sagas = {
  watchReimbursementRequest: takeLatest(
    actionTypes.REIMBURSEMENT_REQUESTS[REQUEST],
    handleGetReimbursementRequests,
  ),
  watchPerticularExpense: takeLatest(
    actionTypes.PERTICULAR_EXPENSE[REQUEST],
    handlePerticularExpense,
  ),
  watchExpenseStatusUpdate: takeLatest(
    actionTypes.EXPENSE_STATUS_UPDATE[REQUEST],
    handleExpenseStatusUpdate,
  ),
  watchHandleCreateExpense: takeLatest(actionTypes.CREATE_EXPENSE[REQUEST], handleCreateExpense),
  watchHandleDownloadAttachments: takeLatest(
    actionTypes.DOWNLOAD_ATTACHMENTS[REQUEST],
    handleDownloadAttachments,
  ),
}
