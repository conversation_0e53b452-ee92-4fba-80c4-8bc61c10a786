import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import {
  FETCH_EMPLOYEE_DETAILS,
  GET_LEAVE_REPORT,
  FETCH_ATTENDANCE_REPORT,
  GET_LEAVE_BALANCE_REPORT,
  GET_LEAVE_TYPE_REPORT,
  GET_LEAVE_ALLOCATED_REPORT,
  GET_LEAVE_ENCASHMENT_REPORT,
  LEAVE_FREQUENCIES,
  GET_QUATRES,
} from '../actions/actiontypes'
const { REQUEST } = actionTypes

//write sagas function

function* getEmployeeDetails() {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getAllEmployees)
    yield sendPayload(apiResponse, FETCH_EMPLOYEE_DETAILS)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_EMPLOYEE_DETAILS)
  }
}


function* getAttendanceReportDetails(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAttendanceReportApiCall,
      data.data,
    )
    yield sendPayload(apiResponse, FETCH_ATTENDANCE_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ATTENDANCE_REPORT)
  }
}

function* getLeavesReportAPI(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesReportAPICall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_REPORT)
  }
}

function* getLeavesReportBalanceAPI(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesReportBalanceAPICall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_BALANCE_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_BALANCE_REPORT)
  }
}

function* getLeavesReportTypeAPI(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesReportTypeAPICall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_TYPE_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_TYPE_REPORT)
  }
}

function* getLeavesReportEncashmentAPI(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesReportEncashmentAPICall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_ENCASHMENT_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_ENCASHMENT_REPORT)
  }
}

function* getLeavesReportAllocatedAPI(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeavesReportAllocatedAPICall,
      data.data,
    )
    yield sendPayload(apiResponse, GET_LEAVE_ALLOCATED_REPORT)
  } catch (e) {
    yield sendPayloadFailure(e, GET_LEAVE_ALLOCATED_REPORT)
  }
}

function* getLeavesFrequencies(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getLeaveFrequencies,
    )
    yield sendPayload(apiResponse, LEAVE_FREQUENCIES)
  } catch (e) {
    yield sendPayloadFailure(e, LEAVE_FREQUENCIES)
  }
}

function* getQuatre(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(ApiService.getQuatre, data.data)
    yield sendPayload(apiResponse, GET_QUATRES)
  } catch (e) {
    yield sendPayloadFailure(e, GET_QUATRES)
  }
}

export const sagas = {
  //watcher come here
  watchEmployeeDetails: takeLatest(actionTypes.FETCH_EMPLOYEE_DETAILS[REQUEST], getEmployeeDetails),
  watchAttendanceReportDetails: takeLatest(actionTypes.FETCH_ATTENDANCE_REPORT[REQUEST], getAttendanceReportDetails),
  watchLeaveReport: takeLatest(actionTypes.GET_LEAVE_REPORT[REQUEST], getLeavesReportAPI),
  watchLeaveReportBalance: takeLatest(
    actionTypes.GET_LEAVE_BALANCE_REPORT[REQUEST],
    getLeavesReportBalanceAPI,
  ),
  watchLeaveReportType: takeLatest(
    actionTypes.GET_LEAVE_TYPE_REPORT[REQUEST],
    getLeavesReportTypeAPI,
  ),
  watchLeaveReportEncashment: takeLatest(
    actionTypes.GET_LEAVE_ENCASHMENT_REPORT[REQUEST],
    getLeavesReportEncashmentAPI,
  ),
  watchLeaveReportAllocated: takeLatest(
    actionTypes.GET_LEAVE_ALLOCATED_REPORT[REQUEST],
    getLeavesReportAllocatedAPI,
  ),
  watchGetLeavesFrequencies: takeLatest(
    actionTypes.LEAVE_FREQUENCIES[REQUEST],
    getLeavesFrequencies,
  ),
  watchGetQuatre: takeLatest(GET_QUATRES[REQUEST], getQuatre),
}
