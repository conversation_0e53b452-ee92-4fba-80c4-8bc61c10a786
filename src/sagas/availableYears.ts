import { call, put, takeLatest } from 'redux-saga/effects';
import { getAvailableYears } from '../services';
import { GET_AVAILABLE_YEARS } from '../actions/actiontypes';
import { actionTypes } from '../actions';

const { REQUEST } = actionTypes;

function* getAvailableYearsSaga(action: any): Generator<any, void, any> {
  try {
    const response = yield call(getAvailableYears, action.data.userId);
    
    // Ensure we have the correct response structure
    const yearsData = {
      response: response?.data || response
    };
    
    yield put({
      type: GET_AVAILABLE_YEARS.SUCCESS,
      payload: yearsData
    });

  } catch (error) {
    yield put({
      type: GET_AVAILABLE_YEARS.FAILURE,
      payload: error
    });
  }
}

export const sagas = {
  watchAvailableYears: takeLatest(actionTypes.GET_AVAILABLE_YEARS[REQUEST], getAvailableYearsSaga)
};
