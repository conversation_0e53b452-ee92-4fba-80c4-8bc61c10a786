import { call, takeLatest } from 'redux-saga/effects'
import { actionTypes } from '../actions'
import * as ApiService from '../services'
import { sendPayload, sendPayloadFailure } from './helper'
import {
  ADD_NEW_COMMENT,
  ASSIGNED_SR_REQUEST,
  FETCH_ASSIGNED_REQUEST,
  FETCH_DOWNLOADABLE_URL,
  FETCH_SERVICE_REQUEST_DEPARTMENT,
  FETCH_UPDATED_SERVICE_REQUEST,
} from '../actions/actiontypes'
const { REQUEST, LOGIN_USER, CREATE_SERVICE_REQUEST } = actionTypes

//write sagas function

function* handleCreateNewRequest(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.createNewRequest,
      data.data,
    )
    yield sendPayload(apiResponse, CREATE_SERVICE_REQUEST)
  } catch (e) {
    yield sendPayloadFailure(e, CREATE_SERVICE_REQUEST)
  }
}

function* handleAddNewComment(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.addNewComment,
      data.data,
    )
    yield sendPayload(apiResponse, ADD_NEW_COMMENT)
  } catch (e) {
    yield sendPayloadFailure(e, ADD_NEW_COMMENT)
  }
}
function* handleAssignedRequest(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.assignedRequest,
      data,
    )
    yield sendPayload(apiResponse, FETCH_ASSIGNED_REQUEST)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_ASSIGNED_REQUEST)
  }
}

function* handleServiceRequestDepartment(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getServiceRequestDepartment,
    data)
    yield sendPayload(apiResponse, FETCH_SERVICE_REQUEST_DEPARTMENT)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_SERVICE_REQUEST_DEPARTMENT)
  }
}

function* handleUpdatedServiceRequest(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.updatedRequest,
      data,
    )
    yield sendPayload(apiResponse, FETCH_UPDATED_SERVICE_REQUEST)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_UPDATED_SERVICE_REQUEST)
  }
}

function* handleDownloadableURL(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.downloadableURL,
      data,
    )
    yield sendPayload(apiResponse, FETCH_DOWNLOADABLE_URL)
  } catch (e) {
    yield sendPayloadFailure(e, FETCH_DOWNLOADABLE_URL)
  }
}

function* handleAssignedSrRequest(data: any) {
  try {
    const apiResponse: Generator<string, number, string> = yield call(
      ApiService.getAssignedSrRequest,
      data,
    )
    yield sendPayload(apiResponse, ASSIGNED_SR_REQUEST)
  } catch (e) {
    yield sendPayloadFailure(e, ASSIGNED_SR_REQUEST)
  }
}

export const sagas = {
  //watcher come here
  watchCreateSR: takeLatest(actionTypes.CREATE_SERVICE_REQUEST[REQUEST], handleCreateNewRequest),
  watchAddNewComment: takeLatest(actionTypes.ADD_NEW_COMMENT[REQUEST], handleAddNewComment),
  watchAssignedRequest: takeLatest(
    actionTypes.FETCH_ASSIGNED_REQUEST[REQUEST],
    handleAssignedRequest,
  ),
  watchUpdatedServiceRequest: takeLatest(
    actionTypes.FETCH_UPDATED_SERVICE_REQUEST[REQUEST],
    handleUpdatedServiceRequest,
  ),
  watchHandleDownloadableURL: takeLatest(
    actionTypes.FETCH_DOWNLOADABLE_URL[REQUEST],
    handleDownloadableURL,
  ),
  watchServiceRequestDepartment: takeLatest(
    actionTypes.FETCH_SERVICE_REQUEST_DEPARTMENT[REQUEST],
    handleServiceRequestDepartment,
  ),
  handleAssignedSrRequest: takeLatest(actionTypes.ASSIGNED_SR_REQUEST[REQUEST], handleAssignedSrRequest),
}
