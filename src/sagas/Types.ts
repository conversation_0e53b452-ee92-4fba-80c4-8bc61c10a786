import { Action } from 'redux'
import {
  dataPayloadType,
  getClientModulesRequestType,
  getClientUserByIdRequestType,
  getRolesAndPermissionRequestType,
} from '../actions/Types'

type response = {
  status: number
}
export interface error {
  error: object
  response: response
}

export interface getClientModulesType extends Action {
  data: getClientModulesRequestType
}

export interface deleteClientUserType extends Action {
  data: { userId: string; clientId: string }
}

export interface getClientUserByIdType extends Action {
  data: getClientUserByIdRequestType
}

export interface getRolesAndPermissions extends Action {
  data: getRolesAndPermissionRequestType
}

export interface payloadType extends Action {
  data: dataPayloadType
}

export interface ApiResponse {
  data: any;
  status: number;
  statusText: string;
  headers: object;
  config: object;
  request: object;
}

export interface IdApplicants {
  id: string ;
}