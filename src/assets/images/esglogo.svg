<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83" height="77" viewBox="0 0 83 77">
  <defs>
    <filter id="Rectangle_17113" x="0" y="0" width="83" height="77" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_57396" data-name="Group 57396" transform="translate(-1531 -844)">
    <g transform="matrix(1, 0, 0, 1, 1531, 844)" filter="url(#Rectangle_17113)">
      <rect id="Rectangle_17113-2" data-name="Rectangle 17113" width="65" height="59" rx="7.5" transform="translate(9 6)" fill="#fff"/>
    </g>
    <g id="Page-3" transform="translate(1553 860)">
      <g id="App-Landing-Page-Goodera---Tab1-Copy" transform="translate(-869 -364)">
        <g id="Sustainability" transform="translate(867 362)">
          <rect id="Rectangle" width="44" height="44" fill="none"/>
          <g id="Group-3" transform="translate(2 2)">
            <path id="Path-8" d="M17,0,27,1.95V30H17Z" fill="#ff7a49"/>
            <path id="Path-9" d="M27,7h6V30H27Z" fill="#ffaf92"/>
            <path id="Path-10" d="M7,12.82,8.977,11h5.938L17,12.82V30H7Z" fill="#ffaf92"/>
            <path id="Combined-Shape" d="M0,20.4A20.025,20.025,0,0,1,24,39.6,20.025,20.025,0,0,1,0,20.4Z" fill="#7cd6bf"/>
            <path id="Combined-Shape-2" data-name="Combined-Shape" d="M16-20.4a20.1,20.1,0,0,0,4,.4A20,20,0,0,0,40-39.6a20.1,20.1,0,0,0-4-.4A20,20,0,0,0,16-20.4Z" transform="translate(56) rotate(180)" fill="#3cc19f"/>
            <rect id="Rectangle-2" data-name="Rectangle" width="4" height="3" rx="1" transform="translate(10 15)" fill="#ffd7c8"/>
            <rect id="Rectangle-3" data-name="Rectangle" width="4" height="3" rx="1" transform="translate(20 6)" fill="#ffd7c8"/>
            <rect id="Rectangle-4" data-name="Rectangle" width="4" height="3" rx="1" transform="translate(20 14)" fill="#ffd7c8"/>
            <rect id="Rectangle-5" data-name="Rectangle" width="4" height="2" rx="1" transform="translate(28 12)" fill="#ffd7c8"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
