import { createTheme } from '@mui/material'
import style from './utils/styles.json'

export const themeBreakpoint = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
})

const AppTheme = createTheme({
  palette: {
    primary: {
      main: style.PRIMARY_COLOR,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          backgroundColor: style.PRIMARY_COLOR,
          color: '#FFF',
          fontWeight: 'bold',
          fontSize: 'large',
          textTransform: 'none',
          '&:hover': {
            backgroundColor: style.PRIMARY_COLOR,
            color: '#fff',
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'capitalize',
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
        margin: 'normal',
        required: true,
        fullWidth: true,
      },
      styleOverrides: {
        root: {
          [themeBreakpoint.breakpoints.down('xl')]: {
            width: '100%',
          },
          [themeBreakpoint.breakpoints.up('xl')]: {
            width: '100%',
          },
          marginBottom: '16px',
          '& .MuiFormLabel-root.Mui-focused': {
            color: `${style.PRIMARY_COLOR} !important`,
          },
          '& .MuiFormLabel-root.Mui-error': {
            color: '#d32f2f !important',
          },
          '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: `${style.PRIMARY_COLOR} !important`,
          },
          '& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline': {
            borderColor: '#d32f2f !important',
          },
          '&:hover fieldset': {
            borderColor: 'grey',
          },
          '& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input:-webkit-autofill': {
            'box-shadow': '0 0 0 30px white inset !important',
          },
        },
      },
    },
    MuiFormControl: {
      styleOverrides: {
        root: {
          [themeBreakpoint.breakpoints.down('xl')]: {
            width: '100%',
          },
          [themeBreakpoint.breakpoints.up('xl')]: {
            width: '100%',
          },
        },
      },
    },
    MuiLink: {
      defaultProps: {
        textAlign: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        display: 'flex',
        target: '_blank',
      },
    },
    MuiStack: {
      defaultProps: {
        direction: 'row',
        alignItems: 'center',
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          '&.Mui-checked': {
            color: style.PRIMARY_COLOR,
          },
        },
      },
    },
  },
})

export default AppTheme
