import { request } from 'http'
import { IData } from '../components/Dashboard/Types'
import {
  GetDataType,
  TableDataType,
} from '../components/ProjectManagement/ProjectSheet/ProjectSheetTypes'
import {
  CandidateCount,
  CollegeDriveData,
  CollegesPayload,
  GetAccessToken,
  InterviewerWork,
  IRequestData,
  JoinedCandidates,
  LoginAuthData,
  OrgData,
  TemplatePayload,
} from '../components/Types'
import { ICreateComment } from '../models/create-comment.model'
import * as actions from './actiontypes'
import {
  FETCH_EMPINFO,
  FETCH_USERS,
  FETCH_BACKINFO,
  FETCH_HOLIDAYS,
  FETCH_ASSET,
  FET<PERSON>_DESIGNATION,
  <PERSON>ET<PERSON>_DESIGNATIONBAND,
  FETCH_EMPLOYMENTTYPE,
  FETCH_SAPERIOD,
  FETCH_TIMING,
  <PERSON>ETCH_BASICINFO,
  <PERSON><PERSON><PERSON>_SR,
  <PERSON><PERSON><PERSON>_BANKINFO,
  FET<PERSON>_COUNTRY,
  FETCH_STATE,
  FETCH_ACCOUNTTYPE,
  FETCH_MARITIAL_STATUS,
  FETCH_LEAVE_DETAILS,
  FETCH_QUALIFICATION,
  FETCH_QUALIFICATION_SKILL,
  CREATE_SERVICE_REQUEST,
  FETCH_DEPARTMENT_LIST,
  FETCH_ISSUE_TYPE_LIST,
  SEND_LOAN_REQUEST,
  USER_INFO,
  HOME_PAGE_INFO,
  FETCH_USER_BY_ID,
  FETCH_REF_BY_ID,
  FETCH_COMPENSATION_DETAILS,
  FETCH_LOAN_DETAILS,
  FETCH_EMPLOYEE_DETAILS,
  ADD_NEW_COMMENT,
  FETCH_LOAN_INSTALLMENT_DATA,
  GET_TIMESHEET,
  GET_PAY_SLIP,
  FETCH_DRS_DATA,
  FETCH_SUB_DRS_DATA,
  GET_LEAVE_TIMESHEET,
  GET_LEAVE_DATA,
  ACCEPT_LEAVE_DATA,
  REJECT_LEAVE_DATA,
  FETCH_PROJECT_DATA,
  FETCH_HIGHLIGHTS,
  STATUS_SUMMARY,
  FETCH_ATTENDANCE,
  FETCH_ASSIGNED_SR,
  ADD_NEW_JOINERS,
  TAX_REPORT_DATA,
  TAX_FINANCIAL_YEAR,
  FETCH_ROUND,
  FETCH_TAGS,
  FETCH_POSITIONS,
  FETCH_RECRUITERS,
  FETCH_TEMPLATE_DETAILS,
  FETCH_BATCHES,
  FETCH_ADDQUALIFICATION,
  FETCH_EXPECTED_JOINERS,
  FETCH_FEEDBACK,
  FETCH_CANDIDATEPOSITION,
  FETCH_TPO_DETAILS,
  FETCH_ROUND_TYPE,
  FETCH_DATEANDTIME,
  FETCH_ORGANISATIONDETAILS,
  FETCH_USER,
  FETCH_TAG_CANDIDATES,
  FETCH_BLOCK_DOMAIN_DATA,
  FETCH_EMAIL_TEMPLATES,
  FETCH_STATIC_DATA,
  FETCH_INTERVIEWER,
  CREATE_STATIC_DATA,
  FETCH_EXPERIENCES,
  FETCH_BLOCKEDSUBJECTS,
  FETCH_ROUNDS_BY_TYPE,
  FETCH_ROUNDCANDIDATES,
  FETCH_TAGCANDIDATES,
  ADD_REJECTED_SUBJECT,
  ADD_MULTIPLE_CANDIDATES,
  ADD_JOB_EXPERIENCE,
  FETCH_DATE_TIME_BY_ROUND,
  FETCH_TEMPLATE_BY_ROUND,
  FETCH_CANDIDATE_BY_FILTERS,
  DELETE_CANDIDATE_BY_ID,
  SENDMAIL_CANDIDATE_BY_IDS,
  VIEW_ATTACHMENTS_CANDIDATE,
  FETCH_VIDEO_URL,
  FETCH_INTERVIEWER_PANNEL,
  FETCH_APPLICANTS_QUALIFICATION,
  FETCH_UNAPPROVED_CANDIDATE,
  FETCH_SPAMING,
  DELETE_UNAPPROVED_CANDIDATE,
  ADD_MANAGEBATCHES,
  ADD_MANAGEQUALIFICATION,
  EDIT_BATCH,
  EDIT_QUALIFICATION,
  ADD_ORG_DETAILS,
  ADD_USER_DETAILS,
  ADD_DATE_TIME_DETAILS,
  DELETE_ORG_DETAILS,
  DELETE_USER_DETAILS,
  FETCH_INSTITUTE_DETAILS,
  ADD_JOB_POSITION,
  ADD_INST_DETAILS,
  EDIT_INST_DETAILS,
  SUB_INST_DETAILS,
  DEL_INST_DETAILS,
  ROUND_FEEDBACK_DROPDOWN,
  FETCH_JOB_EXPERIENCE,
  EDIT_STATIC_DATA,
  EDIT_INTERVIEWER_DATA,
  DELETE_REJECTED_BODY,
  EDIT_REJECTED_BODY,
  FETCH_NEW_TAG,
  FETCH_EDIT_TAG,
  FETCH_DELETE_TAG,
  FETCH_ADD_ROUND,
  DELETE_ROUNDS,
  EDIT_ROUNDS,
  DELETE_TPO,
  EDIT_TPO,
  ADD_TPO,
  ORGANISATION_DETAILS_BY_TYPE,
  ADD_BLOCK_DOMAIN_DATA,
  DELETE_BLOCK_DOMAIN_DATA,
  EDIT_BLOCK_DOMAIN_DATA,
  ADD_CANDIDATE_FEEDBACK,
  DELETE_ATTACHMENT,
  DELETE_CANDIDATE_FEEDBACK,
  EDIT_CANDIDATE,
  EDIT_REJECTED_SUBJECT,
  EDIT_JOB_EXPERIENCE,
  DELETE_JOB_EXPERIENCE,
  DELETE_REJECTED_SUBJECT,
  ADD_FEEDBACK,
  DELETE_FEEDBACK,
  ADD_CANDIDATE,
  ADD_RESUME,
  EDIT_CANDIDATEFORM,
  FETCH_SEND_REMAINDER,
  FETCH_COOLING_OFF_PERIOD,
  EDIT_FEEDBACK,
  SUB_EDIT_FEEDBACK,
  APPLICANTS_APPROVE,
  APPLICANTS_REJECT,
  DELETE_MULTIPLE_APPLICANTS,
  FETCH_APPLICANTS_RESUME,
  FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID,
  TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID,
  VIEW_RECRUITER_CALLS,
  VIEW_CANDIDATE_FEEDBACK,
  VIEW_CANDIDATE_TAGS,
  VIEW_CANDIDATE_LOGS
} from './actiontypes'
import { EmpHistoryDataTypes, EmpManagerDataTypes } from '../components/EmployeeInfo/Info'
import { dataPayloadType, SendMailCandidateParams } from './Types'
import { AddMultipleCandidatesPayload } from 'components/Recruitment/Settings/Recruitment/UploadCandidate/UploadCandidateType'
import {
  DeleteJobExperiencesPayload,
  EditJobExperiencesPayload,
} from 'components/Recruitment/Settings/Candidate/ExperienceTable/ExperienceTableType'
import {
  DeleteRejectedSubjectsPayload,
  EditRejectedSubjectsPayload,
} from 'components/Recruitment/Settings/System/BlockedSubject/BlockedSubjectType'
import { IdApplicants } from 'sagas/Types'
import {
  GET_DAILY_SUMMARIES,
  GET_AVAILABLE_YEARS,
  MONTHLY_SUMMARIES,
  WEEKLY_SUMMARIES,
  FETCH_TEMPLATES,
  CREATE_TEMPLATE,
  UPDATE_TEMPLATE,
  DELETE_TEMPLATE,
  SEARCH_TEMPLATES,
  FETCH_TEMPLATE_BY_ID,
  ASSIGN_TEMPLATE,
  GET_ASSIGNED_TEMPLATE,
  EDIT_TEMPLATE_VALUES,
  GET_KPI_TEMPLATES,
  LOAD_TEMPLATE_OR_OPTIONS
} from './actiontypes'
import { IDailySummariesResponse } from '../models/daily-summaries.model'
import { DailySummariesPayload } from './Types'

const {
  SUCCESS,
  REQUEST,
  FAILURE,
  RESET,
  GET_USER_DATA,
  LOGIN,
  LOGOUT,
  GET_ACCESS_TOKEN,
  LOGIN_USER,
  action,
} = actions

export const getUserData = {
  request: () => action(GET_USER_DATA[REQUEST]),
  success: (response: IData) => action(GET_USER_DATA[SUCCESS], { response }),
  failure: () => action(GET_USER_DATA[FAILURE]),
  reset: () => action(GET_USER_DATA[RESET], {}),
}

export const getUserAuthentication = {
  request: (data: LoginAuthData) => action(LOGIN[REQUEST], { data }),
  success: (response: LoginAuthData) => action(LOGIN[SUCCESS], { response }),
  failure: () => action(LOGIN[FAILURE]),
  reset: () => action(LOGIN[RESET], {}),
}

//Action for renewing access token
export const renewAccessToken = {
  request: (data: GetAccessToken) => action(GET_ACCESS_TOKEN[REQUEST], { data }),
  success: (response: GetAccessToken) => action(GET_ACCESS_TOKEN[SUCCESS], { response }),
  failure: () => action(GET_ACCESS_TOKEN[FAILURE]),
  reset: () => action(GET_ACCESS_TOKEN[RESET], {}),
}

export const getUserLogout = {
  request: () => action(LOGOUT[REQUEST]),
  success: () => action(LOGOUT[SUCCESS]),
  failure: () => action(LOGOUT[FAILURE]),
  reset: () => action(LOGOUT[RESET], {}),
}

export const getUserLoginUser = {
  request: (data: any) => action(LOGIN_USER[REQUEST], { data }),
  success: (response: any) => action(LOGIN_USER[SUCCESS], { response }),
  failure: () => action(LOGIN_USER[FAILURE]),
  reset: () => action(LOGIN_USER[RESET], {}),
}

export const fetchUserData = {
  request: () => action(FETCH_USERS[REQUEST]),
  success: (response: IData) => action(FETCH_USERS[SUCCESS], { response }),
  failure: () => action(FETCH_USERS[FAILURE]),
  reset: () => action(FETCH_USERS[RESET], {}),
}

export const fetchBackInfo = {
  request: () => action(FETCH_BACKINFO[REQUEST]),
  success: (response: IData) => action(FETCH_BACKINFO[SUCCESS], { response }),
  failure: () => action(FETCH_BACKINFO[FAILURE]),
  reset: () => action(FETCH_BACKINFO[RESET], {}),
}

export const fetchHolidays = {
  request: () => action(FETCH_HOLIDAYS[REQUEST]),
  success: (response: IData) => action(FETCH_HOLIDAYS[SUCCESS], { response }),
  failure: () => action(FETCH_HOLIDAYS[FAILURE]),
  reset: () => action(FETCH_HOLIDAYS[RESET], {}),
}

export const fetchHighlights = {
  request: () => action(FETCH_HIGHLIGHTS[REQUEST]),
  success: (response: IData) => action(FETCH_HIGHLIGHTS[SUCCESS], { response }),
  failure: () => action(FETCH_HIGHLIGHTS[FAILURE]),
  reset: () => action(FETCH_HIGHLIGHTS[RESET], {}),
}

export const fetchAsset = {
  request: () => action(FETCH_ASSET[REQUEST]),
  success: (response: IData) => action(FETCH_ASSET[SUCCESS], { response }),
  failure: () => action(FETCH_ASSET[FAILURE]),
  reset: () => action(FETCH_ASSET[RESET], {}),
}

export const fetchEmpInfo = {
  request: () => action(FETCH_EMPINFO[REQUEST]),
  success: (response: IData) => action(FETCH_EMPINFO[SUCCESS], { response }),
  failure: () => action(FETCH_EMPINFO[FAILURE]),
  reset: () => action(FETCH_EMPINFO[RESET], {}),
}

export const fetchDesignation = {
  request: () => action(FETCH_DESIGNATION[REQUEST]),
  success: (response: IData) => action(FETCH_DESIGNATION[SUCCESS], { response }),
  failure: () => action(FETCH_DESIGNATION[FAILURE]),
  reset: () => action(FETCH_DESIGNATION[RESET], {}),
}

export const fetchDesignationBand = {
  request: () => action(FETCH_DESIGNATIONBAND[REQUEST]),
  success: (response: IData) => action(FETCH_DESIGNATIONBAND[SUCCESS], { response }),
  failure: () => action(FETCH_DESIGNATIONBAND[FAILURE]),
  reset: () => action(FETCH_DESIGNATIONBAND[RESET], {}),
}

export const fetchEmplymentType = {
  request: () => action(FETCH_EMPLOYMENTTYPE[REQUEST]),
  success: (response: IData) => action(FETCH_EMPLOYMENTTYPE[SUCCESS], { response }),
  failure: () => action(FETCH_EMPLOYMENTTYPE[FAILURE]),
  reset: () => action(FETCH_EMPLOYMENTTYPE[RESET], {}),
}

export const fetchSAPeriod = {
  request: () => action(FETCH_SAPERIOD[REQUEST]),
  success: (response: IData) => action(FETCH_SAPERIOD[SUCCESS], { response }),
  failure: () => action(FETCH_SAPERIOD[FAILURE]),
  reset: () => action(FETCH_SAPERIOD[RESET], {}),
}

export const fetchTiming = {
  request: () => action(FETCH_TIMING[REQUEST]),
  success: (response: IData) => action(FETCH_TIMING[SUCCESS], { response }),
  failure: () => action(FETCH_TIMING[FAILURE]),
  reset: () => action(FETCH_TIMING[RESET], {}),
}

export const fetchBasicInfo = {
  request: (data: any) => action(FETCH_BASICINFO[REQUEST], { data }),
  success: (response: IData) => action(FETCH_BASICINFO[SUCCESS], { response }),
  failure: () => action(FETCH_BASICINFO[FAILURE]),
  reset: () => action(FETCH_BASICINFO[RESET], {}),
}

export const getLoanInstallment = {
  request: (data: any) => action(FETCH_LOAN_INSTALLMENT_DATA[REQUEST], { data }),
  success: (response: IData) => action(FETCH_LOAN_INSTALLMENT_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_LOAN_INSTALLMENT_DATA[FAILURE]),
  reset: () => action(FETCH_LOAN_INSTALLMENT_DATA[RESET], {}),
}

export const getDrsData = {
  request: () => action(FETCH_DRS_DATA[REQUEST]),
  success: (response: IData) => action(FETCH_DRS_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_DRS_DATA[FAILURE]),
  reset: () => action(FETCH_DRS_DATA[RESET], {}),
}

export const getSubDrsData = {
  request: (data: { id: number }) => action(FETCH_SUB_DRS_DATA[REQUEST], data),
  success: (response: IData) => action(FETCH_SUB_DRS_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_SUB_DRS_DATA[FAILURE]),
  reset: () => action(FETCH_SUB_DRS_DATA[RESET], {}),
}

export const fetchSR = {
  request: (data: any) => action(FETCH_SR[REQUEST], { data }),
  success: (response: IData) => action(FETCH_SR[SUCCESS], { response }),
  failure: () => action(FETCH_SR[FAILURE]),
  reset: () => action(FETCH_SR[RESET], {}),
}

export const fetchAssignedSR = {
  request: (data: any) => action(FETCH_ASSIGNED_SR[REQUEST], { data }),
  success: (response: IData) => action(FETCH_ASSIGNED_SR[SUCCESS], { response }),
  failure: () => action(FETCH_ASSIGNED_SR[FAILURE]),
  reset: () => action(FETCH_ASSIGNED_SR[RESET], {}),
}

export const getProjectData = {
  request: () => action(FETCH_PROJECT_DATA[REQUEST]),
  success: (response: IData) => action(FETCH_PROJECT_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_PROJECT_DATA[FAILURE]),
  reset: () => action(FETCH_PROJECT_DATA[RESET], {}),
}

export const fetchBankInfo = {
  request: (data: any) => action(FETCH_BANKINFO[REQUEST], { data }),
  success: (response: any) => action(FETCH_BANKINFO[SUCCESS], { response }),
  failure: () => action(FETCH_BANKINFO[FAILURE]),
  reset: () => action(FETCH_BANKINFO[RESET], {}),
}

export const fetchCountry = {
  request: () => action(FETCH_COUNTRY[REQUEST]),
  success: (response: IData) => action(FETCH_COUNTRY[SUCCESS], { response }),
  failure: () => action(FETCH_COUNTRY[FAILURE]),
  reset: () => action(FETCH_COUNTRY[RESET], {}),
}

export const fetchState = {
  request: () => action(FETCH_STATE[REQUEST]),
  success: (response: IData) => action(FETCH_STATE[SUCCESS], { response }),
  failure: () => action(FETCH_STATE[FAILURE]),
  reset: () => action(FETCH_STATE[RESET], {}),
}

export const fetchAccountType = {
  request: () => action(FETCH_ACCOUNTTYPE[REQUEST]),
  success: (response: IData) => action(FETCH_ACCOUNTTYPE[SUCCESS], { response }),
  failure: () => action(FETCH_ACCOUNTTYPE[FAILURE]),
  reset: () => action(FETCH_ACCOUNTTYPE[RESET], {}),
}

export const fetchMaritialStatus = {
  request: () => action(FETCH_MARITIAL_STATUS[REQUEST]),
  success: (response: IData) => action(FETCH_MARITIAL_STATUS[SUCCESS], { response }),
  failure: () => action(FETCH_MARITIAL_STATUS[FAILURE]),
  reset: () => action(FETCH_MARITIAL_STATUS[RESET], {}),
}

export const fetchLeaveDetails = {
  request: (data: any) => action(FETCH_LEAVE_DETAILS[REQUEST], { data }),
  success: (response: IData) => action(FETCH_LEAVE_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_LEAVE_DETAILS[FAILURE]),
  reset: () => action(FETCH_LEAVE_DETAILS[RESET], {}),
}

export const fetchDepartmentList = {
  request: (data: any) => action(FETCH_DEPARTMENT_LIST[REQUEST], { data }),
  success: (response: any) => action(FETCH_DEPARTMENT_LIST[SUCCESS], { response }),
  failure: () => action(FETCH_DEPARTMENT_LIST[FAILURE]),
  reset: () => action(FETCH_DEPARTMENT_LIST[RESET], {}),
}

export const fetchQualification = {
  request: () => action(FETCH_QUALIFICATION[REQUEST]),
  success: (response: IData) => action(FETCH_QUALIFICATION[SUCCESS], { response }),
  failure: () => action(FETCH_QUALIFICATION[FAILURE]),
  reset: () => action(FETCH_QUALIFICATION[RESET], {}),
}

export const fetchQualificationSkill = {
  request: () => action(FETCH_QUALIFICATION_SKILL[REQUEST]),
  success: (response: IData) => action(FETCH_QUALIFICATION_SKILL[SUCCESS], { response }),
  failure: () => action(FETCH_QUALIFICATION_SKILL[FAILURE]),
  reset: () => action(FETCH_QUALIFICATION_SKILL[RESET], {}),
}

export const createServiceRequest = {
  request: (data: any) => action(CREATE_SERVICE_REQUEST[REQUEST], { data }),
  success: (response: IRequestData) => action(CREATE_SERVICE_REQUEST[SUCCESS], { response }),
  failure: () => action(CREATE_SERVICE_REQUEST[FAILURE]),
  reset: () => action(CREATE_SERVICE_REQUEST[RESET], {}),
}

export const getIssueTypeList = {
  request: (data: any) => action(FETCH_ISSUE_TYPE_LIST[REQUEST], { data }),
  success: (response: any) => action(FETCH_ISSUE_TYPE_LIST[SUCCESS], { response }),
  failure: () => action(FETCH_ISSUE_TYPE_LIST[FAILURE]),
  reset: () => action(FETCH_ISSUE_TYPE_LIST[RESET], {}),
}

export const getManagerDataById = {
  request: (data: any) => action(FETCH_USER_BY_ID[REQUEST], { data }),
  success: (response: any) => action(FETCH_USER_BY_ID[SUCCESS], { response }),
  failure: () => action(FETCH_USER_BY_ID[FAILURE]),
  reset: () => action(FETCH_USER_BY_ID[RESET], {}),
}

export const getCompensationDetails = {
  request: (data: any) => action(FETCH_COMPENSATION_DETAILS[REQUEST], { data }),
  success: (response: any) => action(FETCH_COMPENSATION_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_COMPENSATION_DETAILS[FAILURE]),
  reset: () => action(FETCH_COMPENSATION_DETAILS[RESET], {}),
}

export const getLoanDetails = {
  request: () => action(FETCH_LOAN_DETAILS[REQUEST], {}),
  success: (response: any) => action(FETCH_LOAN_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_LOAN_DETAILS[FAILURE]),
  reset: () => action(FETCH_LOAN_DETAILS[RESET], {}),
}

export const getRefDataById = {
  request: (data: any) => action(FETCH_REF_BY_ID[REQUEST], { data }),
  success: (response: any) => action(FETCH_REF_BY_ID[SUCCESS], { response }),
  failure: () => action(FETCH_REF_BY_ID[FAILURE]),
  reset: () => action(FETCH_REF_BY_ID[RESET], {}),
}

export const getUserDetails = {
  request: () => action(USER_INFO[REQUEST], {}),
  success: (response: any) => action(USER_INFO[SUCCESS], { response }),
  failure: () => action(USER_INFO[FAILURE]),
  reset: () => action(USER_INFO[RESET], {}),
}

export const getHomePageDetails = {
  request: () => action(HOME_PAGE_INFO[REQUEST], {}),
  success: (response: any) => action(HOME_PAGE_INFO[SUCCESS], { response }),
  failure: () => action(HOME_PAGE_INFO[FAILURE]),
  reset: () => action(HOME_PAGE_INFO[RESET], {}),
}

export const getStatusSummary = {
  request: (data: any) => action(STATUS_SUMMARY[REQUEST], { data }),
  success: (response: any) => action(STATUS_SUMMARY[SUCCESS], { response }),
  failure: () => action(STATUS_SUMMARY[FAILURE]),
  reset: () => action(STATUS_SUMMARY[RESET], {}),
}

export const sendLoanRequest = {
  request: (data: any) => action(SEND_LOAN_REQUEST[REQUEST], { data }),
  success: (response: any) => action(SEND_LOAN_REQUEST[SUCCESS], { response }),
  failure: () => action(SEND_LOAN_REQUEST[FAILURE]),
  reset: () => action(SEND_LOAN_REQUEST[RESET], {}),
}

export const addNewJoiners = {
  request: (data: object) => action(actions.ADD_NEW_JOINERS[REQUEST], { data }),
  success: (response: string) => action(actions.ADD_NEW_JOINERS[SUCCESS], { response }),
  failure: () => action(actions.ADD_NEW_JOINERS[FAILURE]),
  reset: () => action(actions.ADD_NEW_JOINERS[RESET], {}),
}

export const updateNewJoiners = {
  request: (data: any) => action(actions.UPDATE_NEW_JOINERS[REQUEST], data),
  success: (response: string) => action(actions.UPDATE_NEW_JOINERS[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_NEW_JOINERS[FAILURE]),
  reset: () => action(actions.UPDATE_NEW_JOINERS[RESET], {}),
}

export const uploadCertificate = {
  request: (data: object) => action(actions.UPLOAD_CERTIFICATE[REQUEST], data),
  success: (response: string) => action(actions.UPLOAD_CERTIFICATE[SUCCESS], { response }),
  failure: () => action(actions.UPLOAD_CERTIFICATE[FAILURE]),
  reset: () => action(actions.UPLOAD_CERTIFICATE[RESET], {}),
}

export const getNewJoinersDetails = {
  request: (data: object) => action(actions.GET_NEW_JOINERS_DETAILS[REQUEST], data),
  success: (response: string) => action(actions.GET_NEW_JOINERS_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.GET_NEW_JOINERS_DETAILS[FAILURE]),
  reset: () => action(actions.GET_NEW_JOINERS_DETAILS[RESET], {}),
}

export const getAllEmployees = {
  request: () => action(FETCH_EMPLOYEE_DETAILS[REQUEST], {}),
  success: (response: any) => action(FETCH_EMPLOYEE_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_EMPLOYEE_DETAILS[FAILURE]),
  reset: () => action(FETCH_EMPLOYEE_DETAILS[RESET], {}),
}

export const addNewComment = {
  request: (data: ICreateComment) => action(ADD_NEW_COMMENT[REQUEST], { data }),
  success: (response: any) => action(ADD_NEW_COMMENT[SUCCESS], { response }),
  failure: () => action(ADD_NEW_COMMENT[FAILURE]),
  reset: () => action(ADD_NEW_COMMENT[RESET], {}),
}

export const getTimesheet = {
  request: () => action(GET_TIMESHEET[REQUEST], {}),
  success: (response: any) => action(GET_TIMESHEET[SUCCESS], { response }),
  failure: () => action(GET_TIMESHEET[FAILURE]),
  reset: () => action(GET_TIMESHEET[RESET], {}),
}

export const getLeaveTimesheets = {
  request: () => action(GET_LEAVE_TIMESHEET[REQUEST], {}),
  success: (response: any) => action(GET_LEAVE_TIMESHEET[SUCCESS], { response }),
  failure: () => action(GET_LEAVE_TIMESHEET[FAILURE]),
  reset: () => action(GET_LEAVE_TIMESHEET[RESET], {}),
}

export const getLeaveData = {
  request: (data: any) => action(GET_LEAVE_DATA[REQUEST], { data }),
  success: (response: any) => action(GET_LEAVE_DATA[SUCCESS], { response }),
  failure: () => action(GET_LEAVE_DATA[FAILURE]),
  reset: () => action(GET_LEAVE_DATA[RESET], {}),
}

export const acceptLeaveData = {
  request: (data: any) => action(ACCEPT_LEAVE_DATA[REQUEST], { data }),
  success: (response: any) => action(ACCEPT_LEAVE_DATA[SUCCESS], { response }),
  failure: () => action(ACCEPT_LEAVE_DATA[FAILURE]),
  reset: () => action(ACCEPT_LEAVE_DATA[RESET], {}),
}

export const rejectLeaveData = {
  request: (data: any) => action(REJECT_LEAVE_DATA[REQUEST], { data }),
  success: (response: any) => action(REJECT_LEAVE_DATA[SUCCESS], { response }),
  failure: () => action(REJECT_LEAVE_DATA[FAILURE]),
  reset: () => action(REJECT_LEAVE_DATA[RESET], {}),
}

export const getPaySlip = {
  request: (data: any) => action(GET_PAY_SLIP[REQUEST], { data }),
  success: (response: any) => action(GET_PAY_SLIP[SUCCESS], { response }),
  failure: () => action(GET_PAY_SLIP[FAILURE]),
  reset: () => action(GET_PAY_SLIP[RESET], {}),
}

export const getAttendanceDetail = {
  request: (data: any) => action(FETCH_ATTENDANCE[REQUEST], data),
  success: (response: IData) => action(FETCH_ATTENDANCE[SUCCESS], { response }),
  failure: () => action(FETCH_ATTENDANCE[FAILURE]),
  reset: () => action(FETCH_ATTENDANCE[RESET], {}),
}

export const getAttendanceTimesheet = {
  request: (data: any) => action(actions.FETCH_ATTENDANCE_TIMESHEET[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_ATTENDANCE_TIMESHEET[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ATTENDANCE_TIMESHEET[FAILURE]),
  reset: () => action(actions.FETCH_ATTENDANCE_TIMESHEET[RESET], {}),
}

export const getRCAs = {
  request: (data: any) => action(actions.FETCH_RCA[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_RCA[SUCCESS], { response }),
  failure: () => action(actions.FETCH_RCA[FAILURE]),
  reset: () => action(actions.FETCH_RCA[RESET], {}),
}

export const getIDSRs = {
  request: (data: any) => action(actions.FETCH_IDSR[REQUEST], data),
  success: (response: any) => action(actions.FETCH_IDSR[SUCCESS], { response }),
  failure: () => action(actions.FETCH_IDSR[FAILURE]),
  reset: () => action(actions.FETCH_IDSR[RESET], {}),
}

export const getPlans = {
  request: (data: any) => action(actions.FETCH_PLANS_FOR_THE_DAY[REQUEST], data),
  success: (response: any) => action(actions.FETCH_PLANS_FOR_THE_DAY[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PLANS_FOR_THE_DAY[FAILURE]),
  reset: () => action(actions.FETCH_PLANS_FOR_THE_DAY[RESET], {}),
}

export const getStatusType = {
  request: () => action(actions.STATUS_TYPE[REQUEST], {}),
  success: (response: any) => action(actions.STATUS_TYPE[SUCCESS], { response }),
  failure: () => action(actions.STATUS_TYPE[FAILURE]),
  reset: () => action(actions.STATUS_TYPE[RESET], {}),
}

export const getTaskStatus = {
  request: () => action(actions.TASK_STATUS[REQUEST], {}),
  success: (response: any) => action(actions.TASK_STATUS[SUCCESS], { response }),
  failure: () => action(actions.TASK_STATUS[FAILURE]),
  reset: () => action(actions.TASK_STATUS[RESET], {}),
}

export const createNewRCA = {
  request: (data: any) => action(actions.CREATE_RCA[REQUEST], data),
  success: (response: IData) => action(actions.CREATE_RCA[SUCCESS], { response }),
  failure: () => action(actions.CREATE_RCA[FAILURE]),
  reset: () => action(actions.CREATE_RCA[RESET], {}),
}

export const createNewIDSR = {
  request: (data: any) => action(actions.CREATE_IDSR[REQUEST], data),
  success: (response: IData) => action(actions.CREATE_IDSR[SUCCESS], { response }),
  failure: () => action(actions.CREATE_IDSR[FAILURE]),
  reset: () => action(actions.CREATE_IDSR[RESET], {}),
}

export const createNewPlanForTheDay = {
  request: (data: any) => action(actions.CREATE_PLAN_FOR_THE_DAY[REQUEST], data),
  success: (response: IData) => action(actions.CREATE_PLAN_FOR_THE_DAY[SUCCESS], { response }),
  failure: () => action(actions.CREATE_PLAN_FOR_THE_DAY[FAILURE]),
  reset: () => action(actions.CREATE_PLAN_FOR_THE_DAY[RESET], {}),
}

export const fetchAsignedRequest = {
  request: (data: any) => action(actions.FETCH_ASSIGNED_REQUEST[REQUEST], data),
  success: (response: any) => action(actions.FETCH_ASSIGNED_REQUEST[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ASSIGNED_REQUEST[FAILURE]),
  reset: () => action(actions.FETCH_ASSIGNED_REQUEST[RESET], {}),
}

export const fetchUpdatedServiceRequest = {
  request: (data: any) => action(actions.FETCH_UPDATED_SERVICE_REQUEST[REQUEST], data),
  success: (response: any) => action(actions.FETCH_UPDATED_SERVICE_REQUEST[SUCCESS], { response }),
  failure: () => action(actions.FETCH_UPDATED_SERVICE_REQUEST[FAILURE]),
  reset: () => action(actions.FETCH_UPDATED_SERVICE_REQUEST[RESET], {}),
}

export const fetchDownloadableURL = {
  request: (data: any) => action(actions.FETCH_DOWNLOADABLE_URL[REQUEST], data),
  success: (response: any) => action(actions.FETCH_DOWNLOADABLE_URL[SUCCESS], { response }),
  failure: () => action(actions.FETCH_DOWNLOADABLE_URL[FAILURE]),
  reset: () => action(actions.FETCH_DOWNLOADABLE_URL[RESET], {}),
}
export const fetchIssueType = {
  request: (data: any) => action(actions.FETCH_ISSUE_TYPE[REQUEST], data),
  success: (response: any) => action(actions.FETCH_ISSUE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ISSUE_TYPE[FAILURE]),
  reset: () => action(actions.FETCH_ISSUE_TYPE[RESET], {}),
}

export const fetchDocURL = {
  request: (data: any) => action(actions.FETCH_DOC_URL[REQUEST], data),
  success: (response: any) => action(actions.FETCH_DOC_URL[SUCCESS], { response }),
  failure: () => action(actions.FETCH_DOC_URL[FAILURE]),
  reset: () => action(actions.FETCH_DOC_URL[RESET], {}),
}

export const managerViewData = {
  request: (data: any) => action(actions.MANAGER_VIEW_DATA[REQUEST], data),
  success: (response: any) => action(actions.MANAGER_VIEW_DATA[SUCCESS], { response }),
  failure: () => action(actions.MANAGER_VIEW_DATA[FAILURE]),
  reset: () => action(actions.MANAGER_VIEW_DATA[RESET], {}),
}

export const fetchProjectsName = {
  request: (data: any) => action(actions.FETCH_PROJECTS_NAME[REQUEST], data),
  success: (response: any) => action(actions.FETCH_PROJECTS_NAME[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECTS_NAME[FAILURE]),
  reset: () => action(actions.FETCH_PROJECTS_NAME[RESET], {}),
}
export const projectGraphData = {
  request: (data: any) => action(actions.PROJECT_GRAPH_DATA[REQUEST], data),
  success: (response: any) => action(actions.PROJECT_GRAPH_DATA[SUCCESS], { response }),
  failure: () => action(actions.PROJECT_GRAPH_DATA[FAILURE]),
  reset: () => action(actions.PROJECT_GRAPH_DATA[RESET], {}),
}

export const desgnationGraphData = {
  request: (data: any) => action(actions.DESIGNATION_GRAPH_DATA[REQUEST], data),
  success: (response: any) => action(actions.DESIGNATION_GRAPH_DATA[SUCCESS], { response }),
  failure: () => action(actions.DESIGNATION_GRAPH_DATA[FAILURE]),
  reset: () => action(actions.DESIGNATION_GRAPH_DATA[RESET], {}),
}

export const designationList = {
  request: () => action(actions.DESIGNATION_LIST[REQUEST]),
  success: (response: any) => action(actions.DESIGNATION_LIST[SUCCESS], { response }),
  failure: () => action(actions.DESIGNATION_LIST[FAILURE]),
  reset: () => action(actions.DESIGNATION_LIST[RESET], {}),
}

export const projectDomain = {
  request: () => action(actions.FETCH_PROJECT_DOMAIN[REQUEST]),
  success: (response: any) => action(actions.FETCH_PROJECT_DOMAIN[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_DOMAIN[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_DOMAIN[RESET], {}),
}

export const projectSource = {
  request: () => action(actions.FETCH_PROJECT_SOURCE[REQUEST]),
  success: (response: any) => action(actions.FETCH_PROJECT_SOURCE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_SOURCE[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_SOURCE[RESET], {}),
}

export const projectTypes = {
  request: (data: any) => action(actions.FETCH_PROJECT_TYPES[REQUEST], data),
  success: (response: any) => action(actions.FETCH_PROJECT_TYPES[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_TYPES[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_TYPES[RESET], {}),
}
export const fetchTechnoligies = {
  request: () => action(actions.FETCH_TECHNOLOGIES[REQUEST]),
  success: (response: any) => action(actions.FETCH_TECHNOLOGIES[SUCCESS], { response }),
  failure: () => action(actions.FETCH_TECHNOLOGIES[FAILURE]),
  reset: () => action(actions.FETCH_TECHNOLOGIES[RESET], {}),
}
export const fetchProjectLocation = {
  request: () => action(actions.FETCH_PROJECT_LOCATION[REQUEST]),
  success: (response: any) => action(actions.FETCH_PROJECT_LOCATION[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_LOCATION[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_LOCATION[RESET], {}),
}

export const addNewProject = {
  request: (data: any) => action(actions.ADD_NEW_PROJECT[REQUEST], data),
  success: (response: any) => action(actions.ADD_NEW_PROJECT[SUCCESS], { response }),
  failure: () => action(actions.ADD_NEW_PROJECT[FAILURE]),
  reset: () => action(actions.ADD_NEW_PROJECT[RESET], {}),
}

export const fetchDeletingProject = {
  request: (data: any) => action(actions.DELETE_PROJECT[REQUEST], data),
  success: (response: any) => action(actions.DELETE_PROJECT[SUCCESS], { response }),
  failure: () => action(actions.DELETE_PROJECT[FAILURE]),
  reset: () => action(actions.DELETE_PROJECT[RESET], {}),
}

export const fetchProjectCustomers = {
  request: () => action(actions.FETCH_PROJECT_CUSTOMERS[REQUEST]),
  success: (response: any) => action(actions.FETCH_PROJECT_CUSTOMERS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_CUSTOMERS[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_CUSTOMERS[RESET], {}),
}

export const fetchMandateType = {
  request: (data: object) => action(actions.FETCH_MANDATE_TYPE[REQUEST], data),
  success: (response: string) => action(actions.FETCH_MANDATE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_MANDATE_TYPE[FAILURE]),
  reset: () => action(actions.FETCH_MANDATE_TYPE[RESET], {}),
}

export const fetchDomainType = {
  request: (data: object) => action(actions.FETCH_DOMAIN_TYPE[REQUEST], data),
  success: (response: string) => action(actions.FETCH_DOMAIN_TYPE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_DOMAIN_TYPE[FAILURE]),
  reset: () => action(actions.FETCH_DOMAIN_TYPE[RESET], {}),
}

export const fetchRestartedProject = {
  request: (data: any) => action(actions.RESTART_PROJECT[REQUEST], data),
  success: (response: any) => action(actions.RESTART_PROJECT[SUCCESS], { response }),
  failure: () => action(actions.RESTART_PROJECT[FAILURE]),
  reset: () => action(actions.RESTART_PROJECT[RESET], {}),
}

export const fetchProjectDetails = {
  request: (data: any) => action(actions.FETCH_PROJECT_DETAILS[REQUEST], data),
  success: (response: any) => action(actions.FETCH_PROJECT_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_DETAILS[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_DETAILS[RESET], {}),
}

export const fetchWorkingEmp = {
  request: () => action(actions.FETCH_WORKING_EMP[REQUEST]),
  success: (response: any) => action(actions.FETCH_WORKING_EMP[SUCCESS], { response }),
  failure: () => action(actions.FETCH_WORKING_EMP[FAILURE]),
  reset: () => action(actions.FETCH_WORKING_EMP[RESET], {}),
}

export const fetchEmpBasedOnRoles = {
  request: (data: any) => action(actions.ADD_EMP_BASED_ON_ROLE[REQUEST], data),
  success: (response: any) => action(actions.ADD_EMP_BASED_ON_ROLE[SUCCESS], { response }),
  failure: () => action(actions.ADD_EMP_BASED_ON_ROLE[FAILURE]),
  reset: () => action(actions.ADD_EMP_BASED_ON_ROLE[RESET], {}),
}

export const fetchUpdatedProject = {
  request: (data: any) => action(actions.UPDATE_PROJECT[REQUEST], data),
  success: (response: any) => action(actions.UPDATE_PROJECT[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_PROJECT[FAILURE]),
  reset: () => action(actions.UPDATE_PROJECT[RESET], {}),
}

export const fetchDeletedRecords = {
  request: (data: any) => action(actions.DELETE_EMP_BASED_ON_ROLE[REQUEST], data),
  success: (response: any) => action(actions.DELETE_EMP_BASED_ON_ROLE[SUCCESS], { response }),
  failure: () => action(actions.DELETE_EMP_BASED_ON_ROLE[FAILURE]),
  reset: () => action(actions.DELETE_EMP_BASED_ON_ROLE[RESET], {}),
}

export const fetchEditedProject = {
  request: (data: any) => action(actions.EDIT_PROJECT[REQUEST], data),
  success: (response: any) => action(actions.EDIT_PROJECT[SUCCESS], { response }),
  failure: () => action(actions.EDIT_PROJECT[FAILURE]),
  reset: () => action(actions.EDIT_PROJECT[RESET], {}),
}

export const fetchDesignations = {
  request: () => action(actions.ORGANIZATION_DESIGNATION[REQUEST]),
  success: (response: any) => action(actions.ORGANIZATION_DESIGNATION[SUCCESS], { response }),
  failure: () => action(actions.ORGANIZATION_DESIGNATION[FAILURE]),
  reset: () => action(actions.ORGANIZATION_DESIGNATION[RESET], {}),
}

export const fetchOrganisationDesignationData = {
  request: (data: { hierarchyName: string }) =>
    action(actions.ORGANISATION_DESIGNATION_DATA[REQUEST], data),
  success: (response: any) => action(actions.ORGANISATION_DESIGNATION_DATA[SUCCESS], { response }),
  failure: () => action(actions.ORGANISATION_DESIGNATION_DATA[FAILURE]),
  reset: () => action(actions.ORGANISATION_DESIGNATION_DATA[RESET], {}),
}

export const fetchProjectReports = {
  request: (data: any) => action(actions.FETCH_PROJECT_REPORTS[REQUEST], data),
  success: (response: any) => action(actions.FETCH_PROJECT_REPORTS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_REPORTS[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_REPORTS[RESET], {}),
}

export const getAllResourceReport = {
  request: (data: {
    startDate: string
    endDate: string
    project: string
    projectSource: string
    location: string
    employmentType: string
  }) => {
    return action(actions.FETCH_ALL_PROJECT_RESOURCE_REPORT[REQUEST], { data })
  },
  success: (response: IData) =>
    action(actions.FETCH_ALL_PROJECT_RESOURCE_REPORT[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_PROJECT_RESOURCE_REPORT[FAILURE]),
  reset: () => action(actions.FETCH_ALL_PROJECT_RESOURCE_REPORT[RESET], {}),
}
export const getAllResourceReportDropdownData = {
  request: (data: any) => action(actions.PROJECT_RESOURCE_REPORT_DROPDOWN[REQUEST], { data }),
  success: (response: any) =>
    action(actions.PROJECT_RESOURCE_REPORT_DROPDOWN[SUCCESS], { response }),
  failure: () => action(actions.PROJECT_RESOURCE_REPORT_DROPDOWN[FAILURE]),
  reset: () => action(actions.PROJECT_RESOURCE_REPORT_DROPDOWN[RESET], {}),
}

export const fetchProjectManagementReports = {
  request: (data: any) => action(actions.FETCH_PROJECT_MANAGEMENT_REPORT[REQUEST], { data }),
  success: (response: any) =>
    action(actions.FETCH_PROJECT_MANAGEMENT_REPORT[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_MANAGEMENT_REPORT[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_MANAGEMENT_REPORT[RESET], {}),
}

export const fetchProjectQAReport = {
  request: (data: { data: { endDate: string; startDate: string; page: number; limit: number } }) =>
    action(actions.FETCH_PROJECT_QA_REPORT[REQUEST], data),
  success: (response: string) => action(actions.FETCH_PROJECT_QA_REPORT[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_QA_REPORT[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_QA_REPORT[RESET], {}),
}

export const fetchNonBillableResources = {
  request: () => action(actions.FETCH_NON_BILLABLE_RESOURCES[REQUEST]),
  success: (response: { Employee_id: number[] }) =>
    action(actions.FETCH_NON_BILLABLE_RESOURCES[SUCCESS], { response }),
  failure: () => action(actions.FETCH_NON_BILLABLE_RESOURCES[FAILURE]),
  reset: () => action(actions.FETCH_NON_BILLABLE_RESOURCES[RESET], {}),
}

export const updateNonBillableResource = {
  request: (data: { Employee_id: number[] }) =>
    action(actions.UPDATE_NON_BILLABLE_RESOURCES[REQUEST], { data }),
  success: (response: IRequestData) =>
    action(actions.UPDATE_NON_BILLABLE_RESOURCES[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_NON_BILLABLE_RESOURCES[FAILURE]),
  reset: () => action(actions.UPDATE_NON_BILLABLE_RESOURCES[RESET], {}),
}

export const fetchProjectCustomersList = {
  request: (data: object) => action(actions.FETCH_PROJECT_CUSTOMERS_LIST[REQUEST], data),
  success: (response: string) =>
    action(actions.FETCH_PROJECT_CUSTOMERS_LIST[SUCCESS], { response }),
  failure: () => action(actions.FETCH_PROJECT_CUSTOMERS_LIST[FAILURE]),
  reset: () => action(actions.FETCH_PROJECT_CUSTOMERS_LIST[RESET], {}),
}

export const createProjectCustomers = {
  request: (data: object) => action(actions.CREATE_PROJECT_CUSTOMERS[REQUEST], data),
  success: (response: string) => action(actions.CREATE_PROJECT_CUSTOMERS[SUCCESS], { response }),
  failure: () => action(actions.CREATE_PROJECT_CUSTOMERS[FAILURE]),
  reset: () => action(actions.CREATE_PROJECT_CUSTOMERS[RESET], {}),
}

export const createMandateType = {
  request: (data: object) => action(actions.CREATE_MANDATE_TYPE[REQUEST], data),
  success: (response: string) => action(actions.CREATE_MANDATE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.CREATE_MANDATE_TYPE[FAILURE]),
  reset: () => action(actions.CREATE_MANDATE_TYPE[RESET], {}),
}

export const createDomainType = {
  request: (data: object) => action(actions.CREATE_DOMAIN_TYPE[REQUEST], data),
  success: (response: string) => action(actions.CREATE_DOMAIN_TYPE[SUCCESS], { response }),
  failure: () => action(actions.CREATE_DOMAIN_TYPE[FAILURE]),
  reset: () => action(actions.CREATE_DOMAIN_TYPE[RESET], {}),
}

export const uploadUserImg = {
  request: (data: object) => action(actions.UPLOAD_USER_IMG[REQUEST], { data }),
  success: (response: string) => action(actions.UPLOAD_USER_IMG[SUCCESS], { response }),
  failure: () => action(actions.UPLOAD_USER_IMG[FAILURE]),
  reset: () => action(actions.UPLOAD_USER_IMG[RESET], {}),
}

export const getUserImage = {
  request: (data: { id: number }) => action(actions.GET_USER_IMAGE[REQUEST], data),
  success: (Response: string) => action(actions.GET_USER_IMAGE[SUCCESS], { Response }),
  failure: () => action(actions.GET_USER_IMAGE[FAILURE]),
  reset: () => action(actions.GET_USER_IMAGE[RESET], {}),
}

export const getExpectedJoinersImage = {
  request: (data: { id: number }) => action(actions.GET_EXPECTED_JOINERS_IMAGE[REQUEST], data),
  success: (Response: string) => action(actions.GET_EXPECTED_JOINERS_IMAGE[SUCCESS], { Response }),
  failure: () => action(actions.GET_EXPECTED_JOINERS_IMAGE[FAILURE]),
  reset: () => action(actions.GET_EXPECTED_JOINERS_IMAGE[RESET], {}),
}

export const createUser = {
  request: (data: object) => action(actions.CREATE_USER[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_USER[SUCCESS], { response }),
  failure: () => action(actions.CREATE_USER[FAILURE]),
  reset: () => action(actions.CREATE_USER[RESET], {}),
}

export const updateUser = {
  request: (data: any) => action(actions.UPDATE_USER[REQUEST], data),
  success: (response: string) => action(actions.UPDATE_USER[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_USER[FAILURE]),
  reset: () => action(actions.UPDATE_USER[RESET], {}),
}

export const deleteUser = {
  request: (data: { data: { id: number } }) => action(actions.DELETE_USER[REQUEST], data),
  success: (response: string) => action(actions.DELETE_USER[SUCCESS], { response }),
  failure: () => action(actions.DELETE_USER[FAILURE]),
  reset: () => action(actions.DELETE_USER[RESET], {}),
}

export const fetchAllRoles = {
  request: () => action(actions.FETCH_ALL_ROLE[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_ROLE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_ROLE[FAILURE]),
  reset: () => action(actions.FETCH_ALL_ROLE[RESET], {}),
}

export const fetchAllStates = {
  request: () => action(actions.FETCH_ALL_STATE[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_STATE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_STATE[FAILURE]),
  reset: () => action(actions.FETCH_ALL_STATE[RESET], {}),
}

export const fetchAllCountries = {
  request: () => action(actions.FETCH_ALL_COUNTRY[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_COUNTRY[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_COUNTRY[FAILURE]),
  reset: () => action(actions.FETCH_ALL_COUNTRY[RESET], {}),
}

export const fetchAllFloors = {
  request: () => action(actions.FETCH_ALL_FLOOR[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_FLOOR[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_FLOOR[FAILURE]),
  reset: () => action(actions.FETCH_ALL_FLOOR[RESET], {}),
}

export const fetchAllUserList = {
  request: (data: object) => action(actions.FETCH_ALL_USER_LIST[REQUEST], data),
  success: (response: string) => action(actions.FETCH_ALL_USER_LIST[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_USER_LIST[FAILURE]),
  reset: () => action(actions.FETCH_ALL_USER_LIST[RESET], {}),
}

export const fetchAllWorkstation = {
  request: () => action(actions.FETCH_ALL_WORKSTATION[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_WORKSTATION[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_WORKSTATION[FAILURE]),
  reset: () => action(actions.FETCH_ALL_WORKSTATION[RESET], {}),
}

export const fetchAllLocation = {
  request: () => action(actions.FETCH_ALL_LOCATION[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_LOCATION[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_LOCATION[FAILURE]),
  reset: () => action(actions.FETCH_ALL_LOCATION[RESET], {}),
}

export const fetchAllClientLocation = {
  request: () => action(actions.FETCH_ALL_CLIENT_LOCATION[REQUEST]),
  success: (response: string) => action(actions.FETCH_ALL_CLIENT_LOCATION[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ALL_CLIENT_LOCATION[FAILURE]),
  reset: () => action(actions.FETCH_ALL_CLIENT_LOCATION[RESET], {}),
}

export const deleteProjectCustomer = {
  request: (data: { data: { id: number } }) =>
    action(actions.DELETE_PROJECT_CUSTOMER[REQUEST], data),
  success: (response: string) => action(actions.DELETE_PROJECT_CUSTOMER[SUCCESS], { response }),
  failure: () => action(actions.DELETE_PROJECT_CUSTOMER[FAILURE]),
  reset: () => action(actions.DELETE_PROJECT_CUSTOMER[RESET], {}),
}

export const updateProjectCustomer = {
  request: (data: { data: { id: number; customer_name: string } }) =>
    action(actions.UPDATE_PROJECT_CUSTOMER[REQUEST], data),
  success: (response: string) => action(actions.UPDATE_PROJECT_CUSTOMER[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_PROJECT_CUSTOMER[FAILURE]),
  reset: () => action(actions.UPDATE_PROJECT_CUSTOMER[RESET], {}),
}

export const updateMandateType = {
  request: (data: { data: { id: number; customer_name: string } }) =>
    action(actions.UPDATE_MANDATE_TYPE[REQUEST], data),
  success: (response: string) => action(actions.UPDATE_MANDATE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_MANDATE_TYPE[FAILURE]),
  reset: () => action(actions.UPDATE_MANDATE_TYPE[RESET], {}),
}

export const updateDomainType = {
  request: (data: { data: { id: number; customer_name: string } }) =>
    action(actions.UPDATE_DOMAIN_TYPE[REQUEST], data),
  success: (response: string) => action(actions.UPDATE_DOMAIN_TYPE[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_DOMAIN_TYPE[FAILURE]),
  reset: () => action(actions.UPDATE_DOMAIN_TYPE[RESET], {}),
}

export const deleteMandateType = {
  request: (data: { data: { id: number } }) => action(actions.DELETE_MANDATE_TYPE[REQUEST], data),
  success: (response: string) => action(actions.DELETE_MANDATE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.DELETE_MANDATE_TYPE[FAILURE]),
  reset: () => action(actions.DELETE_MANDATE_TYPE[RESET], {}),
}

interface NodeProps {
  name: string
  emp_id: string
  designation: string
  imageURL: string
  drs?: NodeProps[]
}
export const peopleBasedOrgChart = {
  request: (data: NodeProps) => action(actions.PEOPLE_BASED_ORG_CHART_DATA[REQUEST], data),
  success: (response: NodeProps) =>
    action(actions.PEOPLE_BASED_ORG_CHART_DATA[SUCCESS], { response }),
  failure: () => action(actions.PEOPLE_BASED_ORG_CHART_DATA[FAILURE]),
  reset: () => action(actions.PEOPLE_BASED_ORG_CHART_DATA[RESET], {}),
}

export const departmentBasedOrgChart = {
  request: (data: any) => action(actions.DEPARTMENT_BASED_ORG_CHART_DATA[REQUEST], data),
  success: (response: any) =>
    action(actions.DEPARTMENT_BASED_ORG_CHART_DATA[SUCCESS], { response }),
  failure: () => action(actions.DEPARTMENT_BASED_ORG_CHART_DATA[FAILURE]),
  reset: () => action(actions.DEPARTMENT_BASED_ORG_CHART_DATA[RESET], {}),
}

export const designationBasedOrgChart = {
  request: (data: NodeProps) => action(actions.DESIGNATION_BASED_ORG_CHART_DATA[REQUEST], data),
  success: (response: NodeProps) =>
    action(actions.DESIGNATION_BASED_ORG_CHART_DATA[SUCCESS], { response }),
  failure: () => action(actions.DESIGNATION_BASED_ORG_CHART_DATA[FAILURE]),
  reset: () => action(actions.DESIGNATION_BASED_ORG_CHART_DATA[RESET], {}),
}

export const employeeHistory = {
  request: (data: { id: number }) => action(actions.EMPLOYEE_HISTORY[REQUEST], data),
  success: (response: EmpHistoryDataTypes) =>
    action(actions.EMPLOYEE_HISTORY[SUCCESS], { response }),
  failure: () => action(actions.EMPLOYEE_HISTORY[FAILURE]),
  reset: () => action(actions.EMPLOYEE_HISTORY[RESET], {}),
}

export const managerHistory = {
  request: (data: { id: number }) => action(actions.MANAGER_HISTORY[REQUEST], data),
  success: (Response: EmpManagerDataTypes) =>
    action(actions.MANAGER_HISTORY[SUCCESS], { Response }),
  failure: () => action(actions.MANAGER_HISTORY[FAILURE]),
  reset: () => action(actions.MANAGER_HISTORY[RESET], {}),
}
export interface LogoApiResponse {
  type: string
  data: string
}

export const companyLogo = {
  request: (data: { id: number }) => action(actions.COMPANY_LOGO[REQUEST], data),
  success: (Response: LogoApiResponse) => action(actions.COMPANY_LOGO[SUCCESS], { Response }),
  failure: () => action(actions.COMPANY_LOGO[FAILURE]),
  reset: () => action(actions.COMPANY_LOGO[RESET], {}),
}
export interface Company {
  id: number
  company_name: string
}

export interface CompanyIdTypes {
  type: string
  data: Company[]
}

export const companyId = {
  request: () => action(actions.COMPANY_ID[REQUEST]),
  success: (Response: CompanyIdTypes) => action(actions.COMPANY_ID[SUCCESS], { Response }),
  failure: () => action(actions.COMPANY_ID[FAILURE]),
  reset: () => action(actions.COMPANY_ID[RESET], {}),
}

export const approvePlanForTheDay = {
  request: (data: {}) => action(actions.APPROVE_PLANFORTHEDAY[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.APPROVE_PLANFORTHEDAY[SUCCESS], { Response }),
  failure: () => action(actions.APPROVE_PLANFORTHEDAY[FAILURE]),
  reset: () => action(actions.APPROVE_PLANFORTHEDAY[RESET], {}),
}

export const addCommentTimesheet = {
  request: (data: {}) => action(actions.ADD_COMMENT_TIMESHEET[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.ADD_COMMENT_TIMESHEET[SUCCESS], { Response }),
  failure: () => action(actions.ADD_COMMENT_TIMESHEET[FAILURE]),
  reset: () => action(actions.ADD_COMMENT_TIMESHEET[RESET], {}),
}

export const addCommentPlanForTheDay = {
  request: (data: {}) => action(actions.ADD_COMMENT_PLANFORTHEDAY[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.ADD_COMMENT_PLANFORTHEDAY[SUCCESS], { Response }),
  failure: () => action(actions.ADD_COMMENT_PLANFORTHEDAY[FAILURE]),
  reset: () => action(actions.ADD_COMMENT_PLANFORTHEDAY[RESET], {}),
}

export const getCommentPLanForTheDay = {
  request: (data: {}) => action(actions.GET_COMMENT_PLANFORTHEDAY[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_COMMENT_PLANFORTHEDAY[SUCCESS], { Response }),
  failure: () => action(actions.GET_COMMENT_PLANFORTHEDAY[FAILURE]),
  reset: () => action(actions.GET_COMMENT_PLANFORTHEDAY[RESET], {}),
}

export const getCommentForTimesheet = {
  request: (data: {}) => action(actions.GET_COMMENT_TIMESHEET[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_COMMENT_TIMESHEET[SUCCESS], { Response }),
  failure: () => action(actions.GET_COMMENT_TIMESHEET[FAILURE]),
  reset: () => action(actions.GET_COMMENT_TIMESHEET[RESET], {}),
}

export const getPlanForTheDay = {
  request: (data: {}) => action(actions.GET_PLANFORTHEDAY[REQUEST], data),
  success: (Response: CompanyIdTypes) => action(actions.GET_PLANFORTHEDAY[SUCCESS], { Response }),
  failure: () => action(actions.GET_PLANFORTHEDAY[FAILURE]),
  reset: () => action(actions.GET_PLANFORTHEDAY[RESET], {}),
}

export const editPlanForTheDay = {
  request: (data: {}) => action(actions.EDIT_PLANFORTHEDAY[REQUEST], data),
  success: (Response: CompanyIdTypes) => action(actions.EDIT_PLANFORTHEDAY[SUCCESS], { Response }),
  failure: () => action(actions.EDIT_PLANFORTHEDAY[FAILURE]),
  reset: () => action(actions.EDIT_PLANFORTHEDAY[RESET], {}),
}

export const getDownLoadCsvForMyTeam = {
  request: (data: {}) => action(actions.DOWNLOAD_CSV_FOR_MY_TEAM[REQUEST], data),
  success: (Response: any) => action(actions.DOWNLOAD_CSV_FOR_MY_TEAM[SUCCESS], { Response }),
  failure: () => action(actions.DOWNLOAD_CSV_FOR_MY_TEAM[FAILURE]),
  reset: () => action(actions.DOWNLOAD_CSV_FOR_MY_TEAM[RESET], {}),
}

export const getSingleIdsr = {
  request: (data: {}) => action(actions.GET_SINGLE_IDSR[REQUEST], data),
  success: (Response: any) => action(actions.GET_SINGLE_IDSR[SUCCESS], { Response }),
  failure: () => action(actions.GET_SINGLE_IDSR[FAILURE]),
  reset: () => action(actions.GET_SINGLE_IDSR[RESET], {}),
}

export const editSingleIdsr = {
  request: (data: {}) => action(actions.EDIT_SINGLE_IDSR[REQUEST], data),
  success: (Response: any) => action(actions.EDIT_SINGLE_IDSR[SUCCESS], { Response }),
  failure: () => action(actions.EDIT_SINGLE_IDSR[FAILURE]),
  reset: () => action(actions.EDIT_SINGLE_IDSR[RESET], {}),
}

export const getPayrollForm16 = {
  request: (data: any) => action(actions.PAYROLL_FORM16[REQUEST], data),
  success: (Response: any) => action(actions.PAYROLL_FORM16[SUCCESS], { Response }),
  failure: () => action(actions.PAYROLL_FORM16[FAILURE]),
  reset: () => action(actions.PAYROLL_FORM16[RESET], {}),
}

export const getFinancialYear = {
  request: () => action(actions.GET_FINANCIAL_YEAR[REQUEST]),
  success: (Response: any) => action(actions.GET_FINANCIAL_YEAR[SUCCESS], { Response }),
  failure: () => action(actions.GET_FINANCIAL_YEAR[FAILURE]),
  reset: () => action(actions.GET_FINANCIAL_YEAR[RESET], {}),
}

export const uploadForm16 = {
  request: (data: { id_user: number; file: string; currentYear: any }) =>
    action(actions.UPLOAD_FORM16[REQUEST], data),
  success: (Response: any) => action(actions.UPLOAD_FORM16[SUCCESS], { Response }),
  failure: () => action(actions.UPLOAD_FORM16[FAILURE]),
  reset: () => action(actions.UPLOAD_FORM16[RESET], {}),
}

export const sendMail = {
  request: (data: { userId: number; accountsDepartmentId: number; currentYear: any }) =>
    action(actions.SEND_EMAIL[REQUEST], data),
  success: (Response: any) => action(actions.SEND_EMAIL[SUCCESS], { Response }),
  failure: () => action(actions.SEND_EMAIL[FAILURE]),
  reset: () => action(actions.SEND_EMAIL[RESET], {}),
}

export const downloadForm16 = {
  request: (data: any) => action(actions.DOWNLOAD_FORM16[REQUEST], data),
  success: (Response: any) => action(actions.DOWNLOAD_FORM16[SUCCESS], { Response }),
  failure: () => action(actions.DOWNLOAD_FORM16[FAILURE]),
  reset: () => action(actions.DOWNLOAD_FORM16[RESET], {}),
}

export const fetchReports = {
  request: (data: any) => action(actions.FETCH_REPORTS[REQUEST], data),
  success: (response: any) => action(actions.FETCH_REPORTS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_REPORTS[FAILURE]),
  reset: () => action(actions.FETCH_REPORTS[RESET], {}),
}

export const fetchCertificateInfo = {
  request: (data: any) => action(actions.GET_CERTIFICATE_INFO[REQUEST], data),
  success: (response: any) => action(actions.GET_CERTIFICATE_INFO[SUCCESS], { response }),
  failure: () => action(actions.GET_CERTIFICATE_INFO[FAILURE]),
  reset: () => action(actions.GET_CERTIFICATE_INFO[RESET], {}),
}

export const fetchEmployee = {
  request: (data: any) => action(actions.GET_EMPLOYEE[REQUEST], data),
  success: (response: any) => action(actions.GET_EMPLOYEE[SUCCESS], { response }),
  failure: () => action(actions.GET_EMPLOYEE[FAILURE]),
  reset: () => action(actions.GET_EMPLOYEE[RESET], {}),
}

export const fetchEmployeeInfo = {
  request: (data: any) => action(actions.GET_EMPLOYEEMENT_INFO[REQUEST], data),
  success: (response: any) => action(actions.GET_EMPLOYEEMENT_INFO[SUCCESS], { response }),
  failure: () => action(actions.GET_EMPLOYEEMENT_INFO[FAILURE]),
  reset: () => action(actions.GET_EMPLOYEEMENT_INFO[RESET], {}),
}

export const fetchUserTechnology = {
  request: (data: any) => action(actions.GET_USER_TECHNOLOGY[REQUEST], data),
  success: (response: any) => action(actions.GET_USER_TECHNOLOGY[SUCCESS], { response }),
  failure: () => action(actions.GET_USER_TECHNOLOGY[FAILURE]),
  reset: () => action(actions.GET_USER_TECHNOLOGY[RESET], {}),
}

export const fetchCountryDistribution = {
  request: (data: any) => action(actions.GET_COUNTRY_DISTRIBUTION[REQUEST], data),
  success: (response: any) => action(actions.GET_COUNTRY_DISTRIBUTION[SUCCESS], { response }),
  failure: () => action(actions.GET_COUNTRY_DISTRIBUTION[FAILURE]),
  reset: () => action(actions.GET_COUNTRY_DISTRIBUTION[RESET], {}),
}

export const fetchGradeLable = {
  request: () => action(actions.GET_GRADE_LABLE[REQUEST]),
  success: (response: any) => action(actions.GET_GRADE_LABLE[SUCCESS], { response }),
  failure: () => action(actions.GET_GRADE_LABLE[FAILURE]),
  reset: () => action(actions.GET_GRADE_LABLE[RESET], {}),
}

export const fetchCertiBySplitUp = {
  request: (data: any) => action(actions.GET_CERTIFICATE_BY_SPLIT[REQUEST], data),
  success: (response: any) => action(actions.GET_CERTIFICATE_BY_SPLIT[SUCCESS], { response }),
  failure: () => action(actions.GET_CERTIFICATE_BY_SPLIT[FAILURE]),
  reset: () => action(actions.GET_CERTIFICATE_BY_SPLIT[RESET], {}),
}

export const fetchCertiPercentage = {
  request: (data: any) => action(actions.GET_CERTI_PERCENTAGE[REQUEST], data),
  success: (response: any) => action(actions.GET_CERTI_PERCENTAGE[SUCCESS], { response }),
  failure: () => action(actions.GET_CERTI_PERCENTAGE[FAILURE]),
  reset: () => action(actions.GET_CERTI_PERCENTAGE[RESET], {}),
}

export const fetchUserStatistics = {
  request: () => action(actions.GET_USER_STATICS[REQUEST]),
  success: (response: any) => action(actions.GET_USER_STATICS[SUCCESS], { response }),
  failure: () => action(actions.GET_USER_STATICS[FAILURE]),
  reset: () => action(actions.GET_USER_STATICS[RESET], {}),
}

export const downloadCertificate = {
  request: (data: any) => action(actions.DOWNLOAD_CERTIFICATE[REQUEST], data),
  success: (response: any) => action(actions.DOWNLOAD_CERTIFICATE[SUCCESS], { response }),
  failure: () => action(actions.DOWNLOAD_CERTIFICATE[FAILURE]),
  reset: () => action(actions.DOWNLOAD_CERTIFICATE[RESET], {}),
}

export const getTaxReport = {
  request: (data: any) => action(TAX_REPORT_DATA[REQUEST], { data }),
  success: (response: any) => action(TAX_REPORT_DATA[SUCCESS], { response }),
  failure: () => action(TAX_REPORT_DATA[FAILURE]),
  reset: () => action(TAX_REPORT_DATA[RESET], {}),
}

export const getTaxFinancialYear = {
  request: (data: any) => action(TAX_FINANCIAL_YEAR[REQUEST], { data }),
  success: (response: any) => action(TAX_FINANCIAL_YEAR[SUCCESS], { response }),
  failure: () => action(TAX_FINANCIAL_YEAR[FAILURE]),
  reset: () => action(TAX_FINANCIAL_YEAR[RESET], {}),
}

export const addBankInfo = {
  request: (data: any) => action(actions.ADD_BANK_INFO[REQUEST], { data }),
  success: (response: string) => action(actions.ADD_BANK_INFO[SUCCESS], { response }),
  failure: () => action(actions.ADD_BANK_INFO[FAILURE]),
  reset: () => action(actions.ADD_BANK_INFO[RESET], {}),
}

export const fetchBackgroundInfo = {
  request: (data: any) => action(actions.FETCH_BACKGROUND_INFO[REQUEST], { data }),
  success: (response: string) => action(actions.FETCH_BACKGROUND_INFO[SUCCESS], { response }),
  failure: () => action(actions.FETCH_BACKGROUND_INFO[FAILURE]),
  reset: () => action(actions.FETCH_BACKGROUND_INFO[RESET], {}),
}

export const addBackgroundInfo = {
  request: (data: any) => action(actions.ADD_BACKGROUND_INFO[REQUEST], { data }),
  success: (response: string) => action(actions.ADD_BACKGROUND_INFO[SUCCESS], { response }),
  failure: () => action(actions.ADD_BACKGROUND_INFO[FAILURE]),
  reset: () => action(actions.ADD_BACKGROUND_INFO[RESET], {}),
}

export const fetchEmpSalarySlip = {
  request: (data: any) => action(actions.EMP_SALARY_SLIP[REQUEST], { data }),
  success: (response: string) => action(actions.EMP_SALARY_SLIP[SUCCESS], { response }),
  failure: () => action(actions.EMP_SALARY_SLIP[FAILURE]),
  reset: () => action(actions.EMP_SALARY_SLIP[RESET], {}),
}

export const fetchCompensation = {
  request: (data: any) => action(actions.EMP_COMPENSATION[REQUEST], { data }),
  success: (response: string) => action(actions.EMP_COMPENSATION[SUCCESS], { response }),
  failure: () => action(actions.EMP_COMPENSATION[FAILURE]),
  reset: () => action(actions.EMP_COMPENSATION[RESET], {}),
}

export const fetchDownloadDocument = {
  request: (data: any) => action(actions.DOWNLOAD_DOCUMENT[REQUEST], { data }),
  success: (response: string) => action(actions.DOWNLOAD_DOCUMENT[SUCCESS], { response }),
  failure: () => action(actions.DOWNLOAD_DOCUMENT[FAILURE]),
  reset: () => action(actions.DOWNLOAD_DOCUMENT[RESET], {}),
}

export const fetchLeaveType = {
  request: (data: any) => action(actions.FETCH_LEAVE_TYPE[REQUEST], { data }),
  success: (response: string) => action(actions.FETCH_LEAVE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_LEAVE_TYPE[FAILURE]),
  reset: () => action(actions.FETCH_LEAVE_TYPE[RESET], {}),
}

export const createLeave = {
  request: (data: any) => action(actions.CREATE_LEAVE[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_LEAVE[SUCCESS], { response }),
  failure: () => action(actions.CREATE_LEAVE[FAILURE]),
  reset: () => action(actions.CREATE_LEAVE[RESET], {}),
}

export const updateLeave = {
  request: (data: any) => action(actions.UPDATE_LEAVE[REQUEST], { data }),
  success: (response: string) => action(actions.UPDATE_LEAVE[SUCCESS], { response }),
  failure: () => action(actions.UPDATE_LEAVE[FAILURE]),
  reset: () => action(actions.UPDATE_LEAVE[RESET], {}),
}

export const fetchPayrollDropdown = {
  request: (data: any) => action(actions.PAYROLL_DROPDOWN_TYPE[REQUEST], { data }),
  success: (response: string) => action(actions.PAYROLL_DROPDOWN_TYPE[SUCCESS], { response }),
  failure: () => action(actions.PAYROLL_DROPDOWN_TYPE[FAILURE]),
  reset: () => action(actions.PAYROLL_DROPDOWN_TYPE[RESET], {}),
}

export const deleteLeave = {
  request: (data: any) => action(actions.DELETE_LEAVE[REQUEST], { data }),
  success: (response: string) => action(actions.DELETE_LEAVE[SUCCESS], { response }),
  failure: () => action(actions.DELETE_LEAVE[FAILURE]),
  reset: () => action(actions.DELETE_LEAVE[RESET], {}),
}

export const createPayroll = {
  request: (data: any) => action(actions.CREATE_PAYROLL[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_PAYROLL[SUCCESS], { response }),
  failure: () => action(actions.CREATE_PAYROLL[FAILURE]),
  reset: () => action(actions.CREATE_PAYROLL[RESET], {}),
}

export const fetchPayroll = {
  request: (data: any) => action(actions.GET_PAYROLL[REQUEST], { data }),
  success: (response: string) => action(actions.GET_PAYROLL[SUCCESS], { response }),
  failure: () => action(actions.GET_PAYROLL[FAILURE]),
  reset: () => action(actions.GET_PAYROLL[RESET], {}),
}

export const createHolidayForAdmin = {
  request: (data: any) => action(actions.CREATE_HOLIDAY[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_HOLIDAY[SUCCESS], { response }),
  failure: () => action(actions.CREATE_HOLIDAY[FAILURE]),
  reset: () => action(actions.CREATE_HOLIDAY[RESET], {}),
}

export const fetchEmpDesignations = {
  request: (data: any) => action(actions.GET_EMP_DESIGNATIONS[REQUEST], { data }),
  success: (response: string) => action(actions.GET_EMP_DESIGNATIONS[SUCCESS], { response }),
  failure: () => action(actions.GET_EMP_DESIGNATIONS[FAILURE]),
  reset: () => action(actions.GET_EMP_DESIGNATIONS[RESET], {}),
}

export const createEmpDesignations = {
  request: (data: any) => action(actions.CREATE_EMP_DESIGNATIONS[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_EMP_DESIGNATIONS[SUCCESS], { response }),
  failure: () => action(actions.CREATE_EMP_DESIGNATIONS[FAILURE]),
  reset: () => action(actions.CREATE_EMP_DESIGNATIONS[RESET], {}),
}

export const editDesignations = {
  request: (data: any) => action(actions.EDIT_DESIGNATIONS[REQUEST], { data }),
  success: (response: string) => action(actions.EDIT_DESIGNATIONS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_DESIGNATIONS[FAILURE]),
  reset: () => action(actions.EDIT_DESIGNATIONS[RESET], {}),
}

export const createTiming = {
  request: (data: any) => action(actions.CREATE_TIMING[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_TIMING[SUCCESS], { response }),
  failure: () => action(actions.CREATE_TIMING[FAILURE]),
  reset: () => action(actions.CREATE_TIMING[RESET], {}),
}

export const createQualification = {
  request: (data: any) => action(actions.CREATE_QAULIFICATION[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_QAULIFICATION[SUCCESS], { response }),
  failure: () => action(actions.CREATE_QAULIFICATION[FAILURE]),
  reset: () => action(actions.CREATE_QAULIFICATION[RESET], {}),
}

export const createLeaveType = {
  request: (data: any) => action(actions.CREATE_LEAVE_TYPE[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_LEAVE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.CREATE_LEAVE_TYPE[FAILURE]),
  reset: () => action(actions.CREATE_LEAVE_TYPE[RESET], {}),
}

export const createLeaves = {
  request: (data: any) => action(actions.CREATE_LEAVES[REQUEST], { data }),
  success: (response: string) => action(actions.CREATE_LEAVES[SUCCESS], { response }),
  failure: () => action(actions.CREATE_LEAVES[FAILURE]),
  reset: () => action(actions.CREATE_LEAVES[RESET], {}),
}

export const fetchLeavesInfo = {
  request: (data: any) => action(actions.GET_LEAVE_INFO[REQUEST], { data }),
  success: (response: string) => action(actions.GET_LEAVE_INFO[SUCCESS], { response }),
  failure: () => action(actions.GET_LEAVE_INFO[FAILURE]),
  reset: () => action(actions.GET_LEAVE_INFO[RESET], {}),
}

export const getAssetsData = {
  request: (data: {}) => action(actions.ASSETS_DATA[REQUEST], data),
  success: (response: any) => action(actions.ASSETS_DATA[SUCCESS], { response }),
  failure: () => action(actions.ASSETS_DATA[FAILURE]),
  reset: () => action(actions.ASSETS_DATA[RESET], {}),
}

export const getEmpData = {
  request: (data: {}) => action(actions.FETCH_EMP_DATA[REQUEST], data),
  success: (response: any) => action(actions.FETCH_EMP_DATA[SUCCESS], { response }),
  failure: () => action(actions.FETCH_EMP_DATA[FAILURE]),
  reset: () => action(actions.FETCH_EMP_DATA[RESET], {}),
}

export const getWorkingEmpData = {
  request: (data: {}) => action(actions.FETCH_EMP_ASSET_DATA[REQUEST], data),
  success: (response: any) => action(actions.FETCH_EMP_ASSET_DATA[SUCCESS], { response }),
  failure: () => action(actions.FETCH_EMP_ASSET_DATA[FAILURE]),
  reset: () => action(actions.FETCH_EMP_ASSET_DATA[RESET], {}),
}

export const fetchAssetsData = {
  request: (data: {}) => action(actions.FETCH_ASSETS[REQUEST], data),
  success: (response: any) => action(actions.FETCH_ASSETS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ASSETS[FAILURE]),
  reset: () => action(actions.FETCH_ASSETS[RESET], {}),
}

export const fetchNumberOfAssetsData = {
  request: () => action(actions.FETCH_NUMBER_OF_ASSETS[REQUEST]),
  success: (response: any) => action(actions.FETCH_NUMBER_OF_ASSETS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_NUMBER_OF_ASSETS[FAILURE]),
  reset: () => action(actions.FETCH_NUMBER_OF_ASSETS[RESET], {}),
}

export const updateAssignAsset = {
  request: (data: {}) => action(actions.ASSIGN_ASSET[REQUEST], data),
  success: (response: any) => action(actions.ASSIGN_ASSET[SUCCESS], { response }),
  failure: () => action(actions.ASSIGN_ASSET[FAILURE]),
  reset: () => action(actions.ASSIGN_ASSET[RESET], {}),
}

export const employeeAssets = {
  request: (data: {}) => action(actions.EMPLOYEE_ASSETS_DETAILS[REQUEST], data),
  success: (response: any) => action(actions.EMPLOYEE_ASSETS_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.EMPLOYEE_ASSETS_DETAILS[FAILURE]),
  reset: () => action(actions.EMPLOYEE_ASSETS_DETAILS[RESET], {}),
}

export const updateUnallocateAsset = {
  request: (data: {}) => action(actions.UNALLOCATE_ASSET[REQUEST], data),
  success: (response: any) => action(actions.UNALLOCATE_ASSET[SUCCESS], { response }),
  failure: () => action(actions.UNALLOCATE_ASSET[FAILURE]),
  reset: () => action(actions.UNALLOCATE_ASSET[RESET], {}),
}

export const updateAddNewAsset = {
  request: (data: {}) => action(actions.ADD_NEW_ASSET[REQUEST], data),
  success: (response: any) => action(actions.ADD_NEW_ASSET[SUCCESS], { response }),
  failure: () => action(actions.ADD_NEW_ASSET[FAILURE]),
  reset: () => action(actions.ADD_NEW_ASSET[RESET], {}),
}

export const fetchAssetsDataAsExcel = {
  request: (data: {}) => action(actions.FETCH_ASSETS_DATA_AS_EXCEL[REQUEST], data),
  success: (Response: any) => action(actions.FETCH_ASSETS_DATA_AS_EXCEL[SUCCESS], { Response }),
  failure: () => action(actions.FETCH_ASSETS_DATA_AS_EXCEL[FAILURE]),
  reset: () => action(actions.FETCH_ASSETS_DATA_AS_EXCEL[RESET], {}),
}

export const fetchAssetAbbrevationData = {
  request: () => action(actions.ASSET_ABBERATION[REQUEST]),
  success: (response: any) => action(actions.ASSET_ABBERATION[SUCCESS], { response }),
  failure: () => action(actions.ASSET_ABBERATION[FAILURE]),
  reset: () => action(actions.ASSET_ABBERATION[RESET], {}),
}

export const fetchAssetLocationData = {
  request: () => action(actions.ASSET_LOCATION[REQUEST]),
  success: (response: any) => action(actions.ASSET_LOCATION[SUCCESS], { response }),
  failure: () => action(actions.ASSET_LOCATION[FAILURE]),
  reset: () => action(actions.ASSET_LOCATION[RESET], {}),
}

export const fetchAssetMakeData = {
  request: () => action(actions.ASSET_MAKE[REQUEST]),
  success: (response: any) => action(actions.ASSET_MAKE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_MAKE[FAILURE]),
  reset: () => action(actions.ASSET_MAKE[RESET], {}),
}

export const fetchAssetStatusData = {
  request: () => action(actions.ASSET_STATUS[REQUEST]),
  success: (response: any) => action(actions.ASSET_STATUS[SUCCESS], { response }),
  failure: () => action(actions.ASSET_STATUS[FAILURE]),
  reset: () => action(actions.ASSET_STATUS[RESET], {}),
}

export const fetchAssetOSData = {
  request: () => action(actions.ASSET_OS[REQUEST]),
  success: (response: any) => action(actions.ASSET_OS[SUCCESS], { response }),
  failure: () => action(actions.ASSET_OS[FAILURE]),
  reset: () => action(actions.ASSET_OS[RESET], {}),
}

export const fetchAssetTechnologiesData = {
  request: () => action(actions.ASSET_TECHNOLOGIES[REQUEST]),
  success: (response: any) => action(actions.ASSET_TECHNOLOGIES[SUCCESS], { response }),
  failure: () => action(actions.ASSET_TECHNOLOGIES[FAILURE]),
  reset: () => action(actions.ASSET_TECHNOLOGIES[RESET], {}),
}

export const getWeeklySummaries = {
  request: (data: any) => action(actionTypes.WEEKLY_SUMMARIES[REQUEST], { data }),
  success: (data: any) => action(actionTypes.WEEKLY_SUMMARIES[SUCCESS], { data }),
  failure: (error: any) => action(actionTypes.WEEKLY_SUMMARIES[FAILURE], { error }),
  reset: () => action(actionTypes.WEEKLY_SUMMARIES[RESET], {}),
}

export const getMonthlySummaries = {
  request: (data: any) => action(actionTypes.MONTHLY_SUMMARIES[REQUEST], { data }),
  success: (data: any) => action(actionTypes.MONTHLY_SUMMARIES[SUCCESS], { data }),
  failure: (error: any) => action(actionTypes.MONTHLY_SUMMARIES[FAILURE], { error }),
  reset: () => action(actionTypes.MONTHLY_SUMMARIES[RESET], {}),
}

export const getDailySummaries = {
  request: (data: any) => action(actions.GET_DAILY_SUMMARIES[REQUEST], { data }),
  success: (response: any) => action(actions.GET_DAILY_SUMMARIES[SUCCESS], { response }),
  failure: (error: any) => action(actions.GET_DAILY_SUMMARIES[FAILURE], { error }),
  reset: () => action(actions.GET_DAILY_SUMMARIES[RESET], {}),
}

export const getAssetCategoreyData = {
  request: (data: {}) => action(actions.ASSET_CATEGORY[REQUEST], data),
  success: (response: any) => action(actions.ASSET_CATEGORY[SUCCESS], { response }),
  failure: () => action(actions.ASSET_CATEGORY[FAILURE]),
  reset: () => action(actions.ASSET_CATEGORY[RESET], {}),
}

export const getAssetOSData = {
  request: (data: {}) => action(actions.ASSET_OS_DATA[REQUEST], data),
  success: (response: any) => action(actions.ASSET_OS_DATA[SUCCESS], { response }),
  failure: () => action(actions.ASSET_OS_DATA[FAILURE]),
  reset: () => action(actions.ASSET_OS_DATA[RESET], {}),
}

export const updateAssetOs = {
  request: (data: {}) => action(actions.ASSET_OS_UPDATE[REQUEST], data),
  success: (response: any) => action(actions.ASSET_OS_UPDATE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_OS_UPDATE[FAILURE]),
  reset: () => action(actions.ASSET_OS_UPDATE[RESET], {}),
}

export const updateAssetCategory = {
  request: (data: {}) => action(actions.ASSET_CATEGORY_UPDATE[REQUEST], data),
  success: (response: any) => action(actions.ASSET_CATEGORY_UPDATE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_CATEGORY_UPDATE[FAILURE]),
  reset: () => action(actions.ASSET_CATEGORY_UPDATE[RESET], {}),
}

export const deleteAssetCategory = {
  request: (data: {}) => action(actions.ASSET_CATEGORY_DELETE[REQUEST], data),
  success: (response: any) => action(actions.ASSET_CATEGORY_DELETE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_CATEGORY_DELETE[FAILURE]),
  reset: () => action(actions.ASSET_CATEGORY_DELETE[RESET], {}),
}

export const getAssetMakeData = {
  request: (data: {}) => action(actions.ASSET_MAKE_DATA[REQUEST], data),
  success: (response: any) => action(actions.ASSET_MAKE_DATA[SUCCESS], { response }),
  failure: () => action(actions.ASSET_MAKE_DATA[FAILURE]),
  reset: () => action(actions.ASSET_MAKE_DATA[RESET], {}),
}

export const addAssetMake = {
  request: (data: {}) => action(actions.ADD_ASSET_MAKE[REQUEST], data),
  success: (response: any) => action(actions.ADD_ASSET_MAKE[SUCCESS], { response }),
  failure: () => action(actions.ADD_ASSET_MAKE[FAILURE]),
  reset: () => action(actions.ADD_ASSET_MAKE[RESET], {}),
}

export const deleteAssetMake = {
  request: (data: {}) => action(actions.ASSET_MAKE_DELETE[REQUEST], data),
  success: (response: any) => action(actions.ASSET_MAKE_DELETE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_MAKE_DELETE[FAILURE]),
  reset: () => action(actions.ASSET_MAKE_DELETE[RESET], {}),
}

export const getAssetStatusData = {
  request: (data: {}) => action(actions.ASSET_STATUS_DATA[REQUEST], data),
  success: (response: any) => action(actions.ASSET_STATUS_DATA[SUCCESS], { response }),
  failure: () => action(actions.ASSET_STATUS_DATA[FAILURE]),
  reset: () => action(actions.ASSET_STATUS_DATA[RESET], {}),
}

export const addAssetStatus = {
  request: (data: {}) => action(actions.ADD_ASSET_STATUS[REQUEST], data),
  success: (response: any) => action(actions.ADD_ASSET_STATUS[SUCCESS], { response }),
  failure: () => action(actions.ADD_ASSET_STATUS[FAILURE]),
  reset: () => action(actions.ADD_ASSET_STATUS[RESET], {}),
}

export const deleteAssetStatus = {
  request: (data: {}) => action(actions.ASSET_STATUS_DELETE[REQUEST], data),
  success: (response: any) => action(actions.ASSET_STATUS_DELETE[SUCCESS], { response }),
  failure: () => action(actions.ASSET_STATUS_DELETE[FAILURE]),
  reset: () => action(actions.ASSET_STATUS_DELETE[RESET], {}),
}

export const getTechnologiesData = {
  request: (data: {}) => action(actions.TECHNOLOGIES_DATA[REQUEST], data),
  success: (response: any) => action(actions.TECHNOLOGIES_DATA[SUCCESS], { response }),
  failure: () => action(actions.TECHNOLOGIES_DATA[FAILURE]),
  reset: () => action(actions.TECHNOLOGIES_DATA[RESET], {}),
}

export const addTechonologies = {
  request: (data: {}) => action(actions.ADD_TECHNOLOGIES[REQUEST], data),
  success: (response: any) => action(actions.ADD_TECHNOLOGIES[SUCCESS], { response }),
  failure: () => action(actions.ADD_TECHNOLOGIES[FAILURE]),
  reset: () => action(actions.ADD_TECHNOLOGIES[RESET], {}),
}

export const deleteTechnologies = {
  request: (data: {}) => action(actions.DELETE_TECHNOLOGIES[REQUEST], data),
  success: (response: any) => action(actions.DELETE_TECHNOLOGIES[SUCCESS], { response }),
  failure: () => action(actions.DELETE_TECHNOLOGIES[FAILURE]),
  reset: () => action(actions.DELETE_TECHNOLOGIES[RESET], {}),
}

export const deleteAssetOs = {
  request: (data: {}) => action(actions.DELETE_ASSET_OS[REQUEST], data),
  success: (response: any) => action(actions.DELETE_ASSET_OS[SUCCESS], { response }),
  failure: () => action(actions.DELETE_ASSET_OS[FAILURE]),
  reset: () => action(actions.DELETE_ASSET_OS[RESET], {}),
}

export const getEmployeeAssetsCountData = {
  request: () => action(actions.EMPLOYEE_ASSETS_COUNT[REQUEST]),
  success: (response: any) => action(actions.EMPLOYEE_ASSETS_COUNT[SUCCESS], { response }),
  failure: () => action(actions.EMPLOYEE_ASSETS_COUNT[FAILURE]),
  reset: () => action(actions.EMPLOYEE_ASSETS_COUNT[RESET], {}),
}

export const getAssetsAbbrevationCountData = {
  request: () => action(actions.ASSET_ABBREVATION_COUNT[REQUEST]),
  success: (response: any) => action(actions.ASSET_ABBREVATION_COUNT[SUCCESS], { response }),
  failure: () => action(actions.ASSET_ABBREVATION_COUNT[FAILURE]),
  reset: () => action(actions.ASSET_ABBREVATION_COUNT[RESET], {}),
}

export const getAssetStatusCountData = {
  request: () => action(actions.ASSET_STATUS_COUNT[REQUEST]),
  success: (response: any) => action(actions.ASSET_STATUS_COUNT[SUCCESS], { response }),
  failure: () => action(actions.ASSET_STATUS_COUNT[FAILURE]),
  reset: () => action(actions.ASSET_STATUS_COUNT[RESET], {}),
}

export const fetchLeaveTypeData = {
  request: (data: any) => action(actions.FETCH_LEAVE_TYPES[REQUEST], { data }),
  success: (response: any) => action(actions.FETCH_LEAVE_TYPES[SUCCESS], { response }),
  failure: () => action(actions.FETCH_LEAVE_TYPES[FAILURE]),
  reset: () => action(actions.FETCH_LEAVE_TYPES[RESET], {}),
}
export const getDownloadAssetsQrData = {
  request: () => action(actions.DOWNLOAD_ASSETS_QR[REQUEST]),
  success: (response: any) => action(actions.DOWNLOAD_ASSETS_QR[SUCCESS], { response }),
  failure: () => action(actions.DOWNLOAD_ASSETS_QR[FAILURE]),
  reset: () => action(actions.DOWNLOAD_ASSETS_QR[RESET], {}),
}

export const getAssignAssetReportData = {
  request: () => action(actions.ASSIGN_ASSET_REPORT[REQUEST]),
  success: (response: any) => action(actions.ASSIGN_ASSET_REPORT[SUCCESS], { response }),
  failure: () => action(actions.ASSIGN_ASSET_REPORT[FAILURE]),
  reset: () => action(actions.ASSIGN_ASSET_REPORT[RESET], {}),
}

export const fetchLeaveReports = {
  request: (data: {}) => action(actions.GET_LEAVE_REPORT[REQUEST], data),
  success: (Response: CompanyIdTypes) => action(actions.GET_LEAVE_REPORT[SUCCESS], { Response }),
  failure: () => action(actions.GET_LEAVE_REPORT[FAILURE]),
  reset: () => action(actions.GET_LEAVE_REPORT[RESET], {}),
}

export const fetchLeaveReportsBalance = {
  request: (data: {}) => action(actions.GET_LEAVE_BALANCE_REPORT[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_LEAVE_BALANCE_REPORT[SUCCESS], { Response }),
  failure: () => action(actions.GET_LEAVE_BALANCE_REPORT[FAILURE]),
  reset: () => action(actions.GET_LEAVE_BALANCE_REPORT[RESET], {}),
}

export const fetchLeaveReportsType = {
  request: (data: {}) => action(actions.GET_LEAVE_TYPE_REPORT[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_LEAVE_TYPE_REPORT[SUCCESS], { Response }),
  failure: () => action(actions.GET_LEAVE_TYPE_REPORT[FAILURE]),
  reset: () => action(actions.GET_LEAVE_TYPE_REPORT[RESET], {}),
}

export const fetchLeaveReportsAllocated = {
  request: (data: {}) => action(actions.GET_LEAVE_ALLOCATED_REPORT[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_LEAVE_ALLOCATED_REPORT[SUCCESS], { Response }),
  failure: () => action(actions.GET_LEAVE_ALLOCATED_REPORT[FAILURE]),
  reset: () => action(actions.GET_LEAVE_ALLOCATED_REPORT[RESET], {}),
}

export const fetchLeaveReportsEncashment = {
  request: (data: {}) => action(actions.GET_LEAVE_ENCASHMENT_REPORT[REQUEST], data),
  success: (Response: CompanyIdTypes) =>
    action(actions.GET_LEAVE_ENCASHMENT_REPORT[SUCCESS], { Response }),
  failure: () => action(actions.GET_LEAVE_ENCASHMENT_REPORT[FAILURE]),
  reset: () => action(actions.GET_LEAVE_ENCASHMENT_REPORT[RESET], {}),
}

export const getLeaveCategoryData = {
  request: () => action(actions.GET_CATEGORY[REQUEST]),
  success: (response: any) => action(actions.GET_CATEGORY[SUCCESS], { response }),
  failure: () => action(actions.GET_CATEGORY[FAILURE]),
  reset: () => action(actions.GET_CATEGORY[RESET], {}),
}

export const fetchDownloadQR = {
  request: (data: any) => action(actions.DOWNLOAD_QR[REQUEST], { data }),
  success: (response: string) => action(actions.DOWNLOAD_QR[SUCCESS], { response }),
  failure: () => action(actions.DOWNLOAD_QR[FAILURE]),
  reset: () => action(actions.DOWNLOAD_QR[RESET], {}),
}

export const fetchAssignedSrRequest = {
  request: (data: any) => action(actions.ASSIGNED_SR_REQUEST[REQUEST], { data }),
  success: (response: string) => action(actions.ASSIGNED_SR_REQUEST[SUCCESS], { response }),
  failure: () => action(actions.ASSIGNED_SR_REQUEST[FAILURE]),
  reset: () => action(actions.ASSIGNED_SR_REQUEST[RESET], {}),
}

export const fetchCertificationReport = {
  request: (data: any) => action(actions.CERTIFICATION_REPORT[REQUEST], { data }),
  success: (response: any) => action(actions.CERTIFICATION_REPORT[SUCCESS], { response }),
  failure: () => action(actions.CERTIFICATION_REPORT[FAILURE]),
  reset: () => action(actions.CERTIFICATION_REPORT[RESET], {}),
}

export const temporaryEmp = {
  request: (data: { [key: string]: string }) => action(actions.TEMPORARY_EMP[REQUEST], { data }),
  success: (response: any) => action(actions.TEMPORARY_EMP[SUCCESS], { response }),
  failure: () => action(actions.TEMPORARY_EMP[FAILURE]),
  reset: () => action(actions.TEMPORARY_EMP[RESET], {}),
}

export const leaveFrequencies = {
  request: () => action(actions.LEAVE_FREQUENCIES[REQUEST]),
  success: (response: any) => action(actions.LEAVE_FREQUENCIES[SUCCESS], { response }),
  failure: () => action(actions.LEAVE_FREQUENCIES[FAILURE]),
  reset: () => action(actions.LEAVE_FREQUENCIES[RESET], {}),
}

export const getQuatre = {
  request: (data: { [key: string]: string }) => action(actions.GET_QUATRES[REQUEST], { data }),
  success: (response: any) => action(actions.GET_QUATRES[SUCCESS], { response }),
  failure: () => action(actions.GET_QUATRES[FAILURE]),
  reset: () => action(actions.GET_QUATRES[RESET], {}),
}

export const getAvailableYears = {
  request: (data: { userId: string }) => action(actions.GET_AVAILABLE_YEARS[REQUEST], { data }),
  success: (response: any) => action(actions.GET_AVAILABLE_YEARS[SUCCESS], { response }),
  failure: (error: any) => action(actions.GET_AVAILABLE_YEARS[FAILURE], { error }),
  reset: () => action(actions.GET_AVAILABLE_YEARS[RESET], {}),
}

export const getReimbursementRequests = {
  request: (data: any) => action(actions.REIMBURSEMENT_REQUESTS[REQUEST], { data }),
  success: (response: any) => action(actions.REIMBURSEMENT_REQUESTS[SUCCESS], { response }),
  failure: () => action(actions.REIMBURSEMENT_REQUESTS[FAILURE]),
  reset: () => action(actions.REIMBURSEMENT_REQUESTS[RESET], {}),
}

export const getParticularExpense = {
  request: (data: any) => action(actions.PERTICULAR_EXPENSE[REQUEST], { data }),
  success: (response: any) => action(actions.PERTICULAR_EXPENSE[SUCCESS], { response }),
  failure: () => action(actions.PERTICULAR_EXPENSE[FAILURE]),
  reset: () => action(actions.PERTICULAR_EXPENSE[RESET], {}),
}

export const getExpenseStatusUpdate = {
  request: (data: any) => action(actions.EXPENSE_STATUS_UPDATE[REQUEST], { data }),
  success: (response: any) => action(actions.EXPENSE_STATUS_UPDATE[SUCCESS], { response }),
  failure: () => action(actions.EXPENSE_STATUS_UPDATE[FAILURE]),
  reset: () => action(actions.EXPENSE_STATUS_UPDATE[RESET], {}),
}

export const createExpense = {
  request: (data: { [key: string]: string | null | File[] }) =>
    action(actions.CREATE_EXPENSE[REQUEST], { data }),
  success: (response: any) => action(actions.CREATE_EXPENSE[SUCCESS], { response }),
  failure: () => action(actions.CREATE_EXPENSE[FAILURE]),
  reset: () => action(actions.CREATE_EXPENSE[RESET], {}),
}

export const fetchAttendanceReport = {
  request: (data: any) => action(actions.FETCH_ATTENDANCE_REPORT[REQUEST], data),
  success: (response: any) => action(actions.FETCH_ATTENDANCE_REPORT[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ATTENDANCE_REPORT[FAILURE]),
  reset: () => action(actions.FETCH_ATTENDANCE_REPORT[RESET], {}),
}

export const fetchServiceRequestDepartment = {
  request: (data: any) => action(actions.FETCH_SERVICE_REQUEST_DEPARTMENT[REQUEST], data),
  success: (response: any) =>
    action(actions.FETCH_SERVICE_REQUEST_DEPARTMENT[SUCCESS], { response }),
  failure: () => action(actions.FETCH_SERVICE_REQUEST_DEPARTMENT[FAILURE]),
  reset: () => action(actions.FETCH_SERVICE_REQUEST_DEPARTMENT[RESET], {}),
}

export const downloadAttachments = {
  request: (data: { [key: string]: string }) =>
    action(actions.DOWNLOAD_ATTACHMENTS[REQUEST], { data }),
  success: (response: any) => action(actions.DOWNLOAD_ATTACHMENTS[SUCCESS], { response }),
  failure: () => action(actions.DOWNLOAD_ATTACHMENTS[FAILURE]),
  reset: () => action(actions.DOWNLOAD_ATTACHMENTS[RESET], {}),
}
// recruitment //
export const fetchRounds = {
  request: () => action(actions.FETCH_ROUND[REQUEST]),
  success: (response: IData) => action(FETCH_ROUND[SUCCESS], { response }),
  failure: () => action(FETCH_ROUND[FAILURE]),
  reset: () => action(FETCH_ROUND[RESET], {}),
}

export const fetchPositions = {
  request: () => action(FETCH_POSITIONS[REQUEST]),
  success: (response: IData) => action(FETCH_POSITIONS[SUCCESS], { response }),
  failure: () => action(FETCH_POSITIONS[FAILURE]),
  reset: () => action(FETCH_POSITIONS[RESET], {}),
}

export const fetchTags = {
  request: () => action(actions.FETCH_TAGS[REQUEST]),
  success: (response: IData) => action(FETCH_TAGS[SUCCESS], { response }),
  failure: () => action(FETCH_TAGS[FAILURE]),
  reset: () => action(FETCH_TAGS[RESET], {}),
}

export const fetchRecruiters = {
  request: (data: {}) => action(actions.FETCH_RECRUITERS[REQUEST], data),
  success: (response: IData) => action(FETCH_RECRUITERS[SUCCESS], { response }),
  failure: () => action(FETCH_RECRUITERS[FAILURE]),
  reset: () => action(FETCH_RECRUITERS[RESET], {}),
}

export const getTemplates = {
  request: () => action(FETCH_TEMPLATE_DETAILS[REQUEST], {}),
  success: (response: IData) => action(FETCH_TEMPLATE_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_TEMPLATE_DETAILS[FAILURE]),
  reset: () => action(FETCH_TEMPLATE_DETAILS[RESET], {}),
}
export const fetchBatches = {
  request: () => action(FETCH_BATCHES[REQUEST]),
  success: (response: IData) => action(FETCH_BATCHES[SUCCESS], { response }),
  failure: () => action(FETCH_BATCHES[FAILURE]),
  reset: () => action(FETCH_BATCHES[RESET], {}),
}

export const fetchAddQualification = {
  request: () => action(actions.FETCH_ADDQUALIFICATION[REQUEST]),
  success: (response: IData) => action(FETCH_ADDQUALIFICATION[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ADDQUALIFICATION[FAILURE]),
  reset: () => action(actions.FETCH_ADDQUALIFICATION[RESET], {}),
}

export const deleteRecruitmentTemplate = {
  request: (data: { id: number }) => action(actions.DELETE_RECRUITMENT_TEMPLATE[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_RECRUITMENT_TEMPLATE[SUCCESS], { response }),
  failure: () => action(actions.DELETE_RECRUITMENT_TEMPLATE[FAILURE]),
  reset: () => action(actions.DELETE_RECRUITMENT_TEMPLATE[RESET], {}),
}

export const deleteCollege = {
  request: (data: { id: number }) => action(actions.DELETE_COLLEGE[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_COLLEGE[SUCCESS], { response }),
  failure: () => action(actions.DELETE_COLLEGE[FAILURE]),
  reset: () => action(actions.DELETE_COLLEGE[RESET], {}),
}

export const getColleges = {
  request: (data: CollegesPayload) => action(actions.FETCH_COLLEGE_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_COLLEGE_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.FETCH_COLLEGE_DETAILS[FAILURE]),
  reset: () => action(actions.FETCH_COLLEGE_DETAILS[RESET], {}),
}

export const fetchExpectedJoiners = {
  request: (data: {}) => action(actions.FETCH_EXPECTED_JOINERS[REQUEST], data),
  success: (response: IData) => action(FETCH_EXPECTED_JOINERS[SUCCESS], { response }),
  failure: () => action(FETCH_EXPECTED_JOINERS[FAILURE]),
  reset: () => action(FETCH_EXPECTED_JOINERS[RESET], {}),
}

export const fetchRoundsByType = {
  request: (data: {}) => action(actions.FETCH_ROUNDS_BY_TYPE[REQUEST], data),
  success: (response: IData) => action(FETCH_ROUNDS_BY_TYPE[SUCCESS], { response }),
  failure: () => action(FETCH_ROUNDS_BY_TYPE[FAILURE]),
  reset: () => action(FETCH_ROUNDS_BY_TYPE[RESET], {}),
}

export const fetchFeedback = {
  request: () => action(actions.FETCH_FEEDBACK[REQUEST]),
  success: (response: IData) => action(FETCH_FEEDBACK[SUCCESS], { response }),
  failure: () => action(FETCH_FEEDBACK[FAILURE]),
  reset: () => action(FETCH_FEEDBACK[RESET], {}),
}

export const fetchCandidatePosition = {
  request: () => action(actions.FETCH_CANDIDATEPOSITION[REQUEST]),
  success: (response: IData) => action(FETCH_CANDIDATEPOSITION[SUCCESS], { response }),
  failure: () => action(FETCH_CANDIDATEPOSITION[FAILURE]),
  reset: () => action(FETCH_CANDIDATEPOSITION[RESET], {}),
}

export const fetchTpoDetails = {
  request: (data: {}) => action(FETCH_TPO_DETAILS[REQUEST], data),
  success: (response: IData) => action(FETCH_TPO_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_TPO_DETAILS[FAILURE]),
  reset: () => action(FETCH_TPO_DETAILS[RESET], {}),
}

export const fetchrRoundType = {
  request: () => action(FETCH_ROUND_TYPE[REQUEST]),
  success: (response: IData) => action(FETCH_ROUND_TYPE[SUCCESS], { response }),
  failure: () => action(FETCH_ROUND_TYPE[FAILURE]),
  reset: () => action(FETCH_ROUND_TYPE[RESET], {}),
}

export const fetchDateandTime = {
  request: () => action(actions.FETCH_DATEANDTIME[REQUEST]),
  success: (response: IData) => action(FETCH_DATEANDTIME[SUCCESS], { response }),
  failure: () => action(FETCH_DATEANDTIME[FAILURE]),
  reset: () => action(FETCH_DATEANDTIME[RESET], {}),
}

export const fetchAddRound = {
  request: (data: {}) => action(FETCH_ADD_ROUND[REQUEST], data),
  success: (response: IData) => action(FETCH_ADD_ROUND[SUCCESS], { response }),
  failure: () => action(FETCH_ROUND[FAILURE]),
  reset: () => action(FETCH_ROUND[RESET], {}),
}

export const fetchOrganisationDetails = {
  request: () => action(actions.FETCH_ORGANISATIONDETAILS[REQUEST]),
  success: (response: IData) => action(FETCH_ORGANISATIONDETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_ORGANISATIONDETAILS[FAILURE]),
  reset: () => action(FETCH_ORGANISATIONDETAILS[RESET], {}),
}

export const fetchUser = {
  request: (data: {}) => action(actions.FETCH_USER[REQUEST]),
  success: (response: IData) => action(FETCH_USER[SUCCESS], { response }),
  failure: () => action(FETCH_USER[FAILURE]),
  reset: () => action(FETCH_USER[RESET], {}),
}

export const fetchTagCandidates = {
  request: () => action(FETCH_TAG_CANDIDATES[REQUEST], {}),
  success: (response: any) => action(FETCH_TAG_CANDIDATES[SUCCESS], { response }),
  failure: () => action(FETCH_TAG_CANDIDATES[FAILURE]),
  reset: () => action(FETCH_TAG_CANDIDATES[RESET], {}),
}

export const fetchBlockDomainsData = {
  request: () => action(FETCH_BLOCK_DOMAIN_DATA[REQUEST], {}),
  success: (response: any) => action(FETCH_BLOCK_DOMAIN_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_BLOCK_DOMAIN_DATA[FAILURE]),
  reset: () => action(FETCH_BLOCK_DOMAIN_DATA[RESET], {}),
}
export const getCandidatebyID = {
  request: (data: { id: string }) => action(actions.FETCH_CANDIDATEBYID[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_CANDIDATEBYID[SUCCESS], { response }),
  failure: () => action(actions.FETCH_CANDIDATEBYID[FAILURE]),
  reset: () => action(actions.FETCH_CANDIDATEBYID[RESET], {}),
}

export const addManageQualification = {
  request: (data: {}) => action(actions.ADD_MANAGEQUALIFICATION[REQUEST], data),
  success: (response: IData) => action(ADD_MANAGEQUALIFICATION[SUCCESS], { response }),
  failure: () => action(ADD_MANAGEQUALIFICATION[FAILURE]),
  reset: () => action(ADD_MANAGEQUALIFICATION[RESET], {}),
}

export const addManageBatches = {
  request: (data: {}) => action(actions.ADD_MANAGEBATCHES[REQUEST], data),
  success: (response: IData) => action(ADD_MANAGEBATCHES[SUCCESS], { response }),
  failure: () => action(ADD_MANAGEBATCHES[FAILURE]),
  reset: () => action(ADD_MANAGEBATCHES[RESET], {}),
}

export const deleteQualification = {
  request: (data: { id: number }) => action(actions.DELETE_QUALIFICATION[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_QUALIFICATION[SUCCESS], { response }),
  failure: () => action(actions.DELETE_QUALIFICATION[FAILURE]),
  reset: () => action(actions.DELETE_QUALIFICATION[RESET], {}),
}

export const deleteBatch = {
  request: (data: { id: number }) => action(actions.DELETE_BATCH[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_BATCH[SUCCESS], { response }),
  failure: () => action(actions.DELETE_BATCH[FAILURE]),
  reset: () => action(actions.DELETE_BATCH[RESET], {}),
}

export const fetchExperiences = {
  request: () => action(actions.FETCH_EXPERIENCES[REQUEST]),
  success: (response: IData) => action(FETCH_EXPERIENCES[SUCCESS], { response }),
  failure: () => action(FETCH_EXPERIENCES[FAILURE]),
  reset: () => action(FETCH_EXPERIENCES[RESET], {}),
}

export const getEmailTemplates = {
  request: () => action(FETCH_EMAIL_TEMPLATES[REQUEST], {}),
  success: (response: any) => action(FETCH_EMAIL_TEMPLATES[SUCCESS], { response }),
  failure: () => action(FETCH_EMAIL_TEMPLATES[FAILURE]),
  reset: () => action(FETCH_EMAIL_TEMPLATES[RESET], {}),
}
export const fetchStaticData = {
  request: () => action(FETCH_STATIC_DATA[REQUEST], {}),
  success: (response: any) => action(FETCH_STATIC_DATA[SUCCESS], { response }),
  failure: () => action(FETCH_STATIC_DATA[FAILURE]),
  reset: () => action(FETCH_STATIC_DATA[RESET], {}),
}

export const fetchInterviewer = {
  request: (data: {}) => action(FETCH_INTERVIEWER[REQUEST], {}),
  success: (response: IData) => action(FETCH_INTERVIEWER[SUCCESS], { response }),
  failure: () => action(FETCH_INTERVIEWER[FAILURE]),
  reset: () => action(FETCH_INTERVIEWER[RESET], {}),
}

export const createStaticData = {
  request: (data: {}) => action(CREATE_STATIC_DATA[REQUEST], {}),
  success: (response: IData) => action(CREATE_STATIC_DATA[SUCCESS], { response }),
  failure: () => action(CREATE_STATIC_DATA[FAILURE]),
  reset: () => action(CREATE_STATIC_DATA[RESET], {}),
}
export const fetchDateTimeByRound = {
  request: (data: { data: dataPayloadType }) =>
    action(actions.FETCH_DATE_TIME_BY_ROUND[REQUEST], data),
  success: (response: any) => action(FETCH_DATE_TIME_BY_ROUND[SUCCESS], { response }),
  failure: () => action(FETCH_DATE_TIME_BY_ROUND[FAILURE]),
  reset: () => action(FETCH_DATE_TIME_BY_ROUND[RESET], {}),
}
export const FetchTemplateByRound = {
  request: (data: { data: dataPayloadType }) => action(FETCH_TEMPLATE_BY_ROUND[REQUEST], data),
  success: (response: any) => action(FETCH_TEMPLATE_BY_ROUND[SUCCESS], { response }),
  failure: () => action(FETCH_TEMPLATE_BY_ROUND[FAILURE]),
  reset: () => action(FETCH_TEMPLATE_BY_ROUND[RESET], {}),
}
export const FetchCandidateByFilters = {
  request: (data: {}) => action(FETCH_CANDIDATE_BY_FILTERS[REQUEST], data),
  success: (response: any) => action(FETCH_CANDIDATE_BY_FILTERS[SUCCESS], { response }),
  failure: () => action(FETCH_CANDIDATE_BY_FILTERS[FAILURE]),
  reset: () => action(FETCH_CANDIDATE_BY_FILTERS[RESET], {}),
}
export const deleteCandidateById = {
  request: (data: { data: dataPayloadType }) => action(DELETE_CANDIDATE_BY_ID[REQUEST], data),
  success: (response: any) => action(DELETE_CANDIDATE_BY_ID[SUCCESS], { response }),
  failure: () => action(DELETE_CANDIDATE_BY_ID[FAILURE]),
  reset: () => action(DELETE_CANDIDATE_BY_ID[RESET], {}),
}
export const sendmailCandidateByIds = {
  request: (data: { data: SendMailCandidateParams }) =>
    action(SENDMAIL_CANDIDATE_BY_IDS[REQUEST], data),
  success: (response: any) => action(SENDMAIL_CANDIDATE_BY_IDS[SUCCESS], { response }),
  failure: () => action(SENDMAIL_CANDIDATE_BY_IDS[FAILURE]),
  reset: () => action(SENDMAIL_CANDIDATE_BY_IDS[RESET], {}),
}
export const fetchViewAttachmentsCandidate = {
  request: (data: { data: dataPayloadType }) => action(VIEW_ATTACHMENTS_CANDIDATE[REQUEST], data),
  success: (response: any) => action(VIEW_ATTACHMENTS_CANDIDATE[SUCCESS], { response }),
  failure: () => action(VIEW_ATTACHMENTS_CANDIDATE[FAILURE]),
  reset: () => action(VIEW_ATTACHMENTS_CANDIDATE[RESET], {}),
}
export const fetchVideoUrl = {
  request: (data: { data: dataPayloadType }) => action(FETCH_VIDEO_URL[REQUEST], data),
  success: (response: any) => action(FETCH_VIDEO_URL[SUCCESS], { response }),
  failure: () => action(FETCH_VIDEO_URL[FAILURE]),
  reset: () => action(FETCH_VIDEO_URL[RESET], {}),
}
export const fetchInterviewerPannel = {
  request: () => action(FETCH_INTERVIEWER_PANNEL[REQUEST]),
  success: (response: IData) => action(FETCH_INTERVIEWER_PANNEL[SUCCESS], { response }),
  failure: () => action(FETCH_INTERVIEWER_PANNEL[FAILURE]),
  reset: () => action(FETCH_INTERVIEWER_PANNEL[RESET], {}),
}

export const fetchBlockedSubjects = {
  request: () => action(actions.FETCH_BLOCKEDSUBJECTS[REQUEST]),
  success: (response: IData) => action(FETCH_BLOCKEDSUBJECTS[SUCCESS], { response }),
  failure: () => action(FETCH_BLOCKEDSUBJECTS[FAILURE]),
  reset: () => action(FETCH_BLOCKEDSUBJECTS[RESET], {}),
}

export const fetchCandidateRounds = {
  request: () => action(actions.FETCH_ROUNDCANDIDATES[REQUEST]),
  success: (response: IData) => action(FETCH_ROUNDCANDIDATES[SUCCESS], { response }),
  failure: () => action(FETCH_ROUNDCANDIDATES[FAILURE]),
  reset: () => action(FETCH_ROUNDCANDIDATES[RESET], {}),
}
export const fetchCandidateTags = {
  request: () => action(actions.FETCH_TAGCANDIDATES[REQUEST]),
  success: (response: IData) => action(FETCH_TAGCANDIDATES[SUCCESS], { response }),
  failure: () => action(FETCH_TAGCANDIDATES[FAILURE]),
  reset: () => action(FETCH_TAGCANDIDATES[RESET], {}),
}
export const addMultipleCandidates = {
  request: (payload: AddMultipleCandidatesPayload) =>
    action(actions.ADD_MULTIPLE_CANDIDATES[REQUEST], payload),
  success: (response: IData) => action(actions.ADD_MULTIPLE_CANDIDATES[SUCCESS], { response }),
  failure: (error: string) => action(ADD_MULTIPLE_CANDIDATES[FAILURE], { error }),
  reset: () => action(ADD_MULTIPLE_CANDIDATES[RESET], {}),
}
export const addRejectedSubjects = {
  request: (data: {}) => action(actions.ADD_REJECTED_SUBJECT[REQUEST], data),
  success: (response: IData) => action(ADD_REJECTED_SUBJECT[SUCCESS], { response }),
  failure: () => action(ADD_REJECTED_SUBJECT[FAILURE]),
  reset: () => action(ADD_REJECTED_SUBJECT[RESET], {}),
}
export const addJobExperiences = {
  request: (data: {}) => action(actions.ADD_JOB_EXPERIENCE[REQUEST], data),
  success: (response: IData) => action(ADD_JOB_EXPERIENCE[SUCCESS], { response }),
  failure: () => action(ADD_JOB_EXPERIENCE[FAILURE]),
  reset: () => action(ADD_JOB_EXPERIENCE[RESET], {}),
}
export const getQualification = {
  request: () => action(FETCH_APPLICANTS_QUALIFICATION[REQUEST], {}),
  success: (response: any) => action(FETCH_APPLICANTS_QUALIFICATION[SUCCESS], { response }),
  failure: () => action(FETCH_APPLICANTS_QUALIFICATION[FAILURE]),
  reset: () => action(FETCH_APPLICANTS_QUALIFICATION[RESET], {}),
}

export const getUnapprovedCandidate = {
  request: (data: {}) => action(FETCH_UNAPPROVED_CANDIDATE[REQUEST], data),
  success: (response: IData) => action(FETCH_UNAPPROVED_CANDIDATE[SUCCESS], { response }),
  failure: () => action(FETCH_UNAPPROVED_CANDIDATE[FAILURE]),
  reset: () => action(FETCH_UNAPPROVED_CANDIDATE[RESET], {}),
}

export const getSpaming = {
  request: (data: {}) => action(FETCH_SPAMING[REQUEST], data),
  success: (response: IData) => action(FETCH_SPAMING[SUCCESS], { response }),
  failure: () => action(FETCH_SPAMING[FAILURE]),
  reset: () => action(FETCH_SPAMING[RESET], {}),
}

export const deleteUnapprovedCandidate = {
  request: (data: {}) => action(DELETE_UNAPPROVED_CANDIDATE[REQUEST], data),
  success: (response: IData) => action(DELETE_UNAPPROVED_CANDIDATE[SUCCESS], { response }),
  failure: () => action(DELETE_UNAPPROVED_CANDIDATE[FAILURE]),
  reset: () => action(DELETE_UNAPPROVED_CANDIDATE[RESET], {}),
}

export const fetchDeleteExpectedJoiners = {
  request: (data: { id: number }) => action(actions.DELETE_EXPECTED_JOINERS[REQUEST], data),
  success: (response: any) => action(actions.DELETE_EXPECTED_JOINERS[SUCCESS], { response }),
  failure: () => action(actions.DELETE_EXPECTED_JOINERS[FAILURE]),
  reset: () => action(actions.DELETE_EXPECTED_JOINERS[RESET], {}),
}

export const addDriveDetails = {
  request: (data: CollegeDriveData) => action(actions.ADD_DRIVE[REQUEST], data),
  success: (response: IData) => action(actions.ADD_DRIVE[SUCCESS], { response }),
  failure: () => action(actions.ADD_DRIVE[FAILURE]),
  reset: () => action(actions.ADD_DRIVE[RESET], {}),
}
export const addExpectedJoiners = {
  request: (data: {}) => action(actions.ADD_EXPECTED_JOINERS[REQUEST], data),
  success: (response: IData) => action(actions.ADD_EXPECTED_JOINERS[SUCCESS], { response }),
  failure: () => action(actions.ADD_EXPECTED_JOINERS[FAILURE]),
  reset: () => action(actions.ADD_EXPECTED_JOINERS[RESET], {}),
}

export const editExpectedJoiners = {
  request: (data: {}) => action(actions.EDIT_EXPECTED_JOINERS[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_EXPECTED_JOINERS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_EXPECTED_JOINERS[FAILURE]),
  reset: () => action(actions.EDIT_EXPECTED_JOINERS[RESET], {}),
}

export const addBlockedBody = {
  request: (data: {}) => action(actions.ADD_BLOCKED_BODY[REQUEST], data),
  success: (response: IData) => action(actions.ADD_BLOCKED_BODY[SUCCESS], { response }),
  failure: () => action(actions.ADD_BLOCKED_BODY[FAILURE]),
  reset: () => action(actions.ADD_BLOCKED_BODY[RESET], {}),
}

// export const deleteBlockedBody = {
//   request: (data: {id:number}) => action(actions.DELETE_BLOCKED_BODY[REQUEST], data),
//   success: (response: any) => action(actions.DELETE_BLOCKED_BODY[SUCCESS], { response }),
//   failure: () => action(actions.DELETE_BLOCKED_BODY[FAILURE]),
//   reset: () => action(actions.DELETE_BLOCKED_BODY[RESET], {}),
// }

export const getBlockedBody = {
  request: () => action(actions.GET_BLOCKED_BODY[REQUEST]),
  success: (response: IData) => action(actions.GET_BLOCKED_BODY[SUCCESS], { response }),
  failure: () => action(actions.GET_BLOCKED_BODY[FAILURE]),
  reset: () => action(actions.GET_BLOCKED_BODY[RESET], {}),
}

export const addRejectedBody = {
  request: (data: {}) => action(actions.ADD_REJECTED_BODY[REQUEST], data),
  success: (response: IData) => action(actions.ADD_REJECTED_BODY[SUCCESS], { response }),
  failure: () => action(actions.ADD_REJECTED_BODY[FAILURE]),
  reset: () => action(actions.ADD_REJECTED_BODY[RESET], {}),
}

export const editRejectedBody = {
  request: (payload: { id: number; body: string }) =>
    action(actions.EDIT_REJECTED_BODY[REQUEST], payload),
  success: (response: IData) => action(EDIT_REJECTED_BODY[SUCCESS], { response }),
  failure: () => action(EDIT_REJECTED_BODY[FAILURE]),
  reset: () => action(EDIT_REJECTED_BODY[RESET], {}),
}

export const deleteRejectedBody = {
  request: (data: { id: number }) => action(actions.DELETE_REJECTED_BODY[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_REJECTED_BODY[SUCCESS], { response }),
  failure: () => action(DELETE_REJECTED_BODY[FAILURE]),
  reset: () => action(DELETE_REJECTED_BODY[RESET], {}),
}

export const EditOrgDetails = {
  request: (data: OrgData) => action(actions.EDIT_ORG[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_ORG[SUCCESS], { response }),
  failure: () => action(actions.EDIT_ORG[FAILURE]),
  reset: () => action(actions.EDIT_ORG[RESET], {}),
}

export const interviewerWork = {
  request: (data: InterviewerWork) => action(actions.FETCH_INTERVIEWER_WORK[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_INTERVIEWER_WORK[SUCCESS], { response }),
  failure: () => action(actions.FETCH_INTERVIEWER_WORK[FAILURE]),
  reset: () => action(actions.FETCH_INTERVIEWER_WORK[RESET], {}),
}
export const getNewTag = {
  request: (data: {}) => action(FETCH_NEW_TAG[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_NEW_TAG[SUCCESS], { response }),
  failure: () => action(FETCH_NEW_TAG[FAILURE]),
  reset: () => action(FETCH_NEW_TAG[RESET], {}),
}

export const editTag = {
  request: (data: {}) => action(FETCH_EDIT_TAG[REQUEST], data),
  success: (response: IData) => action(FETCH_EDIT_TAG[SUCCESS], { response }),
  failure: () => action(FETCH_EDIT_TAG[FAILURE]),
  reset: () => action(FETCH_EDIT_TAG[RESET], {}),
}

export const getDeleteTag = {
  request: (data: {}) => action(FETCH_DELETE_TAG[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_DELETE_TAG[SUCCESS], { response }),
  failure: () => action(FETCH_DELETE_TAG[FAILURE]),
  reset: () => action(FETCH_DELETE_TAG[RESET], {}),
}

export const candidateCount = {
  request: (data: CandidateCount) => action(actions.CANDIDATE_COUNT_BY_ROUND[REQUEST], data),
  success: (response: IData) => action(actions.CANDIDATE_COUNT_BY_ROUND[SUCCESS], { response }),
  failure: () => action(actions.CANDIDATE_COUNT_BY_ROUND[FAILURE]),
  reset: () => action(actions.CANDIDATE_COUNT_BY_ROUND[RESET], {}),
}

export const addBlockDomains = {
  request: (data: {}) => action(actions.ADD_BLOCK_DOMAIN_DATA[REQUEST], data),
  success: (response: IData) => action(ADD_BLOCK_DOMAIN_DATA[SUCCESS], { response }),
  failure: () => action(ADD_BLOCK_DOMAIN_DATA[FAILURE]),
  reset: () => action(ADD_BLOCK_DOMAIN_DATA[RESET], {}),
}

export const deleteBlockDomains = {
  request: (data: { id: number }) => action(actions.DELETE_BLOCK_DOMAIN_DATA[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_BLOCK_DOMAIN_DATA[SUCCESS], { response }),
  failure: () => action(DELETE_BLOCK_DOMAIN_DATA[FAILURE]),
  reset: () => action(DELETE_BLOCK_DOMAIN_DATA[RESET], {}),
}

export const editBlockDomains = {
  request: (payload: {}) => action(actions.EDIT_BLOCK_DOMAIN_DATA[REQUEST], payload),
  success: (response: IData) => action(EDIT_BLOCK_DOMAIN_DATA[SUCCESS], { response }),
  failure: () => action(EDIT_BLOCK_DOMAIN_DATA[FAILURE]),
  reset: () => action(EDIT_BLOCK_DOMAIN_DATA[RESET], {}),
}

export const addFeedbackCandidate = {
  request: (data: {}) => action(actions.ADD_CANDIDATE_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(ADD_CANDIDATE_FEEDBACK[SUCCESS], { response }),
  failure: () => action(ADD_CANDIDATE_FEEDBACK[FAILURE]),
  reset: () => action(ADD_CANDIDATE_FEEDBACK[RESET], {}),
}

export const deleteAttachment = {
  request: (data: {}) => action(actions.DELETE_ATTACHMENT[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_ATTACHMENT[SUCCESS], { response }),
  failure: () => action(DELETE_ATTACHMENT[FAILURE]),
  reset: () => action(DELETE_ATTACHMENT[RESET], {}),
}

export const deleteCandidateFeedback = {
  request: (data: {}) => action(actions.DELETE_CANDIDATE_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_CANDIDATE_FEEDBACK[SUCCESS], { response }),
  failure: () => action(DELETE_CANDIDATE_FEEDBACK[FAILURE]),
  reset: () => action(DELETE_CANDIDATE_FEEDBACK[RESET], {}),
}

export const editCandidate = {
  request: (payload: {}) => action(actions.EDIT_CANDIDATE[REQUEST], payload),
  success: (response: IData) => action(EDIT_CANDIDATE[SUCCESS], { response }),
  failure: () => action(EDIT_CANDIDATE[FAILURE]),
  reset: () => action(EDIT_CANDIDATE[RESET], {}),
}

export const joinedCandidates = {
  request: (data: JoinedCandidates) => action(actions.FETCH_JOINED_CANDIDATES[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_JOINED_CANDIDATES[SUCCESS], { response }),
  failure: () => action(actions.FETCH_JOINED_CANDIDATES[FAILURE]),
  reset: () => action(actions.FETCH_JOINED_CANDIDATES[RESET], {}),
}
export const editBatch = {
  request: (payload: { id: number; batch: string }) => action(actions.EDIT_BATCH[REQUEST], payload),
  success: (response: IData) => action(EDIT_BATCH[SUCCESS], { response }),
  failure: () => action(EDIT_BATCH[FAILURE]),
  reset: () => action(EDIT_BATCH[RESET], {}),
}
export const editQualification = {
  request: (payload: { id: number; qualification: string }) =>
    action(actions.EDIT_QUALIFICATION[REQUEST], payload),
  success: (response: IData) => action(EDIT_QUALIFICATION[SUCCESS], { response }),
  failure: () => action(EDIT_QUALIFICATION[FAILURE]),
  reset: () => action(EDIT_QUALIFICATION[RESET], {}),
}
export const fetchRepresentativeType = {
  request: () => action(actions.FETCH_REPRESENTATIVE_TYPE[REQUEST]),
  success: (response: IData) => action(actions.FETCH_REPRESENTATIVE_TYPE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_REPRESENTATIVE_TYPE[FAILURE]),
  reset: () => action(actions.FETCH_REPRESENTATIVE_TYPE[RESET], {}),
}

export const fetchAddOrganisationDetails = {
  request: (data: {}) => action(actions.ADD_ORG_DETAILS[REQUEST], data),
  success: (response: IData) => action(ADD_ORG_DETAILS[SUCCESS], { response }),
  failure: () => action(ADD_ORG_DETAILS[FAILURE]),
  reset: () => action(ADD_ORG_DETAILS[RESET], {}),
}

export const addEmailTemplateData = {
  request: (data: {}) => action(actions.ADD_EMAIL_TEMPLATE_DATA[REQUEST], data),
  success: (response: string) => action(actions.ADD_EMAIL_TEMPLATE_DATA[SUCCESS], { response }),
  failure: () => action(actions.ADD_EMAIL_TEMPLATE_DATA[FAILURE]),
  reset: () => action(actions.ADD_EMAIL_TEMPLATE_DATA[RESET], {}),
}

export const fetchRoundsForTemplate = {
  request: () => action(actions.FETCH_ROUNDS_FOR_TEMPLATE[REQUEST], {}),
  success: (response: IData) => action(actions.FETCH_ROUNDS_FOR_TEMPLATE[SUCCESS], { response }),
  failure: () => action(actions.FETCH_ROUNDS_FOR_TEMPLATE[FAILURE]),
  reset: () => action(actions.FETCH_ROUNDS_FOR_TEMPLATE[RESET], {}),
}

export const editTemplateDetails = {
  request: (data: TemplatePayload) => action(actions.EDIT_TEMPLATE[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_TEMPLATE[SUCCESS], { response }),
  failure: () => action(actions.EDIT_TEMPLATE[FAILURE]),
  reset: () => action(actions.EDIT_TEMPLATE[RESET], {}),
}

export const fetchAddUserDetails = {
  request: (data: {}) => action(actions.ADD_USER_DETAILS[REQUEST], data),
  success: (response: IData) => action(ADD_USER_DETAILS[SUCCESS], { response }),
  failure: () => action(ADD_USER_DETAILS[FAILURE]),
  reset: () => action(ADD_USER_DETAILS[RESET], {}),
}

export const fetchAddDateTimeDetails = {
  request: (data: {}) => action(actions.ADD_DATE_TIME_DETAILS[REQUEST], data),
  success: (response: IData) => action(ADD_DATE_TIME_DETAILS[SUCCESS], { response }),
  failure: () => action(ADD_DATE_TIME_DETAILS[FAILURE]),
  reset: () => action(ADD_DATE_TIME_DETAILS[RESET], {}),
}

export const fetchDeleteOrgDetails = {
  request: (data: { id: number }) => action(actions.DELETE_ORG_DETAILS[REQUEST], data),
  success: (response: IData) => action(DELETE_ORG_DETAILS[SUCCESS], { response }),
  failure: () => action(DELETE_ORG_DETAILS[FAILURE]),
  reset: () => action(DELETE_ORG_DETAILS[RESET], {}),
}

export const fetchDeleteUserDetails = {
  request: (data: { id: number }) => action(actions.DELETE_USER_DETAILS[REQUEST], data),
  success: (response: IData) => action(DELETE_USER_DETAILS[SUCCESS], { response }),
  failure: () => action(DELETE_USER_DETAILS[FAILURE]),
  reset: () => action(DELETE_USER_DETAILS[RESET], {}),
}

export const fetchDeleteDateTimeDetails = {
  request: (data: { id: number }) => action(actions.DELETE_DATE_TIME_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_DATE_TIME_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.DELETE_DATE_TIME_DETAILS[FAILURE]),
  reset: () => action(actions.DELETE_DATE_TIME_DETAILS[RESET], {}),
}

export const fetchEditOrgDetails = {
  request: (data: {}) => action(actions.EDIT_ORG_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_ORG_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_ORG_DETAILS[FAILURE]),
  reset: () => action(actions.EDIT_ORG_DETAILS[RESET], {}),
}
export const addCandidate = {
  request: (data: {}) => action(actions.ADD_CANDIDATE[REQUEST], data),
  success: (response: any) => action(ADD_CANDIDATE[SUCCESS], response),
  failure: () => action(ADD_CANDIDATE[FAILURE]),
  reset: () => action(ADD_CANDIDATE[RESET], {}),
}

export const fetchEditUserDetails = {
  request: (data: {}) => action(actions.EDIT_USER_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_USER_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_USER_DETAILS[FAILURE]),
  reset: () => action(actions.EDIT_USER_DETAILS[RESET], {}),
}

export const editCandidateInline = {
  request: (data: { data: dataPayloadType }) =>
    action(actions.EDIT_CANDIDATE_INLINE[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_CANDIDATE_INLINE[SUCCESS], { response }),
  failure: () => action(actions.EDIT_CANDIDATE_INLINE[FAILURE]),
  reset: () => action(actions.EDIT_CANDIDATE_INLINE[RESET], {}),
}

export const addInstituteDetails = {
  request: (data: { data: string }) => action(actions.FETCH_INSTITUTE_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.FETCH_INSTITUTE_DETAILS[SUCCESS], { response }),
  failure: () => action(FETCH_INSTITUTE_DETAILS[FAILURE]),
  reset: () => action(FETCH_INSTITUTE_DETAILS[RESET], {}),
}

export const addJobPosition = {
  request: (data: { data: {} }) => action(actions.ADD_JOB_POSITION[REQUEST], data),
  success: (response: IData) => action(ADD_JOB_POSITION[SUCCESS], { response }),
  failure: () => action(actions.ADD_JOB_POSITION[FAILURE]),
  reset: () => action(actions.ADD_JOB_POSITION[RESET], {}),
}

export const addInstDetails = {
  request: (data: { data: {} }) => action(actions.ADD_INST_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.ADD_INST_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.ADD_INST_DETAILS[FAILURE]),
  reset: () => action(actions.ADD_INST_DETAILS[RESET], {}),
}

export const editInstDetails = {
  request: (data: { data: {} }) => action(actions.EDIT_INST_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_INST_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_INST_DETAILS[FAILURE]),
  reset: () => action(actions.EDIT_INST_DETAILS[RESET], {}),
}

export const subInstDetails = {
  request: (data: { data: {} }) => action(actions.SUB_INST_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.SUB_INST_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.SUB_INST_DETAILS[FAILURE]),
  reset: () => action(actions.SUB_INST_DETAILS[RESET], {}),
}
export const delInstDetails = {
  request: (data: { data: {} }) => action(actions.DEL_INST_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.DEL_INST_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.DEL_INST_DETAILS[FAILURE]),
  reset: () => action(actions.DEL_INST_DETAILS[RESET], {}),
}

export const roundfeedbackDetails = {
  request: (data: { data: {} }) => action(actions.ROUND_FEEDBACK_DROPDOWN[REQUEST], data),
  success: (response: IData) => action(actions.ROUND_FEEDBACK_DROPDOWN[SUCCESS], { response }),
  failure: () => action(actions.ROUND_FEEDBACK_DROPDOWN[FAILURE]),
  reset: () => action(actions.ROUND_FEEDBACK_DROPDOWN[RESET], {}),
}

export const fetchJobExperience = {
  request: () => action(FETCH_JOB_EXPERIENCE[REQUEST], {}),
  success: (response: any) => action(FETCH_JOB_EXPERIENCE[SUCCESS], { response }),
  failure: () => action(FETCH_JOB_EXPERIENCE[FAILURE]),
  reset: () => action(FETCH_JOB_EXPERIENCE[RESET], {}),
}
export const addStaticData = {
  request: (data: {}) => action(actions.ADD_STATIC_DATA[REQUEST], data),
  success: (response: string) => action(actions.ADD_STATIC_DATA[SUCCESS], { response }),
  failure: () => action(actions.ADD_STATIC_DATA[FAILURE]),
  reset: () => action(actions.ADD_STATIC_DATA[RESET], {}),
}
export const addInterviewerData = {
  request: (data: {}) => action(actions.ADD_INTERVIEWER_DATA[REQUEST], data),
  success: (response: string) => action(actions.ADD_INTERVIEWER_DATA[SUCCESS], { response }),
  failure: () => action(actions.ADD_INTERVIEWER_DATA[FAILURE]),
  reset: () => action(actions.ADD_INTERVIEWER_DATA[RESET], {}),
}
export const editStaticData = {
  request: (data: {}) => action(actions.EDIT_STATIC_DATA[REQUEST], data),
  success: (response: IData) => action(EDIT_STATIC_DATA[SUCCESS], { response }),
  failure: () => action(EDIT_STATIC_DATA[FAILURE]),
  reset: () => action(EDIT_STATIC_DATA[RESET], {}),
}
export const editInterviewerData = {
  request: (data: {}) => action(actions.EDIT_INTERVIEWER_DATA[REQUEST], data),
  success: (response: IData) => action(EDIT_INTERVIEWER_DATA[SUCCESS], { response }),
  failure: () => action(actions.EDIT_INTERVIEWER_DATA[FAILURE]),
  reset: () => action(EDIT_INTERVIEWER_DATA[RESET], {}),
}
export const deleteStaticData = {
  request: (data: { id: number }) => action(actions.DELETE_STATIC_DATA[REQUEST], data),
  success: (response: any) => action(actions.DELETE_STATIC_DATA[SUCCESS], { response }),
  failure: () => action(actions.DELETE_STATIC_DATA[FAILURE]),
  reset: () => action(actions.DELETE_STATIC_DATA[RESET], {}),
}
export const deleteInterviewerData = {
  request: (data: { id: number }) => action(actions.DELETE_INTERVIEWER_DATA[REQUEST], data),
  success: (response: any) => action(actions.DELETE_INTERVIEWER_DATA[SUCCESS], { response }),
  failure: () => action(actions.DELETE_INTERVIEWER_DATA[FAILURE]),
  reset: () => action(actions.DELETE_INTERVIEWER_DATA[RESET], {}),
}

export const DeleteRounds = {
  request: (data: {}) => action(actions.DELETE_ROUNDS[REQUEST], data),
  success: (response: IData) => action(DELETE_ROUNDS[SUCCESS], { response }),
  failure: () => action(DELETE_ROUNDS[FAILURE]),
  reset: () => action(DELETE_ROUNDS[RESET], {}),
}

export const DeleteTpo = {
  request: (data: {}) => {
    return action(actions.DELETE_TPO[REQUEST], data)
  },
  success: (response: IData) => action(DELETE_TPO[SUCCESS], { response }),
  failure: () => action(DELETE_TPO[FAILURE]),
  reset: () => action(DELETE_TPO[RESET], {}),
}
export const editpositionDetails = {
  request: (data: { data: {} }) => action(actions.EDIT_POSITION_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_POSITION_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.EDIT_POSITION_DETAILS[FAILURE]),
  reset: () => action(actions.EDIT_POSITION_DETAILS[RESET], {}),
}

export const submitpositionDetails = {
  request: (data: { data: {} }) => action(actions.SUB_POSITION_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.SUB_POSITION_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.SUB_POSITION_DETAILS[FAILURE]),
  reset: () => action(actions.SUB_POSITION_DETAILS[RESET], {}),
}

export const deletepositionDetails = {
  request: (data: { data: {} }) => action(actions.DELETE_POSITION_DETAILS[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_POSITION_DETAILS[SUCCESS], { response }),
  failure: () => action(actions.DELETE_POSITION_DETAILS[FAILURE]),
  reset: () => action(actions.DELETE_POSITION_DETAILS[RESET], {}),
}

export const AddFeedback = {
  request: (data: { data: {} }) => action(actions.ADD_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.ADD_FEEDBACK[SUCCESS], { response }),
  failure: () => action(actions.ADD_FEEDBACK[FAILURE]),
  reset: () => action(actions.ADD_FEEDBACK[RESET], {}),
}

export const DeleteFeedback = {
  request: (data: { data: {} }) => action(actions.DELETE_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_FEEDBACK[SUCCESS], { response }),
  failure: () => action(actions.DELETE_FEEDBACK[FAILURE]),
  reset: () => action(actions.DELETE_FEEDBACK[RESET], {}),
}
export const EditFeedback = {
  request: (data: { data: {} }) => action(actions.EDIT_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_FEEDBACK[SUCCESS], { response }),
  failure: () => action(actions.EDIT_FEEDBACK[FAILURE]),
  reset: () => action(actions.EDIT_FEEDBACK[RESET], {}),
}
export const SubEditFeedback = {
  request: (data: { data: {} }) => action(actions.SUB_EDIT_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.SUB_EDIT_FEEDBACK[SUCCESS], { response }),
  failure: () => action(actions.SUB_EDIT_FEEDBACK[FAILURE]),
  reset: () => action(actions.SUB_EDIT_FEEDBACK[RESET], {}),
}

export const EditRounds = {
  request: (data: {}) => {
    return action(actions.EDIT_ROUNDS[REQUEST], data)
  },
  success: (response: IData) => action(EDIT_ROUNDS[SUCCESS], { response }),
  failure: () => action(EDIT_ROUNDS[FAILURE]),
  reset: () => action(EDIT_ROUNDS[RESET], {}),
}

export const Edit_Tpo = {
  request: (data: {}) => {
    return action(actions.EDIT_TPO[REQUEST], data)
  },
  success: (response: IData) => action(EDIT_TPO[SUCCESS], { response }),
  failure: () => action(EDIT_TPO[FAILURE]),
  reset: () => action(EDIT_TPO[RESET], {}),
}

export const AddTpo = {
  request: (data: {}) => {
    return action(actions.ADD_TPO[REQUEST], data)
  },
  success: (response: IData) => action(actions.ADD_TPO[SUCCESS], { response }),
  failure: () => action(ADD_TPO[FAILURE]),
  reset: () => action(ADD_TPO[RESET], {}),
}

export const viewOrganisationDetailsByType = {
  request: (data: { data: {} }) => action(actions.ORGANISATION_DETAILS_BY_TYPE[REQUEST], data),
  success: (response: IData) => action(ORGANISATION_DETAILS_BY_TYPE[SUCCESS], { response }),
  failure: () => action(ORGANISATION_DETAILS_BY_TYPE[FAILURE]),
  reset: () => action(ORGANISATION_DETAILS_BY_TYPE[RESET], {}),
}
export const fetchEditDateTimeDetails = {
  request: (data: {}) => action(actions.EDIT_DATE_TIME[REQUEST], data),
  success: (response: IData) => action(actions.EDIT_DATE_TIME[SUCCESS], { response }),
  failure: () => action(actions.EDIT_DATE_TIME[FAILURE]),
  reset: () => action(actions.EDIT_DATE_TIME[RESET], {}),
}

export const editRejectedSubjects = {
  request: (payload: EditRejectedSubjectsPayload) =>
    action(actions.EDIT_REJECTED_SUBJECT[REQUEST], payload),
  success: (response: IData) => action(EDIT_REJECTED_SUBJECT[SUCCESS], { response }),
  failure: () => action(EDIT_REJECTED_SUBJECT[FAILURE]),
  reset: () => action(EDIT_REJECTED_SUBJECT[RESET], {}),
}
export const editJobExperiences = {
  request: (payload: EditJobExperiencesPayload) =>
    action(actions.EDIT_JOB_EXPERIENCE[REQUEST], payload),
  success: (response: IData) => action(EDIT_JOB_EXPERIENCE[SUCCESS], { response }),
  failure: () => action(EDIT_JOB_EXPERIENCE[FAILURE]),
  reset: () => action(EDIT_JOB_EXPERIENCE[RESET], {}),
}
export const deleteJobExperiences = {
  request: (data: DeleteJobExperiencesPayload) =>
    action(actions.DELETE_JOB_EXPERIENCE[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_JOB_EXPERIENCE[SUCCESS], { response }),
  failure: () => action(DELETE_JOB_EXPERIENCE[FAILURE]),
  reset: () => action(DELETE_JOB_EXPERIENCE[RESET], {}),
}
export const deleteRejectedSubjects = {
  request: (data: DeleteRejectedSubjectsPayload) =>
    action(actions.DELETE_REJECTED_SUBJECT[REQUEST], data),
  success: (response: IData) => action(actions.DELETE_REJECTED_SUBJECT[SUCCESS], { response }),
  failure: () => action(DELETE_REJECTED_SUBJECT[FAILURE]),
  reset: () => action(DELETE_REJECTED_SUBJECT[RESET], {}),
}

export const editCandidateForm = {
  request: (data: {}) => action(actions.EDIT_CANDIDATEFORM[REQUEST], data),
  success: (response: IData) => action(EDIT_CANDIDATEFORM[SUCCESS], { response }),
  failure: () => action(EDIT_CANDIDATEFORM[FAILURE]),
  reset: () => action(EDIT_CANDIDATEFORM[RESET], {}),
}

export const addResume = {
  request: (payload: any) => action(ADD_RESUME[REQUEST], payload),
  success: (response: IData) => action(ADD_RESUME[SUCCESS], { response }),
  failure: () => action(ADD_RESUME[FAILURE]),
  reset: () => action(ADD_RESUME[RESET], {}),
}

export const viewRecruitersCalls = {
  request: (data: { data: dataPayloadType }) => action(actions.VIEW_RECRUITER_CALLS[REQUEST], data),
  success: (response: IData) => action(actions.VIEW_RECRUITER_CALLS[SUCCESS], { response }),
  failure: () => action(actions.VIEW_RECRUITER_CALLS[FAILURE]),
  reset: () => action(actions.VIEW_RECRUITER_CALLS[RESET], {}),
}

export const viewCandidateFeedback = {
  request: (data: { data: {} }) => action(actions.VIEW_CANDIDATE_FEEDBACK[REQUEST], data),
  success: (response: IData) => action(actions.VIEW_CANDIDATE_FEEDBACK[SUCCESS], { response }),
  failure: () => action(actions.VIEW_CANDIDATE_FEEDBACK[FAILURE]),
  reset: () => action(actions.VIEW_CANDIDATE_FEEDBACK[RESET], {}),
}

export const viewCandidateTags = {
  request: (data: { data: { id: string | number } }) => action(actions.VIEW_CANDIDATE_TAGS[REQUEST], data),
  success: (response: IData) => action(actions.VIEW_CANDIDATE_TAGS[SUCCESS], { response }),
  failure: () => action(actions.VIEW_CANDIDATE_TAGS[FAILURE]),
  reset: () => action(actions.VIEW_CANDIDATE_TAGS[RESET], {}),
}

export const templateByRound = {
  request: (data: { id: number }) => action(actions.TEMPLATE_BY_ROUND[REQUEST], data),
  success: (response: any) => action(actions.TEMPLATE_BY_ROUND[SUCCESS], { response }),
  failure: () => action(actions.TEMPLATE_BY_ROUND[FAILURE]),
  reset: () => action(actions.TEMPLATE_BY_ROUND[RESET], {}),
}

export const dateTimeByRound = {
  request: (data: { id: number }) => action(actions.DATETIME_BY_ROUND[REQUEST], data),
  success: (response: any) => action(actions.DATETIME_BY_ROUND[SUCCESS], { response }),
  failure: () => action(actions.DATETIME_BY_ROUND[FAILURE]),
  reset: () => action(actions.DATETIME_BY_ROUND[RESET], {}),
}

export const fetchSendRemainder = {
  request: () => action(FETCH_SEND_REMAINDER[REQUEST]),
  success: (response: any) => action(FETCH_SEND_REMAINDER[SUCCESS], { response }),
  failure: () => action(FETCH_SEND_REMAINDER[FAILURE]),
  reset: () => action(FETCH_SEND_REMAINDER[RESET], {}),
}

export const fetchCoolingOffPeriod = {
  request: () => action(actions.FETCH_COOLING_OFF_PERIOD[REQUEST]),
  success: (response: any) => action(FETCH_COOLING_OFF_PERIOD[SUCCESS], { response }),
  failure: () => action(FETCH_COOLING_OFF_PERIOD[FAILURE]),
  reset: () => action(FETCH_COOLING_OFF_PERIOD[RESET], {}),
}

export const addUploadResume = {
  request: (data: { data: {} }) => action(actions.ADD_UPLOAD_RESUME[REQUEST], data),
  success: (response: IData) => action(actions.ADD_UPLOAD_RESUME[SUCCESS], { response }),
  failure: () => action(actions.ADD_UPLOAD_RESUME[FAILURE]),
  reset: () => action(actions.ADD_UPLOAD_RESUME[RESET], {}),
}

export const addUploadAssignment = {
  request: (data: { data: {} }) => action(actions.ADD_UPLOAD_ASSIGNMENT[REQUEST], data),
  success: (response: IData) => action(actions.ADD_UPLOAD_ASSIGNMENT[SUCCESS], { response }),
  failure: () => action(actions.ADD_UPLOAD_ASSIGNMENT[FAILURE]),
  reset: () => action(actions.ADD_UPLOAD_ASSIGNMENT[RESET], {}),
}

export const approveApplicants = {
  request: (data: {}) => action(APPLICANTS_APPROVE[REQUEST], data),
  success: (response: IData) => action(APPLICANTS_APPROVE[SUCCESS], { response }),
  failure: () => action(APPLICANTS_APPROVE[FAILURE]),
  reset: () => action(APPLICANTS_APPROVE[RESET], {}),
}

export const rejectApplicants = {
  request: (data: {}) => action(APPLICANTS_REJECT[REQUEST], data),
  success: (response: IData) => action(APPLICANTS_REJECT[SUCCESS], { response }),
  failure: () => action(APPLICANTS_REJECT[FAILURE]),
  reset: () => action(APPLICANTS_REJECT[RESET], {}),
}

export const deleteApplicants = {
  request: (data: IdApplicants[]) => action(DELETE_MULTIPLE_APPLICANTS[REQUEST], { data }),
  success: (response: IData) => action(DELETE_MULTIPLE_APPLICANTS[SUCCESS], { response }),
  failure: () => action(DELETE_MULTIPLE_APPLICANTS[FAILURE]),
  reset: () => action(DELETE_MULTIPLE_APPLICANTS[RESET], {}),
}

export const resumeApplicants = {
  request: (data: {}) => action(FETCH_APPLICANTS_RESUME[REQUEST], data),
  success: (response: IData) => action(FETCH_APPLICANTS_RESUME[SUCCESS], { response }),
  failure: () => action(FETCH_APPLICANTS_RESUME[FAILURE]),
  reset: () => action(FETCH_APPLICANTS_RESUME[RESET], {}),
}

export const getShorcutKeysByCandidateId = {
  request: (data: {}) => action(FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID[REQUEST], data),
  success: (response: IData) => action(FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID[SUCCESS], { response }),
  failure: () => action(FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID[FAILURE]),
  reset: () => action(FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID[RESET], {}),
}

export const toggleShortcutKeyToCandidateId = {
  request: (data: {}) => action(TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID[REQUEST], data),
  success: (response: IData) => action(TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID[SUCCESS], { response }),
  failure: () => action(TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID[FAILURE]),
  reset: () => action(TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID[RESET], {}),
}

export const viewCandidateLogs = {
  request: (data: {}) => action(VIEW_CANDIDATE_LOGS[REQUEST], data),
  success: (response: IData) => action(VIEW_CANDIDATE_LOGS[SUCCESS], { response }),
  failure: () => action(VIEW_CANDIDATE_LOGS[FAILURE]),
  reset: () => action(VIEW_CANDIDATE_LOGS[RESET], {}),
}

export const actionTypes = {
  ...actions,
}

// Template Management Action Creators
export const fetchTemplates = (data?: any) => action(FETCH_TEMPLATES.REQUEST, { data })
export const fetchTemplatesSuccess = (data: any) => action(FETCH_TEMPLATES.SUCCESS, { data })
export const fetchTemplatesFailure = (error: any) => action(FETCH_TEMPLATES.FAILURE, { error })
export const fetchTemplatesReset = () => action(FETCH_TEMPLATES.RESET)

export const createTemplate = (data: any) => action(CREATE_TEMPLATE.REQUEST, { data })
export const createTemplateSuccess = (data: any) => action(CREATE_TEMPLATE.SUCCESS, { data })
export const createTemplateFailure = (error: any) => action(CREATE_TEMPLATE.FAILURE, { error })
export const createTemplateReset = () => action(CREATE_TEMPLATE.RESET)

export const updateTemplate = (data: any) => action(UPDATE_TEMPLATE.REQUEST, { data })
export const updateTemplateSuccess = (data: any) => action(UPDATE_TEMPLATE.SUCCESS, { data })
export const updateTemplateFailure = (error: any) => action(UPDATE_TEMPLATE.FAILURE, { error })
export const updateTemplateReset = () => action(UPDATE_TEMPLATE.RESET)

export const deleteTemplate = (data: any) => action(DELETE_TEMPLATE.REQUEST, { data })
export const deleteTemplateSuccess = (data: any) => action(DELETE_TEMPLATE.SUCCESS, { data })
export const deleteTemplateFailure = (error: any) => action(DELETE_TEMPLATE.FAILURE, { error })
export const deleteTemplateReset = () => action(DELETE_TEMPLATE.RESET)

export const searchTemplates = (data: any) => action(SEARCH_TEMPLATES.REQUEST, { data })
export const searchTemplatesSuccess = (data: any) => action(SEARCH_TEMPLATES.SUCCESS, { data })
export const searchTemplatesFailure = (error: any) => action(SEARCH_TEMPLATES.FAILURE, { error })
export const searchTemplatesReset = () => action(SEARCH_TEMPLATES.RESET)

export const fetchTemplateById = (data: any) => action(FETCH_TEMPLATE_BY_ID.REQUEST, { data })
export const fetchTemplateByIdSuccess = (data: any) => action(FETCH_TEMPLATE_BY_ID.SUCCESS, { data })
export const fetchTemplateByIdFailure = (error: any) => action(FETCH_TEMPLATE_BY_ID.FAILURE, { error })
export const fetchTemplateByIdReset = () => action(FETCH_TEMPLATE_BY_ID.RESET)

// DR Section KPI Template Management Action Creators
export const assignTemplate = (data: any) => action(ASSIGN_TEMPLATE.REQUEST, { data })
export const assignTemplateSuccess = (data: any) => action(ASSIGN_TEMPLATE.SUCCESS, { data })
export const assignTemplateFailure = (error: any) => action(ASSIGN_TEMPLATE.FAILURE, { error })
export const assignTemplateReset = () => action(ASSIGN_TEMPLATE.RESET)

export const getAssignedTemplate = (data: any) => action(GET_ASSIGNED_TEMPLATE.REQUEST, { data })
export const getAssignedTemplateSuccess = (data: any) => action(GET_ASSIGNED_TEMPLATE.SUCCESS, { data })
export const getAssignedTemplateFailure = (error: any) => action(GET_ASSIGNED_TEMPLATE.FAILURE, { error })
export const getAssignedTemplateReset = () => action(GET_ASSIGNED_TEMPLATE.RESET)

export const editTemplateValues = (data: any) => action(EDIT_TEMPLATE_VALUES.REQUEST, { data })
export const editTemplateValuesSuccess = (data: any) => action(EDIT_TEMPLATE_VALUES.SUCCESS, { data })
export const editTemplateValuesFailure = (error: any) => action(EDIT_TEMPLATE_VALUES.FAILURE, { error })
export const editTemplateValuesReset = () => action(EDIT_TEMPLATE_VALUES.RESET)

export const getKPITemplates = (data?: any) => action(GET_KPI_TEMPLATES.REQUEST, { data })
export const getKPITemplatesSuccess = (data: any) => action(GET_KPI_TEMPLATES.SUCCESS, { data })
export const getKPITemplatesFailure = (error: any) => action(GET_KPI_TEMPLATES.FAILURE, { error })
export const getKPITemplatesReset = () => action(GET_KPI_TEMPLATES.RESET)

// Load Template or Options Actions (intelligent template fetching)
export const loadTemplateOrOptions = (data: any) => action(LOAD_TEMPLATE_OR_OPTIONS.REQUEST, { data })
export const loadTemplateOrOptionsSuccess = (data: any) => action(LOAD_TEMPLATE_OR_OPTIONS.SUCCESS, { data })
export const loadTemplateOrOptionsFailure = (error: any) => action(LOAD_TEMPLATE_OR_OPTIONS.FAILURE, { error })
export const loadTemplateOrOptionsReset = () => action(LOAD_TEMPLATE_OR_OPTIONS.RESET)
