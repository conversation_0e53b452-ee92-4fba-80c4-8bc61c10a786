import { ACCTypes } from './Types'
export const REQUEST = 'REQUEST'
export const SUCCESS = 'SUCCESS'
export const FAILURE = 'FAILURE'
export const RESET = 'RESET'

// This function will return Action Type object
export const createRequestTypes = (base: string) => {
  return [REQUEST, SUCCESS, FAILURE, RESET].reduce((acc: ACCTypes, type) => {
    acc[type] = `${base}_${type}`
    return acc
  }, {})
}

export const action = (type: string, payload = {}) => {
  return { type, ...payload }
}

export const GET_USER_DATA = createRequestTypes('GET_USER_DATA')

export const LOGIN = createRequestTypes('LOGIN')

export const LOGOUT = createRequestTypes('LOGOUT')

export const GET_ACCESS_TOKEN = createRequestTypes('GET_ACCESS_TOKEN')

export const LOGIN_USER = createRequestTypes('LOGIN_USER')

export const FETCH_USERS = createRequestTypes('FETCH_USERS')

export const FETCH_BACKINFO = createRequestTypes('FETCH_BACKINFO')

export const FETCH_HOLIDAYS = createRequestTypes('FETCH_HOLIDAYS')

export const FETCH_ASSET = createRequestTypes('FETCH_ASSET')

export const FETCH_LOAN_INSTALLMENT_DATA = createRequestTypes('FETCH_LOAN_INSTALLMENT_DATA')

export const FETCH_DRS_DATA = createRequestTypes('FETCH_DRS_DATA')

export const FETCH_SUB_DRS_DATA = createRequestTypes('FETCH_SUB_DRS_DATA')

export const FETCH_EMPINFO = createRequestTypes('FETCH_EMPINFO')

export const FETCH_DESIGNATION = createRequestTypes('FETCH_DESIGNATION')

export const FETCH_DESIGNATIONBAND = createRequestTypes('FETCH_DESIGNATIONBAND')

export const FETCH_EMPLOYMENTTYPE = createRequestTypes('FETCH_EMPLOYMENTTYPE')

export const FETCH_SAPERIOD = createRequestTypes('FETCH_SAPERIOD')

export const FETCH_TIMING = createRequestTypes('FETCH_TIMING')

export const FETCH_BASICINFO = createRequestTypes('FETCH_BASICINFO')

export const FETCH_SR = createRequestTypes('FETCH_SR')

export const FETCH_ASSIGNED_SR = createRequestTypes('FETCH_ASSIGNED_SR')

export const FETCH_BANKINFO = createRequestTypes('FETCH_BANKINFO')

export const FETCH_STATE = createRequestTypes('FETCH_STATE')

export const FETCH_COUNTRY = createRequestTypes('FETCH_COUNTRY')

export const FETCH_ACCOUNTTYPE = createRequestTypes('FETCH_ACCOUNTTYPE')

export const FETCH_MARITIAL_STATUS = createRequestTypes('FETCH_MARITIAL_STATUS')

export const FETCH_LEAVE_DETAILS = createRequestTypes('FETCH_LEAVE_DETAILS')

export const FETCH_QUALIFICATION = createRequestTypes('FETCH_QUALIFICATION')

export const FETCH_QUALIFICATION_SKILL = createRequestTypes('FETCH_QUALIFICATION_SKILL')

export const CREATE_SERVICE_REQUEST = createRequestTypes('CREATE_SERVICE_REQUEST')

export const FETCH_ISSUE_TYPE_LIST = createRequestTypes('FETCH_ISSUE_TYPE_LIST')

export const FETCH_USER_BY_ID = createRequestTypes('FETCH_USER_BY_ID')

export const FETCH_REF_BY_ID = createRequestTypes('FETCH_REF_BY_ID')

export const FETCH_COMPENSATION_DETAILS = createRequestTypes('FETCH_COMPENSATION_DETAILS')

export const FETCH_LOAN_DETAILS = createRequestTypes('FETCH_LOAN_DETAILS')

export const FETCH_DEPARTMENT_LIST = createRequestTypes('FETCH_DEPARTMENT_LIST')

export const SEND_LOAN_REQUEST = createRequestTypes('SEND_LOAN_REQUEST')

export const STATUS_SUMMARY = createRequestTypes('STATUS_SUMMARY')

export const USER_INFO = createRequestTypes('USER_INFO')

export const HOME_PAGE_INFO = createRequestTypes('HOME_PAGE_INFO')

export const FETCH_EMPLOYEE_DETAILS = createRequestTypes('FETCH_EMPLOYEE_DETAILS')

export const ADD_NEW_COMMENT = createRequestTypes('ADD_NEW_COMMENT')

export const GET_TIMESHEET = createRequestTypes('GET_TIMESHEET')

export const GET_LEAVE_TIMESHEET = createRequestTypes('GET_LEAVE_TIMESHEET')

export const GET_PAY_SLIP = createRequestTypes('GET_PAY_SLIP')

export const GET_LEAVE_DATA = createRequestTypes('GET_LEAVE_DATA')

export const ACCEPT_LEAVE_DATA = createRequestTypes('ACCEPT_LEAVE_DATA')

export const REJECT_LEAVE_DATA = createRequestTypes('REJECT_LEAVE_DATA')

export const FETCH_PROJECT_DATA = createRequestTypes('FETCH_PROJECT_DATA')

export const FETCH_HIGHLIGHTS = createRequestTypes('FETCH_HIGHLIGHTS')

export const FETCH_ATTENDANCE = createRequestTypes('FETCH_ATTENDANCE')

export const FETCH_ATTENDANCE_TIMESHEET = createRequestTypes('FETCH_ATTENDANCE_TIMESHEET')

export const FETCH_RCA = createRequestTypes('FETCH_RCA')

export const FETCH_IDSR = createRequestTypes('FETCH_IDSR')

export const FETCH_PLANS_FOR_THE_DAY = createRequestTypes('FETCH_PLANS_FOR_THE_DAY')

export const STATUS_TYPE = createRequestTypes('STATUS_TYPE')

export const TASK_STATUS = createRequestTypes('TASK_STATUS')

export const CREATE_RCA = createRequestTypes('CREATE_RCA')

export const CREATE_IDSR = createRequestTypes('CREATE_IDSR')

export const CREATE_PLAN_FOR_THE_DAY = createRequestTypes('CREATE_PLAN_FOR_THE_DAY')

export const FETCH_ASSIGNED_REQUEST = createRequestTypes('FETCH_ASSIGNED_REQUEST')

export const FETCH_UPDATED_SERVICE_REQUEST = createRequestTypes('FETCH_UPDATED_SERVICE_REQUEST')

export const FETCH_DOWNLOADABLE_URL = createRequestTypes('FETCH_DOWNLOADABLE_URL')

export const MANAGER_VIEW_DATA = createRequestTypes('MANAGER_VIEW_DATA')

export const FETCH_DOC_URL = createRequestTypes('FETCH_DOC_URL')

export const FETCH_PROJECTS_NAME = createRequestTypes('FETCH_PROJECTS_NAME')

export const PROJECT_GRAPH_DATA = createRequestTypes('PROJECT_GRAPH_DATA')

export const DESIGNATION_GRAPH_DATA = createRequestTypes('DESIGNATION_GRAPH_DATA')

export const DESIGNATION_LIST = createRequestTypes('DESIGNATION_LIST')

export const FETCH_PROJECT_TYPES = createRequestTypes('FETCH_PROJECT_TYPES')

export const FETCH_PROJECT_DOMAIN = createRequestTypes('FETCH_PROJECT_DOMAIN')

export const FETCH_PROJECT_SOURCE = createRequestTypes('FETCH_PROJECT_SOURCE')

export const FETCH_TECHNOLOGIES = createRequestTypes('FETCH_TECHNOLOGIES')

export const FETCH_PROJECT_LOCATION = createRequestTypes('FETCH_PROJECT_LOCATION')

export const ADD_NEW_PROJECT = createRequestTypes('ADD_NEW_PROJECT')

export const DELETE_PROJECT = createRequestTypes('DELETE_PROJECT')

export const FETCH_PROJECT_CUSTOMERS = createRequestTypes('FETCH_PROJECT_CUSTOMERS')

export const FETCH_MANDATE_TYPE = createRequestTypes('FETCH_MANDATE_TYPE')

export const RESTART_PROJECT = createRequestTypes('RESTART_PROJECT')

export const FETCH_PROJECT_DETAILS = createRequestTypes('FETCH_PROJECT_DETAILS')

export const FETCH_WORKING_EMP = createRequestTypes('FETCH_WORKING_EMP')

export const ADD_EMP_BASED_ON_ROLE = createRequestTypes('ADD_EMP_BASED_ON_ROLE')

export const UPDATE_PROJECT = createRequestTypes('UPDATE_PROJECT')

export const DELETE_EMP_BASED_ON_ROLE = createRequestTypes('DELETE_EMP_BASED_ON_ROLE')

export const EDIT_PROJECT = createRequestTypes('EDIT_PROJECT')

export const ORGANIZATION_DESIGNATION = createRequestTypes('ORGANIZATION_DESIGNATION')

export const ORGANISATION_DESIGNATION_DATA = createRequestTypes('ORGANISATION_DESIGNATION_DATA')

export const FETCH_PROJECT_REPORTS = createRequestTypes('FETCH_PROJECT_REPORTS')

export const FETCH_ALL_PROJECT_RESOURCE_REPORT = createRequestTypes(
  'FETCH_ALL_PROJECT_RESOURCE_REPORT',
)

export const PROJECT_RESOURCE_REPORT_DROPDOWN = createRequestTypes(
  'PROJECT_RESOURCE_REPORT_DROPDOWN',
)

export const FETCH_PROJECT_QA_REPORT = createRequestTypes('FETCH_PROJECT_QA_REPORT')

export const FETCH_PROJECT_CUSTOMERS_LIST = createRequestTypes('FETCH_PROJECT_CUSTOMERS_LIST')

export const CREATE_PROJECT_CUSTOMERS = createRequestTypes('CREATE_PROJECT_CUSTOMERS')

export const CREATE_MANDATE_TYPE = createRequestTypes('CREATE_MANDATE_TYPE')

export const UPDATE_MANDATE_TYPE = createRequestTypes('UPDATE_MANDATE_TYPE')

export const DELETE_MANDATE_TYPE = createRequestTypes('DELETE_MANDATE_TYPE')

export const CREATE_DOMAIN_TYPE = createRequestTypes('CREATE_DOMAIN_TYPE')

export const UPDATE_DOMAIN_TYPE = createRequestTypes('UPDATE_DOMAIN_TYPE')

export const DELETE_PROJECT_CUSTOMER = createRequestTypes('DELETE_PROJECT_CUSTOMER')

export const UPDATE_PROJECT_CUSTOMER = createRequestTypes('UPDATE_PROJECT_CUSTOMER')

export const FETCH_NON_BILLABLE_RESOURCES = createRequestTypes('FETCH_NON_BILLABLE_RESOURCES')

export const UPDATE_NON_BILLABLE_RESOURCES = createRequestTypes('UPDATE_NON_BILLABLE_RESOURCES')

export const FETCH_PROJECT_MANAGEMENT_REPORT = createRequestTypes('FETCH_PROJECT_MANAGEMENT_REPORT')

export const PEOPLE_BASED_ORG_CHART_DATA = createRequestTypes('PEOPLE_BASED_ORG_CHART_DATA')

export const DEPARTMENT_BASED_ORG_CHART_DATA = createRequestTypes('DEPARTMENT_BASED_ORG_CHART_DATA')

export const DESIGNATION_BASED_ORG_CHART_DATA = createRequestTypes(
  'DESIGNATION_BASED_ORG_CHART_DATA',
)

export const EMPLOYEE_HISTORY = createRequestTypes('EMP_HISTORY')

export const MANAGER_HISTORY = createRequestTypes('MANAGER_HISTORY')

export const COMPANY_LOGO = createRequestTypes('COMPANY_LOGO')

export const COMPANY_ID = createRequestTypes('COMPANY_ID')

export const UPLOAD_USER_IMG = createRequestTypes('UPLOAD_USER_IMG')

export const CREATE_USER = createRequestTypes('CREATE_USER')

export const UPDATE_USER = createRequestTypes('UPDATE_USER')

export const FETCH_ALL_ROLE = createRequestTypes('FETCH_ALL_ROLE')

export const FETCH_ALL_STATE = createRequestTypes('FETCH_ALL_STATE')

export const FETCH_ALL_COUNTRY = createRequestTypes('FETCH_ALL_COUNTRY')

export const FETCH_ALL_FLOOR = createRequestTypes('FETCH_ALL_FLOOR')

export const FETCH_ALL_WORKSTATION = createRequestTypes('FETCH_ALL_WORKSTATION')

export const FETCH_ALL_LOCATION = createRequestTypes('FETCH_ALL_LOCATION')

export const FETCH_ALL_CLIENT_LOCATION = createRequestTypes('FETCH_ALL_CLIENT_LOCATION')

export const FETCH_ALL_USER_LIST = createRequestTypes('FETCH_ALL_USER_LIST')

export const GET_USER_IMAGE = createRequestTypes('GET_USER_IMAGE')

export const GET_EXPECTED_JOINERS_IMAGE = createRequestTypes('GET_EXPECTED_JOINERS_IMAGE')

export const DELETE_USER = createRequestTypes('DELETE_USER')

export const APPROVE_PLANFORTHEDAY = createRequestTypes('APPROVE_TIMESHEET')

export const ADD_COMMENT_TIMESHEET = createRequestTypes('ADD_COMMENT_TIMESHEET')

export const ADD_COMMENT_PLANFORTHEDAY = createRequestTypes('ADD_COMMENT_PLANFORTHEDAY')

export const GET_COMMENT_PLANFORTHEDAY = createRequestTypes('GET_COMMENT_PLANFORTHEDAY')

export const GET_COMMENT_TIMESHEET = createRequestTypes('GET_COMMENT_TIMESHEET')

export const GET_PLANFORTHEDAY = createRequestTypes('GET_PLANFORTHEDAY')

export const EDIT_PLANFORTHEDAY = createRequestTypes('EDIT_PLANFORTHEDAY')

export const DOWNLOAD_CSV_FOR_MY_TEAM = createRequestTypes('DOWNLOAD_CSV_FOR_MY_TEAM')

export const GET_SINGLE_IDSR = createRequestTypes('GET_SINGLE_IDSR')

export const EDIT_SINGLE_IDSR = createRequestTypes('EDIT_SINGLE_IDSR')

export const PAYROLL_FORM16 = createRequestTypes('PAYROLL_FORM16')

export const UPLOAD_FORM16 = createRequestTypes('UPLOAD_FORM16')

export const UPLOAD_CERTIFICATE = createRequestTypes('UPLOAD_CERTIFICATE')

export const DOWNLOAD_FORM16 = createRequestTypes('DOWNLOAD_FORM16')

export const GET_FINANCIAL_YEAR = createRequestTypes('GET_FINANCIAL_YEAR')

export const SEND_EMAIL = createRequestTypes('SEND_EMAIL')

export const FETCH_REPORTS = createRequestTypes('FETCH_REPORTS')

export const ADD_NEW_JOINERS = createRequestTypes('ADD_NEW_JOINERS')

export const UPDATE_NEW_JOINERS = createRequestTypes('UPDATE_NEW_JOINERS')

export const GET_NEW_JOINERS_DETAILS = createRequestTypes('GET_NEW_JOINERS_DETAILS')

export const GET_CERTIFICATE_INFO = createRequestTypes('GET_CERTIFICATE_INFO')

export const DOWNLOAD_CERTIFICATE = createRequestTypes('DOWNLOAD_CERTIFICATE')

export const TAX_REPORT_DATA = createRequestTypes('TAX_REPORT_DATA')

export const TAX_FINANCIAL_YEAR = createRequestTypes('TAX_FINANCIAL_YEAR')

export const ADD_BANK_INFO = createRequestTypes('ADD_BANK_INFO')

export const GET_EMPLOYEE = createRequestTypes('GET_EMPLOYEE')
export const FETCH_DOMAIN_TYPE = createRequestTypes('FETCH_DOMAIN_TYPE')

export const GET_EMPLOYEEMENT_INFO = createRequestTypes('GET_EMPLOYEEMENT_INFO')
export const GET_USER_TECHNOLOGY = createRequestTypes('GET_USER_TECHNOLOGY')

export const GET_GRADE_LABLE = createRequestTypes('GET_GRADE_LABLE')
export const GET_CERTIFICATE_BY_SPLIT = createRequestTypes('GET_CERTIFICATE_BY_SPLIT')
export const GET_CERTI_PERCENTAGE = createRequestTypes('GET_CERTI_PERCENTAGE')
export const GET_COUNTRY_DISTRIBUTION = createRequestTypes('GET_COUNTRY_DISTRIBUTION')
export const GET_USER_STATICS = createRequestTypes('GET_USER_STATICS')

export const ADD_BACKGROUND_INFO = createRequestTypes('ADD_BACKGROUND_INFO')

export const EMP_SALARY_SLIP = createRequestTypes('EMP_SALARY_SLIP')

export const EMP_COMPENSATION = createRequestTypes('EMP_COMPENSATION')

export const DOWNLOAD_DOCUMENT = createRequestTypes('DOWNLOAD_DOCUMENT')

export const FETCH_BACKGROUND_INFO = createRequestTypes('FETCH_BACKGROUND_INFO')

export const FETCH_LEAVE_TYPE = createRequestTypes('FETCH_LEAVE_TYPE')

export const CREATE_LEAVE = createRequestTypes('CREATE_LEAVE')

export const UPDATE_LEAVE = createRequestTypes('UPDATE_LEAVE')

export const DELETE_LEAVE = createRequestTypes('DELETE_LEAVE')

export const PAYROLL_DROPDOWN_TYPE = createRequestTypes('PAYROLL_DROPDOWN_TYPE')

export const CREATE_PAYROLL = createRequestTypes('CREATE_PAYROLL')

export const GET_PAYROLL = createRequestTypes('GET_PAYROLL')

export const GET_EMP_DESIGNATIONS = createRequestTypes('GET_EMP_DESIGNATIONS')

export const CREATE_EMP_DESIGNATIONS = createRequestTypes('CREATE_EMP_DESIGNATIONS')

export const EDIT_DESIGNATIONS = createRequestTypes('EDIT_DESIGNATIONS')

export const CREATE_HOLIDAY = createRequestTypes('CREATE_HOLIDAY')

export const CREATE_TIMING = createRequestTypes('CREATE_TIMING')

export const CREATE_QAULIFICATION = createRequestTypes('CREATE_QAULIFICATION')

export const CREATE_LEAVES = createRequestTypes('CREATE_LEAVES')

export const GET_LEAVE_INFO = createRequestTypes('GET_LEAVE_INFO')

export const ASSETS_DATA = createRequestTypes('ASSETS_DATA')

export const FETCH_NUMBER_OF_ASSETS = createRequestTypes('FETCH_NUMBER_OF_ASSETS')

export const FETCH_EMP_DATA = createRequestTypes('FETCH_EMP_DATA')

export const FETCH_EMP_ASSET_DATA = createRequestTypes('FETCH_EMP_ASSET_DATA')

export const FETCH_ASSETS = createRequestTypes('FETCH_ASSETS')

export const ASSIGN_ASSET = createRequestTypes('ASSIGN_ASSET')

export const UNALLOCATE_ASSET = createRequestTypes('UNALLOCATE_ASSET')

export const ADD_NEW_ASSET = createRequestTypes('ADD_NEW_ASSET')

export const FETCH_ASSETS_DATA_AS_EXCEL = createRequestTypes('FETCH_ASSETS_DATA_AS_EXCEL')

export const EMPLOYEE_ASSETS_DETAILS = createRequestTypes('EMPLOYEE_ASSETS_DETAILS')

export const ASSET_ABBERATION = createRequestTypes('ASSET_ABBERATION')

export const ASSET_LOCATION = createRequestTypes('ASSET_LOCATION')

export const ASSET_MAKE = createRequestTypes('ASSET_MAKE')

export const ASSET_STATUS = createRequestTypes('ASSET_STATUS')

export const ASSET_OS = createRequestTypes('ASSET_OS')

export const ASSET_TECHNOLOGIES = createRequestTypes('ASSET_TECHNOLOGIES')

export const ASSET_CATEGORY = createRequestTypes('ASSET_CATEGORY')

export const ASSET_OS_DATA = createRequestTypes('ASSET_OS_DATA')

export const ASSET_OS_UPDATE = createRequestTypes('ASSET_OS_UPDATE')

export const ASSET_CATEGORY_UPDATE = createRequestTypes('ASSET_CATEGORY_UPDATE')

export const ASSET_CATEGORY_DELETE = createRequestTypes('ASSET_CATEGORY_DELETE')

export const ASSET_MAKE_DATA = createRequestTypes('ASSET_MAKE_DATA')

export const ADD_ASSET_MAKE = createRequestTypes('ADD_ASSET_MAKE')

export const ASSET_MAKE_DELETE = createRequestTypes('ASSET_MAKE_DELETE')

export const ASSET_STATUS_DATA = createRequestTypes('ASSET_STATUS_DATA')

export const ADD_ASSET_STATUS = createRequestTypes('ADD_ASSET_STATUS')

export const ASSET_STATUS_DELETE = createRequestTypes('ASSET_STATUS_DELETE')

export const TECHNOLOGIES_DATA = createRequestTypes('TECHNOLOGIES_DATA')

export const ADD_TECHNOLOGIES = createRequestTypes('ADD_TECHNOLOGIES')

export const DELETE_TECHNOLOGIES = createRequestTypes('DELETE_TECHNOLOGIES')

export const DELETE_ASSET_OS = createRequestTypes('DELETE_ASSET_OS')

export const EMPLOYEE_ASSETS_COUNT = createRequestTypes('EMPLOYEE_ASSETS_COUNT')

export const ASSET_ABBREVATION_COUNT = createRequestTypes('ASSET_ABBREVATION_COUNT')

export const ASSET_STATUS_COUNT = createRequestTypes('ASSET_STATUS_COUNT')

export const FETCH_ISSUE_TYPE = createRequestTypes('FETCH_ISSUE_TYPE')

export const FETCH_LEAVE_TYPES = createRequestTypes('FETCH_LEAVE_TYPES')

export const DOWNLOAD_ASSETS_QR = createRequestTypes('DOWNLOAD_ASSETS_QR')

export const DOWNLOAD_QR = createRequestTypes('DOWNLOAD_QR')

export const ASSIGN_ASSET_REPORT = createRequestTypes('ASSIGN_ASSET_REPORT')

export const GET_LEAVE_REPORT = createRequestTypes('GET_LEAVE_REPORT')

export const GET_LEAVE_BALANCE_REPORT = createRequestTypes('GET_LEAVE_BALANCE_REPORT')

export const GET_LEAVE_TYPE_REPORT = createRequestTypes('GET_LEAVE_TYPE_REPORT')

export const GET_LEAVE_ALLOCATED_REPORT = createRequestTypes('GET_LEAVE_ALLOCATED_REPORT')

export const GET_LEAVE_ENCASHMENT_REPORT = createRequestTypes('GET_LEAVE_ENCASHMENT_REPORT')

export const GET_CATEGORY = createRequestTypes('GET_CATEGORY')

export const CREATE_LEAVE_TYPE = createRequestTypes('CREATE_LEAVE_TYPE')

export const ASSIGNED_SR_REQUEST = createRequestTypes('ASSIGNED_SR_REQUEST')

export const CERTIFICATION_REPORT = createRequestTypes('CERTIFICATION_REPORT')

export const TEMPORARY_EMP = createRequestTypes('TEMPORARY_EMP')

export const LEAVE_FREQUENCIES = createRequestTypes('LEAVE_FREQUENCIES')

export const GET_QUATRES = createRequestTypes('GET_QUATRES')

export const GET_DAILY_SUMMARIES = createRequestTypes('GET_DAILY_SUMMARIES')

export const GET_AVAILABLE_YEARS = createRequestTypes('GET_AVAILABLE_YEARS')

export const MONTHLY_SUMMARIES = createRequestTypes('MONTHLY_SUMMARIES')

export const WEEKLY_SUMMARIES = createRequestTypes('WEEKLY_SUMMARIES')

export const REIMBURSEMENT_REQUESTS = createRequestTypes('REIMBURSEMENT_REQUESTS')

export const PERTICULAR_EXPENSE = createRequestTypes('PERTICULAR_EXPENSE')

export const EXPENSE_STATUS_UPDATE = createRequestTypes('EXPENSE_STATUS_UPDATE')

export const CREATE_EXPENSE = createRequestTypes('CREATE_EXPENSE')

export const FETCH_ATTENDANCE_REPORT = createRequestTypes('FETCH_ATTENDANCE_REPORT')

export const FETCH_SERVICE_REQUEST_DEPARTMENT = createRequestTypes(
  'FETCH_SERVICE_REQUEST_DEPARTMENT',
)

export const DOWNLOAD_ATTACHMENTS = createRequestTypes('DOWNLOAD_ATTACHMENTS')
// recruitment //
export const FETCH_ROUND = createRequestTypes('FETCH_ROUND')
export const FETCH_POSITIONS = createRequestTypes('FETCH_POSITIONS')
export const FETCH_TAGS = createRequestTypes('FETCH_TAGS')
export const FETCH_RECRUITERS = createRequestTypes('FETCH_RECRUITERS')
export const FETCH_TEMPLATE_DETAILS = createRequestTypes('FETCH_TEMPLATE_DETAILS')
export const DELETE_RECRUITMENT_TEMPLATE = createRequestTypes('DELETE_RECRUITMENT_TEMPLATE')
export const DELETE_COLLEGE = createRequestTypes('DELETE_COLLEGE')
export const FETCH_COLLEGE_DETAILS = createRequestTypes('FETCH_COLLEGE_DETAILS')
export const FETCH_BATCHES = createRequestTypes('FETCH_BATCHES')
export const FETCH_ADDQUALIFICATION = createRequestTypes('FETCH_ADDQUALIFICATION')
export const FETCH_EXPECTED_JOINERS = createRequestTypes('FETCH_EXPECTED_JOINERS')
export const FETCH_FEEDBACK = createRequestTypes('FETCH_FEEDBACK')
export const FETCH_CANDIDATEPOSITION = createRequestTypes('FETCH_CANDIDATEPOSITION')
export const FETCH_TPO_DETAILS = createRequestTypes('FETCH_TPO_DETAILS')
export const FETCH_ROUND_TYPE = createRequestTypes('FETCH_ROUND_TYPE')
export const FETCH_DATEANDTIME = createRequestTypes('FETCH_DATEANDTIME')
export const FETCH_ORGANISATIONDETAILS = createRequestTypes('FETCH_ORGANISATIONDETAILS')
export const FETCH_USER = createRequestTypes('FETCH_USER')
export const FETCH_TAG_CANDIDATES = createRequestTypes('FETCH_TAG_CANDIDATES')
export const FETCH_BLOCK_DOMAIN_DATA = createRequestTypes('FETCH_BLOCK_DOMAIN_DATA')
export const FETCH_EMAIL_TEMPLATES = createRequestTypes('FETCH_EMAIL_TEMPLATES')
export const FETCH_STATIC_DATA = createRequestTypes('FETCH_STATIC_DATA')
export const FETCH_INTERVIEWER = createRequestTypes('FETCH_INTERVIEWER')
export const CREATE_STATIC_DATA = createRequestTypes('CREATE_STATIC_DATA')
export const FETCH_EXPERIENCES = createRequestTypes('FETCH_EXPERIENCES')
export const FETCH_BLOCKEDSUBJECTS = createRequestTypes('FETCH_BLOCKEDSUBJECTS')
export const FETCH_ROUNDCANDIDATES = createRequestTypes('FETCH_ROUNDCANDIDATES')
export const FETCH_TAGCANDIDATES = createRequestTypes('FETCH_TAGCANDIDATES')
export const ADD_MULTIPLE_CANDIDATES = createRequestTypes('ADD_MULTIPLE_CANDIDATES')
export const ADD_REJECTED_SUBJECT = createRequestTypes('ADD_REJECTED_SUBJECT')
export const ADD_JOB_EXPERIENCE = createRequestTypes('ADD_JOB_EXPERIENCE')
export const FETCH_DATE_TIME_BY_ROUND = createRequestTypes('FETCH_DATE_TIME_BY_ROUND')
export const FETCH_TEMPLATE_BY_ROUND = createRequestTypes('FETCH_TEMPLATE_BY_ROUND')
export const FETCH_CANDIDATE_BY_FILTERS = createRequestTypes('FETCH_CANDIDATE_BY_FILTERS')
export const DELETE_CANDIDATE_BY_ID = createRequestTypes('DELETE_CANDIDATE_BY_ID')
export const SENDMAIL_CANDIDATE_BY_IDS = createRequestTypes('SENDMAIL_CANDIDATE_BY_IDS')
export const VIEW_ATTACHMENTS_CANDIDATE = createRequestTypes('VIEW_ATTACHMENTS_CANDIDATE')
export const FETCH_VIDEO_URL = createRequestTypes('FETCH_VIDEO_URL')
export const FETCH_INTERVIEWER_PANNEL = createRequestTypes('FETCH_INTERVIEWER_PANNEL')
export const FETCH_APPLICANTS_QUALIFICATION = createRequestTypes('FETCH_APPLICANTS_QUALIFICATION')
export const FETCH_UNAPPROVED_CANDIDATE = createRequestTypes('FETCH_UNAPPROVED_CANDIDATE')
export const FETCH_SPAMING = createRequestTypes('FETCH_SPAMING')
export const DELETE_UNAPPROVED_CANDIDATE = createRequestTypes('DELETE_UNAPPROVED_CANDIDATE')
export const FETCH_ROUNDS_BY_TYPE = createRequestTypes('FETCH_ROUNDS_BY_TYPE')
export const ADD_DRIVE = createRequestTypes('ADD_DRIVE')
export const EDIT_ORG = createRequestTypes('EDIT_ORG')
export const FETCH_INTERVIEWER_WORK = createRequestTypes('FETCH_INTERVIEWER_WORK')
export const CANDIDATE_COUNT_BY_ROUND = createRequestTypes('CANDIDATE_COUNT_BY_ROUND')
export const FETCH_JOINED_CANDIDATES = createRequestTypes('FETCH_JOINED_CANDIDATES')
export const DELETE_EXPECTED_JOINERS = createRequestTypes('DELETE_EXPECTED_JOINERS')
export const FETCH_CANDIDATEBYID = createRequestTypes('FETCH_CANDIDATEBYID')
export const ADD_MANAGEQUALIFICATION = createRequestTypes('ADD_MANAGEQUALIFICATION')
export const ADD_MANAGEBATCHES = createRequestTypes('ADD_MANAGEBATCHES')
export const DELETE_QUALIFICATION = createRequestTypes('DELETE_QUALIFICATION')
export const DELETE_BATCH = createRequestTypes('DELETE_BATCH')
export const EDIT_BATCH = createRequestTypes('EDIT_BATCH')
export const EDIT_QUALIFICATION = createRequestTypes('EDIT_QUALIFICATION')
export const FETCH_REPRESENTATIVE_TYPE = createRequestTypes('FETCH_REPRESENTATIVE_TYPE')
export const ADD_ORG_DETAILS = createRequestTypes('ADD_ORG_DETAILS')
export const ADD_USER_DETAILS = createRequestTypes('ADD_USER_DETAILS')
export const ADD_DATE_TIME_DETAILS = createRequestTypes('ADD_DATE_TIME_DETAILS')
export const DELETE_ORG_DETAILS = createRequestTypes('DELETE_ORG_DETAILS')
export const DELETE_USER_DETAILS = createRequestTypes('DELETE_USER_DETAILS')
export const DELETE_DATE_TIME_DETAILS = createRequestTypes('DELETE_DATE_TIME_DETAILS')
export const EDIT_ORG_DETAILS = createRequestTypes('EDIT_ORG_DETAILS')
export const EDIT_USER_DETAILS = createRequestTypes('EDIT_USER_DETAILS')
export const FETCH_ROUNDS_FOR_TEMPLATE = createRequestTypes('FETCH_ROUNDS_FOR_TEMPLATE')
export const ADD_EMAIL_TEMPLATE_DATA = createRequestTypes('ADD_EMAIL_TEMPLATE_DATA')
export const EDIT_TEMPLATE = createRequestTypes('EDIT_TEMPLATE')
export const EDIT_CANDIDATE_INLINE = createRequestTypes('EDIT_CANDIDATE_INLINE')
export const FETCH_INSTITUTE_DETAILS = createRequestTypes('FETCH_INSTITUTE_DETAILS')
export const ADD_JOB_POSITION = createRequestTypes('ADD_JOB_POSITION')
export const ADD_INST_DETAILS = createRequestTypes('ADD_INST_DETAILS')
export const EDIT_INST_DETAILS = createRequestTypes('EDIT_INST_DETAILS')
export const SUB_INST_DETAILS = createRequestTypes('SUB_INST_DETAILS')
export const DEL_INST_DETAILS = createRequestTypes('DEL_INST_DETAILS')
export const ROUND_FEEDBACK_DROPDOWN = createRequestTypes('ROUND_FEEDBACK_DROPDOWN')
export const FETCH_JOB_EXPERIENCE = createRequestTypes('FETCH_JOB_EXPERIENCE')
export const ADD_STATIC_DATA = createRequestTypes('ADD_STATIC_DATA')
export const ADD_INTERVIEWER_DATA = createRequestTypes('ADD_INTERVIEWER_DATA')
export const EDIT_STATIC_DATA = createRequestTypes('EDIT_STATIC_DATA')
export const EDIT_INTERVIEWER_DATA = createRequestTypes('EDIT_INTERVIEWER_DATA')
export const DELETE_STATIC_DATA = createRequestTypes('DELETE_STATIC_DATA')
export const DELETE_INTERVIEWER_DATA = createRequestTypes('DELETE_INTERVIEWER_DATA')
export const ADD_EXPECTED_JOINERS = createRequestTypes('ADD_EXPECTED_JOINERS')
export const EDIT_EXPECTED_JOINERS = createRequestTypes('EDIT_EXPECTED_JOINERS')
export const ADD_BLOCKED_BODY = createRequestTypes('ADD_BLOCKED_BODY')
export const DELETE_BLOCKED_BODY = createRequestTypes('DELETE_BLOCKED_BODY')
export const GET_BLOCKED_BODY = createRequestTypes('GET_BLOCKED_BODY')
export const ADD_REJECTED_BODY = createRequestTypes('ADD_REJECTED_BODY')
export const DELETE_REJECTED_BODY = createRequestTypes('DELETE_REJECTED_BODY')
export const EDIT_REJECTED_BODY = createRequestTypes('EDIT_REJECTED_BODY')
export const FETCH_NEW_TAG = createRequestTypes('FETCH_NEW_TAG')
export const FETCH_EDIT_TAG = createRequestTypes('FETCH_EDIT_TAG')
export const FETCH_DELETE_TAG = createRequestTypes('FETCH_DELETE_TAG')
export const FETCH_ADD_ROUND = createRequestTypes('FETCH_ADD_ROUND')
export const DELETE_ROUNDS = createRequestTypes('DELETE_ROUNDS')
export const EDIT_ROUNDS = createRequestTypes('EDIT_ROUNDS')
export const EDIT_TPO = createRequestTypes('EDIT_TPO')
export const DELETE_TPO = createRequestTypes('DELETE_TPO')
export const ADD_TPO = createRequestTypes('ADD_TPO')
export const ORGANISATION_DETAILS_BY_TYPE = createRequestTypes('ORGANISATION_DETAILS_BY_TYPE')
export const ADD_BLOCK_DOMAIN_DATA = createRequestTypes('ADD_BLOCK_DOMAIN_DATA')
export const DELETE_BLOCK_DOMAIN_DATA = createRequestTypes('DELETE_BLOCK_DOMAIN_DATA')
export const EDIT_BLOCK_DOMAIN_DATA = createRequestTypes('EDIT_BLOCK_DOMAIN_DATA')
export const ADD_CANDIDATE_FEEDBACK = createRequestTypes('ADD_CANDIDATE_FEEDBACK')
export const DELETE_ATTACHMENT = createRequestTypes('DELETE_ATTACHMENT')
export const DELETE_CANDIDATE_FEEDBACK = createRequestTypes('DELETE_CANDIDATE_FEEDBACK')
export const EDIT_CANDIDATE = createRequestTypes('EDIT_CANDIDATE')
export const FETCH_BATCH_DROPDOWN = createRequestTypes('FETCH_BATCH_DROPDOWN')
export const EDIT_DATE_TIME = createRequestTypes('EDIT_DATE_TIME')
export const EDIT_REJECTED_SUBJECT = createRequestTypes('EDIT_REJECTED_SUBJECT')
export const EDIT_JOB_EXPERIENCE = createRequestTypes('EDIT_JOB_EXPERIENCE')
export const DELETE_JOB_EXPERIENCE = createRequestTypes('DELETE_JOB_EXPERIENCE')
export const DELETE_REJECTED_SUBJECT = createRequestTypes('DELETE_REJECTED_SUBJECT')
export const EDIT_POSITION_DETAILS = createRequestTypes('EDIT_POSITION_DETAILS')
export const SUB_POSITION_DETAILS = createRequestTypes('SUB_POSITION_DETAILS')
export const DELETE_POSITION_DETAILS = createRequestTypes('DELETE_POSITION_DETAILS')
export const ADD_FEEDBACK = createRequestTypes('ADD_FEEDBACK')
export const DELETE_FEEDBACK = createRequestTypes('DELETE_FEEDBACK')
export const ADD_CANDIDATE = createRequestTypes('ADD_CANDIDATE')
export const EDIT_CANDIDATEFORM = createRequestTypes('EDIT_CANDIDATEFORM')
export const ADD_RESUME = createRequestTypes('ADD_RESUME')
export const VIEW_RECRUITER_CALLS = createRequestTypes('VIEW_RECRUITER_CALLS')
export const TEMPLATE_BY_ROUND = createRequestTypes('TEMPLATE_BY_ROUND')
export const DATETIME_BY_ROUND = createRequestTypes('DATETIME_BY_ROUND')
export const FETCH_SEND_REMAINDER = createRequestTypes('FETCH_SEND_REMAINDER')
export const FETCH_COOLING_OFF_PERIOD = createRequestTypes('FETCH_COOLING_OFF_PERIOD')
export const VIEW_CANDIDATE_FEEDBACK = createRequestTypes('VIEW_CANDIDATE_FEEDBACK')
export const VIEW_CANDIDATE_TAGS = createRequestTypes('VIEW_CANDIDATE_TAGS')
export const VIEW_CANDIDATE_LOGS = createRequestTypes('VIEW_CANDIDATE_LOGS')
export const ADD_UPLOAD_RESUME = createRequestTypes('ADD_UPLOAD_RESUME')
export const ADD_UPLOAD_ASSIGNMENT = createRequestTypes('ADD_UPLOAD_ASSIGNMENT')

export const EDIT_FEEDBACK = createRequestTypes('EDIT_FEEDBACK')
export const SUB_EDIT_FEEDBACK = createRequestTypes('SUB_EDIT_FEEDBACK')

export const APPLICANTS_APPROVE = createRequestTypes('APPLICANTS_APPROVE')
export const APPLICANTS_REJECT = createRequestTypes('APPLICANTS_REJECT')
export const DELETE_MULTIPLE_APPLICANTS = createRequestTypes('DELETE_MULTIPLE_APPLICANTS')
export const FETCH_APPLICANTS_RESUME = createRequestTypes('FETCH_APPLICANTS_RESUME')
export const FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID = createRequestTypes(
  'FETCH_SHORTCUT_KEY_BY_CANDIDATE_ID',
)
export const TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID = createRequestTypes(
  'TOGGLE_SHORTCUTKRY_TO_CANDIDATE_ID',
)

export const GET_EXPENSE_DETAILS = createRequestTypes('GET_EXPENSE_DETAILS')

// Template Management Action Types
export const FETCH_TEMPLATES = createRequestTypes('FETCH_TEMPLATES')

export const CREATE_TEMPLATE = createRequestTypes('CREATE_TEMPLATE')

export const UPDATE_TEMPLATE = createRequestTypes('UPDATE_TEMPLATE')

export const DELETE_TEMPLATE = createRequestTypes('DELETE_TEMPLATE')

export const SEARCH_TEMPLATES = createRequestTypes('SEARCH_TEMPLATES')

export const FETCH_TEMPLATE_BY_ID = createRequestTypes('FETCH_TEMPLATE_BY_ID')

// DR Section KPI Template Management Action Types
export const ASSIGN_TEMPLATE = createRequestTypes('ASSIGN_TEMPLATE')

export const GET_ASSIGNED_TEMPLATE = createRequestTypes('GET_ASSIGNED_TEMPLATE')

export const EDIT_TEMPLATE_VALUES = createRequestTypes('EDIT_TEMPLATE_VALUES')

export const GET_KPI_TEMPLATES = createRequestTypes('GET_KPI_TEMPLATES')

export const LOAD_TEMPLATE_OR_OPTIONS = createRequestTypes('LOAD_TEMPLATE_OR_OPTIONS')
