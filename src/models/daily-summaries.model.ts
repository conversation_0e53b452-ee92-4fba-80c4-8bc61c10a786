export interface IDailySummary {
  id: number;
  user_id: number | null;
  IDSR_date: string | null;  // We keep this as string since DateTime is serialized as ISO string
  ISO_Day_Number: number | null;
  summary: string;
  created_at: string;  // DateTime is serialized as ISO string
}

export interface IDailySummariesResponse {
  statusCode: number;
  message: string;
  data: IDailySummary[];
}

export const defaultValue: Readonly<IDailySummary> = {
  id: 0,
  user_id: null,
  IDSR_date: null,
  ISO_Day_Number: null,
  summary: '',
  created_at: ''
};
