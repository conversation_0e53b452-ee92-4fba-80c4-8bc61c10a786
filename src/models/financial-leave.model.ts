import { ILeaveDetails } from './leave-detail.model'

export interface IFinancialLeave {
  financialYear?: string
  allocatedLeaves: ILeaveDetails[]
  deductibleLeavesAvailed: ILeaveDetails[]
  nonDeductibleLeavesAvailed: ILeaveDetails[]
  totalLeaveCount: number
  totalDeductibleLeaves: number
  totalNonDeductibleLeavesAvailed: number
}

export const defaultValue: Readonly<IFinancialLeave> = {
  allocatedLeaves: [],
  deductibleLeavesAvailed: [],
  nonDeductibleLeavesAvailed: [],
  totalLeaveCount: 0,
  totalDeductibleLeaves: 0,
  totalNonDeductibleLeavesAvailed: 0,
}
