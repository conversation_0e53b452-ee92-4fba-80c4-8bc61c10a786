import { useState } from 'react'
import { OpenableState, OpenableStateResult } from './Types'

function useOpenable(): OpenableStateResult {
  const [openableState, setOpenableState] = useState<OpenableState>({ kind: 'closed' })

  const onClose = () => {
    setOpenableState({ kind: 'closed' })
  }

  const onOpen = () => {
    setOpenableState({ kind: 'open' })
  }

  const isOpen = () => {
    switch (openableState.kind) {
      case 'closed':
        return false
      case 'open':
        return true
    }
  }

  const onOpenChange = () => {
    switch (openableState.kind) {
      case 'closed':
        return onOpen()
      case 'open':
        return onClose()
    }
  }

  return { isOpen: isOpen(), onOpen, onClose, onOpenChange }
}

export default useOpenable
