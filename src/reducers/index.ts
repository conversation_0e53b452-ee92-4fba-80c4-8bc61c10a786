import { combineReducers } from 'redux'
import entities, * as Entities from './entities'
import ui, * as UI from './ui'
export const dashboardEntity = Entities.dashboardState
export const employeePortalEntity = Entities.employeePortalState
export const employeePortalUI = UI.getAllEmpPortalUI
export const financeEntity = Entities.financeState
export const userAuthenticationEntity = Entities.userAuthenticationState
export const loginUserEntity = Entities.loginUserState
export const SREntity = Entities.ServiceRequest
export const LoanRequestEntity = Entities.loanRequest
export const dashboardUI = UI.dashboardState
export const userAuthenticationUI = UI.userAuthenticationState
export const SRUI = UI.servicepostState
export const loaderStateUI = UI.loaderState
export const loginUserUI = UI.loginUserState
export const employeeRecordsUI = Entities.employeeDataRequest
export const employeeDataSateUI = UI.employeeDataSate
export const financeUI = UI.financeState
export const attendanceEntity = Entities.attendanceState
export const attendanceUI = UI.attendanceState
export const projectManagementEntity = Entities.getAllProjectManagement
export const projectManagementUI = UI.projectmanagement
export const orgChartEntity = Entities.getAllOrgChartState
export const orgChartUI = UI.getAllOrgChartUI
export const fetchUserDetailsUI = UI.fetchAllUsersUI
export const fetchUserDetailsEntities = Entities.fetchAllUsers
export const fetchEmpHrControlUI = UI.fetchHrControlUI
export const fetchEmpHrControlEntities = Entities.hrControl
export const getAssetsPortal = Entities.assetsManagementState
export const getAssetsManagementPortalUI = UI.getAssetsManagementPortalUI
export const dailySummariesEntity = Entities.dailySummaries
export const dailySummariesUI = UI.dailySummaries
export const availableYearsEntity = Entities.availableYears
export const availableYearsUI = UI.getAvailableYearsUI
export const monthlySummariesEntity = Entities.dailySummaries
export const monthlySummariesUI = UI.dailySummaries
export const weeklySummariesEntity = Entities.dailySummaries
export const weeklySummariesUI = UI.dailySummaries
export const getExpenseDetailsEntity = Entities.getExpenseDetailsEntity
export const getExpenseDetailsUI = UI.getExpenseDeatilsUI
export const recruitmentEntity = Entities.recruitmentState
export const recruitmentStateUI = UI.recruitmentDataState
export const templatesEntity = Entities.templatesState

const reducers = () =>
  combineReducers({
    entities,
    ui,
  })
export default reducers
