import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions as actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  GET_LEAVE_REPORT,
  FETCH_ATTENDANCE_REPORT,
  FETCH_SERVICE_REQUEST_DEPARTMENT,
  REQUEST,
  SUCCESS,
  GET_LEAVE_BALANCE_REPORT,
  GET_LEAVE_TYPE_REPORT,
  GET_LEAVE_ALLOCATED_REPORT,
  GET_LEAVE_ENCASHMENT_REPORT,
  LEAVE_FREQUENCIES,
  GET_QUATRES,
} from '../../actions/actiontypes'

const { FETCH_EMPLOYEE_DETAILS } = actionTypes

const entity = () => {
  const getAllEmployeesDetails = (state = [], action: actions) => {
    switch (action.type) {
      case FETCH_EMPLOYEE_DETAILS[SUCCESS]:
        return action.payload
      case FETCH_EMPLOYEE_DETAILS[REQUEST]:
        return []

      default:
        return state
    }
  }
  const getLeavesReports = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_REPORT[SUCCESS]:
        return action.payload
      case GET_LEAVE_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getAttendanceReports = (state = [], action: actions) => {
    switch (action.type) {
      case FETCH_ATTENDANCE_REPORT[SUCCESS]:
        return action.payload
      case FETCH_ATTENDANCE_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getLeavesReportsBalance = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_BALANCE_REPORT[SUCCESS]:
        return action.payload
      case GET_LEAVE_BALANCE_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getLeavesReportsType = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_TYPE_REPORT[SUCCESS]:
        return action.payload
      case GET_LEAVE_TYPE_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getLeavesReportsAllocated = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_ALLOCATED_REPORT[SUCCESS]:
        return action.payload
      case GET_LEAVE_ALLOCATED_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getLeavesReportsEncashment = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_ENCASHMENT_REPORT[SUCCESS]:
        return action.payload
      case GET_LEAVE_ENCASHMENT_REPORT[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getLeavesFrequencies = (state = [], action: actions) => {
    switch (action.type) {
      case LEAVE_FREQUENCIES[SUCCESS]:
        return action.payload
      case LEAVE_FREQUENCIES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getQuatre = (state = [], action: actions) => {
    switch (action.type) {
      case GET_QUATRES[SUCCESS]:
        return action.payload
      case GET_QUATRES[REQUEST]:
        return []
      default:
        return state
    }
  }

  return combineReducers({
    getAllEmployeesDetails,
    getLeavesReports,
    getAttendanceReports,
    getLeavesReportsBalance,
    getLeavesReportsEncashment,
    getLeavesReportsAllocated,
    getLeavesReportsType,
    getLeavesFrequencies,
    getQuatre,
  })
}

export default entity

export const getAllEmployeesList = (state: RootState) => state.entities.EmployeeDataRequest
