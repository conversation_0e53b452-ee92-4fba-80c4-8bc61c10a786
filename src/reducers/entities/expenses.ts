import { combineReducers } from 'redux'
import { Actions as actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  CREATE_EXPENSE,
  DOWNLOAD_ATTACHMENTS,
  EXPENSE_STATUS_UPDATE,
  FAILURE,
  PERTICULAR_EXPENSE,
  REIMBURSEMENT_REQUESTS,
  REQUEST,
  RESET,
  SUCCESS,
} from '../../actions/actiontypes'

const entity = () => {
  const getReimbursementRequests = (state = [], action: actions) => {
    switch (action.type) {
      case REIMBURSEMENT_REQUESTS[SUCCESS]:
        return action.payload
      case REIMBURSEMENT_REQUESTS[REQUEST]:
      case REIMBURSEMENT_REQUESTS[RESET]:
      case REIMBURSEMENT_REQUESTS[FAILURE]:
        return []
      default:
        return state
    }
  }

  const getPerticularExpense = (state = {}, action: actions) => {
    switch (action.type) {
      case PERTICULAR_EXPENSE[SUCCESS]:
        return action.payload
      case PERTICULAR_EXPENSE[REQUEST]:
      case PERTICULAR_EXPENSE[RESET]:
      case PERTICULAR_EXPENSE[FAILURE]:
        return {}
      default:
        return state
    }
  }

  const getExpenseStatusUpdate = (state = {}, action: actions) => {
    switch (action.type) {
      case EXPENSE_STATUS_UPDATE[SUCCESS]:
        return action.payload
      case EXPENSE_STATUS_UPDATE[REQUEST]:
      case EXPENSE_STATUS_UPDATE[RESET]:
      case EXPENSE_STATUS_UPDATE[FAILURE]:
        return {}
      default:
        return state
    }
  }

  const createExpense = (state = {}, action: actions) => {
    switch (action.type) {
      case CREATE_EXPENSE[SUCCESS]:
        return action.payload
      case CREATE_EXPENSE[REQUEST]:
      case CREATE_EXPENSE[RESET]:
      case CREATE_EXPENSE[FAILURE]:
        return {}
      default:
        return state
    }
  }

  const downloadAttachments = (state = [], action: actions) => {
    switch (action.type) {
      case DOWNLOAD_ATTACHMENTS[SUCCESS]:
        return action.payload
      case DOWNLOAD_ATTACHMENTS[REQUEST]:
      case DOWNLOAD_ATTACHMENTS[RESET]:
      case DOWNLOAD_ATTACHMENTS[FAILURE]:
        return []
      default:
        return state
    }
  }

  return combineReducers({
    getReimbursementRequests,
    getPerticularExpense,
    getExpenseStatusUpdate,
    createExpense,
    downloadAttachments,
  })
}

export default entity

export const getExpenseDetailsEntity = (state: RootState) => state.entities.getExpenseDetailsEntity
