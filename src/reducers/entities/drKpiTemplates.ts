import { ASSIGN_TEMPLATE, GET_ASSIGNED_TEMPLATE, EDIT_TEMPLATE_VALUES, GET_KPI_TEMPLATES, LOAD_TEMPLATE_OR_OPTIONS } from '../../actions/actiontypes'

export interface AssignedTemplate {
  id: number
  name: string
  categories: {
    id: number
    name: string
    subcategories: {
      id: number
      title: string
      type: string
      defaultTarget: string
      value?: string
      highlights?: string
      lowlights?: string
      actions_taken?: string
    }[]
  }[]
}

export interface KPITemplate {
  id: string
  title: string
  description?: string
  categories: {
    name: string
    subcategories: {
      title: string
      type: 'Qualitative' | 'Quantitative'
      defaultTarget: string
    }[]
  }[]
  created_at?: Date
}

export interface DrKpiTemplatesState {
  assignedTemplate: AssignedTemplate | null
  availableTemplates: KPITemplate[]
  isLoading: boolean
  isAssigning: boolean
  isEditing: boolean
  isFetchingTemplates: boolean
  isLoadingTemplateOrOptions: boolean
  error: string | null
  assignError: string | null
  editError: string | null
  templatesError: string | null
  loadTemplateOrOptionsError: string | null
}

const initialState: DrKpiTemplatesState = {
  assignedTemplate: null,
  availableTemplates: [],
  isLoading: false,
  isAssigning: false,
  isEditing: false,
  isFetchingTemplates: false,
  isLoadingTemplateOrOptions: false,
  error: null,
  assignError: null,
  editError: null,
  templatesError: null,
  loadTemplateOrOptionsError: null,
}

const drKpiTemplatesReducer = (state = initialState, action: any): DrKpiTemplatesState => {
  switch (action.type) {
    // Assign Template Actions
    case ASSIGN_TEMPLATE.REQUEST:
      return {
        ...state,
        isAssigning: true,
        assignError: null,
      }
    case ASSIGN_TEMPLATE.SUCCESS:
      return {
        ...state,
        isAssigning: false,
        assignError: null,
      }
    case ASSIGN_TEMPLATE.FAILURE:
      return {
        ...state,
        isAssigning: false,
        assignError: action.payload.error,
      }
    case ASSIGN_TEMPLATE.RESET:
      return {
        ...state,
        isAssigning: false,
        assignError: null,
      }

    // Get Assigned Template Actions
    case GET_ASSIGNED_TEMPLATE.REQUEST:
      return {
        ...state,
        isLoading: true,
        error: null,
      }
    case GET_ASSIGNED_TEMPLATE.SUCCESS:
      console.log('🔄 GET_ASSIGNED_TEMPLATE.SUCCESS - Reducer received:', action.payload?.data || action.data)
      return {
        ...state,
        isLoading: false,
        assignedTemplate: action.payload?.data || action.data,
        error: null,
      }
    case GET_ASSIGNED_TEMPLATE.FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload.error,
      }
    case GET_ASSIGNED_TEMPLATE.RESET:
      return {
        ...state,
        isLoading: false,
        assignedTemplate: null,
        error: null,
      }

    // Edit Template Values Actions
    case EDIT_TEMPLATE_VALUES.REQUEST:
      return {
        ...state,
        isEditing: true,
        editError: null,
      }
    case EDIT_TEMPLATE_VALUES.SUCCESS:
      return {
        ...state,
        isEditing: false,
        editError: null,
      }
    case EDIT_TEMPLATE_VALUES.FAILURE:
      return {
        ...state,
        isEditing: false,
        editError: action.payload.error,
      }
    case EDIT_TEMPLATE_VALUES.RESET:
      return {
        ...state,
        isEditing: false,
        editError: null,
      }

    // Get KPI Templates Actions
    case GET_KPI_TEMPLATES.REQUEST:
      return {
        ...state,
        isFetchingTemplates: true,
        templatesError: null,
      }
    case GET_KPI_TEMPLATES.SUCCESS:
      return {
        ...state,
        isFetchingTemplates: false,
        availableTemplates: action.payload.data,
        templatesError: null,
      }
    case GET_KPI_TEMPLATES.FAILURE:
      return {
        ...state,
        isFetchingTemplates: false,
        templatesError: action.payload.error,
      }
    case GET_KPI_TEMPLATES.RESET:
      return {
        ...state,
        isFetchingTemplates: false,
        availableTemplates: [],
        templatesError: null,
      }

    // Load Template Or Options Actions
    case LOAD_TEMPLATE_OR_OPTIONS.REQUEST:
      return {
        ...state,
        isLoadingTemplateOrOptions: true,
        loadTemplateOrOptionsError: null,
      }
    case LOAD_TEMPLATE_OR_OPTIONS.SUCCESS:
      const payload = action.payload?.data || action.data
      const { type, data } = payload
      console.log('🔄 LOAD_TEMPLATE_OR_OPTIONS.SUCCESS - Reducer received:', { type, data })
      
      if (type === 'assigned') {
        // Template is assigned - ensure we have the latest data
        console.log('✅ LOAD_TEMPLATE_OR_OPTIONS.SUCCESS - Updating assignedTemplate with fresh data')
        return {
          ...state,
          isLoadingTemplateOrOptions: false,
          assignedTemplate: data,
          availableTemplates: [],
          loadTemplateOrOptionsError: null,
        }
      } else if (type === 'options') {
        // No template assigned, show options
        console.log('✅ LOAD_TEMPLATE_OR_OPTIONS.SUCCESS - Updating availableTemplates')
        return {
          ...state,
          isLoadingTemplateOrOptions: false,
          assignedTemplate: null,
          availableTemplates: data,
          loadTemplateOrOptionsError: null,
        }
      }
      return state
    case LOAD_TEMPLATE_OR_OPTIONS.FAILURE:
      return {
        ...state,
        isLoadingTemplateOrOptions: false,
        loadTemplateOrOptionsError: action.payload?.error || action.error,
      }
    case LOAD_TEMPLATE_OR_OPTIONS.RESET:
      return {
        ...state,
        isLoadingTemplateOrOptions: false,
        assignedTemplate: null,
        availableTemplates: [],
        loadTemplateOrOptionsError: null,
      }

    default:
      return state
  }
}

export default drKpiTemplatesReducer

// Selectors
export const getDrKpiTemplates = (state: any) => state.entities.drKpiTemplates 