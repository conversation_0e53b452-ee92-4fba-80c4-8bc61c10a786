import { combineReducers } from 'redux'
import {
  FETCH_TEMPLATES,
  CREATE_TEMPLATE,
  UPDATE_TEMPLATE,
  DELETE_TEMPLATE,
  SEARCH_TEMPLATES,
  FETCH_TEMPLATE_BY_ID,
  REQUEST,
  SUCCESS,
  FA<PERSON>URE,
  RESET
} from '../../actions/actiontypes'

export interface Template {
  id: string;
  title: string;
  description?: string;
  categories: any[];
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

interface TemplateAction {
  type: string;
  payload?: any;
  error?: any;
  response?: any;
}

const templates = (state: Template[] = [], action: TemplateAction) => {
  switch (action.type) {
    case FETCH_TEMPLATES.SUCCESS:
      console.log('Templates Reducer: FETCH_TEMPLATES.SUCCESS action received:', action)
      console.log('Templates Reducer: action.payload:', action.payload)
      return action.payload || []
    case CREATE_TEMPLATE.SUCCESS:
      return [...state, action.payload]
    case UPDATE_TEMPLATE.SUCCESS:
      return state.map(template => 
        template.id === action.payload.id ? action.payload : template
      )
    case DELETE_TEMPLATE.SUCCESS:
      return state.filter(template => template.id !== action.payload.id)
    case FETCH_TEMPLATES.RESET:
    case CREATE_TEMPLATE.RESET:
    case UPDATE_TEMPLATE.RESET:
    case DELETE_TEMPLATE.RESET:
      return []
    default:
      return state
  }
}

const currentTemplate = (state: Template | null = null, action: TemplateAction) => {
  switch (action.type) {
    case FETCH_TEMPLATE_BY_ID.SUCCESS:
      return action.payload
    case CREATE_TEMPLATE.SUCCESS:
    case UPDATE_TEMPLATE.SUCCESS:
      return action.payload
    case FETCH_TEMPLATE_BY_ID.RESET:
    case DELETE_TEMPLATE.SUCCESS:
      return null
    default:
      return state
  }
}

const searchResults = (state: Template[] = [], action: TemplateAction) => {
  switch (action.type) {
    case SEARCH_TEMPLATES.SUCCESS:
      return action.payload || []
    case SEARCH_TEMPLATES.RESET:
      return []
    default:
      return state
  }
}

const loading = (state = false, action: TemplateAction) => {
  switch (action.type) {
    case FETCH_TEMPLATES.REQUEST:
    case CREATE_TEMPLATE.REQUEST:
    case UPDATE_TEMPLATE.REQUEST:
    case DELETE_TEMPLATE.REQUEST:
    case SEARCH_TEMPLATES.REQUEST:
    case FETCH_TEMPLATE_BY_ID.REQUEST:
      return true
    case FETCH_TEMPLATES.SUCCESS:
    case FETCH_TEMPLATES.FAILURE:
    case CREATE_TEMPLATE.SUCCESS:
    case CREATE_TEMPLATE.FAILURE:
    case UPDATE_TEMPLATE.SUCCESS:
    case UPDATE_TEMPLATE.FAILURE:
    case DELETE_TEMPLATE.SUCCESS:
    case DELETE_TEMPLATE.FAILURE:
    case SEARCH_TEMPLATES.SUCCESS:
    case SEARCH_TEMPLATES.FAILURE:
    case FETCH_TEMPLATE_BY_ID.SUCCESS:
    case FETCH_TEMPLATE_BY_ID.FAILURE:
      return false
    case FETCH_TEMPLATES.RESET:
    case CREATE_TEMPLATE.RESET:
    case UPDATE_TEMPLATE.RESET:
    case DELETE_TEMPLATE.RESET:
    case SEARCH_TEMPLATES.RESET:
    case FETCH_TEMPLATE_BY_ID.RESET:
      return false
    default:
      return state
  }
}

const error = (state: string | null = null, action: TemplateAction) => {
  switch (action.type) {
    case FETCH_TEMPLATES.FAILURE:
    case CREATE_TEMPLATE.FAILURE:
    case UPDATE_TEMPLATE.FAILURE:
    case DELETE_TEMPLATE.FAILURE:
    case SEARCH_TEMPLATES.FAILURE:
    case FETCH_TEMPLATE_BY_ID.FAILURE:
      return action.payload?.message || action.error || 'An error occurred'
    case FETCH_TEMPLATES.REQUEST:
    case CREATE_TEMPLATE.REQUEST:
    case UPDATE_TEMPLATE.REQUEST:
    case DELETE_TEMPLATE.REQUEST:
    case SEARCH_TEMPLATES.REQUEST:
    case FETCH_TEMPLATE_BY_ID.REQUEST:
    case FETCH_TEMPLATES.SUCCESS:
    case CREATE_TEMPLATE.SUCCESS:
    case UPDATE_TEMPLATE.SUCCESS:
    case DELETE_TEMPLATE.SUCCESS:
    case SEARCH_TEMPLATES.SUCCESS:
    case FETCH_TEMPLATE_BY_ID.SUCCESS:
      return null
    case FETCH_TEMPLATES.RESET:
    case CREATE_TEMPLATE.RESET:
    case UPDATE_TEMPLATE.RESET:
    case DELETE_TEMPLATE.RESET:
    case SEARCH_TEMPLATES.RESET:
    case FETCH_TEMPLATE_BY_ID.RESET:
      return null
    default:
      return state
  }
}

export default () => combineReducers({
  templates,
  currentTemplate,
  searchResults,
  loading,
  error
})

// Selectors
export const getTemplatesState = (state: any) => state.entities.templates
export const getTemplates = (state: any) => state.entities.templates.templates
export const getCurrentTemplate = (state: any) => state.entities.templates.currentTemplate
export const getSearchResults = (state: any) => state.entities.templates.searchResults
export const getTemplatesLoading = (state: any) => state.entities.templates.loading
export const getTemplatesError = (state: any) => state.entities.templates.error 