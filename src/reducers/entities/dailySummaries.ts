import { combineReducers } from 'redux';
import { GET_DAILY_SUMMARIES, MONTHLY_SUMMARIES, WEEKLY_SUMMARIES } from '../../actions/actiontypes';
import { Actions } from '../../actions/Types';
import { RootState } from '../../configureStore';
import { IDailySummariesResponse } from '../../models/daily-summaries.model';

interface IMonthlySummariesResponse {
  data: Array<{
    id: number;
    Month: string;
    summary: string;
  }>;
}

interface IWeeklySummariesResponse {
  data: Array<{
    id: number;
    Week_number: string;
    summary: string;
  }>;
}

const entity = () => {
  const getDailySummariesData = (state: IDailySummariesResponse | null = null, action: Actions) => {
    switch (action.type) {
      case GET_DAILY_SUMMARIES.SUCCESS:
        return action.payload?.response || state;
      case GET_DAILY_SUMMARIES.REQUEST:
      case GET_DAILY_SUMMARIES.FAILURE:
        return null;
      default:
        return state;
    }
  };

  const getMonthlySummariesData = (state: IMonthlySummariesResponse | null = null, action: Actions) => {
    switch (action.type) {
      case MONTHLY_SUMMARIES.SUCCESS:
        return action.payload?.response || state;
      case MONTHLY_SUMMARIES.REQUEST:
      case MONTHLY_SUMMARIES.FAILURE:
        return null;
      default:
        return state;
    }
  };

  const getWeeklySummariesData = (state: IWeeklySummariesResponse | null = null, action: Actions) => {
    switch (action.type) {
      case WEEKLY_SUMMARIES.SUCCESS:
        return action.payload?.response || state;
      case WEEKLY_SUMMARIES.REQUEST:
      case WEEKLY_SUMMARIES.FAILURE:
        return null;
      default:
        return state;
    }
  };

  return combineReducers({
    getDailySummariesData,
    getMonthlySummariesData,
    getWeeklySummariesData
  });
};

export default entity;

export const getDailySummaries = (state: RootState) => state.entities.dailySummaries;
export const getMonthlySummaries = (state: RootState) => state.entities.dailySummaries;
export const getWeeklySummaries = (state: RootState) => state.entities.dailySummaries;
