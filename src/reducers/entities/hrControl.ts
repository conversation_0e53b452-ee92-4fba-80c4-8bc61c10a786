import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions as actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import { GET_EMP_DESIGNATIONS, SUCCESS, REQUEST, CREATE_HOLIDAY, CREATE_TIMING, CREATE_QAULIFICATION, CREATE_LEAVES, GET_LEAVE_INFO, FETCH_LEAVE_TYPES, GET_CATEGORY, CREATE_LEAVE_TYPE } from '../../actions/actiontypes'

const entity = () => {
    
  const getEmpDesignations = (state = [], action: actions) => {
    switch (action.type) {
      case GET_EMP_DESIGNATIONS[SUCCESS]:
        return action.payload
      case GET_EMP_DESIGNATIONS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getLeaveTypes = (state = [], action: actions) => {
    switch (action.type) {
      case FETCH_LEAVE_TYPES[SUCCESS]:
        return action.payload
      case FETCH_LEAVE_TYPES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getEmpCreatedHolidays = (state = [], action: actions) => {
    switch (action.type) {
      case CREATE_HOLIDAY[SUCCESS]:
        return action.payload
      case CREATE_HOLIDAY[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getEmpCreatedLeaves = (state = [], action: actions) => {
    switch (action.type) {
      case CREATE_LEAVES[SUCCESS]:
        return action.payload
      case CREATE_LEAVES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getEmpLeavesInfo = (state = [], action: actions) => {
    switch (action.type) {
      case GET_LEAVE_INFO[SUCCESS]:
        return action.payload
      case GET_LEAVE_INFO[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCategoryData = (state = [], action: actions) => {
    switch (action.type) {
      case GET_CATEGORY[SUCCESS]:
        return action.payload
      case GET_CATEGORY[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCreateTiming = (state = [], action: actions) => {
    switch (action.type) {
      case CREATE_TIMING[SUCCESS]:
        return action.payload
      case CREATE_TIMING[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCreateQualification = (state = [], action: actions) => {
    switch (action.type) {
      case CREATE_QAULIFICATION[SUCCESS]:
        return action.payload
      case CREATE_QAULIFICATION[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCreateLeaveTypes = (state = [], action: actions) => {
    switch (action.type) {
      case CREATE_LEAVE_TYPE[SUCCESS]:
        return action.payload
      case CREATE_LEAVE_TYPE[REQUEST]:
        return []
      default:
        return state
    }
  }

  return combineReducers({
    getEmpDesignations,
    getCreateTiming,
    getEmpCreatedHolidays,
    getCreateQualification,
    getEmpCreatedLeaves,
    getEmpLeavesInfo,
    getLeaveTypes,
    getCategoryData,
    getCreateLeaveTypes
  })
}

export default entity

export const fetchHrControlData = (state: RootState) => state.entities.hrControl
