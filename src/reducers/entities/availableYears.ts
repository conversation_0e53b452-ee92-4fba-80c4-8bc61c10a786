import { combineReducers } from 'redux';
import { GET_AVAILABLE_YEARS } from '../../actions/actiontypes';
import { Actions } from '../../actions/Types';
import { RootState } from '../../configureStore';

interface IAvailableYearsResponse {
  data: Array<{
    id: number;
    year: string;
  }>;
}

const entity = () => {
  const getAvailableYearsData = (state: IAvailableYearsResponse | null = null, action: Actions) => {
    switch (action.type) {
      case GET_AVAILABLE_YEARS.SUCCESS:
        return action.payload?.response || state;
      case GET_AVAILABLE_YEARS.REQUEST:
        return null;
      case GET_AVAILABLE_YEARS.FAILURE:
        return null;
      default:
        return state;
    }
  };

  return combineReducers({
    getAvailableYearsData
  });
};

export default entity;

export const getAvailableYears = (state: RootState) => state.entities.availableYears;