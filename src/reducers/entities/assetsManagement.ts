import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions as actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import { ADD_ASSET_MAKE, ADD_ASSET_STATUS, ADD_NEW_ASSET, ASSET_ABBERATION, ASSET_ABBREVATION_COUNT, ASSET_CATEGORY, ASSET_LOCATION, ASSET_<PERSON><PERSON>, ASSET_MAKE_DATA, ASSET_OS, ASSET_OS_DATA, ASSET_STATUS, ASSET_STATUS_COUNT, ASSET_STATUS_DATA, ASSET_TECHNOLOGIES, ASSETS_DATA, ASSIGN_ASSET, ASSIGN_ASSET_REPORT, DOWNLOAD_ASSETS_QR, DOWNLOAD_QR, EMPLOYEE_ASSETS_COUNT, EMPLOYEE_ASSETS_DETAILS, <PERSON><PERSON><PERSON>_ASSETS, <PERSON>ET<PERSON>_EMP_DATA, FETCH_NUMBER_OF_ASSETS, TECH<PERSON><PERSON>OGIES_DATA, UNALLOCATE_ASSET, } from '../../actions/actiontypes'

const { SUCCESS, REQUEST, FAILURE, RESET } = actionTypes

const entity = () => {
    const getAssetsData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSETS_DATA[SUCCESS]:
            return action.payload
          case ASSETS_DATA[REQUEST]:
          case ASSETS_DATA[RESET]:
          case ASSETS_DATA[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssignAsset = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSIGN_ASSET[SUCCESS]:
            return action.payload
          case ASSIGN_ASSET[REQUEST]:
          case ASSIGN_ASSET[RESET]:
          case ASSIGN_ASSET[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getUnallocateAsset = (state = {}, action: actions) => {
        switch (action.type) {
          case UNALLOCATE_ASSET[SUCCESS]:
            return action.payload
          case UNALLOCATE_ASSET[REQUEST]:
          case UNALLOCATE_ASSET[RESET]:
          case UNALLOCATE_ASSET[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAddNewAsset = (state = {}, action: actions) => {
        switch (action.type) {
          case ADD_NEW_ASSET[SUCCESS]:
            return action.payload
          case ADD_NEW_ASSET[REQUEST]:
          case ADD_NEW_ASSET[RESET]:
          case ADD_NEW_ASSET[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getNumberOfAssetsData = (state = {}, action: actions) => {
        switch (action.type) {
          case FETCH_NUMBER_OF_ASSETS[SUCCESS]:
            return action.payload
          case FETCH_NUMBER_OF_ASSETS[REQUEST]:
          case FETCH_NUMBER_OF_ASSETS[RESET]:
          case FETCH_NUMBER_OF_ASSETS[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getEmpData = (state = [], action: actions) => {
        switch (action.type) {
          case FETCH_EMP_DATA[SUCCESS]:
            return action.payload
          case FETCH_EMP_DATA[REQUEST]:
          case FETCH_EMP_DATA[RESET]:
          case FETCH_EMP_DATA[FAILURE]:
            return []
          default:
            return state
        }
      }

    
      const getAllAssetList = (state = [], action: actions) => {
        switch (action.type) {
          case FETCH_ASSETS[SUCCESS]:
            return action.payload
          case FETCH_ASSETS[REQUEST]:
          case FETCH_ASSETS[RESET]:
          case FETCH_ASSETS[FAILURE]:
            return []
          default:
            return state
        }
      }

      const getEmpAssetsData = (state = [], action: actions) => {
        switch (action.type) {
          case EMPLOYEE_ASSETS_DETAILS[SUCCESS]:
            return action.payload
          case EMPLOYEE_ASSETS_DETAILS[REQUEST]:
          case EMPLOYEE_ASSETS_DETAILS[RESET]:
          case EMPLOYEE_ASSETS_DETAILS[FAILURE]:
            return []
          default:
            return state
        }
      }

      const getAssetAbbeiration = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_ABBERATION[SUCCESS]:
            return action.payload
          case ASSET_ABBERATION[REQUEST]:
          case ASSET_ABBERATION[RESET]:
          case ASSET_ABBERATION[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetLocation = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_LOCATION[SUCCESS]:
            return action.payload
          case ASSET_LOCATION[REQUEST]:
          case ASSET_LOCATION[RESET]:
          case ASSET_LOCATION[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetMake = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_MAKE[SUCCESS]:
            return action.payload
          case ASSET_MAKE[REQUEST]:
          case ASSET_MAKE[RESET]:
          case ASSET_MAKE[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetStatus = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_STATUS[SUCCESS]:
            return action.payload
          case ASSET_STATUS[REQUEST]:
          case ASSET_STATUS[RESET]:
          case ASSET_STATUS[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetOS = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_OS[SUCCESS]:
            return action.payload
          case ASSET_OS[REQUEST]:
          case ASSET_OS[RESET]:
          case ASSET_OS[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetTechnologies = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_TECHNOLOGIES[SUCCESS]:
            return action.payload
          case ASSET_TECHNOLOGIES[REQUEST]:
          case ASSET_TECHNOLOGIES[RESET]:
          case ASSET_TECHNOLOGIES[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetCategoryData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_CATEGORY[SUCCESS]:
            return action.payload
          case ASSET_CATEGORY[REQUEST]:
          case ASSET_CATEGORY[RESET]:
          case ASSET_CATEGORY[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetOSData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_OS_DATA[SUCCESS]:
            return action.payload
          case ASSET_OS_DATA[REQUEST]:
          case ASSET_OS_DATA[RESET]:
          case ASSET_OS_DATA[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetMakeData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_MAKE_DATA[SUCCESS]:
            return action.payload
          case ASSET_MAKE_DATA[REQUEST]:
          case ASSET_MAKE_DATA[RESET]:
          case ASSET_MAKE_DATA[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const addAssetMake = (state = {}, action: actions) => {
        switch (action.type) {
          case ADD_ASSET_MAKE[SUCCESS]:
            return action.payload
          case ADD_ASSET_MAKE[REQUEST]:
          case ADD_ASSET_MAKE[RESET]:
          case ADD_ASSET_MAKE[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetStatusData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_STATUS_DATA[SUCCESS]:
            return action.payload
          case ASSET_STATUS_DATA[REQUEST]:
          case ASSET_STATUS_DATA[RESET]:
          case ASSET_STATUS_DATA[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const addAssetStatus = (state = {}, action: actions) => {
        switch (action.type) {
          case ADD_ASSET_STATUS[SUCCESS]:
            return action.payload
          case ADD_ASSET_STATUS[REQUEST]:
          case ADD_ASSET_STATUS[RESET]:
          case ADD_ASSET_STATUS[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getTechnologiesData = (state = {}, action: actions) => {
        switch (action.type) {
          case TECHNOLOGIES_DATA[SUCCESS]:
            return action.payload
          case TECHNOLOGIES_DATA[REQUEST]:
          case TECHNOLOGIES_DATA[RESET]:
          case TECHNOLOGIES_DATA[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getEmployeeAssetsCountData = (state = {}, action: actions) => {
        switch (action.type) {
          case EMPLOYEE_ASSETS_COUNT[SUCCESS]:
            return action.payload
          case EMPLOYEE_ASSETS_COUNT[REQUEST]:
          case EMPLOYEE_ASSETS_COUNT[RESET]:
          case EMPLOYEE_ASSETS_COUNT[FAILURE]:
            return {}
          default:
            return state
        }
      }


      const getAssetAbbrevationCountData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_ABBREVATION_COUNT[SUCCESS]:
            return action.payload
          case ASSET_ABBREVATION_COUNT[REQUEST]:
          case ASSET_ABBREVATION_COUNT[RESET]:
          case ASSET_ABBREVATION_COUNT[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssetStatusCountData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSET_STATUS_COUNT[SUCCESS]:
            return action.payload
          case ASSET_STATUS_COUNT[REQUEST]:
          case ASSET_STATUS_COUNT[RESET]:
          case ASSET_STATUS_COUNT[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getAssignAssetReportData = (state = {}, action: actions) => {
        switch (action.type) {
          case ASSIGN_ASSET_REPORT[SUCCESS]:
            return action.payload
          case ASSIGN_ASSET_REPORT[REQUEST]:
          case ASSIGN_ASSET_REPORT[RESET]:
          case ASSIGN_ASSET_REPORT[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const getDownloadAssetsQrData = (state = {}, action: actions) => {
        switch (action.type) {
          case DOWNLOAD_ASSETS_QR[SUCCESS]:
            return action.payload
          case DOWNLOAD_ASSETS_QR[REQUEST]:
          case DOWNLOAD_ASSETS_QR[RESET]:
          case DOWNLOAD_ASSETS_QR[FAILURE]:
            return {}
          default:
            return state
        }
      }

      const downloadAssetsQr = (state = {}, action: actions) => {
        switch (action.type) {
          case DOWNLOAD_QR[SUCCESS]:
            return action.payload
          case DOWNLOAD_QR[REQUEST]:
          case DOWNLOAD_QR[RESET]:
          case DOWNLOAD_QR[FAILURE]:
            return {}
          default:
            return state
        }
      }

      




  return combineReducers({
    getAssetsData,
    getNumberOfAssetsData,
    getEmpData,
    getAllAssetList,
    getEmpAssetsData,
    getAssignAsset,
    getUnallocateAsset,
    getAddNewAsset,
    getAssetAbbeiration,
    getAssetLocation,
    getAssetMake,
    getAssetStatus,
    getAssetOS,
    getAssetTechnologies,
    getAssetCategoryData,
    getAssetOSData,
    getAssetMakeData,
    addAssetMake,
    getAssetStatusData,
    addAssetStatus,
    getTechnologiesData,
    getEmployeeAssetsCountData,
    getAssetAbbrevationCountData,
    getAssetStatusCountData,
    getAssignAssetReportData,
    getDownloadAssetsQrData,
    downloadAssetsQr,
  })
}

export default entity

export const getAssetsPortal = (state: RootState) => state.entities.assetsPortal
