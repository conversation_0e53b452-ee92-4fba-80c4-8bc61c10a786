import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  CREATE_EXPENSE,
  DOWNLOAD_ATTACHMENTS,
  EXPENSE_STATUS_UPDATE,
  FAILURE,
  PERTICULAR_EXPENSE,
  REIMBURSEMENT_REQUESTS,
  RESET,
  SUCCESS,
} from '../../actions/actiontypes'

const { REQUEST } = actionTypes

const ui = () => {
  const isGettingReimburseRequest = (state = false, action: Actions) => {
    switch (action.type) {
      case REIMBURSEMENT_REQUESTS[REQUEST]:
        return true
      default:
        return false
    }
  }

  const isGettingPerticularExpense = (state = false, action: Actions) => {
    switch (action.type) {
      case PERTICULAR_EXPENSE[REQUEST]:
        return true
      default:
        return false
    }
  }

  const isGettingExpenseUpdate = (state = false, action: Actions) => {
    switch (action.type) {
      case EXPENSE_STATUS_UPDATE[REQUEST]:
        return true
      default:
        return false
    }
  }

  const createExpense = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_EXPENSE[REQUEST]:
        return true
      case CREATE_EXPENSE[SUCCESS]:
      case CREATE_EXPENSE[RESET]:
      case CREATE_EXPENSE[FAILURE]:
        return false
      default:
        return state
    }
  }

  const downloadAttachments = (state = false, action: Actions) => {
    switch (action.type) {
      case DOWNLOAD_ATTACHMENTS[REQUEST]:
        return true
      case DOWNLOAD_ATTACHMENTS[SUCCESS]:
      case DOWNLOAD_ATTACHMENTS[RESET]:
      case DOWNLOAD_ATTACHMENTS[FAILURE]:
        return false
      default:
        return state
    }
  }

  return combineReducers({
    isGettingReimburseRequest,
    isGettingPerticularExpense,
    isGettingExpenseUpdate,
    createExpense,
    downloadAttachments
  })
}

export default ui

export const getExpenseDeatilsUI = (state: RootState) => state.ui.getExpenseDeatilsUI
