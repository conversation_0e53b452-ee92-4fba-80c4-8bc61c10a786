import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  FETCH_EMPLOYEE_DETAILS,
  GET_LEAVE_REPORT,
  FETCH_ATTENDANCE_REPORT,
  GET_LEAVE_BALANCE_REPORT,
  GET_LEAVE_TYPE_REPORT,
  GET_LEAVE_ALLOCATED_REPORT,
  GET_LEAVE_ENCASHMENT_REPORT,
  GET_QUATRES,
} from '../../actions/actiontypes'

const { SUCCESS, REQUEST, FAILURE } = actionTypes

const ui = () => {
  const isGetAllEmployeeList = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_EMPLOYEE_DETAILS[SUCCESS]:
        return true
      case FETCH_EMPLOYEE_DETAILS[FAILURE]:
      case FETCH_EMPLOYEE_DETAILS[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isGetLeavesReportDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_REPORT[SUCCESS]:
        return true
      case GET_LEAVE_REPORT[FAILURE]:
      case GET_LEAVE_REPORT[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isAttendanceReportloded = (state = false, action: Actions) => {
      switch (action.type) {
        case FETCH_ATTENDANCE_REPORT[SUCCESS]:
          return true
        case FETCH_ATTENDANCE_REPORT[FAILURE]:
          return false
        case FETCH_ATTENDANCE_REPORT[REQUEST]:
          return false
        default:
          return state
      }
    }

  const isGetLeavesReportBalanceDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_BALANCE_REPORT[SUCCESS]:
        return true
      case GET_LEAVE_BALANCE_REPORT[FAILURE]:
      case GET_LEAVE_BALANCE_REPORT[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isGetLeavesReportTypeDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_TYPE_REPORT[SUCCESS]:
        return true
      case GET_LEAVE_TYPE_REPORT[FAILURE]:
      case GET_LEAVE_TYPE_REPORT[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isGetLeavesReportAllocatedDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_ALLOCATED_REPORT[SUCCESS]:
        return true
      case GET_LEAVE_ALLOCATED_REPORT[FAILURE]:
      case GET_LEAVE_ALLOCATED_REPORT[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isGetLeavesReportEncashmentDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_ENCASHMENT_REPORT[SUCCESS]:
        return true
      case GET_LEAVE_ENCASHMENT_REPORT[FAILURE]:
      case GET_LEAVE_ENCASHMENT_REPORT[REQUEST]:
        return false
      default:
        return state
    }
  }

  const leaveReportLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_REPORT[REQUEST]:
      case GET_LEAVE_BALANCE_REPORT[REQUEST]:
      case GET_LEAVE_TYPE_REPORT[REQUEST]:
      case GET_LEAVE_ALLOCATED_REPORT[REQUEST]:
      case GET_LEAVE_ENCASHMENT_REPORT[REQUEST]:
        return true
      case GET_LEAVE_REPORT[SUCCESS]:
      case GET_LEAVE_BALANCE_REPORT[SUCCESS]:
      case GET_LEAVE_TYPE_REPORT[SUCCESS]:
      case GET_LEAVE_ALLOCATED_REPORT[SUCCESS]:
      case GET_LEAVE_ENCASHMENT_REPORT[SUCCESS]:
        return false
      case GET_LEAVE_REPORT[FAILURE]:
      case GET_LEAVE_BALANCE_REPORT[FAILURE]:
      case GET_LEAVE_TYPE_REPORT[FAILURE]:
      case GET_LEAVE_ALLOCATED_REPORT[FAILURE]:
      case GET_LEAVE_ENCASHMENT_REPORT[FAILURE]:
        return false
      default:
        return state
    }
  }

  const isGetLeavesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_LEAVE_REPORT[FAILURE]:
      case GET_LEAVE_REPORT[SUCCESS]:
        return false
      case GET_LEAVE_REPORT[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isGetAllEmployeeListFailed = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_EMPLOYEE_DETAILS[SUCCESS]:
      case FETCH_EMPLOYEE_DETAILS[REQUEST]:
        return false
      case FETCH_EMPLOYEE_DETAILS[FAILURE]:
        return true
      default:
        return state
    }
  }

  const getQuatre = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_QUATRES[SUCCESS]:
      case GET_QUATRES[REQUEST]:
        return false
      case GET_QUATRES[FAILURE]:
        return true
      default:
        return state
    }
  }

  return combineReducers({
    isGetAllEmployeeList,
    isGetAllEmployeeListFailed,
    isGetLeavesReportDone,
    isGetLeavesLoader,
    isGetLeavesReportBalanceDone,
    isAttendanceReportloded,
    isGetLeavesReportTypeDone,
    isGetLeavesReportAllocatedDone,
    isGetLeavesReportEncashmentDone,
    leaveReportLoader,
    getQuatre,
  })
}

export default ui

export const getAllEmpList = (state: RootState) => state.ui.employeeList
