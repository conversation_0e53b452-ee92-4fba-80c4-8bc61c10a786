import { combineReducers } from 'redux';
import { GET_AVAILABLE_YEARS } from '../../actions/actiontypes';
import { Actions } from '../../actions/Types';
import { RootState } from '../../configureStore';

const ui = () => {
  const isGetAvailableYearsLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_AVAILABLE_YEARS.REQUEST:
        return true;
      case GET_AVAILABLE_YEARS.SUCCESS:
      case GET_AVAILABLE_YEARS.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetAvailableYearsSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_AVAILABLE_YEARS.SUCCESS:
        return true;
      case GET_AVAILABLE_YEARS.REQUEST:
      case GET_AVAILABLE_YEARS.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetAvailableYearsError = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_AVAILABLE_YEARS.FAILURE:
        return true;
      case GET_AVAILABLE_YEARS.REQUEST:
      case GET_AVAILABLE_YEARS.SUCCESS:
        return false;
      default:
        return state;
    }
  };

  return combineReducers({
    isGetAvailableYearsLoader,
    isGetAvailableYearsSuccess,
    isGetAvailableYearsError
  });
};

export default ui;

export const getAvailableYearsUI = (state: RootState) => state.ui.availableYears;