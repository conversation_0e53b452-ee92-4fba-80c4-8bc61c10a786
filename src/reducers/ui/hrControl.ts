import { combineReducers } from 'redux'
import { actionTypes } from '../../actions'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  CREATE_EMP_DESIGNATIONS,
  EDIT_DESIGNATIONS,
  GET_EMP_DESIGNATIONS,
  CREATE_HOLIDAY,
  RESET,
  CREATE_TIMING,
  CREATE_QAULIFICATION,
  CREATE_LEAVES,
  FETCH_LEAVE_TYPES,
  CREATE_LEAVE_TYPE,
} from '../../actions/actiontypes'

const { SUCCESS, REQUEST, FAILURE, LOGIN_USER } = actionTypes

const ui = () => {
  const isEmpDesignationDone = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_EMP_DESIGNATIONS[SUCCESS]:
        return true
      case GET_EMP_DESIGNATIONS[FAILURE]:
      case GET_EMP_DESIGNATIONS[REQUEST]:
        return false
      case GET_EMP_DESIGNATIONS[RESET]:
        return false
      default:
        return state
    }
  }

  const isEmpDesignationLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_EMP_DESIGNATIONS[SUCCESS]:
        return false
      case GET_EMP_DESIGNATIONS[FAILURE]:
      case GET_EMP_DESIGNATIONS[RESET]:
        return false
      case GET_EMP_DESIGNATIONS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCreateDesignationDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_EMP_DESIGNATIONS[SUCCESS]:
        return true
      case CREATE_EMP_DESIGNATIONS[FAILURE]:
      case CREATE_EMP_DESIGNATIONS[REQUEST]:
        return false
      case CREATE_EMP_DESIGNATIONS[RESET]:
        return false
      default:
        return state
    }
  }

  const isCreateDesignationLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_EMP_DESIGNATIONS[SUCCESS]:
        return false
      case CREATE_EMP_DESIGNATIONS[FAILURE]:
      case CREATE_EMP_DESIGNATIONS[RESET]:
        return false
      case CREATE_EMP_DESIGNATIONS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCreateLeavesDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_LEAVES[SUCCESS]:
        return true
      case CREATE_LEAVES[FAILURE]:
      case CREATE_LEAVES[REQUEST]:
        return false
      case CREATE_LEAVES[RESET]:
        return false
      default:
        return state
    }
  }

  const isCreateLeavesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_LEAVES[SUCCESS]:
        return false
      case CREATE_LEAVES[FAILURE]:
      case CREATE_LEAVES[RESET]:
        return false
      case CREATE_LEAVES[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isEditDesignationDone = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_DESIGNATIONS[SUCCESS]:
        return true
      case EDIT_DESIGNATIONS[FAILURE]:
      case EDIT_DESIGNATIONS[REQUEST]:
        return false
      case EDIT_DESIGNATIONS[RESET]:
        return false
      default:
        return state
    }
  }

  const isEditDesignationLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_DESIGNATIONS[SUCCESS]:
        return false
      case EDIT_DESIGNATIONS[FAILURE]:
      case EDIT_DESIGNATIONS[RESET]:
        return false
      case EDIT_DESIGNATIONS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCreateTimingDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_TIMING[SUCCESS]:
        return true
      case CREATE_TIMING[FAILURE]:
      case CREATE_TIMING[REQUEST]:
        return false
      case CREATE_TIMING[RESET]:
        return false
      default:
        return state
    }
  }

  const isCreateTimingLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_TIMING[SUCCESS]:
        return false
      case CREATE_TIMING[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCreateHolidayDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_HOLIDAY[SUCCESS]:
        return true
      case CREATE_HOLIDAY[FAILURE]:
      case CREATE_HOLIDAY[REQUEST]:
        return false
      case CREATE_HOLIDAY[RESET]:
        return false
      default:
        return state
    }
  }

  const isCreateHolidayLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_HOLIDAY[SUCCESS]:
        return false
      case CREATE_HOLIDAY[FAILURE]:
      case CREATE_HOLIDAY[RESET]:
        return false
      case CREATE_HOLIDAY[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCreateQualificationDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_QAULIFICATION[SUCCESS]:
        return true
      case CREATE_QAULIFICATION[FAILURE]:
      case CREATE_QAULIFICATION[REQUEST]:
        return false
      case CREATE_QAULIFICATION[RESET]:
        return false
      default:
        return state
    }
  }

  const isCreateQualificationLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_QAULIFICATION[SUCCESS]:
        return false
      case CREATE_QAULIFICATION[FAILURE]:
      case CREATE_QAULIFICATION[RESET]:
        return false
      case CREATE_QAULIFICATION[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isFetchLeaveTypesDone = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_LEAVE_TYPES[SUCCESS]:
        return true
      case FETCH_LEAVE_TYPES[FAILURE]:
      case FETCH_LEAVE_TYPES[REQUEST]:
        return false
      case FETCH_LEAVE_TYPES[RESET]:
        return false
      default:
        return state
    }
  }

  const isFetchLeaveTypesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_LEAVE_TYPES[SUCCESS]:
        return false
      case FETCH_LEAVE_TYPES[FAILURE]:
      case FETCH_LEAVE_TYPES[RESET]:
        return false
      case FETCH_LEAVE_TYPES[REQUEST]:
        return true
      default:
        return state
    }
  }


  const isAddLeaveTypesDone = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_LEAVE_TYPE[SUCCESS]:
        return true
      case CREATE_LEAVE_TYPE[FAILURE]:
      case CREATE_LEAVE_TYPE[REQUEST]:
        return false
      case CREATE_LEAVE_TYPE[RESET]:
        return false
      default:
        return state
    }
  }

  const isAddLeaveTypesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case CREATE_LEAVE_TYPE[SUCCESS]:
        return false
      case CREATE_LEAVE_TYPE[FAILURE]:
      case CREATE_LEAVE_TYPE[RESET]:
        return false
      case CREATE_LEAVE_TYPE[REQUEST]:
        return true
      default:
        return state
    }
  }

  return combineReducers({
    isEmpDesignationDone,
    isEmpDesignationLoader,
    isCreateDesignationLoader,
    isCreateDesignationDone,
    isEditDesignationDone,
    isEditDesignationLoader,
    isCreateHolidayDone,
    isCreateHolidayLoader,
    isCreateTimingDone,
    isCreateTimingLoader,
    isCreateQualificationDone,
    isCreateQualificationLoader,
    isCreateLeavesDone,
    isCreateLeavesLoader,
    isFetchLeaveTypesDone,
    isFetchLeaveTypesLoader,
    isAddLeaveTypesDone,
    isAddLeaveTypesLoader
  })
}

export default ui

export const fetchHrControlData = (state: RootState) => state.ui.fetchHrControlUI
