import { combineReducers } from 'redux'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import { ADD_ASSET_MAKE, ADD_ASSET_STATUS, ADD_NEW_ASSET, ADD_TECHNOLOGIES, ASSET_CATEGORY, ASSET_CATEGORY_DELETE, ASSET_CATEGORY_UPDATE, ASSET_MAKE_DATA, ASSET_MAKE_DELETE, ASSET_OS_DATA, ASSET_OS_UPDATE, ASSET_STATUS_DATA, ASSET_STATUS_DELETE, ASSETS_DATA, ASSIGN_ASSET, DELETE_ASSET_OS, DELETE_TECHNOLOGIES, DOWNLOAD_QR, EMPLOYEE_ASSETS_DETAILS, FAILURE, FETCH_ASSETS, FETCH_EMP_DATA, FETCH_NUMBER_OF_ASSETS, REQUEST, RESET, SUCCESS, TECHNOLOGIES_DATA, UNALLOCATE_ASSET } from '../../actions/actiontypes'

const ui = () => {

  const isAssetsPortalLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSETS_DATA[SUCCESS]:
      case ASSETS_DATA[FAILURE]:
      case ASSETS_DATA[RESET]:
        return false
      case ASSETS_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isNumberOfAssetsLodder = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_NUMBER_OF_ASSETS[SUCCESS]:
      case FETCH_NUMBER_OF_ASSETS[FAILURE]:
      case FETCH_NUMBER_OF_ASSETS[RESET]:
        return false
      case FETCH_NUMBER_OF_ASSETS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isEmpData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_EMP_DATA[SUCCESS]:
      case FETCH_EMP_DATA[FAILURE]:
      case FETCH_EMP_DATA[RESET]:
        return false
      case FETCH_EMP_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isAssetList = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ASSETS[SUCCESS]:
      case FETCH_ASSETS[FAILURE]:
      case FETCH_ASSETS[RESET]:
        return false
      case FETCH_ASSETS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isEmpAssetsData = (state = false, action: Actions) => {
    switch (action.type) {
      case EMPLOYEE_ASSETS_DETAILS[SUCCESS]:
      case EMPLOYEE_ASSETS_DETAILS[FAILURE]:
      case EMPLOYEE_ASSETS_DETAILS[RESET]:
        return false
      case EMPLOYEE_ASSETS_DETAILS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isEmpAssetsDataSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case EMPLOYEE_ASSETS_DETAILS[SUCCESS]:
        return true
      case EMPLOYEE_ASSETS_DETAILS[FAILURE]:
      case EMPLOYEE_ASSETS_DETAILS[RESET]:
      case EMPLOYEE_ASSETS_DETAILS[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isAssignAssetLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSIGN_ASSET[SUCCESS]:
      case ASSIGN_ASSET[FAILURE]:
      case ASSIGN_ASSET[RESET]:
        return false
      case ASSIGN_ASSET[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isAssignAssetSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSIGN_ASSET[REQUEST]:
      case ASSIGN_ASSET[FAILURE]:
      case ASSIGN_ASSET[RESET]:
        return false
      case ASSIGN_ASSET[SUCCESS]:
        return true
      default:
        return state
    }
  }

  const isAddNewAssetLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_NEW_ASSET[SUCCESS]:
      case ADD_NEW_ASSET[FAILURE]:
      case ADD_NEW_ASSET[RESET]:
        return false
      case ADD_NEW_ASSET[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isAddNewAssetSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_NEW_ASSET[REQUEST]:
      case ADD_NEW_ASSET[FAILURE]:
      case ADD_NEW_ASSET[RESET]:
        return false
      case ADD_NEW_ASSET[SUCCESS]:
        return true
      default:
        return state
    }
  }

  const isUnallocateAssetLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case UNALLOCATE_ASSET[SUCCESS]:
      case UNALLOCATE_ASSET[FAILURE]:
      case UNALLOCATE_ASSET[RESET]:
        return false
      case UNALLOCATE_ASSET[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isUnallocateAssetSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case UNALLOCATE_ASSET[SUCCESS]:
        return true
      case UNALLOCATE_ASSET[REQUEST]:
      case UNALLOCATE_ASSET[FAILURE]:
      case UNALLOCATE_ASSET[RESET]:
        return false
      default:
        return state
    }
  }

  const isAssetCategoreyLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_CATEGORY[SUCCESS]:
      case ASSET_CATEGORY[FAILURE]:
      case ASSET_CATEGORY[RESET]:
        return false
      case ASSET_CATEGORY[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isAssetOsLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_OS_DATA[SUCCESS]:
      case ASSET_OS_DATA[FAILURE]:
      case ASSET_OS_DATA[RESET]:
        return false
      case ASSET_OS_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const UpdateAssetOsSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_OS_UPDATE[SUCCESS]:
        return true
      case ASSET_OS_UPDATE[REQUEST]:
      case ASSET_OS_UPDATE[FAILURE]:
      case ASSET_OS_UPDATE[RESET]:
        return false
      default:
        return state
    }
  }

  const UpdateAssetCategorySuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_CATEGORY_UPDATE[SUCCESS]:
        return true
      case ASSET_CATEGORY_UPDATE[REQUEST]:
      case ASSET_CATEGORY_UPDATE[FAILURE]:
      case ASSET_CATEGORY_UPDATE[RESET]:
        return false
      default:
        return state
    }
  }

  const deleteAssetCategorySuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_CATEGORY_DELETE[SUCCESS]:
        return true
      case ASSET_CATEGORY_DELETE[REQUEST]:
      case ASSET_CATEGORY_DELETE[FAILURE]:
      case ASSET_CATEGORY_DELETE[RESET]:
        return false
      default:
        return state
    }
  }

  const isAssetMakeLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_MAKE_DATA[SUCCESS]:
      case ASSET_MAKE_DATA[FAILURE]:
      case ASSET_MAKE_DATA[RESET]:
        return false
      case ASSET_MAKE_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const addAssetMakeSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_ASSET_MAKE[SUCCESS]:
        return true
      case ADD_ASSET_MAKE[REQUEST]:
      case ADD_ASSET_MAKE[FAILURE]:
      case ADD_ASSET_MAKE[RESET]:
        return false
      default:
        return state
    }
  }

  const addAssetOsSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_OS_UPDATE[SUCCESS]:
        return true
      case ASSET_OS_UPDATE[REQUEST]:
      case ASSET_OS_UPDATE[FAILURE]:
      case ASSET_OS_UPDATE[RESET]:
        return false
      default:
        return state
    }
  }

  const deleteAssetMakeSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_MAKE_DELETE[SUCCESS]:
        return true
      case ASSET_MAKE_DELETE[REQUEST]:
      case ASSET_MAKE_DELETE[FAILURE]:
      case ASSET_MAKE_DELETE[RESET]:
        return false
      default:
        return state
    }
  }

  const isAssetStatusLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_STATUS_DATA[SUCCESS]:
      case ASSET_STATUS_DATA[FAILURE]:
      case ASSET_STATUS_DATA[RESET]:
        return false
      case ASSET_STATUS_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const addAssetStatusSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_ASSET_STATUS[SUCCESS]:
        return true
      case ADD_ASSET_STATUS[REQUEST]:
      case ADD_ASSET_STATUS[FAILURE]:
      case ADD_ASSET_STATUS[RESET]:
        return false
      default:
        return state
    }
  }

  const deleteAssetStatusSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ASSET_STATUS_DELETE[SUCCESS]:
        return true
      case ASSET_STATUS_DELETE[REQUEST]:
      case ASSET_STATUS_DELETE[FAILURE]:
      case ASSET_STATUS_DELETE[RESET]:
        return false
      default:
        return state
    }
  }

  

  const addTechnologiesSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_TECHNOLOGIES[SUCCESS]:
        return true
      case ADD_TECHNOLOGIES[REQUEST]:
      case ADD_TECHNOLOGIES[FAILURE]:
      case ADD_TECHNOLOGIES[RESET]:
        return false
      default:
        return state
    }
  }

  const isTechnologiesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case TECHNOLOGIES_DATA[SUCCESS]:
      case TECHNOLOGIES_DATA[FAILURE]:
      case TECHNOLOGIES_DATA[RESET]:
        return false
      case TECHNOLOGIES_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }


  const deleteTechnologiesSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_TECHNOLOGIES[SUCCESS]:
        return true
      case DELETE_TECHNOLOGIES[REQUEST]:
      case DELETE_TECHNOLOGIES[FAILURE]:
      case DELETE_TECHNOLOGIES[RESET]:
        return false
      default:
        return state
    }
  }


  const deleteAssetOsSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_ASSET_OS[SUCCESS]:
        return true
      case DELETE_ASSET_OS[REQUEST]:
      case DELETE_ASSET_OS[FAILURE]:
      case DELETE_ASSET_OS[RESET]:
        return false
      default:
        return state
    }
  }

  const isDownloadQrLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case DOWNLOAD_QR[SUCCESS]:
      case DOWNLOAD_QR[FAILURE]:
      case DOWNLOAD_QR[RESET]:
        return false
      case DOWNLOAD_QR[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isDownloadQrSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case DOWNLOAD_QR[SUCCESS]:
        return true
      case DOWNLOAD_QR[REQUEST]:
      case DOWNLOAD_QR[FAILURE]:
      case DOWNLOAD_QR[RESET]:
        return false
      default:
        return state
    }
  }

  
  return combineReducers({
    isNumberOfAssetsLodder,
    isEmpData,
    isAssetList,
    isEmpAssetsData,
    isAssignAssetLoader,
    isAssignAssetSuccess,
    isAddNewAssetLoader,
    isAddNewAssetSuccess,
    isUnallocateAssetLoader,
    isUnallocateAssetSuccess,
    isEmpAssetsDataSuccess,
    isAssetsPortalLoader,
    isAssetCategoreyLoader,
    isAssetOsLoader,
    UpdateAssetOsSuccess,
    UpdateAssetCategorySuccess,
    deleteAssetCategorySuccess,
    isAssetMakeLoader,
    addAssetMakeSuccess,
    deleteAssetMakeSuccess,
    isAssetStatusLoader,
    addAssetStatusSuccess,
    deleteAssetStatusSuccess,
    addTechnologiesSuccess,
    isTechnologiesLoader,
    deleteTechnologiesSuccess,
    deleteAssetOsSuccess,
    isDownloadQrLoader,
    isDownloadQrSuccess,
    addAssetOsSuccess,
  })
}

export default ui

export const getAllAssetsPortalUI = (state: RootState) => state.ui.assetsPortal
