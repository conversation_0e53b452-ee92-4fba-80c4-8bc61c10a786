import { combineReducers } from 'redux';
import { GET_DAILY_SUMMARIES, MONTHLY_SUMMARIES, WEEKLY_SUMMARIES } from '../../actions/actiontypes';
import { Actions } from '../../actions/Types';
import { RootState } from '../../configureStore';

const ui = () => {
  const isGetDailySummariesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_DAILY_SUMMARIES.REQUEST:
        return true;
      case GET_DAILY_SUMMARIES.SUCCESS:
      case GET_DAILY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetDailySummariesSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_DAILY_SUMMARIES.SUCCESS:
        return true;
      case GET_DAILY_SUMMARIES.REQUEST:
      case GET_DAILY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetDailySummariesError = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_DAILY_SUMMARIES.FAILURE:
        return true;
      case GET_DAILY_SUMMARIES.SUCCESS:
      case GET_DAILY_SUMMARIES.REQUEST:
        return false;
      default:
        return state;
    }
  };

  const isGetMonthlySummariesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case MONTHLY_SUMMARIES.REQUEST:
        return true;
      case MONTHLY_SUMMARIES.SUCCESS:
      case MONTHLY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetMonthlySummariesSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case MONTHLY_SUMMARIES.SUCCESS:
        return true;
      case MONTHLY_SUMMARIES.REQUEST:
      case MONTHLY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetMonthlySummariesError = (state = false, action: Actions) => {
    switch (action.type) {
      case MONTHLY_SUMMARIES.FAILURE:
        return true;
      case MONTHLY_SUMMARIES.SUCCESS:
      case MONTHLY_SUMMARIES.REQUEST:
        return false;
      default:
        return state;
    }
  };

  const isGetWeeklySummariesLoader = (state = false, action: Actions) => {
    switch (action.type) {
      case WEEKLY_SUMMARIES.REQUEST:
        return true;
      case WEEKLY_SUMMARIES.SUCCESS:
      case WEEKLY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetWeeklySummariesSuccess = (state = false, action: Actions) => {
    switch (action.type) {
      case WEEKLY_SUMMARIES.SUCCESS:
        return true;
      case WEEKLY_SUMMARIES.REQUEST:
      case WEEKLY_SUMMARIES.FAILURE:
        return false;
      default:
        return state;
    }
  };

  const isGetWeeklySummariesError = (state = false, action: Actions) => {
    switch (action.type) {
      case WEEKLY_SUMMARIES.FAILURE:
        return true;
      case WEEKLY_SUMMARIES.SUCCESS:
      case WEEKLY_SUMMARIES.REQUEST:
        return false;
      default:
        return state;
    }
  };

  return combineReducers({
    isGetDailySummariesLoader,
    isGetDailySummariesSuccess,
    isGetDailySummariesError,
    isGetMonthlySummariesLoader,
    isGetMonthlySummariesSuccess,
    isGetMonthlySummariesError,
    isGetWeeklySummariesLoader,
    isGetWeeklySummariesSuccess,
    isGetWeeklySummariesError
  });
};

export default ui;

export const getDailySummariesUI = (state: RootState) => state.ui.dailySummaries;
export const getMonthlySummariesUI = (state: RootState) => state.ui.dailySummaries;
export const getWeeklySummariesUI = (state: RootState) => state.ui.dailySummaries;
