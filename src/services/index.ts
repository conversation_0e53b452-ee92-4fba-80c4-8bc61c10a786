import { CompanyIdTypes } from '../actions'
import {
  GetAccessToken,
  LoginAuthData,
  designationGraphData,
  DeleteData,
  CollegeDriveData,
  OrgData,
  JoinedCandidates,
  TemplatePayload,
  InterviewerWork,
  CandidateCount,
  CollegesPayload,
} from '../components/Types'
import { NodeProps } from '../sagas/orgChart'
import { ProjectCustomersType } from '../sagas/projectGraph'
import * as RestAPI from './rest'
import { Search } from '@mui/icons-material'
import { dataPayloadType } from '../actions/Types'
import { AddMultipleCandidatesPayload } from 'components/Recruitment/Settings/Recruitment/UploadCandidate/UploadCandidateType'
import {
  DeleteJobExperiencesPayload,
  EditJobExperiencesPayload,
} from 'components/Recruitment/Settings/Candidate/ExperienceTable/ExperienceTableType'
import {
  DeleteRejectedSubjectsPayload,
  EditRejectedSubjectsPayload,
} from 'components/Recruitment/Settings/System/BlockedSubject/BlockedSubjectType'
import { RESET } from 'actions/actiontypes'

export const loginUserEndpoint = (data: LoginAuthData) =>
  RestAPI.POST(`/google-authentication`, data)

export const getUserDataEndPoint = () => RestAPI.GET(`/users`)

export const getBackInfoDataEndPoint = () => RestAPI.GET(`/users/background-info`)

export const getHolidaysEndPoint = () => RestAPI.GET(`/holidays`)

export const getHighlightsEndPoint = () => RestAPI.GET(`/highlights`)

export const getAssetsEndPoint = () => RestAPI.GET(`/assets`)

export const getEmpInfoDataEndPoint = () => RestAPI.GET(`/users/emp-info`)

export const getDesignationEndPoint = () => RestAPI.GET(`/users/designation`)

export const getDesignationBandEndPoint = () => RestAPI.GET(`/users/designation-band`)

export const getEmployementTypeEndPoint = () => RestAPI.GET(`/users/employeement-type`)

export const getSAPeriodEndPoint = () => RestAPI.GET(`/users/service-agreement`)

export const getTimingEndPoint = () => RestAPI.GET(`/users/officetimings`)

export const getBasicInfoPoint = (data: any) =>
  RestAPI.GET(`/users/basic-info?userId=${data.userId}&isGlobal=${data.isGlobal}`)

export const getSREndPoint = (data: any) =>
  RestAPI.GET(`/service-requests?page=${data.page}&pageSize=${data.pageSize}`)

export const getAssignedSREndPoint = (data: any) =>
  RestAPI.GET(`/assigned-requests?page=${data.page}&pageSize=${data.pageSize}`)

export const getBankInfoEndPoint = (data: any) => RestAPI.GET(`/bank-info?userId=${data.userId}`)

export const getDrsEndPoint = () => RestAPI.GET(`users/drs-list`)

export const getSubDrsEndPoint = (data: { id: number }) =>
  RestAPI.GET(`/api1/Drscount?userId=${data.id}`)

export const getCountryEndPoint = () => RestAPI.GET(`/users/countries`)

export const getTimesheetEndPoint = () => RestAPI.GET(`/timesheets`)

export const getLeaveTimesheetEndPoint = () => RestAPI.GET(`leaves/leaveTimesheets`)

export const getPaySlipEndPoint = (data: any) => RestAPI.GET(`/payslips?tID=${data}`)

export const getTaxReportData = (data: any) =>
  RestAPI.GET(`/report/deductions?uid=${data.userId}&yid=${data.yearId}`)

export const getTaxFinancialYear = (data: any) =>
  RestAPI.GET(`/report/financial-year?uid=${data.userId}`)

export const getLeaveDataEndPoint = (data: any) => {
  let url = `leaves/underlings?status=${data.status}&tID=${data.timeSheetId}&leave_type=${data.leave_type}`
  if (data.filter_type) {
    url += `&filter_type=${data.filter_type}`
  }
  return RestAPI.GET(url)
}

export const getAcceptLeaveDataEndPoint = (data: any) =>
  RestAPI.POST(`leaves/statusUpdate`, { ...data })

export const getRejectLeaveDataEndPoint = (data: any) =>
  RestAPI.GET(`leaves/underlings?tID=${data}`)

export const getProjectDetailsEndPoint = () => RestAPI.GET(`users/projects`)

export const getStateEndPoint = () => RestAPI.GET(`/users/states`)

export const getAccountTypeEndPoint = () => RestAPI.GET(`/bank-info/account-type`)

export const getMaritalStatusEndPoint = () => RestAPI.GET(`/users/marital-status`)

export const getLeaveDetailsEndPoint = (data: any) => RestAPI.GET(`/leaves`, data)

export const getDepartmentListEndPoint = (data: any) => {
  return RestAPI.GET(`/department?filterType=${data?.filterType ? data?.filterType : 0}`)
}

export const getEmployeePoint = (data: any) => RestAPI.GET(`users/specificUser?id=${data.id}`)

export const getUserDetails = () => RestAPI.GET(`users/user-info`)

export const getUserByIdEndPoint = (data: any) => RestAPI.GET(`users/user-by-id/${data.id}`)

export const getReferredByIdEndPoint = (data: any) => RestAPI.GET(`users/user-by-id/${data.id}`)

export const getHomePageDetails = () => RestAPI.GET(`users/home-page`)

export const getIssueTypeListEndPoint = (data: any) => RestAPI.GET('type', data)

export const getIssueTypeListAPI = (data: any) => {
  return RestAPI.GET('type', data)
}

export const getServiceRequestDepartment = (data: any) => {
  return RestAPI.GET(`/users/service-request-department?userId=${data.userId}`)
}

export const getQualificationEndPoint = () => RestAPI.GET(`/users/qualification-set`)

export const getQualificationSkillEndPoint = () => RestAPI.GET(`/users/qualification-skill-set`)

export const getCertificateInfo = (data: { id: number }) =>
  RestAPI.GET(`/users/usercertificate?id=${data.id}`)

export const downloadCertificate = (data: { id: number; filename: string }) =>
  RestAPI.GET(`/downlodable-certificate?certificateId=${data.id}&fileName=${data.filename}`)

export const getAccessTokenEndPoint = (data: GetAccessToken) =>
  RestAPI.OAuthPOST(`oauth/token`, data)

export const logoutUserEndpoint = () => RestAPI.OAuthLogout(`oauth/logout`)

export const getLoginUser = () => RestAPI.GET('login')

export const createNewRequest = (data: any) => RestAPI.POST1('service-requests', data)

export const getCompensationDetailsEndPoint = (data: any) =>
  RestAPI.GET(`compensations?compensationString=${data}`)

export const sendLoanRequest = (data: any) => RestAPI.POST('loans', data)

export const getLoanDetailsEndPoint = () => RestAPI.GET('loans')

export const getLoanInstallmentHistoryEndPoint = (data: any) =>
  RestAPI.GET(`loans/loanDetailsById?loanId=${data}`)

export const getAllEmployees = () => RestAPI.GET('/users/getAllEmployees')

export const addNewComment = (data: any) => RestAPI.POST('/comments', data)

export const assignedRequest = (data: any) =>
  RestAPI.GET(
    `/assigned-requests?page=${data.page}&pageSize=${data.pageSize}&search=${encodeURIComponent(
      data.search,
    )}&filter=${data.srStatus}&id_department=${data.department}&issue_type=${
      data.issueType
    }&startDate=${data.startDate}&endDate=${data.endDate}`,
  )

export const updatedRequest = (data: any) =>
  RestAPI.PUT(`/service-requests/update?srId=${data.srId}`, data)

export const getStatusSummary = (data: any) =>
  RestAPI.GET(`/idsr-summary?userId=${data.userId}&tID=${data.tID}`)

export const getAttendance = (data: any) =>
  RestAPI.GET(`/attendance?userId=${data.userId}&tID=${data.tId}`)

export const getAttendanceTimesheet = (userId: string) =>
  RestAPI.GET(`/attendance-timesheets?userId=${userId}`)

export const getRCAsList = (userId: string) => RestAPI.GET(`/rca?userId=${userId}`)

export const createRCA = (data: any) => RestAPI.POST(`/rca`, data)

export const getIDSRsList = (data: any) =>
  RestAPI.GET(`/idsr?userId=${data.userId}&tId=${data.tId}`)

export const getPlansList = (data: any) =>
  RestAPI.GET(`/planforday?userId=${data.userId}&tId=${data.tId}`)

export const getStatusType = () => RestAPI.GET('/statusType')

export const getTaskStatus = () => RestAPI.GET('/status')

export const createIDSR = (data: any) => RestAPI.POST(`/idsr`, data)

export const createPlanForTheDay = (data: any) => RestAPI.POST(`/planfortheday`, data)

export const downloadableURL = (data: any) => RestAPI.GET(`/Downloadable-file?srId=${data.srId}`)

export const getManagerViewData = (data: any) =>
  RestAPI.GET(`/api1/Managerview?userId=${data.userId}&tID=${data.tID}`)

export const getAssignedSrRequest = (data: any) => {
  return RestAPI.GET(`/service-request?id=${data.data}`)
}

export const getAttendanceReportApiCall = (data: any) => {
  return RestAPI.GET(
    `attendance_history_user_logs?page=${data.page}&limit=${data.limit}&filterDate=${data.filterDate}&search=${data.search}`,
  )
}

export const getDocURL = (data: any) =>
  RestAPI.PATCH(`/api1/url?userId=${data.userId}&url=${data.url}`, data)

export const getAllProjects = (data: any) => RestAPI.GET(`/project?userId=${data.userId}`)

export const getAllGraphData = (data: any) =>
  RestAPI.GET(
    `/projects/userId?userId=${data.userId}&start_date=${data.filterDataByDate.start_date}&end_date=${data.filterDataByDate.end_date}`,
  )

export const getAllProjectTypes = (data: any) =>
  RestAPI.GET(
    `/projects/project/index?status=${data.status}&limit=${data.limit}&page=${data.page}&search=${data.search}`,
  )

export const getProjectDomain = () => RestAPI.GET('/projects/projectDomains')

export const getProjectSource = () => RestAPI.GET('/projects/projectsource')

export const getDesignationGraphData = (data: designationGraphData) =>
  RestAPI.GET(
    `/projects/percentage?desgination=${data.designation}&start_date=${data.startDate}&end_date=${data.endDate}`,
  )

export const getDesignationGraphList = () => RestAPI.GET('users/allDesginations')

export const getAllTechnologies = () => RestAPI.GET(`/projects/technologies`)

export const getAllProjectLocation = () => RestAPI.GET(`/projects/locations`)

export const getNewProjectInfo = (data: any) => RestAPI.POST(`/projects/project/create`, data)

export const getDeletingProject = (data: any) =>
  RestAPI.PUT(`/projects/Project/delete?id=${data.id}`, data)

export const getMandateType = (data: any) =>
  RestAPI.GET(
    `/projects/mandatetype/index?page=${data.page}&pageSize=${data.pageSize}&search=${data.search}`,
  )

export const getDomainType = (data: any) =>
  RestAPI.GET(`/projects/domain?page=${data.page}&pageSize=${data.pageSize}&search=${data.search}`)

export const getProjectCustomers = () => RestAPI.GET(`/projects/Customer/index`)

export const getRestartedProject = (data: any) =>
  RestAPI.PUT(`/projects/restart?id=${data.id}`, data)

export const getprojectDetails = (data: any) =>
  RestAPI.GET(`/projects/projectDetails?id=${data.id}`)

export const getAllEmployeesList = () => RestAPI.GET(`/projects/employees`)

export const AddEmpBasedOnRoles = (data: any) => RestAPI.POST(`/projects/developer/create`, data)

export const UpdateEmpBasedOnRoles = (data: any) =>
  RestAPI.PUT(`/projects/developer/update?id=${data.id}`, data)

export const DeleteEmpBasedOnRoles = (data: any) =>
  RestAPI.PUT(`/projects/developer/delete?id=${data.id_users}`, data)

export const GetAllEditedProjects = (data: any) =>
  RestAPI.PUT(`/projects/project/update?id=${data.id}`, data)

export const getDesignationList = () => RestAPI.GET('/api1/Allcount')

export const getDesignationDataList = (data: { hierarchyName: string }) =>
  RestAPI.GET(`/projects/desginationss?hierarchyName=${data.hierarchyName}`)

export const getProjectReportsSheetURL = (data: any) =>
  RestAPI.GET(
    `/projects/reports/projectSheet?page=${data.page}&limit=${data.limit}&startDate=${data.startDate}&endDate=${data.endDate}&search=${data.search}`,
  )

export const getAllResourceReportData = (data: any) =>
  RestAPI.GET(
    `/projects/reports/projectResourcesReport?project=${data.project}&projectSource=${data.projectSource}&employmentType=${data.employmentType}&startDate=${data.startDate}&endDate=${data.endDate}&location=${data.location}`,
  )

export const getProjectResourceDropdownData = (data: any) =>
  RestAPI.GET(`/projects/AllProjectDetails?search= ${data.search}`)

export const getProjectManagementReportData = (data: any) =>
  RestAPI.GET(
    `/projects/projectmanagmentreport?date=${data.date}&type=${data.type}&page=${data.page}&limit=${data.limit}&search=${data.search}`,
  )

export const getAllProjectQAReport = (data: any) =>
  RestAPI.GET(
    `/projects/projectqareport?startDate=${data.startDate}&endDate=${data.endDate}&type=${data.type}&page=${data.page}&pageSize=${data.pageSize}&search=${data.search}`,
  )

export const getLeavesReportAPICall = (data: any) => {
  return RestAPI.GET(
    `/leaves/leave-report?startDate=${data.startDate}&endDate=${data.endDate}&page=${data.page}&pageSize=${data.pageSize}&tid=${data.tid}`,
  )
}
export const getLeavesReportBalanceAPICall = (data: any) => {
  return RestAPI.GET(`/leaves/balance-report`, data)
}
export const getLeavesReportTypeAPICall = (data: any) => {
  return RestAPI.GET(`/leaves/leave-type-report?tid=${data.tid}&leaveType=${data.leaveType}`)
}
export const getLeavesReportEncashmentAPICall = (data: any) => {
  return RestAPI.GET(`/leaves/leaves-encashment?yid=${data.yid}`)
}
export const getLeavesReportAllocatedAPICall = (data: any) => {
  return RestAPI.GET(
    `/leaves/leave-allocation-report?${data.quarter ? 'quarter=' + data.quarter + '&' : ''}yid=${
      data.yid
    }`,
  )
}

export const getNonBillableResources = () => RestAPI.GET('/projects/listNonBillableResource')

export const updateNonBillable = (data: { Employee_id: number[] }) =>
  RestAPI.PUT(`/projects/createNonBillResource`, data)

export const getAllProjectCustomers = (data: any) =>
  RestAPI.GET(
    `/projects/projectCustomer/index?page=${data.page}&pageSize=${data.pageSize}&search=${data.search}`,
  )

export const createProjectCustomers = (data: ProjectCustomersType) => {
  return RestAPI.POST(`/projects/projectCustomer/create`, data.data)
}

export const createMandateTypes = (data: any) => RestAPI.POST(`/projects/mandate/create`, data.data)

export const createDomainTypes = (data: any) => RestAPI.POST(`/projects/domain`, data.data)

export const updateMandateType = (data: any) =>
  RestAPI.PATCH(
    `/projects/mandate/update?id=${data.id}&mandate_name=${data.mandateName}`,
    data.data,
  )

export const updateDomainType = (data: any) => RestAPI.PATCH(`/projects/domain`, data.data)

export const deleteOneMandateType = (id: number) =>
  RestAPI.PATCH(`/projects/mandate/delete?id=${id}`, { id })

export const deleteOneProjectCustomers = (id: number) =>
  RestAPI.PUT(`/projects/projectCustomer/delete?id=${id}`, { id })

export const updateProjectCustomer = (data: any) =>
  RestAPI.PUT(
    `/projects/projectcustomer/update?id=${data.id}&CustomerName=${data.CustomerName}&country_code=${data.country_code}&project_domain=${data.project_domain}`,
    data.data,
  )

export const getpeopleBasedOrgChart = (data: NodeProps) => RestAPI.GET(`/projects/Peoplechart`)

export const geteDepartmentBasedOrgChart = (data: any) =>
  RestAPI.GET(`/projects/departmentcharts?department=${data.department}`)

export const getDesignationBasedOrgChart = (data: NodeProps) =>
  RestAPI.GET(`/projects/designationcharts?level=${data.level}`)

export const getEmpHistory = (data: { id: number }) =>
  RestAPI.GET(`/users/desginationhistory?id=${data?.id ?? ''}`)

export const getManagerHistoryData = (data: { id: number }) =>
  RestAPI.GET(`/users/managerhistory?id=${data?.id ?? ''}`)

export const getCompanyLogo = (data: any) => RestAPI.GET(`/users/image?id=${data.id}`)

export const getCompanyId = () => RestAPI.GET(`/users/companiesId`)

export const createUser = (data: any) => RestAPI.POST1(`/users/createUser`, data.data)

export const addNewJoiners = (data: any) => RestAPI.POST1(`/users/createExpectedUser`, data.data)

export const addBankInfoApi = (data: any) => RestAPI.POST(`/users/bankDetail`, data)

export const updateNewJoiners = (data: any) => RestAPI.PATCH1(`/users/updateExpectedUser`, data)

export const getNewJoinersDetails = (data: any) =>
  RestAPI.GET(
    `/users/expected/users?page=${data.page}&pageSize=${data.pageSize}&search=${data.search}`,
  )

export const getCountryDistributionApiCall = (data: any) =>
  RestAPI.GET(`/analytics/country-distribution?Date=`)

export const getCertiPercentageApiCall = (data: any) =>
  RestAPI.GET(`/analytics/certification-percentage?Date=`)

export const getCertificationWiseSplitUpApiCall = (data: any) =>
  RestAPI.GET(`/analytics/certificationWiseSplitUp?Date=`)

export const getGradeLabelApiCall = () => RestAPI.GET(`/analytics/percentagePerGradeLevel`)

export const getUserStatisticsApiCall = () => RestAPI.GET(`/analytics/getUserStatistics`)

export const uploadCertificate = (data: any) => RestAPI.POST1(`/Certificates`, data)

export const uploadUserImg = (data: any) =>
  RestAPI.POST(`/users/imageUpload?id=${data.data.id}`, data.data)

export const updateUser = (data: any) => RestAPI.PATCH1(`/users/updateUser`, data)

export const fetchAllRoles = () => RestAPI.GET(`/users/alluserroles`)

export const fetchAllState = () => RestAPI.GET(`/users/states`)

export const fetchAllCountry = () => RestAPI.GET(`/users/countries`)

export const fetchAllFloors = () => RestAPI.GET(`/users/floors`)

export const fetchAllWorkstation = () => RestAPI.GET(`/users/workstations`)

export const fetchAllLocation = () => RestAPI.GET(`/projects/locations`)

export const fetchAllClietLocation = () => RestAPI.GET(`/users/client_location`)

export const getAllUserList = (data: any) =>
  RestAPI.GET(
    `/users/admin/users?page=${data.page}&pageSize=${data.pageSize}&search=${data.search}&type=${data.type}`,
  )
export const getUserImage = (data: any) => RestAPI.GET(`/users/alluserImages?id=${data?.userid}`)
export const getExpectedJoinersImage = (data: any) =>
  RestAPI.GET(`/users/expectedJoineeImages?id=${data?.userid}`)

export const deleteUser = (userid: any) =>
  RestAPI.PATCH(`/users/deleteUser?id=${userid}`, { userid })

export const getReportsSheetURL = (data: any) =>
  RestAPI.GET(
    `/download?page=${data.page}&limit=${data.limit}&startDate=${data.startDate}&endDate=${
      data.endDate
    }&searchTerm=${data?.search ? data?.search : ''}&searchTermByProject=${
      data?.searchTermByProject ? data?.searchTermByProject : ''
    }&type=${data.reportType}`,
  )

export const downloadCsvForMyTeamReport = (data: any) =>
  RestAPI.DOWNLOADCSV(
    `/downloads?startDate=${data.startDate}&endDate=${data.endDate}&type=${data.reportType}&searchTerm=${data.search}`,
  )

export const getPlanForTheDayApiCall = (data: any) =>
  RestAPI.GET(`/planfortheday/report?planforthedayId=${data.planforthedayId}`)

export const getEmployeementInfoApiCall = (data: any) =>
  RestAPI.GET(`/users/employment-info/detail?userId=${data.data.userId}`)

export const updatePlanForTheDayApiCall = (data: any) =>
  RestAPI.PATCH(`/planfortheday?planforthedayId=${data.planforthedayId}`, data.data)

export const getSingleIdsrApiCall = (data: any) => RestAPI.GET(`/idsr/report?idsrId=${data.idsrId}`)

export const updateSingleIdsrApiCall = (data: any) =>
  RestAPI.PATCH(`/idsr?idsrId=${data.idsrId}`, data.data)

export const getCommentTimesheet = (data: any) => RestAPI.GET(`/comment/idsr?idsrId=${data.id}`)

export const getCommentPlanForTheday = (data: any) =>
  RestAPI.GET(`/comment/planfortheday?planforthedayId=${data.id}`)

export const createCommentPlanForTheDay = (data: any) =>
  RestAPI.PATCH(`/comment/planfortheday?planforthedayId=${data.id}`, data.data)

export const createCommentTimesheet = (data: any) =>
  RestAPI.PATCH(`/comment/idsr?idsrId=${data.id}`, data.data)
export const approvePlanForTheDayAPI = (data: any) =>
  RestAPI.PATCH(`/approve?planforthedayId=${data.id}`, {})

export const uploadForm16 = (data: any) =>
  RestAPI.POST1(`/form16`, {
    id_user: data.id_user,
    file: data.file,
    currentYear: data.currentYear,
  })

export const sendEmailRequest = (data: any) =>
  RestAPI.POST1(`/mailServiceFrom16`, {
    userId: data.userId,
    accountsDepartmentId: data.accountsDepartmentId,
    currentYear: data.currentYear,
  })

export const getForm16Data = (data: any) =>
  RestAPI.GET(
    `/form16?currentYear=${data.currentYear}&status=${data.status}&limit=${data.limit}&page=${data.page}&user=${data.user}`,
  )

export const getFinancialYearData = () => RestAPI.GET(`/financialByHireDate`)

export const getDownloadForm16 = (data: any) =>
  RestAPI.GET(`/downlodable-form16?userId=${data.id}&currentYear=${data.currentYear}`)

export const getEmpSalarySlip = (data: any) =>
  RestAPI.GET(`/payslips?tID=${data.tId}&userId=${data.userId}`)

export const getEmpCompensation = (data: any) =>
  RestAPI.GET(
    `/compensations?compensationString=${
      data?.compensationString ? data?.compensationString : ''
    }&userId=${data.userId}`,
  )

export const getEmpDocumentDownload = (data: any) => {
  return RestAPI.GET(`/users/downlodableEducationalInfo?id=${data?.id}`)
}

export const getAssetsData = (data: {
  page: number
  limit: number
  asset: string
  searchby: string
  search: string
  type: number
}) =>
  RestAPI.GET(
    `/assets/getassets?page=${data.page}&limit=${data.limit}&status=${data.asset}&type=${data.type}&searchby=${data.searchby}&search=${data.search}`,
  )

export const getNumberOfAssets = () => RestAPI.GET('/assets/assetcount')

export const getEmpData = (data: { searchTerm: string }) =>
  RestAPI.GET(`/assets/search?search=${data.searchTerm}`)

export const getEmpAssetData = (data: { searchTerm: string }) =>
  RestAPI.GET(`/assets/search?search=${data.searchTerm}`)

export const getAllAssetsList = (data: { searchAssetTerm: string }) =>
  RestAPI.GET(`/assets/laptop_no?search=${data.searchAssetTerm}`)

export const updateAssignAsset = (data: any) => RestAPI.PATCH('/assets/assign', data)

export const getEmployeeAssets = (data: { employee_id: string }) =>
  RestAPI.GET(`assets/assignedlaptops?employee_id=${data.employee_id}`)

export const updateUnallocateAsset = (data: any) => RestAPI.PATCH('/assets/unallocate', data)

export const updateAddNewAsset = (data: any) => RestAPI.POST('/assets/addasset', data)

export const getAssetsDataAsExcel = (data: any) =>
  RestAPI.DOWNLOADCSV(`/assets/download?status=${data.status}`)

export const getAssetAbiberation = (data: any) => RestAPI.GET('/assets/assetabbrevation')

export const getAssetLocation = (data: any) => RestAPI.GET('/assets/assetlocation')

export const getAssetMake = (data: any) => RestAPI.GET('/assets/assetmake')

export const getAssetStatus = (data: any) => RestAPI.GET('/assets/assetStatus')

export const getAssetOS = (data: any) => RestAPI.GET('/assets/assetos')

export const getAssetTechnologies = (data: any) => RestAPI.GET('/assets/technologies')

export const getAssetCategoryData = (data: { page: number; search: string }) =>
  RestAPI.GET(`/assets/getcategory?page=${data.page}&search=${data.search}`)

export const getAssetOSData = (data: { page: number; search: string }) =>
  RestAPI.GET(`/assets/assetOsDetails?page=${data.page}&search=${data.search}`)

export const updateAssetOs = (data: any) => RestAPI.POST(`/assets/postassetos`, data)

export const updateAssetCategory = (data: any) => RestAPI.POST(`/assets/assetcategory`, data)

export const deleteAssetCategory = (data: any) => RestAPI.PATCH(`/assets/deletecategory`, data)

export const getAssetMakeData = (data: { page: number; search: string }) =>
  RestAPI.GET(`/assets/assetMakeDetail?page=${data.page}&search=${data.search}`)

export const addAssetMake = (data: any) => RestAPI.POST(`/assets/postassetmake`, data)

export const deleteAssetMake = (data: any) => RestAPI.PATCH(`/assets/deleteassetmake`, data)

export const getAssetStatusData = (data: { page: number; search: string }) =>
  RestAPI.GET(`/assets/assetStatus?page=${data.page}&search=${data.search}`)

export const addAssetStatus = (data: any) => RestAPI.POST(`/assets/postassetstatus`, data)

export const deleteAssetStatus = (data: any) => RestAPI.PATCH(`/assets/deleteassetstatus`, data)

export const getTechnologiesData = (data: { page: number; limit: number; search: string }) =>
  RestAPI.GET(`/assets/technology?page=${data.page}&limit=${data.limit}&search=${data.search}`)

export const addTechnologies = (data: any) => RestAPI.POST(`/assets/posttechnology`, data)

export const deleteTechnologies = (data: any) => RestAPI.PATCH(`/assets/deletetechnology`, data)

export const deleteAssetOs = (data: any) => RestAPI.PATCH(`/assets/deleteassetos`, data)

export const getEmployeeAssetCountData = (data: any) => RestAPI.GET(`assets/employeeassetcount`)

export const getAssetAbbrevationCountData = (data: any) => RestAPI.GET(`assets/allAssets`)

export const getAssetStatusCountData = (data: any) => RestAPI.GET(`assets/workingstatus`)

export const getAssignAssetReportData = (data: any) => RestAPI.GET(`assets/assignedreport`)

export const getDownloadAssetsQrData = (data: any) => RestAPI.GET(`assestName`)

export const getUserTechnologyApiCall = (data: any) =>
  RestAPI.GET(`/analytics/user-technology-split?Date=`)

export const createBackgroundInfo = (data: any) => {
  return RestAPI.POST1(`/users/educational`, data)
}

export const getBackgroundInfo = (data: any) => {
  return RestAPI.GET(`/users/background-info?userId=${data.userId}`)
}

export const addLeaveApi = (data: any) =>
  RestAPI.POST(`/leaves/allocate?uid=${data.uid}`, data.data)

export const addPayrollApi = (data: any) => {
  return RestAPI.POST(`/createPayroll`, data)
}

export const getLeavesType = (data: any) => {
  return RestAPI.GET(`/users/leaveType`)
}

export const updateLeaveApi = (data: any) =>
  RestAPI.POST(`/leaves/create?uid=${data.uid}`, data.data)

export const deleteLeaveApi = (data: any) => RestAPI.PATCH(`/leaves/deleteLeaveAccrualFile`, data)

export const getpayrollDropDown = (data: any) => {
  return RestAPI.GET(`/leaves/dropDown`)
}

export const getPayroll = (data: { userId: number }) => RestAPI.GET(`/payroll?uid=${data.userId}`)

export const getDesignationApiCall = (data: any) =>
  RestAPI.GET(
    `/hrcontrol-forms/Designation?page=${data.page}&limit=${data.limit}&search=${data.search}`,
  )

export const addDesignationApi = (data: any) =>
  RestAPI.POST(`/hrcontrol-forms/createDesignation`, data.data)

export const addLeaveTypeApi = (data: any) => {
  return RestAPI.PATCH(`/hrcontrol-forms/leave-types`, data)
}

export const editDesignationApiCall = (data: any) =>
  RestAPI.PATCH(`/hrcontrol-forms/editDesignation`, data.data)

export const getLeavesCategoryAPI = () => {
  return RestAPI.GET(`/hrcontrol-forms/listChargeCodeCategory`)
}

export const createHolidayApiCall = (data: any) =>
  RestAPI.PATCH(`hrcontrol-forms/createHoliday`, data)

export const createTimingApiCall = (data: any) => RestAPI.PATCH(`hrcontrol-forms/offceTiming`, data)

export const createQualificationApiCall = (data: any) =>
  RestAPI.PATCH(`hrcontrol-forms/createQualificationSkillSet`, data)

export const addCreateLeavesApi = (data: any) =>
  RestAPI.POST(`/hrcontrol-forms/allocate-leaves`, data)

export const downloadQR = (data: { assestNo: string }) =>
  RestAPI.GET(`/qrcode?assestNo=${data.assestNo}`)

export const getLeavesInfo = (data: any) => RestAPI.GET(`/hrcontrol-forms/addLeaveForEmployee`)

export const getLeavesTypes = (data: any) => {
  return RestAPI.GET(
    `/hrcontrol-forms/leave-types?page=${data?.page}&limit=500&search=${data?.searchQuery}`,
  )
}

export const getCertificationsReports = (data: any) => {
  return RestAPI.GET(
    `/projects/certificationReport?certificationName=${data?.certificationName}&employeeName=${data?.empName}`,
  )
}

export const temporaryEmp = (data: { [key: string]: string }) =>
  RestAPI.PUT(`projects/developer-additional/update`, data)

export const getLeaveFrequencies = () => RestAPI.GET(`leaves/frequencies`)

export const getQuatre = (data: { [key: string]: string }) => RestAPI.GET(`leaves/quarters`, data)

export const getDailySummariesService = (data: any) => {
  return RestAPI.GET(`/daily-summaries?userId=${data.userId}&tId=${data.tId}`)
}
export const getAvailableYears = (userId: string) =>
  RestAPI.GET(`/available-years?userId=${userId}`)

export const getMonthlySummariesEndPoint = (data: { userId: number; yearId: number }) =>
  RestAPI.GET(`/monthly-summaries?userId=${data.userId}&yearId=${data.yearId}`)

export const getWeeklySummariesEndPoint = (data: { userId: number; yearId: number }) =>
  RestAPI.GET(`/weekly-summaries?userId=${data.userId}&yearId=${data.yearId}`)

export const getReimbursementRequestsAPI = (data: { [key: string]: string | number }) =>
  RestAPI.GET(`expenses`, data)

export const getPerticularExpenseAPI = (data: { id: number }) =>
  RestAPI.GET(`particularExpenses?id=${data?.id ?? ''}`)

export const getExpenseStatusUpdateAPI = (data: {
  id: number
  comment: number
  edit: number
  data: {}
}) =>
  RestAPI.PATCH(
    `expenses?comment=${data.comment}&id=${data?.id}&edit=${data.edit ? data.edit : 0}`,
    data.data,
  )

export const createExpense = (data: { [key: string]: string }) => RestAPI.POST1(`expenses`, data)

//recruitment
export const getRoundEndPoint = () => RestAPI.RecruitmentGET(`/viewRounds`)
export const getPositionEndPoint = () => RestAPI.RecruitmentGET(`/viewJobAndExperience`)
export const getTagEndPoint = () => RestAPI.RecruitmentGET(`/viewAllTags`)

export const postDeleteTemplate = (data: DeleteData) =>
  RestAPI.RecruitmentPOST(`/deleteTemplate`, data)

export const postDeleteCollege = (data: DeleteData) =>
  RestAPI.RecruitmentPOST(`/deleteOrganisationDetails`, data)

export const getAllColleges = (data: CollegesPayload) =>
  RestAPI.RecruitmentPOST(`/getCollegedetails`, data)
export const getBatchesEndPoint = () => RestAPI.RecruitmentGET(`/viewBatch`)
export const getAddQualificationEndPOint = () => RestAPI.RecruitmentGET(`/viewQualification`)
export const getExpectedJoinerEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewExpectedJoiners`, data)
export const getFeedbackEndPoint = () => RestAPI.RecruitmentGET(`/viewFeedbackMock`)
export const getCandidatePositionEndPoint = () => RestAPI.RecruitmentGET(`/viewJob`)

export const getInstituteDetailsEndPoint = (data: { id: string }) =>
  RestAPI.RecruitmentPOST(`/viewTpoDetails`, data)

export const AddJobPositionEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addJob`, data)

export const AddInstDetailsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addTpoDetails`, data)

export const EditInstDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/getTpoDetailsById`, data)
export const SubInstDetailsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editTpoDetails`, data)
export const DelInstDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/deleteTpoDetails`, data)
export const roundfeedbackDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewRoundsByType`, data)

export const getEditPositionDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/getJobById`, data)
export const getSubmitPositionDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editJob`, data)
export const getDeletePositionDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/deleteJob`, data)
export const getAddFeedbackDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addFeedbackMock`, data)
export const getDeleteFeedbackDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/deleteFeedbackMock`, data)
export const getEditFeedbackDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/getFeedbackMockById`, data)
export const getSubEditFeedbackDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editFeedbackMock`, data)

export const getEndRoundTypesPointEndPoint = () => RestAPI.RecruitmentGET(`/viewRoundTypes`)
export const getTpoDetailsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/viewTpoDetails`, data)
export const getDateandTimeEndPoint = () => RestAPI.RecruitmentGET(`/viewDateTime`)
export const getOrganisationDetailsEndPoint = () =>
  RestAPI.RecruitmentGET(`/viewOrganisationDetails`)
export const getUserEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/viewUser`, data)
export const getExperienceEndPoint = () => RestAPI.RecruitmentGET(`/viewJobExperience`)
export const getBlockedSubjectEndPoint = () => RestAPI.RecruitmentGET(`/viewRejectedSubject`)
export const getCandidateRoundEndPoint = () => RestAPI.RecruitmentGET(`/viewRounds`)
export const getCandidateTagEndPoint = () => RestAPI.RecruitmentGET(`/viewAllTags`)
export const addMultipleCandidatesEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addMultipleCandidates`, data)
export const addRejectedSubjectsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addRejectedSubject`, data)
export const addJobExperiencesEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addJobExperience`, data)
export const addExpectedJoinersEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addExpectedJoiners`, data)
export const editRejectedSubjectsEndPoint = (payload: EditRejectedSubjectsPayload) =>
  RestAPI.RecruitmentPOST(`/editRejectedSubject`, payload)
export const editJobExperiencesEndPoint = (payload: EditJobExperiencesPayload) =>
  RestAPI.RecruitmentPOST(`/editJobExperience`, payload)
export const deleteJobExperiencesEndPoint = (data: DeleteJobExperiencesPayload) =>
  RestAPI.RecruitmentPOST(`/deleteJobExperience`, data)
export const deleteRejectedSubjectsEndPoint = (data: DeleteRejectedSubjectsPayload) =>
  RestAPI.RecruitmentPOST(`/deleteRejectedSubject`, data)

export function* getScheduleEndPoint(getScheduleEndPoint: any) {
  throw new Error('Function not implemented.')
}
export const viewTagsCandidatesCountEndPoint = () =>
  RestAPI.RecruitmentGET(`/viewTagsCandidatesCount`)
export const getBlockDataEndPoint = () => RestAPI.RecruitmentGET(`/viewRejectedDomain`)
export const getEmailTemplatesEndPoint = () => RestAPI.RecruitmentGET(`/template`)
export const getStaticData = () => RestAPI.RecruitmentGET(`/viewStaticReplacement`)
export const getInterviewer = (data: {}) => RestAPI.RecruitmentPOST(`/viewInterviewer`, data)
export const getAllTemplates = () => RestAPI.RecruitmentGET(`/template`)
export const getRecruitmentEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewWorkingRecruiters`, data)
export const getDateTimeByRoundEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/viewDateTimeByRound`, data)
export const getTemplateByRoundEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/viewTemplateByRound`, data)
export const getCandidatesByFiltersEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/getCandidatesByFilters`, data)
export const deleteCandidateByIdEndPOint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/deleteCandidate`, data)
export const sendmailCandidateEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/sendmail`, data)
export const getViewAttachmentsEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/viewAttachments`, data)
export const getVideoUrlEndPoint = (data: dataPayloadType) => RestAPI.RecruitmentGET(`/url`, data)
export const getInterviewerPannelEndPoint = () => RestAPI.RecruitmentGET(`/viewInterviewersPanel`)
export const getAllQualification = () => RestAPI.RecruitmentGET(`/viewQualification`)

export const getRoundsByTypeEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewRoundsByType`, data)
export const fetchDeleteExpectedJoinersEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteExpectedJoiners`, { id: data.id })

export const postDriveData = (data: CollegeDriveData) =>
  RestAPI.RecruitmentPOST(`/addCollegeDrive`, data)
export const postOrgData = (data: OrgData) =>
  RestAPI.RecruitmentPOST(`/editOrganisationDetails`, data)

export const postInterviewerWork = (data: InterviewerWork) =>
  RestAPI.RecruitmentPOST(`/viewInterviewerWork`, data)
export const postCandidateCount = (data: CandidateCount) =>
  RestAPI.RecruitmentPOST(`/viewCandidatesCountByRound`, data)

export const getAllUnapprovedCandidate = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewUnApprovedCandidate`, data)
export const getAllSpaming = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewSpamingUnApprovedCandidate`, data)
export const deleteAllUnapprovedCandidate = (data: {}) =>
  RestAPI.RecruitmentPOST(`/deleteUnApprovedCandidate`, data)

export const getCandidateByIDEndPOint = (data: { id: string | undefined }) =>
  RestAPI.RecruitmentPOST(`/getCandidateById`, {
    id: data.id,
  })

export const addManageQualificationEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addQualification`, data)

export const addManageBatchesEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addBatch`, data)

export const deleteQualificationEndPoint = (data: { id: number }) => {
  return RestAPI.RecruitmentPOST(`/deleteQualification`, { id: data.id })
}

export const deleteBatchEndPoint = (data: { id: number }) => {
  return RestAPI.RecruitmentPOST(`/deleteBatch`, { id: data.id })
}

export const editBatchEndPoint = (payload: { id: number; batch: string }) => {
  RestAPI.RecruitmentPOST(`/editBatch`, payload)
}
export const editQualificationEndPoint = (payload: { id: number; qualification: string }) => {
  RestAPI.RecruitmentPOST(`/editQualification`, payload)
}
export const getRepresentativeTypeEndPoint = () => RestAPI.RecruitmentGET(`/viewRepresentativeType`)
export const getInterviewerEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/viewInterviewer`, data)
export const getAddOrganisationDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addOrganisationDetails`, data)
export const getAddUserDetailsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addUser`, data)
export const getAddDateTimeDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addDateTime`, data)
export const getDeleteOrgDetailsEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteOrganisationDetails`, { id: data.id })
export const getDeleteUserDetailsEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteUser`, { id: data.id })
export const getDeleteDateTimeDetailsEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteDateTime`, { id: data.id })
export const getEditOrgDetailsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editOrganisationDetails`, data)
export const getEditUserDetailsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editUser`, data)
export const postEmailTemplateData = (data: {}) => RestAPI.RecruitmentPOST(`/template`, data)
export const getRoundsForTemplate = () => RestAPI.RecruitmentGET(`/viewRoundsForTemplate`)
export const postTemplateData = (data: TemplatePayload) =>
  RestAPI.RecruitmentPOST(`/editTemplate`, data)
export const postJoinedCandidates = (data: CandidateCount) =>
  RestAPI.RecruitmentPOST(`/viewJoinedCandidates`, data)
export const getEditCandidateInlineEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/editCandidateInline`, data)

export const getJobExperience = () => RestAPI.RecruitmentGET(`/viewJobAndExperience`)

export const postStaticData = (data: {}) => {
  RestAPI.RecruitmentPOST(`/addStaticReplacement`, data)
}

export const postInterviewerData = (data: {}) => {
  RestAPI.RecruitmentPOST(`/addUser`, data)
}

export const postEditStaticData = (data: {}) => {
  RestAPI.RecruitmentPOST(`/editStaticReplacement`, data)
}

export const postEditInterviewerData = (data: {}) => {
  RestAPI.RecruitmentPOST(`/editUser`, data)
}
export const postDeleteStaticData = (data: { id: number }) => {
  RestAPI.RecruitmentPOST(`/deleteStaticReplacement`, { id: data.id })
}

export const postDeleteInterviewerData = (data: { id: number }) => {
  RestAPI.RecruitmentPOST(`/deleteUser`, { id: data.id })
}
export const editExpectedJoinersEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editExpectedJoiners`, data)

export const addBlockedBodyEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addRejectedBody`, data)

export const getBlockedBodyEndPoint = () => RestAPI.RecruitmentGET(`/viewRejectedBody`)

export const addRejectedBodyEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addRejectedBody`, data)

export const deleteRejectedBodyEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteRejectedBody`, { id: data.id })

export const editRejectedBodyEndPoint = (payload: { id: number; subject: string }) =>
  RestAPI.RecruitmentPOST(`/editRejectedBody`, payload)
export const getAllNewTag = (data: {}) => RestAPI.RecruitmentPOST(`/addNewTag`, data)
export const getAllEditTag = (data: {}) => RestAPI.RecruitmentPOST(`/editTag`, data)
export const getAllDeleteTag = (data: {}) => RestAPI.RecruitmentPOST(`/deleteTag`, data)
export const getAddRoundsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addRound`, data)
export const getEditRoundsEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editRound`, data)
export const getEditTpoEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editTpoDetails`, data)
export const deleteRoundsEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteRound`, { id: data.id })
export const deleteTpoEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteTpoDetails`, { id: data.id })

export const getAddTpoEndPoint = (data: any) => RestAPI.RecruitmentPOST(`/addTpoDetails`, data)
export const viewOrganisationDetailsByType = (data: any) =>
  RestAPI.RecruitmentPOST(`/viewOrganisationDetailsByType`, data)
export const addBlockDomainsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/addRejectedDomain`, data)

export const deleteBlockDomainsEndPoint = (data: { id: number }) =>
  RestAPI.RecruitmentPOST(`/deleteRejectedDomain`, { id: data.id })

export const editBlockDomainsEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editRejectedDomain`, data)

export const addFeedbackCandidateEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/saveCandidateFeedback`, data)

export const deleteAttachmentEndPoint = (data: any) =>
  RestAPI.RecruitmentPOST(`/deleteAttachment`, data)

export const deleteCandidateFeedbackEndPoint = (data: any) =>
  RestAPI.RecruitmentPOST(`/deleteCandidateFeedback`, data)

export const editCandidateEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editCandidate`, data)
export const getBatchDropdownEndPoint = () => RestAPI.RecruitmentGET('viewBatch')
export const getEditDateTimeEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/editDateTime`, data)

export const addCandidateEndPoint = (data: {}) => RestAPI.RecruitmentPOST(`/addCandidate`, data)

export const editCandidateFormEndPoint = (data: {}) =>
  RestAPI.RecruitmentPOST(`/editCandidate`, data)

export const viewRecruitersCallsEndPoint = (data: dataPayloadType) =>
  RestAPI.RecruitmentPOST(`/viewRecruitersCalls`, data)
export const viewCandidateFeedbackEndPoint = (data: any) =>
  RestAPI.RecruitmentPOST(`/viewCandidateFeedback`, data)

export const viewCandidateTagsEndPoint = (data: { id: string | number }) =>
  RestAPI.RecruitmentPOST(`/viewCandidateTags`, { id: data.id })

export const getSendRemainder = () => RestAPI.RecruitmentGET(`/sendReminders`)
export const getCoolingOffPeriod = () => RestAPI.RecruitmentGET(`/viewNoOfMonths`)

export const addUploadResumeEndPoint = (data: any) => RestAPI.RecruitmentPOST(`/uploadResume`, data)

export const addUploadAssignmentEndPoint = (data: any) =>
  RestAPI.RecruitmentPOST(`/uploadAssignment`, data)

export const addResumeEndPoint = (data: FormData) => RestAPI.uploadResume(`/uploadResume`, data)
export const getApprovedApplicants = (data: any) => RestAPI.RecruitmentPOST(`/moveCandidate`, data)
export const getRejectedApplicants = (data: any) => RestAPI.RecruitmentPOST(`/moveCandidate`, data)
export const getMultipleDeleteApplicants = (data: any) =>
  RestAPI.RecruitmentPOST(`/deleteMultipleUnApprovedCandidate`, data)
export const getApplicantsResume = (data: any) => RestAPI.RecruitmentPOST(`/viewAttachments`, data)
export const getShorcutKeysByCandidateId = (data: any) =>
  RestAPI.RecruitmentPOST(`/getShortcutKeysByCandidateId`, data)
export const toggleShortcutKeyToCandidateId = (data: any) =>
  RestAPI.RecruitmentPOST(`/toggleShortcutKeyToCandidateId`, data)
export const downloadAttachments = (data: { [key: string]: string }) =>
  RestAPI.GET(`download-files`, data)

// KPI Template Management APIs for DR Section
export const assignTemplateAPI = (data: { userId: number; tId: number; templateId: number }) =>
  RestAPI.POST(`template/assign`, data)

export const getAssignedTemplateAPI = (data: { userId: number; tId: number }) =>
  RestAPI.GET(`template?userId=${data.userId}&tId=${data.tId}`)

export const editTemplateValuesAPI = (data: {
  userId: number
  tId: number
  templateId: number
  categoryId: number
  subcategoryId: number
  value: string
  highlights?: string
  lowlights?: string
  actions_taken?: string
}) => RestAPI.PUT(`template/categories/subcategories`, data)

export const getKPITemplatesAPI = () => RestAPI.GET(`api/templates`)

export const createComingLateRequest = (data: any) => RestAPI.POST('coming-late-request', data)

// for notifications
export const getNotifications = () => RestAPI.GET('/notifications')

export const markNotificationAsRead = (notificationId: number) =>
  RestAPI.PATCH(`/notifications/${notificationId}/read`, {})

export const aiReview = (data: any) => RestAPI.POST('users/ai-review', data)
export const viewCandidateLogsEndPoint = (data: any) => RestAPI.RecruitmentPOST(`/viewCandidateLogs`, data)
