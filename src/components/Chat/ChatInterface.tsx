import React, { useState, useEffect, useRef } from 'react';
import { Box, TextField, Button, Typography, Paper, List, ListItem, Divider, CircularProgress, Card, CardContent } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

interface Message {
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  serviceRequestId?: number;
  isError?: boolean;
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      text: "Hello! I’m your virtual assistant. How can I help you today? You can ask me about company policies, raise service requests, or get information about your profile.",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getUserIdFromToken = (): number | null => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return null;
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.id || null;
    } catch (error) {
      console.error("Failed to decode token:", error);
      return null;
    }
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      text: input,
      sender: 'user',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const userId = getUserIdFromToken();
      if (!userId) {
        throw new Error("User ID not found in token.");
      }

      // Directly call the Python RAG service
      const pythonServiceUrl = process.env.REACT_APP_PYTHON_RAG_SERVICE_URL || 'http://localhost:5000';
      const response = await axios.post(`${pythonServiceUrl}/query`, {
        query: input,
        user_id: userId
      });

      const botMessage: Message = {
        text: response.data.data.answer,
        sender: 'bot',
        timestamp: new Date(),
        isError: response.data.status !== 'success'
      };
      setMessages(prev => [...prev, botMessage]);

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        text: 'Sorry, I encountered an error connecting to the AI service. Please try again later.',
        sender: 'bot',
        timestamp: new Date(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Paper sx={{ flex: 1, overflow: 'auto', p: 2, mb: 2 }}>
        <List>
          {messages.map((msg, index) => (
            <React.Fragment key={index}>
              <ListItem sx={{ 
                justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                mb: 1
              }}>
                <Paper sx={{ 
                  p: 2, 
                  maxWidth: '70%',
                  bgcolor: msg.sender === 'user' ? 'primary.light' : 'background.paper',
                  color: msg.sender === 'user' ? 'white' : 'text.primary',
                  borderRadius: msg.sender === 'user' ? '20px 20px 0 20px' : '20px 20px 20px 0'
                }}>
                  <Typography variant="body1">{msg.text}</Typography>
                  {msg.serviceRequestId && (
                    <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
                      Service Request #{msg.serviceRequestId} created
                    </Typography>
                  )}
                </Paper>
              </ListItem>
              {index < messages.length - 1 && <Divider variant="fullWidth" component="li" />}
            </React.Fragment>
          ))}
          <div ref={messagesEndRef} />
        </List>
      </Paper>
      
      <Box sx={{ display: 'flex', mb: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Type your message..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSend()}
          disabled={loading}
          sx={{ mr: 1 }}
        />
        <Button 
          variant="contained" 
          color="primary" 
          endIcon={<SendIcon />}
          onClick={handleSend}
          disabled={loading || !input.trim()}
        >
          Send
        </Button>
      </Box>
    </Box>
  );
};

export default ChatInterface;
