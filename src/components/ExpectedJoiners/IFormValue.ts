export interface FormValues {
  first_name: string
  last_name: string
  id_tenant: number
  father_name: string
  image_path: string
  mother_name: string
  employee_id: string
  client_location: number
  location: number
  under_probation: number
  email: string
  created_at: Date
  created_by: string
  track_attendance: boolean
  Workstation: number
  loginId: string
  password: string
  status: number
  present_address: ''
  present_city: string
  present_postal_code: string
  present_state: string
  telephone_no: number
  deleted_by: string
  dependentname: string
  id_relationship: string
  is_deleted: string
  id_country: number
  floor_id: number
  citizenship: string
  permanent_address: string
  permanent_city: string
  permanent_postal_code: string
  permanent_state: string
  mobile_no: string
  id_country_permanent: number
  official_birth_date: Date
  birth_date: Date
  gender: string
  age: number
  blood_group: string
  id_roles: number[]
}
