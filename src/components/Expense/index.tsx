import { Box, Paper, Tab, Tabs } from '@mui/material'
import { useEffect, useState } from 'react'
import ReimbursementRequests from './ReimbursementRequests'
import { useSelector } from 'react-redux'
import UserExpense from './User'

const Expense = () => {
  const [selectedTab, setSelectedTab] = useState(0)
  const latestRoles = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { roles: string[] } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.roles,
  )

  const drsInfo = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { drsCount: { count: number } } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.drsCount?.count,
  )
  const role = latestRoles ?? localStorage.getItem('roles')
  const drsCount = drsInfo ?? localStorage.getItem('drsCount')
  return (
    <Paper sx={{ padding: '10px', width: '96%', margin: '15px auto' }}>
      <Tabs
        sx={{
          '& .MuiButtonBase-root': { paddingTop: '0px' },
          '& .MuiTabs-scroller': { height: '40px' },
        }}
        value={selectedTab}
      >
        <Tab label='My Expense' onClick={() => setSelectedTab(0)} />
        {(role?.includes('Admin') || role?.includes('Accountant') || drsCount > 0) && (
          <Tab label='Assigned Expense' onClick={() => setSelectedTab(1)} />
        )}
      </Tabs>
      <Box>
        {/* Expenses page */}

        {/* Reimbursement Request Page */}
        {selectedTab === 0 && <UserExpense />}
        {selectedTab === 1 && <ReimbursementRequests />}
      </Box>
    </Paper>
  )
}

export default Expense
