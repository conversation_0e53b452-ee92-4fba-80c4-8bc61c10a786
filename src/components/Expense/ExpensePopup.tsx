import React, { memo, useEffect, useRef, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  Dialog,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  Modal,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material'
import {
  CancelButton,
  CommentCancelButton,
  InputField,
  addRoundedIcon,
  addedCommentBox,
  autocompleteSx,
  cancelButtonSx,
  commentCancelSx,
  createButtonSx,
  crossButtonSx,
  datePickerSx,
  dialogTitleSx,
  filterAddChipSx,
  filterChipSx,
  postCommentSx,
  radioForm,
  receiptSx,
} from './styles'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import CloseIcon from '@mui/icons-material/Close'
import AddRoundedIcon from '@mui/icons-material/AddRounded'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import { createExpense, getExpenseStatusUpdate, getParticularExpense } from '../../actions'
import { useDispatch, useSelector } from 'react-redux'
import dayjs, { Dayjs } from 'dayjs'
import Loader from '../Common/Loader'
import { toast } from 'react-toastify'
import CommentIcon from '@mui/icons-material/Comment'
import { duration } from '../../utils/date-format'
import { ExpenseData } from './ExpenseTypes'

type ExpensePopupType = {
  open: boolean
  onClose: () => void
  fetchExpense: ({ page }: { page: number }) => void
  createdData: { [key: string]: string | number } | null
}

const ExpensePopup = memo((props: ExpensePopupType) => {
  const { open, onClose, fetchExpense, createdData } = props
  const typeOptions = ['Travel', 'Food', 'Stay']
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: '2px solid #A5C332',
    boxShadow: 24,
    p: 4,
  }
  const [files, setFiles] = useState<File[] | null>(null)
  const [radio, setRadio] = useState('')
  const receiptInputRef = useRef<HTMLInputElement | null>(null)
  const [values, setValues] = useState<{
    type?: string[]
    amount?: string
    date?: null | Dayjs
    description?: string
    title?: string
    other?: string
  } | null>({ date: dayjs() })
  const [disable, setDisable] = useState(true)
  const [openCommentModal, setOpenCommentModal] = useState(false)
  const [newComment, setNewComment] = useState('')
  const icon = <CheckBoxOutlineBlankIcon fontSize='small' />
  const checkedIcon = <CheckBoxIcon fontSize='small' />
  const dispatch = useDispatch()

  const creatingData = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { createExpense: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.createExpense,
  )

  const creatingDataResponse = useSelector(
    (entity: { entities: { getExpenseDetailsEntity: { createExpense: any } } }) =>
      entity.entities.getExpenseDetailsEntity.createExpense,
  )

  const gettingData = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingPerticularExpense: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingPerticularExpense,
  )

  const isUpdatingStatus = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingExpenseUpdate: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingExpenseUpdate,
  )

  const updatedExpenseStatus = useSelector(
    (entity: { entities: { getExpenseDetailsEntity: { getExpenseStatusUpdate: any } } }) =>
      entity.entities.getExpenseDetailsEntity.getExpenseStatusUpdate,
  )

  const dataToDisplay = useSelector(
    (entity: {
      entities: {
        getExpenseDetailsEntity: { getPerticularExpense: ExpenseData }
      }
    }) => entity.entities.getExpenseDetailsEntity.getPerticularExpense,
  )

  useEffect(() => {
    if (updatedExpenseStatus?.message) {
      toast.success(updatedExpenseStatus?.message)
      dispatch(getExpenseStatusUpdate.reset())
      dispatch(getParticularExpense.reset())
      dispatch(getParticularExpense.request({ id: createdData?.id }))
      handleCloseCommentModal()
    }
  }, [isUpdatingStatus])

  useEffect(() => {
    if (createdData?.id) dispatch(getParticularExpense.request({ id: createdData?.id }))
    return () => {
      dispatch(getParticularExpense.reset())
    }
  }, [])

  useEffect(() => {
    if (Object.keys(creatingDataResponse)?.length) {
      toast.success('Reimbursement request created successfully.')
      dispatch(createExpense?.reset())
      fetchExpense({ page: 1 })
      onClose()
    }
  }, [creatingDataResponse])

  useEffect(() => {
    if (
      (values?.type?.includes('Other') ? values?.other?.trim() : values?.type) &&
      values?.amount &&
      values?.date &&
      values?.description?.trim() &&
      values?.title?.trim() &&
      (radio ? files : true)
    )
      setDisable(false)
    else setDisable(true)
  }, [values, files, radio])

  useEffect(() => {
    if (createdData) {
      const typeArray = (createdData.category as string)?.split(',')
      const other = typeArray?.find((typeValue: string) => !typeOptions?.includes(typeValue))
      const types = typeArray
        ?.map((mainTypes: string) => {
          if (typeOptions?.includes(mainTypes)) return mainTypes
          return
        })
        .filter(Boolean)
      setValues({
        type: other ? [...(types as string[]), 'Other'] : (types as string[]),
        amount: createdData.amount as string,
        date: dayjs(createdData.date as string),
        description: createdData.description as string,
        title: createdData.name as string,
        other: other ?? '',
      })
    }
  }, [createdData])

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return
    const selectedFiles = Array.from(event.target.files)
    setFiles((prev) => [...((prev as File[]) ?? []), ...selectedFiles])
    if (receiptInputRef.current) receiptInputRef.current.value = ''
  }

  const handleReceipt = () => {
    if (receiptInputRef.current) receiptInputRef.current?.click()
  }

  const handleExpenseType = (
    event: React.SyntheticEvent<Element, Event>,
    value: string[] | null,
  ) => {
    if (values?.type?.includes('other'))
      setValues((prev) => {
        delete prev?.['other']
        return { ...prev, type: value ?? [] }
      })
    else setValues((prev) => ({ ...prev, type: value ?? [] }))
  }

  const handleAmount = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
    if (!value?.trim()) {
      setValues((prev) => ({
        ...prev,
        amount: '',
      }))
      return
    }
    const [beforeDecimal, afterDecimal] = value?.trim().split('.')
    const sanitizedValueBeforeDecimal = Number(beforeDecimal)
    const sanitizedValueAfterDecimal = Number(afterDecimal)
    if (!Number.isNaN(sanitizedValueBeforeDecimal)) {
      if (!Number.isNaN(sanitizedValueAfterDecimal))
        setValues((prev) => ({
          ...prev,
          amount: `${beforeDecimal}.${afterDecimal?.toString().slice(0, 2)}`,
        }))
      else
        setValues((prev) => ({
          ...prev,
          amount: `${beforeDecimal}${value?.includes('.') ? '.' : ''}`,
        }))
    }
  }

  const handleExpenseTitle = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
    setValues((prev) => ({ ...prev, title: value }))
  }

  const handleStartDateChange = (value: Dayjs | null) => {
    setValues((prev) => ({ ...prev, date: value }))
  }

  const handleDescription = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
    setValues((prev) => ({ ...prev, description: value }))
  }

  const handleOther = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
    setValues((prev) => ({ ...prev, other: value }))
  }
  const handleCreateEdit = () => {
    dispatch(
      createExpense.request({
        name: values?.title ?? '',
        category:
          values?.type?.length === 1
            ? values?.type?.[0] === 'Other'
              ? values.other?.trim() ?? ''
              : values?.type?.[0] ?? ''
            : values?.type?.reduce((result: string, type: string) => {
                return `${result},${type === 'Other' ? values.other?.trim() : type}`
              }) ?? '',
        amount: values?.amount ?? '',
        date: values?.date ? dayjs(values?.date).format('YYYY-MM-DD') : '',
        description: values?.description ?? '',
        ...(radio ? { files: files ?? null } : {}),
      }),
    )
  }

  const handleDelete = (i: number) => {
    setFiles((prev) => {
      prev?.splice(i, 1)
      return [...(prev ?? [])]
    })
  }

  const handleAddComment = () => {
    setOpenCommentModal(true)
  }

  const handleCloseCommentModal = () => {
    setOpenCommentModal(false)
    setNewComment('')
  }

  const handlePostComment = () => {
    if (newComment.trim()) {
      dispatch(
        getExpenseStatusUpdate.request({
          id: createdData?.id,
          data: { commentData: newComment },
          comment: 1,
        }),
      )
    }
  }

  return (
    <>
      <Dialog open={open} maxWidth='md' onClose={onClose}>
        {creatingData && <Loader state={creatingData} />}
        <DialogTitle sx={dialogTitleSx}>
          {`${!createdData ? 'Create' : ''} Reimbursement Request`}
          <IconButton onClick={onClose} sx={crossButtonSx}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <Divider />
        {(gettingData || isUpdatingStatus) && <Loader state={gettingData || isUpdatingStatus} />}
        <Box p={1} position={'relative'} minHeight={'210px'}>
          <Box
            display={'flex'}
            flexWrap={'wrap'}
            padding={'1px'}
            gap={'16px'}
            mt={1.5}
            justifyContent={'space-between'}
          >
            <InputField
              id='outlined-required'
              label='Title'
              size='small'
              fullWidth
              sx={{ marginTop: '2px' }}
              margin='normal'
              onChange={handleExpenseTitle}
              value={values?.title ?? ''}
              width={values?.type?.includes('Other') ? '26.5%' : '31.5%'}
              flexGrow={1}
              disabled={!!createdData}
            />
            <Autocomplete
              autoFocus={false}
              size='small'
              disablePortal
              clearIcon={null}
              id='expense-type'
              options={[...typeOptions, 'Other']}
              ListboxProps={{
                style: {
                  maxHeight: '150px',
                  overflowY: 'auto',
                },
              }}
              sx={autocompleteSx({
                width: values?.type?.includes('Other') ? '24.5%' : '31.5%',
                flexGrow: 1,
              })}
              onChange={handleExpenseType}
              value={values?.type ?? []}
              multiple
              disabled={!!createdData}
              renderOption={(props, option, { selected }) => (
                <li {...props} key={option}>
                  <Checkbox
                    icon={icon}
                    checkedIcon={checkedIcon}
                    checked={selected}
                    sx={{ padding: '5px 9px' }}
                  />
                  {option}
                </li>
              )}
              renderInput={(params: any) => {
                const { InputProps } = params
                const { startAdornment, ...restInputProps } = InputProps
                return (
                  <TextField
                    label='Type'
                    variant='outlined'
                    required
                    {...params}
                    InputProps={{
                      ...restInputProps,
                      startAdornment: values?.type?.length ? (
                        <Box display={'contents'} alignItems={'center'}>
                          <Box>{startAdornment !== undefined && <>{startAdornment?.[0]}</>}</Box>
                          {startAdornment !== undefined && startAdornment?.length > 1 && (
                            <Typography fontSize={'13px'}>+{startAdornment?.length - 1}</Typography>
                          )}
                        </Box>
                      ) : (
                        startAdornment
                      ),
                    }}
                  />
                )
              }}
            />
            {values?.type?.includes('Other') && (
              <InputField
                id='outlined-required'
                label='Other Expense'
                size='small'
                fullWidth
                sx={{ marginTop: '2px' }}
                margin='normal'
                onChange={handleOther}
                value={values?.other ?? ''}
                width={values?.type?.includes('Other') ? '24.5%' : '31.5%'}
                flexGrow={1}
                disabled={!!createdData}
              />
            )}
            <InputField
              id='outlined-required'
              label='Amount'
              size='small'
              fullWidth
              sx={{ marginTop: '2px' }}
              margin='normal'
              onChange={handleAmount}
              value={values?.amount ?? ''}
              width={values?.type?.includes('Other') ? '18%' : '31.5%'}
              flexGrow={1}
              disabled={!!createdData}
            />
          </Box>
          <Box display={'flex'} flexWrap={'wrap'} padding={'1px'} gap={'16px'} mt={2}>
            <InputField
              id='outlined-required'
              label='Description'
              size='small'
              fullWidth
              multiline={true}
              sx={{ marginTop: '2px' }}
              margin='normal'
              onChange={handleDescription}
              value={values?.description ?? ''}
              width={!!createdData ? '60%' : '31.5%'}
              disabled={!!createdData}
              maxRows={5}
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <Box sx={{ width: '31.5%' }}>
                <DatePicker
                  sx={datePickerSx}
                  label='Date'
                  format='MM-DD-YYYY'
                  value={values?.date ?? null}
                  onChange={handleStartDateChange}
                  slotProps={{
                    textField: {
                      required: false,
                    },
                  }}
                  disableFuture
                  disabled={!!createdData}
                />
              </Box>
            </LocalizationProvider>
            {!createdData && (
              <FormControl sx={radioForm({ width: '31.5%' })}>
                <Typography sx={receiptSx}>{'Receipt '}</Typography>
                <RadioGroup
                  row
                  name='row-radio-buttons-group'
                  value={radio}
                  onChange={(e) => setRadio((e.target as HTMLInputElement).value)}
                >
                  <FormControlLabel value={'YES'} control={<Radio />} label='Yes' />
                  <FormControlLabel value={''} control={<Radio />} label='No' />
                </RadioGroup>
              </FormControl>
            )}
            {radio && (
              <Box display={'flex'} alignItems={'center'} justifyContent={'center'}>
                <Box display={'flex'} gap={'5px'}>
                  <input
                    type='file'
                    accept='image/*,.pdf'
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                    ref={receiptInputRef}
                    multiple={true}
                  />
                  <Chip
                    icon={<AddRoundedIcon sx={addRoundedIcon} />}
                    clickable
                    label='Add Receipt'
                    onClick={handleReceipt}
                    sx={filterAddChipSx}
                    color='primary'
                  />
                  {!!files?.length && (
                    <Box
                      border={'1px solid #193C6D'}
                      p={0.8}
                      borderRadius={'8px'}
                      display={'flex'}
                      gap={'5px'}
                      flexWrap={'wrap'}
                      maxWidth={'80%'}
                      overflow={'scroll'}
                      maxHeight={'150px'}
                    >
                      {files?.map((file: File, i: number) => {
                        return (
                          <Chip
                            label={file.name}
                            variant='outlined'
                            onDelete={() => handleDelete(i)}
                            sx={filterChipSx}
                            color='primary'
                          />
                        )
                      })}
                    </Box>
                  )}
                </Box>
              </Box>
            )}
          </Box>

          <Box
            display={'flex'}
            justifyContent={'flex-end'}
            flexGrow={1}
            mt={1}
            mb={0.5}
            height={'36px'}
            position={'sticky'}
            top={500}
          >
            <CancelButton variant='contained' color='primary' sx={cancelButtonSx} onClick={onClose}>
              Cancel
            </CancelButton>
            {createdData ? (
              <Button
                variant='contained'
                color='primary'
                sx={{ ...createButtonSx, width: '180px' }}
                onClick={handleAddComment}
                startIcon={<CommentIcon />}
              >
                Add Comment
              </Button>
            ) : (
              <Button
                variant='contained'
                color='primary'
                sx={createButtonSx}
                onClick={handleCreateEdit}
                disabled={disable}
              >
                Create
              </Button>
            )}
          </Box>
        </Box>
        {dataToDisplay?.comments?.length > 0 && createdData && (
          <Grid item xs={12} sm={12} mb={1}>
            <Typography sx={{ fontFamily: 'Montserrat-SemiBold', fontSize: '20px' }} align='center'>
              Comments
            </Typography>
            <Box sx={addedCommentBox}>
              {dataToDisplay?.comments?.map((data: any, index: number) => (
                <React.Fragment key={`${index}-${data.comment}`}>
                  <Typography component='span' sx={{ fontSize: 'smaller', color: '#A82E2F' }}>
                    {`${data?.created_by}@${duration(data?.created_at)} ago`}
                  </Typography>
                  <InputField
                    type='text'
                    fullWidth={true}
                    multiline={true}
                    id='comments'
                    name='comments'
                    InputProps={{ readOnly: true }}
                    value={data.comment}
                  />
                </React.Fragment>
              ))}
            </Box>
          </Grid>
        )}
        <Modal
          open={openCommentModal}
          onClose={handleCloseCommentModal}
          aria-labelledby='modal-comment'
          aria-describedby='modal-comment-desc'
        >
          <Box sx={style}>
            <Typography id='modal-comment' variant='h6' component='h6' sx={{ textAlign: 'center' }}>
              Add Comment
            </Typography>
            <TextField
              type='text'
              id='comments'
              name='comments'
              fullWidth
              multiline
              rows={2}
              maxRows={4}
              onChange={(e) => setNewComment(e.target.value)}
            />
            <Box textAlign='left'>
              <Button
                size='small'
                variant='contained'
                sx={postCommentSx}
                onClick={handlePostComment}
                disabled={!newComment?.trim()}
              >
                {'Submit'}
              </Button>
            </Box>
            <Box textAlign='right'>
              <CommentCancelButton
                variant='contained'
                size='small'
                sx={commentCancelSx}
                onClick={handleCloseCommentModal}
              >
                Cancel
              </CommentCancelButton>
            </Box>
          </Box>
        </Modal>
      </Dialog>
    </>
  )
})

export default ExpensePopup
