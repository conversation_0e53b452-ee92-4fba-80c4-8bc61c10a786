import React, { useCallback, useEffect, useState } from 'react'
import { Autocomplete, Box, Button, Chip, Pagination, Stack, TextField } from '@mui/material'
import { autocompleteSx, createButtonSx } from './styles'
import CustomTable from '../Common/Table'
import ExpensePopup from './ExpensePopup'
import { useDispatch, useSelector } from 'react-redux'
import { getReimbursementRequests } from '../../actions'
import DebouncedSearchedBox from '../Common/DebouncedSearchBox'
import { getExpenseDetailsEntity } from '../../reducers/entities/expenses'
import { RootState } from '../../configureStore'
import Loader from '../Common/Loader'
import DateRangePicker from './ReimbursementRequests/DateRangePicker'
import DoneOutlineIcon from '@mui/icons-material/DoneOutline'
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt'
import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt'
import ErrorIcon from '@mui/icons-material/Error'
import dayjs from 'dayjs'
import ShowEnteries from './ReimbursementRequests/ShowEntries'

type TableType = {
  expenseTitle: string
  expenseType: string
  amount: number
  date: string
  description: string
  is_approved: number
}

const UserExpense = () => {
  const tableHeading: { [key: string]: string } = {
    name: 'Title',
    category: 'Type',
    amount: 'Amount',
    date: 'Date',
    description: 'Description',
    is_approved: 'Status',
  }
  const statusValues: { [key: string]: string } = {
    All: '',
    Pending: '0',
    Approved: '1',
    Rejected: '2',
  }
  const allStatus = ['Pending', 'Approved', 'Rejected', 'Reimbursed']
  const expenseData = useSelector(
    (state: RootState) => getExpenseDetailsEntity(state).getReimbursementRequests,
  )
  const gettingData = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingReimburseRequest: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingReimburseRequest,
  )
  const latestRoles = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { roles: string[] } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.roles,
  )

  const drsInfo = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { drsCount: { count: number } } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.drsCount?.count,
  )
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [status, setStatus] = useState<string>('All')
  const [startDate, setStartDate] = useState(
    dayjs(new Date(new Date().getFullYear(), new Date().getMonth(), 1)),
  )
  const [endDate, setEndDate] = useState(dayjs().endOf('month'))
  const [showResults, setShowResults] = useState(false)
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(100)
  const [createdData, setCreatedData] = useState<TableType | null>(null)
  const role = latestRoles ?? localStorage.getItem('roles')
  const drsCount = drsInfo ?? localStorage.getItem('drsCount')
  const dispatch = useDispatch()
  useEffect(() => {
    if (latestRoles) fetchExpense({ page: page })
  }, [searchQuery, status, startDate, endDate, showResults, latestRoles, page, limit])

  const fetchExpense = useCallback(
    ({ page }: { page: number }) => {
      dispatch(
        getReimbursementRequests.request({
          page: page,
          limit: limit,
          is_approve: statusValues[status],
          ...(searchQuery.trim() && { search: searchQuery?.trim() }),
          ...(role?.includes('Admin') || role?.includes('Accountant') || drsCount > 0
            ? {
                ...(startDate && { startDate: startDate?.format('YYYY-MM-DD').toString() }),
                ...(endDate && { endDate: endDate?.format('YYYY-MM-DD').toString() }),
              }
            : {}),
        }),
      )
    },
    [startDate, endDate, showResults, searchQuery, limit, status],
  )

  const onClose = useCallback(() => {
    setOpen(false)
    setCreatedData(null)
  }, [])

  const handleRowClick = useCallback((data: TableType) => {
    setCreatedData(data)
    setOpen(true)
  }, [])

  const getChipColor = (status: number) => {
    switch (status) {
      case 0:
        return 'warning'
      case 1:
        return 'success'
      case 2:
        return 'error'
      case 3:
        return 'secondary'
    }
  }

  const getChipIcon = (status: number) => {
    switch (status) {
      case 0:
        return <ErrorIcon />
      case 1:
        return <ThumbUpAltIcon />
      case 2:
        return <ThumbDownAltIcon />
      case 3:
        return <DoneOutlineIcon />
    }
  }

  const cellRenderer = {
    date: ({ data }: { data: TableType }) => {
      return (
        <>
          {data?.date
            ? new Date(data?.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
              })
            : ''}
        </>
      )
    },
    is_approved: ({ data }: { data: TableType }) => {
      return (
        <Chip
          variant='outlined'
          color={getChipColor(data?.is_approved ?? 0)}
          label={allStatus[data?.is_approved ?? 0]}
          icon={getChipIcon(data?.is_approved ?? 0)}
          sx={{ width: '120px' }}
        />
      )
    },
    amount: ({ data }: { data: TableType }) => {
      return <>{`₹${data.amount}`}</>
    },
  }

  const handleExpenseStatus = (
    event: React.SyntheticEvent<Element, Event>,
    value: string | null,
  ) => {
    setStatus(() => value ?? 'All')
  }

  const handleChangePage = (event: React.ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage)
  }

  return (
    <>
      {open && (
        <ExpensePopup
          open={open}
          onClose={onClose}
          fetchExpense={fetchExpense}
          createdData={createdData}
        />
      )}
      {(gettingData || !latestRoles) && <Loader state={gettingData || !latestRoles} />}
      <Box
        display={'flex'}
        flexWrap={'wrap'}
        justifyContent={'space-between'}
        flexGrow={1}
        mt={1}
        mb={1}
        height={'auto'}
        alignItems={'center'}
      >
        <DebouncedSearchedBox
          placeHolder='Search by Title and Type'
          setSearchQuery={setSearchQuery}
          searchMargin={'0'}
          setPage={setPage}
        />
        <Box
          display={'flex'}
          gap={'16px'}
          flexGrow={1}
          alignItems={'center'}
          justifyContent={'flex-end'}
        >
          <Autocomplete
            autoFocus={false}
            size='small'
            disablePortal
            clearIcon={null}
            defaultValue={'All'}
            options={['All', 'Pending', 'Rejected', 'Approved']}
            renderInput={(params) => (
              <TextField {...params} label='Status' variant='outlined' required={false} />
            )}
            ListboxProps={{
              style: {
                maxHeight: '150px',
                overflowY: 'auto',
              },
            }}
            sx={autocompleteSx()}
            onChange={handleExpenseStatus}
            value={status}
          />
          {(role?.includes('Admin') || role?.includes('Accountant') || drsCount > 0) && (
            <Box display='flex' alignItems='center' justifyContent='center'>
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                setShowResults={setShowResults}
                searchQuery={searchQuery}
              />
            </Box>
          )}
          <Button
            variant='contained'
            color='primary'
            sx={createButtonSx}
            onClick={() => {
              setOpen(true)
            }}
          >
            Create
          </Button>
        </Box>
      </Box>
      <CustomTable
        rowData={expenseData?.result ?? []}
        heading={tableHeading}
        cellRenderer={cellRenderer}
        handleRowClick={handleRowClick}
      />
      {expenseData?.result?.length > 0 && (
        <Stack spacing={2} direction='row' justifyContent='flex-end' mt='10px'>
          <ShowEnteries setPage={setPage} setRowsPerPage={setLimit} rowsPerPage={limit} />
          <Pagination
            count={Math.ceil(expenseData?.count / limit)}
            page={page}
            onChange={handleChangePage}
            color='primary'
          />
        </Stack>
      )}
    </>
  )
}

export default UserExpense
