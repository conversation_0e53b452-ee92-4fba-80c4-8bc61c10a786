type User = {
  first_name: string
  last_name: string
}

export type ReimbursementRequestDataType = {
  id: number
  name: string
  amount: string
  date: string
  category: string
  user: User
  description: string
  is_approved: 0 | 1
  created_at: string
  updated_at: string
}

export type ExpenseData = {
  id: any
  name: string
  amount: number
  date: string
  category: string
  description: string
  is_approved: number
  created_at: string
  url: any
  user_first_name: string
  user_last_name: string
  comments: any
  
}
