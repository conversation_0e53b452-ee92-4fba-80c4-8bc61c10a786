import { Button, TextField, Typography, styled } from '@mui/material'
import style from '../../utils/styles.json'

export const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '28px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '20px',
}))

export const InputField = styled(TextField)<{
  multiline?: boolean
  width?: string
  flexGrow?: number
}>(({ multiline, width, flexGrow }) => ({
  width: `${width} !important` ?? '250px !important',
  flexGrow,
  margin: 0,
  marginTop: '0 !important',
  '& .MuiOutlinedInput-input': {
    padding: !multiline ? '11px 14px' : '0px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
    ...(!multiline ? { height: '35px' } : {}),
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
}))

export const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: style.FONT_BOLD,
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

export const CommentCancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  // height: '42px',
  fontFamily: style.FONT_BOLD,
  width: '20%',
  // borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

export const expenseCard = {
  margin: '20px',
  padding: '20px',
}

export const autocompleteSx = (props?: { width?: string; flexGrow?: number }) => {
  return {
    width: props?.width ?? '250px',
    ...(props?.flexGrow && { flexGrow: props?.flexGrow }),
    '& .MuiFormLabel-asterisk': {
      color: 'red',
    },
    '& .MuiTextField-root': {
      margin: 0,
      marginBottom: 0,
    },
    '& label.Mui-focused': {
      color: '#666666',
      bgcolor: 'white',
    },
    '& .MuiOutlinedInput-root': {
      height: '35px',
      fontSize: '13px',
      '&.Mui-focused fieldset': {
        borderRadius: '20px',
      },
      '& fieldset': {
        borderRadius: '20px',
      },
      '& .MuiInputBase-input': {
        borderRadius: '15px',
        // minWidth: '150px',
        fontFamily: style.FONT_MEDIUM,
        letterSpacing: '0',
      },
    },
    '& .MuiFormLabel-root.MuiInputLabel-root': {
      lineHeight: 'unset',
      fontSize: '13px',
      fontFamily: style.FONT_MEDIUM,
    },
    svg: {
      color: '#666666',
    },
  }
}

export const datePickerSx = {
  border: 'none',
  marginTop: 0,
  marginBottom: 0,
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    height: '35px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiInputLabel-root': {
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
}

export const createButtonSx = {
  fontSize: '13px',
  height: '36px',
  borderRadius: '20px',
  padding: '5px 20px',
  width: '100px',
  fontFamily: style.FONT_MEDIUM,
  '&.Mui-disabled': {
    opacity: 1,
    color: '#ffffff',
    cursor: 'not-allowed',
  },
}

export const crossButtonSx = { position: 'absolute', top: '5px', right: '5px' }

export const filterButtonSx = { color: '#193C6D', fontSize: '30px' }

export const dialogTitleSx = { m: 0, p: 1, position: 'relative', textAlign: 'center' }

export const filterTitleSx = { textAlign: 'center', fontFamily: style.FONT_MEDIUM }

export const filterChipSx = {
  height: '26px',
  fontFamily: style.FONT_MEDIUM,
  fontSize: '11px',
  color: '#193C6D',
  border: '1px solid #193C6D',
  maxWidth: '50%',
}

export const filterAddChipSx = {
  backgroundColor: '#193C6D',
  color: '#FFFFFF',
  '&:hover': {
    backgroundColor: '#193C6D',
    color: '#FFFFFF',
  },
}

export const filterHeadingBoxSx = {
  height: '50px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
}

export const receiptSx = { fontSize: '14px', fontFamily: style.FONT_MEDIUM, color: '#193C6D' }

export const radioForm = (props?: { width?: string }) => ({
  width: `${props?.width} !important` ?? '250px !important',
  display: 'flex',
  flexDirection: 'row',
  gap: '16px',
  alignItems: 'center',
  height: '40px',
})

export const receiptBoxSx = {
  height: '100px',
  width: '250px',
  border: '1px solid #193C6D',
  borderRadius: '4px',
  backgroundColor: 'unset',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '5px',
  padding: 0,
  '&:hover': { backgroundColor: 'unset !important' },
}

export const addRoundedIcon = {
  fontSize: '20px',
  color: '#FFFFFF !important',
}

export const receiptDeleteIconSx = {
  color: 'red',
  fontSize: '20px',
}

export const iconReceiptDeleteIconSx = {
  height: '35px',
}

export const styles = {
  actionBarConatiner: {
    border: '1px solid #E0E0E0',
    borderRadius: '5px',
    marginBottom: '10px',
  },
  actionBar: {
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: '0 1rem',
  },
}

export const cancelButtonSx = {
  fontSize: '13px',
  height: '36px',
  borderRadius: '20px',
  padding: '5px 20px',
  marginRight: '10px',
  marginBottom: 2,
  width: '100px',
  background: '#E2E2E2',
  color: '#000000',
  fontFamily: style.FONT_MEDIUM,
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}

export const postCommentSx = {
  fontSize: '14px',
  padding: '5px 15px 5px 10px',
  bgcolor: '#193C6D',
  '&:hover': {
    bgcolor: '#193C6D',
    opacity: 0.9,
  },
}

export const commentCancelSx = {
  fontSize: '14px',
  width: '25%',
  bottom: '34px',
  padding: '5px 15px 5px 10px',
  bgcolor: '#ECECEC',
  '&:hover': { bgcolor: '#ECECEC', opacity: '0.9' },
}

export const addedCommentBox={
  maxHeight: '400px',
  overflowY: 'scroll',
  margin: 'auto',
  padding: '5px',
  borderTop: 'none',
  border: '2px solid lightgrey',
  maxWidth: 'calc(100% - 32px)',
}