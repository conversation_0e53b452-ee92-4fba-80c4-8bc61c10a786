import { Autocomplete, Box, TextField } from '@mui/material'
import DateRangePicker from './DateRangePicker'
import { Dayjs } from 'dayjs'
import { Dispatch, SetStateAction } from 'react'
import DebouncedSearchedBox from '../../Common/DebouncedSearchBox'
import { styles } from '../styles'
import { autocompleteSx } from '../styles'

const FilterBar = (props: {
  searchQuery: string
  setSearchQuery: Dispatch<SetStateAction<string>>
  setPage: Dispatch<SetStateAction<number>>
  startDate: Dayjs
  endDate: Dayjs
  setStartDate: Dispatch<SetStateAction<Dayjs>>
  setEndDate: Dispatch<SetStateAction<Dayjs>>
  setShowResults: Dispatch<SetStateAction<boolean>>
  setStatus: Dispatch<SetStateAction<string>>
  status: string
}) => {
  const {
    searchQuery,
    setSearchQuery,
    startDate,
    endDate,
    setStartDate,
    setEndDate,
    setShowResults,
    setPage,
    setStatus,
    status,
  } = props

  const handleExpenseStatus = (
    event: React.SyntheticEvent<Element, Event>,
    value: string | null,
  ) => {
    setStatus(() => value ?? 'All')
  }

  return (
    <Box>
      <Box sx={styles.actionBarConatiner}>
        <Box sx={styles.actionBar}>
          <DebouncedSearchedBox
            placeHolder='Search by Name and Title'
            setSearchQuery={setSearchQuery}
            setPage={setPage}
          />
          <Box display='flex' alignItems='center' justifyContent='center'>
            <Autocomplete
              autoFocus={false}
              size='small'
              disablePortal
              clearIcon={null}
              defaultValue={'All'}
              options={['All', 'Pending', 'Rejected', 'Approved']}
              renderInput={(params) => (
                <TextField {...params} label='Status' variant='outlined' required={false} />
              )}
              ListboxProps={{
                style: {
                  maxHeight: '150px',
                  overflowY: 'auto',
                },
              }}
              sx={autocompleteSx()}
              onChange={handleExpenseStatus}
              value={status}
            />
            <DateRangePicker
              startDate={startDate}
              endDate={endDate}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              setShowResults={setShowResults}
              searchQuery={searchQuery}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default FilterBar
