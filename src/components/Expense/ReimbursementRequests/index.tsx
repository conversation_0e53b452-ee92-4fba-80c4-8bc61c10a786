import {
  Box,
  Chip,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import { StyledTableCell } from '../../Common/CommonStyles'
import { useDispatch, useSelector } from 'react-redux'
import { useEffect, useState } from 'react'
import { getReimbursementRequests } from '../../../actions'
import Loader from '../../Common/Loader'
import { ReimbursementRequestDataType } from '.././ExpenseTypes'
import DoneOutlineIcon from '@mui/icons-material/DoneOutline'
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt'
import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt'
import ErrorIcon from '@mui/icons-material/Error'
import dayjs from 'dayjs'
import FilterBar from './FilterBar'
import DialogDetails from './DialogDetails'
import ShowEnteries from './ShowEntries'

const ReimbursementRequests = () => {
  const status = ['Pending', 'Approved', 'Rejected', 'Reimbursed']
  const dispatch = useDispatch()

  const [startDate, setStartDate] = useState(dayjs().subtract(7, 'day'))
  const [endDate, setEndDate] = useState(dayjs())
  const [searchQuery, setSearchQuery] = useState('')
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(100)
  const [showResults, setShowResults] = useState(false)
  const [dialogID, setDialogID] = useState<null | number>(null)
  const [statusState, setStatusState] = useState<string>('Pending')

  const dataToDisplay = useSelector(
    (entity: {
      entities: {
        getExpenseDetailsEntity: {
          getReimbursementRequests: { result: ReimbursementRequestDataType[]; count: number }
        }
      }
    }) => entity.entities.getExpenseDetailsEntity.getReimbursementRequests,
  )

  const gettingData = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingReimburseRequest: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingReimburseRequest,
  )

  const statusValues: { [key: string]: string } = {
    All: '',
    Pending: '0',
    Approved: '1',
    Rejected: '2',
  }

  useEffect(() => {
    if (!dialogID) {
      dispatch(
        getReimbursementRequests.request({
          startDate: startDate?.format('YYYY-MM-DD').toString() ?? '',
          endDate: endDate?.format('YYYY-MM-DD').toString() ?? '',
          page: page ?? 1,
          limit: limit ?? 100,
          search: searchQuery ?? '',
          type: 1,
          is_approve: statusValues[statusState],
        }),
      )
    }
  }, [showResults, page, limit, searchQuery, dialogID, statusState])

  const getChipColor = (status: number) => {
    switch (status) {
      case 0:
        return 'warning'
      case 1:
        return 'success'
      case 2:
        return 'error'
      case 3:
        return 'secondary'
    }
  }

  const getChipIcon = (status: number) => {
    switch (status) {
      case 0:
        return <ErrorIcon />
      case 1:
        return <ThumbUpAltIcon />
      case 2:
        return <ThumbDownAltIcon />
      case 3:
        return <DoneOutlineIcon />
    }
  }

  const handleChangePage = (event: React.ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage)
  }

  return (
    <Box>
      {gettingData && <Loader state={gettingData} />}
      {dialogID && <DialogDetails id={dialogID} setDialogID={setDialogID} />}
      <Box>
        <FilterBar
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          setShowResults={setShowResults}
          setPage={setPage}
          setStatus={setStatusState}
          status={statusState}
        />
      </Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <StyledTableCell>Name</StyledTableCell>
              <StyledTableCell>Title</StyledTableCell>
              <StyledTableCell>Type</StyledTableCell>
              <StyledTableCell>Amount</StyledTableCell>
              <StyledTableCell>Date</StyledTableCell>
              <StyledTableCell>Description</StyledTableCell>
              <StyledTableCell sx={{ width: '140px' }}>Status</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {!gettingData && dataToDisplay?.result?.length ? (
              dataToDisplay?.result?.map((item: ReimbursementRequestDataType) => (
                <TableRow
                  key={item?.id}
                  onClick={() => setDialogID(item?.id)}
                  sx={{ cursor: 'pointer' }}
                >
                  <StyledTableCell>
                    {item?.user?.first_name ?? '' + ' ' + item?.user?.last_name ?? ''}
                  </StyledTableCell>
                  <StyledTableCell>{item?.name ?? 'NA'}</StyledTableCell>
                  <StyledTableCell>{item?.category ?? 'NA'}</StyledTableCell>
                  <StyledTableCell>₹{item?.amount ?? '0'}</StyledTableCell>
                  <StyledTableCell>
                    {item?.date &&
                      new Date(item?.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })}
                  </StyledTableCell>
                  <StyledTableCell>{item?.description ?? 'NA'}</StyledTableCell>
                  <StyledTableCell>
                    <Chip
                      variant='outlined'
                      color={getChipColor(item?.is_approved ?? 0)}
                      label={status[item?.is_approved ?? 0]}
                      icon={getChipIcon(item?.is_approved ?? 0)}
                      sx={{ width: '120px' }}
                    />
                  </StyledTableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <StyledTableCell align='center' colSpan={7}>
                  <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                    No records found
                  </Typography>
                </StyledTableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {dataToDisplay?.result?.length > 0 && (
        <Stack spacing={2} direction='row' justifyContent='flex-end' mt='10px'>
          <ShowEnteries setPage={setPage} setRowsPerPage={setLimit} rowsPerPage={limit} />
          <Pagination
            count={Math.ceil(dataToDisplay?.count / limit)}
            page={page}
            onChange={handleChangePage}
            color='primary'
          />
        </Stack>
      )}
    </Box>
  )
}

export default ReimbursementRequests
