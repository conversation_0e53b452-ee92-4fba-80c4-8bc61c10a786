import {
  Box,
  Button,
  Dialog,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputBase,
  InputLabel,
  Modal,
  TextField,
  Tooltip,
  Typography,
  styled,
} from '@mui/material'
import { duration } from '../../../utils/date-format'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { downloadAttachments, getExpenseStatusUpdate, getParticularExpense } from '../../../actions'
import CloseIcon from '@mui/icons-material/Close'
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt'
import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt'
import DoneOutlineIcon from '@mui/icons-material/DoneOutline'
import CommentIcon from '@mui/icons-material/Comment'
import EditIcon from '@mui/icons-material/Edit'
import { ExpenseData } from '../ExpenseTypes'
import Loader from '../../Common/Loader'
import { toast } from 'react-toastify'
import InfoGif from '../../../assets/images/infoGif.gif'
import styles from '../../../utils/styles.json'
import DownloadForOfflineRoundedIcon from '@mui/icons-material/DownloadForOfflineRounded'

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  'label + &': {
    marginTop: theme.spacing(2.5),
  },
  '& .MuiInputBase-input': {
    borderRadius: 4,
    position: 'relative',
    backgroundColor: '#F3F6F9',
    border: '1px solid',
    borderColor: '#E0E3E7',
    fontSize: '14px',
    width: '270px',
    padding: '7px 12px',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  // height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  // borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '15px',
    lineHeight: '1.8em',
  },
}))

const style = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #A5C332',
  boxShadow: 24,
  p: 4,
}

const DialogDetails = (props: {
  id: number | null
  setDialogID: Dispatch<SetStateAction<number | null>>
}) => {
  const dispatch = useDispatch()
  const dataToDisplay = useSelector(
    (entity: {
      entities: {
        getExpenseDetailsEntity: { getPerticularExpense: ExpenseData }
      }
    }) => entity.entities.getExpenseDetailsEntity.getPerticularExpense,
  )

  const [openCommentModal, setOpenCommentModal] = useState(false)
  const [newComment, setNewComment] = useState('')
  const [modalType, setModalType] = useState<'comment' | 'approval' | null>(null)
  const [commentModalData, setCommentModalData] = useState<{
    id: any
    data: { is_approved: number }
  } | null>(null)
  const [amount, setAmount] = useState<number>(dataToDisplay?.amount)
  const isCommentEmpty = newComment.trim() === ''

  useEffect(() => {
    setAmount(dataToDisplay?.amount)
  }, [dataToDisplay?.amount])

  const updatedExpenseStatus = useSelector(
    (entity: { entities: { getExpenseDetailsEntity: { getExpenseStatusUpdate: any } } }) =>
      entity.entities.getExpenseDetailsEntity.getExpenseStatusUpdate,
  )

  const gettingData = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingPerticularExpense: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingPerticularExpense,
  )

  const isUpdatingStatus = useSelector(
    (entity: { ui: { getExpenseDeatilsUI: { isGettingExpenseUpdate: boolean } } }) =>
      entity.ui.getExpenseDeatilsUI.isGettingExpenseUpdate,
  )

  const latestRoles = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { roles: string[] } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.roles,
  )

  const drsInfo = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { drsCount: { count: number } } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.drsCount?.count,
  )

  const downloadLink = useSelector(
    (entity: {
      entities: {
        getExpenseDetailsEntity: { downloadAttachments: any }
      }
    }) => entity.entities.getExpenseDetailsEntity.downloadAttachments,
  )

  const role = latestRoles ?? localStorage.getItem('roles')
  const drsCount = drsInfo ?? localStorage.getItem('drsCount')

  useEffect(() => {
    if (downloadLink?.length) {
      downloadFilesSequentially(downloadLink)
    }
  })

  useEffect(() => {
    if (updatedExpenseStatus?.message) {
      toast.success(updatedExpenseStatus?.message)
      dispatch(getExpenseStatusUpdate.reset())
      dispatch(getParticularExpense.reset())
      props?.setDialogID(null)
    }
  }, [isUpdatingStatus])

  useEffect(() => {
    dispatch(getParticularExpense.request({ id: props?.id }))
  }, [])

  const handleOpenCommentModal = (id: any, data: { is_approved: number }) => {
    setModalType('approval')
    setCommentModalData({ id, data })
    setOpenCommentModal(true)
  }
  const handleCloseCommentModal = () => {
    setOpenCommentModal(false)
    setNewComment('')
    setCommentModalData(null)
  }

  const handleOpenComment = () => {
    setModalType('comment')
    setOpenCommentModal(true)
  }

  const handleEditExpense = () => {
    dispatch(
      getExpenseStatusUpdate.request({
        id: props?.id,
        data: { amount: amount },
        edit: 1,
        comment: 0,
      }),
    )
  }

  const handlePostComment = () => {
    if (newComment.trim()) {
      if (modalType === 'comment') {
        dispatch(
          getExpenseStatusUpdate.request({
            id: props?.id,
            data: { commentData: newComment },
            comment: 1,
          }),
        )
      } else {
        dispatch(
          getExpenseStatusUpdate.request({
            id: commentModalData?.id,
            data: { ...commentModalData?.data, commentData: newComment },
            comment: 0,
          }),
        )
      }
      handleCloseCommentModal()
    }
  }

  async function downloadFilesSequentially(links: string[]) {
    for (const link of links) {
      await triggerDownload(link)
    }
    dispatch(downloadAttachments?.reset())
  }

  // Helper function to trigger a single download
  function triggerDownload(link: string): Promise<void> {
    return new Promise((resolve) => {
      const a = document.createElement('a')
      a.href = link
      a.download = '' // Let the browser decide the file name
      a.target = '_blank'

      // Required for Firefox
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      // Delay a bit to avoid browser throttling
      setTimeout(() => {
        resolve()
      }, 300) // 300ms between downloads
    })
  }

  const handleDownloadAttachments = () => {
    dispatch(downloadAttachments.request({ id: `${props.id}` }))
  }

  return (
    <Dialog
      open={(props?.id !== null && props?.id > 0) ?? false}
      sx={{
        '& .MuiDialog-paper': {
          minWidth: '80%',
        },
      }}
      onClose={() => {
        dispatch(getExpenseStatusUpdate.reset())
        dispatch(getParticularExpense.reset())
        props?.setDialogID(null)
      }}
    >
      {(gettingData || isUpdatingStatus) && <Loader state={gettingData || isUpdatingStatus} />}
      <Box
        display={'flex'}
        alignItems={'center'}
        justifyContent={'space-between'}
        padding={'10px 20px'}
      >
        <Box>
          <Typography sx={{ fontFamily: 'Montserrat-SemiBold', fontSize: '24px' }}>
            Reimbursement Details
          </Typography>
        </Box>
        <Box onClick={() => props?.setDialogID(null)}>
          <CloseIcon sx={{ cursor: 'pointer' }} />
        </Box>
      </Box>
      <Divider />
      <Box display={'flex'} alignItems={'stretch'}>
        {/* <iframe src={dataToDisplay?.url} width='100%' height='100%' />
        </Box> */}
        <Box
          width='70%'
          bgcolor='#CAD2C7'
          display='flex'
          alignItems='center'
          justifyContent='center'
          flexDirection='column'
        >
          {dataToDisplay?.url?.length ? (
            dataToDisplay.url.map((link: any, index: any) => (
              <iframe key={index} src={link} width='100%' height='100%' />
            ))
          ) : (
            <Typography>No Receipts Uploaded</Typography>
          )}
        </Box>

        <Box width='30%'>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='name'>
                Name
              </InputLabel>
              <BootstrapInput
                id='name'
                disabled
                value={`${
                  (dataToDisplay?.user_first_name ?? '') +
                  ' ' +
                  (dataToDisplay?.user_last_name ?? '')
                }`}
              />
            </FormControl>
          </Box>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='subject'>
                Subject
              </InputLabel>
              <BootstrapInput id='subject' disabled value={dataToDisplay?.name ?? ''} />
            </FormControl>
          </Box>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='description'>
                Description
              </InputLabel>
              <BootstrapInput
                multiline
                id='description'
                disabled
                maxRows={3}
                value={dataToDisplay?.description ?? ''}
              />
            </FormControl>
          </Box>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='category'>
                Category
              </InputLabel>
              <BootstrapInput id='category' disabled value={dataToDisplay?.category ?? ''} />
            </FormControl>
          </Box>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='date'>
                Date
              </InputLabel>
              <BootstrapInput
                id='date'
                disabled
                value={
                  dataToDisplay?.date
                    ? new Date(dataToDisplay?.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })
                    : ''
                }
              />
            </FormControl>
          </Box>
          <Box margin={'10px'}>
            <FormControl variant='standard'>
              <InputLabel shrink htmlFor='amount'>
                Amount
              </InputLabel>
              <BootstrapInput
                id='amount'
                disabled={
                  !(role?.includes('Accountant') || role?.includes('Admin')) ||
                  dataToDisplay?.is_approved !== 0
                }
                value={amount}
                sx={{ width: '97%' }}
                onChange={(e) => {
                  const numericValue = e.target.value
                  if (!isNaN(Number(numericValue))) setAmount(Number(numericValue))
                }}
                startAdornment={<InputAdornment position='start'>₹</InputAdornment>}
              />
            </FormControl>
          </Box>
        </Box>
      </Box>
      <Divider />
      <Box
        padding='10px'
        display={'flex'}
        alignItems={'center'}
        justifyContent={'space-between'}
        gap='20px'
      >
        <Box display={'flex'} alignItems={'center'} justifyContent={'flex-start'} gap={'10px'}>
          {dataToDisplay?.is_approved === 0 && role?.includes('Accountant') && (
            <>
              <Box component={'img'} src={InfoGif} width={'35px'}></Box>
              <Box>
                <Typography fontFamily={'Montserrat-Medium'} color={'primary'} fontSize={'13px'}>
                  Not approved by manager yet!
                </Typography>
              </Box>
            </>
          )}
        </Box>
        <Box display={'flex'} alignItems={'center'} justifyContent={'flex-end'} gap='20px'>
          <Tooltip title='Download Slip'>
            <IconButton onClick={handleDownloadAttachments} disabled={!dataToDisplay?.url?.length}>
              <DownloadForOfflineRoundedIcon
                sx={{ fontSize: '30px', color: !dataToDisplay?.url?.length ? 'unset' : '#193C6D' }}
              />
            </IconButton>
          </Tooltip>
          <Button
            size='small'
            variant='contained'
            disableElevation
            startIcon={<CommentIcon />}
            sx={{
              fontSize: '14px',
              padding: '5px 15px 5px 10px',
              width: '180px !important',
              // bgcolor: '#C11828',
              // '&:hover': { bgcolor: '#C11828', opacity: '0.9' },
            }}
            onClick={() => handleOpenComment()}
          >
            Add Comment
          </Button>
          <Button
            size='small'
            variant='contained'
            disableElevation
            startIcon={<ThumbDownAltIcon />}
            sx={{
              fontSize: '14px',
              padding: '5px 15px 5px 10px',
              bgcolor: '#C11828',
              '&:hover': { bgcolor: '#C11828', opacity: '0.9' },
            }}
            disabled={dataToDisplay?.is_approved === 2 || dataToDisplay?.is_approved === 3}
            onClick={() => handleOpenCommentModal(props?.id, { is_approved: 2 })}
          >
            {dataToDisplay?.is_approved === 2 ? 'Rejected' : 'Reject'}
          </Button>
          {(role?.includes('Admin') || drsCount > 0) && dataToDisplay?.is_approved !== 3 && (
            <Button
              size='small'
              variant='contained'
              startIcon={<ThumbUpAltIcon />}
              sx={{
                fontSize: '14px',
                padding: '5px 15px 5px 10px',
                bgcolor: '#2E7D32',
                '&:hover': { bgcolor: '#2E7D32', opacity: '0.9' },
              }}
              onClick={() => handleOpenCommentModal(props?.id, { is_approved: 1 })}
              disabled={dataToDisplay?.is_approved === 1}
            >
              {dataToDisplay?.is_approved === 1 ? 'Approved' : 'Approve'}
            </Button>
          )}
          {(role?.includes('Accountant') || dataToDisplay?.is_approved === 3) && (
            <Button
              size='small'
              variant='contained'
              startIcon={<DoneOutlineIcon />}
              sx={{
                fontSize: '14px',
                padding: '5px 15px 5px 10px',
                bgcolor: '#9C27B0',
                '&:hover': { bgcolor: '#9C27B0', opacity: '0.9' },
              }}
              onClick={() => handleOpenCommentModal(props?.id, { is_approved: 3 })}
              disabled={dataToDisplay?.is_approved === 3 || dataToDisplay?.is_approved === 2}
            >
              {dataToDisplay?.is_approved === 3 ? 'Reimbursed' : 'Reimburse'}
            </Button>
          )}
          {(role?.includes('Admin') || role?.includes('Accountant')) &&
            dataToDisplay?.is_approved === 0 && (
              <Button
                size='small'
                variant='contained'
                startIcon={<EditIcon />}
                sx={{
                  fontSize: '14px',
                  padding: '5px 15px 5px 10px',
                  bgcolor: '#ed6c02',
                  '&:hover': { bgcolor: '#ed6c02', opacity: '0.9' },
                }}
                onClick={() => handleEditExpense()}
              >
                Update
              </Button>
            )}
        </Box>
      </Box>
      <Divider />
      {dataToDisplay?.comments?.length > 0 && (
        <Grid item xs={12} sm={12}>
          <Typography sx={{ fontFamily: 'Montserrat-SemiBold', fontSize: '20px' }} align='center'>
            Comments
          </Typography>
          <Box
            sx={{
              maxHeight: '400px',
              overflowY: 'scroll',
              margin: '0 auto',
              padding: '5px',
              borderTop: 'none',
              border: '2px solid lightgrey',
              maxWidth: '90%',
            }}
          >
            {dataToDisplay?.comments?.map((data: any, index: number) => (
              <>
                <Typography component='span' sx={{ fontSize: 'smaller', color: '#A82E2F' }}>
                  {`${data?.created_by}@${duration(data?.created_at)} ago`}
                </Typography>
                <InputField
                  type='text'
                  fullWidth={true}
                  multiline={true}
                  id='comments'
                  name='comments'
                  InputProps={{ readOnly: true }}
                  value={data.comment}
                />
              </>
            ))}
          </Box>
        </Grid>
      )}

      <div>
        <Modal
          open={openCommentModal}
          onClose={handleCloseCommentModal}
          aria-labelledby='modal-comment'
          aria-describedby='modal-comment-desc'
        >
          <Box sx={style}>
            <Typography id='modal-comment' variant='h6' component='h6' sx={{ textAlign: 'center' }}>
              Add Comment
            </Typography>
            <TextField
              type='text'
              id='comments'
              name='comments'
              fullWidth
              multiline
              rows={2}
              maxRows={4}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
            <Box textAlign='left'>
              <Button
                size='small'
                variant='contained'
                sx={{
                  fontSize: '14px',
                  padding: '5px 15px 5px 10px',
                  bgcolor:
                    commentModalData?.data?.is_approved === 2
                      ? '#C11828'
                      : commentModalData?.data?.is_approved === 1
                      ? '#2E7D32'
                      : commentModalData?.data?.is_approved === 3
                      ? '#9C27B0'
                      : '#193C6D',
                  '&:hover': {
                    bgcolor:
                      commentModalData?.data?.is_approved === 2
                        ? '#C11828'
                        : commentModalData?.data?.is_approved === 1
                        ? '#2E7D32'
                        : commentModalData?.data?.is_approved === 3
                        ? '#9C27B0'
                        : '#193C6D',
                    opacity: 0.9,
                  },
                }}
                onClick={handlePostComment}
                disabled={isCommentEmpty}
              >
                {commentModalData?.data?.is_approved === 1
                  ? 'Approve'
                  : commentModalData?.data?.is_approved === 2
                  ? 'Reject'
                  : commentModalData?.data?.is_approved === 3
                  ? 'Reimburse'
                  : 'Post Comment'}
              </Button>
            </Box>
            <Box textAlign='right'>
              <CancelButton
                variant='contained'
                size='small'
                sx={{
                  fontSize: '14px',
                  width: '25%',
                  bottom: '34px',
                  padding: '5px 15px 5px 10px',
                  bgcolor: '#ECECEC',
                  '&:hover': { bgcolor: '#ECECEC', opacity: '0.9' },
                }}
                onClick={handleCloseCommentModal}
              >
                Cancel
              </CancelButton>
            </Box>
          </Box>
        </Modal>
      </div>
    </Dialog>
  )
}

export default DialogDetails
