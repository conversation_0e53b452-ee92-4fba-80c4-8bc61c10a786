import React, { forwardRef, memo, useImperativeHandle, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  ClickAwayListener,
  Divider,
  Fade,
  IconButton,
  Paper,
  Popper,
  TextField,
  Typography,
} from '@mui/material'
import {
  InputField,
  autocompleteSx,
  createButtonSx,
  crossButtonSx,
  datePickerSx,
  filterHeadingBoxSx,
  filterTitleSx,
} from './styles'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import CloseIcon from '@mui/icons-material/Close'

type FilterPopperPropType = {
  onClose: () => void
  open: boolean
  setFilteredValues: React.Dispatch<
    React.SetStateAction<{
      [key: string]: string | number
    }>
  >
}

const FilterPopper = memo(
  forwardRef((props: FilterPopperPropType, ref) => {
    const { onClose, open, setFilteredValues } = props
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
    const [values, setValues] = useState<{
      type?: string
      amount?: string
      date?: string | null
      description?: string
      title?: string
    } | null>(null)
    useImperativeHandle(ref, () => ({
      setAnchorEl,
    }))

    const handleExpenseType = (
      event: React.SyntheticEvent<Element, Event>,
      value: string | null,
    ) => {
      setValues((prev) => ({ ...prev, type: value ?? '' }))
    }

    const handleAmount = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
      if (!value?.trim()) {
        setValues((prev) => ({
          ...prev,
          amount: '',
        }))
        return
      }
      const [beforeDecimal, afterDecimal] = value?.trim().split('.')
      const sanitizedValueBeforeDecimal = Number(beforeDecimal)
      const sanitizedValueAfterDecimal = Number(afterDecimal)
      if (!Number.isNaN(sanitizedValueBeforeDecimal)) {
        if (!Number.isNaN(sanitizedValueAfterDecimal))
          setValues((prev) => ({
            ...prev,
            amount: `${beforeDecimal}.${afterDecimal?.toString().slice(0, 2)}`,
          }))
        else
          setValues((prev) => ({
            ...prev,
            amount: `${beforeDecimal}${value?.includes('.') ? '.' : ''}`,
          }))
      }
    }

    const handleExpenseTitle = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
      setValues((prev) => ({ ...prev, title: value }))
    }

    const handleStartDateChange = (value: string | null) => {
      setValues((prev) => ({ ...prev, date: value }))
    }

    const handleDescription = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
      setValues((prev) => ({ ...prev, description: value }))
    }

    return (
      <Popper
        sx={{ zIndex: 1200 }}
        open={open}
        anchorEl={anchorEl}
        placement={'left-start'}
        transition
      >
        {({ TransitionProps }) => (
          <ClickAwayListener onClickAway={onClose}>
            <Fade {...TransitionProps} timeout={350}>
              <Box component={Paper}>
                <Box sx={filterHeadingBoxSx}>
                  <Typography variant='h6' sx={filterTitleSx}>
                    {'Filter'}
                  </Typography>
                  <IconButton onClick={onClose} sx={crossButtonSx}>
                    <CloseIcon />
                  </IconButton>
                </Box>
                <Divider />
                <Box
                  display={'flex'}
                  flexDirection={'column'}
                  gap={'16px'}
                  p={1.5}
                  position={'relative'}
                >
                  <Box display={'flex'} gap={'12px'}>
                    <InputField
                      id='outlined-required'
                      label='Expense Title'
                      size='small'
                      fullWidth
                      sx={{ marginTop: '2px' }}
                      margin='normal'
                      onChange={handleExpenseTitle}
                      value={values?.title ?? ''}
                      required={false}
                    />
                    <Autocomplete
                      autoFocus={false}
                      size='small'
                      disablePortal
                      clearIcon={null}
                      id='expense-type'
                      options={['Travel', 'Food', 'Stay']}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label='Expense Type'
                          variant='outlined'
                          required={false}
                        />
                      )}
                      ListboxProps={{
                        style: {
                          maxHeight: '150px',
                          overflowY: 'auto',
                        },
                      }}
                      sx={autocompleteSx()}
                      onChange={handleExpenseType}
                      value={values?.type ?? ''}
                    />
                  </Box>
                  <Box display={'flex'} gap={'12px'}>
                    <InputField
                      id='outlined-required'
                      label='Amount'
                      size='small'
                      fullWidth
                      type='number'
                      sx={{ marginTop: '2px' }}
                      margin='normal'
                      onChange={handleAmount}
                      value={values?.amount ?? ''}
                      required={false}
                    />
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <Box sx={{ width: '250px' }}>
                        <DatePicker
                          sx={datePickerSx}
                          label='Start Date'
                          format='MM-DD-YYYY'
                          value={values?.date ?? null}
                          onChange={handleStartDateChange}
                          slotProps={{
                            textField: {
                              required: false,
                            },
                          }}
                        />
                      </Box>
                    </LocalizationProvider>
                  </Box>
                  <InputField
                    label='Description'
                    size='small'
                    fullWidth
                    multiline={true}
                    sx={{ marginTop: '2px' }}
                    margin='normal'
                    onChange={handleDescription}
                    value={values?.description ?? ''}
                    required={false}
                  />
                  <Box
                    display={'flex'}
                    justifyContent={'flex-end'}
                    flexGrow={1}
                    mt={1}
                    height={'36px'}
                  >
                    <Button variant='contained' color='primary' sx={createButtonSx}>
                      Apply
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Fade>
          </ClickAwayListener>
        )}
      </Popper>
    )
  }),
)

export default FilterPopper
