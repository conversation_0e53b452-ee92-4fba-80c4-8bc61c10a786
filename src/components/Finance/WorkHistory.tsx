import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { employeeHistory, managerHistory } from '../../actions'
import { EmpHistory, ManagerHistory } from '../EmployeeInfo/Info'
import Loader from '../Common/Loader'
import { Box, Paper } from '@mui/material'
import { HeaderHeading } from '../Common/CommonStyles'
import { ArrowBack } from '@mui/icons-material'

const WorkHistory = () => {
  const userInfo = useSelector(
    (entity: { entities: { dashboard: { getUserDetails: any } } }) =>
      entity.entities.dashboard.getUserDetails,
  )
  const isGettingManagerData = useSelector(
    (entity: { ui: { employeePortal: { isGettingmManagerHistoryData: boolean } } }) =>
      entity.ui.employeePortal.isGettingmManagerHistoryData,
  )

  const isGettingDesignationData = useSelector(
    (entity: { ui: { employeePortal: { isGettingEmpHistoryData: boolean } } }) =>
      entity.ui.employeePortal.isGettingEmpHistoryData,
  )

  const managerData = useSelector(
    (entity: { entities: { employeePortal: { getManagerHistoryData: any } } }) =>
      entity.entities.employeePortal.getManagerHistoryData,
  )

  const designationData = useSelector(
    (entity: { entities: { employeePortal: { getEmpHistoryData: any } } }) =>
      entity.entities.employeePortal.getEmpHistoryData,
  )

  const [dataToDisplay, setDataToDisplay] = useState({
    managerHistoryData: [],
    designationHistoryData: [],
  })

  const location = useLocation()
  const id = location?.state?.id ?? userInfo?.id
  const name = location?.state?.empName ?? ''
  const dispatch = useDispatch()
  const navigate = useNavigate()

  useEffect(() => {
    if (id) {
      dispatch(managerHistory.request({ id: id }))
      dispatch(employeeHistory.request({ id: id }))
    }
    return () => {
      setDataToDisplay({ managerHistoryData: [], designationHistoryData: [] })
    }
  }, [])

  useEffect(() => {
    setDataToDisplay({ managerHistoryData: [], designationHistoryData: [] })
  }, [location.pathname])

  useEffect(() => {
    if (
      !isGettingManagerData &&
      !isGettingDesignationData &&
      managerData?.length &&
      designationData?.length
    ) {
      setDataToDisplay({ managerHistoryData: managerData, designationHistoryData: designationData })
      dispatch(managerHistory.reset())
      dispatch(employeeHistory.reset())
    }
  }, [isGettingManagerData, isGettingDesignationData])

  return (
    <Paper
      elevation={3}
      sx={{
        width: '96%',
        padding: '10px 15px',
        position: 'relative',
        margin: '20px auto',
        background: '#FFFFFF',
        boxShadow: '0px 3px 6px #00000029',
      }}
    >
      <Loader state={isGettingManagerData || isGettingDesignationData} />
      <Box
        onClick={() => navigate(-1)}
        sx={{ position: 'absolute', top: '23px', right: '15px', cursor: 'pointer' }}
      >
        <ArrowBack />
      </Box>
      <Box mb={'15px'}>
        <HeaderHeading>{name}'s Work History</HeaderHeading>
      </Box>
      <Box display={'flex'} alignItems={'flex-start'} justifyContent={'space-between'} gap={'10px'}>
        <EmpHistory
          getEmpHistoryData={isGettingDesignationData ? [] : dataToDisplay.designationHistoryData}
          isGettingEmpHistoryData={false}
        />
        <ManagerHistory
          managerHistoryData={isGettingManagerData ? [] : dataToDisplay.managerHistoryData}
          isGettingmManagerHistoryData={false}
        />
      </Box>
    </Paper>
  )
}

export default WorkHistory
