import React, { useState, useEffect } from 'react'
import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import Paper from '@mui/material/Paper'
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  Dialog,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material'
import {
  HeaderHeading,
  SearchBoxCustom,
  SearchIconStyle,
  StyledTableCell,
  StyledTableRow,
  loaderProps,
} from '../Common/CommonStyles'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { createHolidayForAdmin, fetchHolidays } from '../../actions'
import {
  dashboardEntity,
  dashboardUI,
  fetchEmpHrControlEntities,
  fetchEmpHrControlUI,
} from '../../reducers'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import styles from '../../utils/styles.json'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import html2pdf from 'html2pdf.js'
import PdfIcon from '../../assets/images/pdf_image.png'
import CreateHolidayForm from './CreateHolidayForm'
import { Formik } from 'formik'
import { toast } from 'react-toastify'
import { ReactComponent as DeleteIcon } from '../../assets/images/deleteIcon.svg'
import moment from 'moment'
import Loader from '../Common/Loader'


const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '20px',
  border: '1px solid #DDDDDD',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}

export const SelectField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px', // Adjust the padding here
    fontSize: '15px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
    width: '100px',
  },
  '& .MuiFormLabel-root': {
    marginTop: '2px',
    fontSize: '15px',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
    height: '40px',
  },
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '15px !important',
  fontFamily: `${styles.FONT_MEDIUM} !important`,
}))

const ActionButton = styled(Button)(() => ({
  fontSize: '13px',
  height: '42px',
  float: 'right',
  marginTop: '15px',
  borderRadius: '20px',
  padding: '5px 20px',
  fontFamily: styles.FONT_MEDIUM,
}))

const HolidayListTableData = ({
  data,
  setOpen,
  createHolidayForAdmin,
  loaderState,
  isCreateHolidayLoader,
}: any) => {
  const showYears = data
    .map((value: any, i: any) => new Date(value.date).getFullYear())
    .filter((val: any, id: any, array: any) => {
      return array.indexOf(val) == id
    })
  const [searchQuery, setSearchQuery] = useState('')
  const [hideAction, setHideAction] = useState(true)
  const navigate = useNavigate()
  const [selectedYear, setSelectedYear] = useState<string>(
    showYears[0] ? showYears[0].toString() : new Date().getFullYear().toString()
  );
  
  const [holidayList, setHolidayList] = useState<any[]>(
    data?.length
      ? data.filter((row: any) =>
          String(new Date(row.date).getFullYear()).toLowerCase().includes(showYears[0].toString()),
        )
      : [],
  )

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = holidayList.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchQuery.toLowerCase()),
        ),
      )
      setHolidayList(filtered)
    } else {
      const filtered = data.filter((row: any) =>
        String(new Date(row.date).getFullYear()).toLowerCase().includes(selectedYear),
      )
      setHolidayList(filtered)
    }
  }, [searchQuery, holidayList, selectedYear])

  const sortedHolidayList = holidayList.slice().sort((a, b) => {
    const dateA = new Date(a.date)
    const dateB = new Date(b.date)

    if (dateA < dateB) return -1
    if (dateA > dateB) return 1
    return 0
  })

  const handleDeleteOpenDialog = (rowId: boolean) => {
    createHolidayForAdmin({ id: rowId, is_deleted: 1 })
  }

  const handleHoliday = () => {
    setOpen({ openStatus: true, isEdit: 0, holiday: {} })
    localStorage.setItem('holiday', 'create')
  }

  const handleExportPDF = async (name: string) => {
    setHideAction(false)
    const parent = document.createElement('div')
    const title = document.createElement('p')
    parent.style.textAlign = 'center'
    title.innerText = "TUDIP TECHNOLOGIES HOLIDAY'S LIST"
    title.style.fontSize = '24px'
    title.style.fontWeight = '800'
    title.style.fontFamily = styles.FONT_BOLD
    title.style.display = 'inline-block'
    title.style.marginTop = '-50px !important'
    parent.appendChild(title)
    const element = document.getElementById('pdf-export')
    const leaveReportName = `${name}`

    if (element) {
      await new Promise((resolve) => setTimeout(resolve, 500))

      const options = {
        margin: 10,
        filename: leaveReportName,
        image: { type: 'png', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a3', orientation: 'portrait' },
      }
      parent.appendChild(element.cloneNode(true))
      html2pdf().set(options).from(parent).save()
      setHideAction(true)
    }
  }

  const handleHolidayEdit = (holiday: any) => {
    setOpen({ openStatus: true, isEdit: 1, holiday: holiday })
    localStorage.setItem('holiday', 'edit')
  }

  return (
    <Box>
      {loaderState ||
        (isCreateHolidayLoader && (
      <Loader state={(loaderState || isCreateHolidayLoader)} />

        ))}
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{ float: 'right', pr: '30px', mt: '0px', cursor: 'pointer' }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <HeaderHeading>Tudip Technologies Holiday’s List</HeaderHeading>

      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <SelectField
            select
            size='small'
            fullWidth
            label='Year'
            InputLabelProps={{ required: false }}
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
          >
            {showYears.map((year: any, i: any) => (
              <StyledMenuItem key={year} value={year}>
                {year}
              </StyledMenuItem>
            ))}
          </SelectField>

          <SearchBoxCustom
            id='outlined-basic'
            placeholder='Search Holiday'
            variant='outlined'
            size='small'
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIconStyle />,
            }}
          />
        </Box>
        <Box>
          <ActionButton
            variant='outlined'
            sx={{
              width: '200px',
              mt: 1,
              cursor: 'pointer',
              marginTop: '10px', // Added margin to space out the button
            }}
            startIcon={
              <Avatar sx={{ width: 24, height: 24 }}>
                <img src={PdfIcon} alt='PDF' />
              </Avatar>
            }
            onClick={(e) => handleExportPDF('Holiday-Lists')}
          >
            EXPORT AS PDF
          </ActionButton>

          <ActionButton
            variant='outlined'
            startIcon={<AddTwoToneIcon sx={{ width: 24, height: 24 }} />}
            onClick={() => handleHoliday()}
            sx={{
              width: '200px',
              mt: 1,
              cursor: 'pointer',
              marginTop: '10px', // Added margin to space out the button
              marginRight: '10px',
            }}
          >
            ADD HOLIDAY
          </ActionButton>
        </Box>
      </Box>

      <TableContainer component={Paper} sx={{}}>
        <Table
          sx={{
            minWidth: 700,
          }}
          aria-label='customized table'
          id='pdf-export'
        >
          <TableHead>
            <StyledTableRow>
              <StyledTableCell>S.No.</StyledTableCell>
              <StyledTableCell>Holiday Name</StyledTableCell>
              <StyledTableCell>Description</StyledTableCell>
              <StyledTableCell>Day</StyledTableCell>
              <StyledTableCell>Date</StyledTableCell>
              {hideAction && <StyledTableCell>Action</StyledTableCell>}
            </StyledTableRow>
          </TableHead>
          {sortedHolidayList && sortedHolidayList?.length > 0 ? (
            <TableBody>
              {sortedHolidayList.map((holiday, index: number) => (
                <StyledTableRow key={holiday?.id} onClick={() => handleHolidayEdit(holiday)}>
                  <StyledTableCell>{index + 1}</StyledTableCell>
                  <StyledTableCell>{holiday.name}</StyledTableCell>
                  <StyledTableCell>{holiday.desc}</StyledTableCell>
                  <StyledTableCell>
                    {moment(holiday.date).format('MM/DD/YYYY')}
                  </StyledTableCell>
                  <StyledTableCell>
                    {new Date(holiday.date).toLocaleDateString('en-US')}
                  </StyledTableCell>
                  {hideAction && (
                    <StyledTableCell>
                      <Tooltip title={'Delete'} arrow>
                        <DeleteIcon
                          onClick={(event) => {
                            event.stopPropagation()
                            handleDeleteOpenDialog(holiday.id)
                          }}
                        />
                      </Tooltip>
                    </StyledTableCell>
                  )}
                </StyledTableRow>
              ))}
            </TableBody>
          ) : (
            <TableBody>
              <StyledTableRow>
                <StyledTableCell align='center' colSpan={10}>
                  <Typography variant='subtitle1' sx={{ color: '#483f3f', textAlign: 'center' }}>
                    No matching records found
                  </Typography>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>
    </Box>
  )
}

const AdminHolidayList = (props: any) => {
  const {
    fetchHolidaysData,
    HolidayData,
    loaderState,
    createHolidayForAdmin,
    isCreateHolidayDone,
    isCreateHolidayLoader,
    createHolidayForAdminReset,
    getEmpCreatedHolidays,
  } = props
  const [open, setOpen] = useState({ openStatus: false, isEdit: 0, holiday: {} })

  useEffect(() => {
    fetchHolidaysData()
  }, [])

  useEffect(() => {
    if (isCreateHolidayDone) {
      fetchHolidaysData()
      toast.success(getEmpCreatedHolidays?.message)
      createHolidayForAdminReset()
    }
  }, [isCreateHolidayDone])

  return (
    <>
      {/* {HolidayData?.length > 0 && ( */}
        <div style={MainContainer}>
          <StyledPaper
            style={{
              overflow: 'auto',
              fontFamily: 'Montserrat-Bold',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'left',
              background: 'white',
              opacity: '1',
              textAlign: 'left',
              margin: '20px',
              padding: '10px 25px 25px 25px',
              borderRadius: '10px',
              width: '93%',
              // maxHeight: '80vh',
              minHeight: '69vh', // Allow height to shrink if content is less
              border: '1px solid #DDDDDD',
              boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)', // Optional for a subtle shadow
            }}
          >
            <HolidayListTableData
              data={HolidayData}
              setOpen={setOpen}
              createHolidayForAdmin={createHolidayForAdmin}
              loaderState={loaderState}
              isCreateHolidayLoader={isCreateHolidayLoader}
            />
            <CreateHolidayForm
              open={open}
              setOpen={setOpen}
              createHolidayForAdmin={createHolidayForAdmin}
              isCreateHolidayDone={isCreateHolidayDone}
              isCreateHolidayLoader={isCreateHolidayLoader}
              createHolidayForAdminReset={createHolidayForAdminReset}
              fetchHolidaysData={fetchHolidaysData}
              getEmpCreatedHolidays={getEmpCreatedHolidays}
            />
          </StyledPaper>
        </div>
      {/* )} */}
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    HolidayData: dashboardEntity.getDashboard(state).getHolidaysData,
    loaderState: dashboardUI.getDashboard(state).isHolidaysData,
    getEmpCreatedHolidays:
      fetchEmpHrControlEntities.fetchHrControlData(state).getEmpCreatedHolidays,
    isCreateHolidayDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateHolidayDone,
    isCreateHolidayLoader: fetchEmpHrControlUI.fetchHrControlData(state).isCreateHolidayLoader,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchHolidaysData: () => dispatch(fetchHolidays.request()),
    createHolidayForAdmin: (data: any) => dispatch(createHolidayForAdmin.request(data)),
    createHolidayForAdminReset: () => dispatch(createHolidayForAdmin.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AdminHolidayList)
