import React, { useEffect, useState, useCallback } from 'react';
import { connect } from 'react-redux';
import { getDailySummaries, getAttendanceTimesheet, getAvailableYears, getWeeklySummaries, getMonthlySummaries } from '../../actions';
import { dailySummariesEntity, dailySummariesUI, availableYearsEntity, availableYearsUI, weeklySummariesEntity, weeklySummariesUI, monthlySummariesEntity, monthlySummariesUI } from '../../reducers';
import { dashboardEntity, attendanceEntity } from '../../reducers';
import moment from 'moment';
import { RootState } from '../../configureStore'
import { Dispatch } from 'redux'
import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  FormControl,
  Tabs,
  Tab,
  Paper,
  InputLabel,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import { Theme } from '@mui/material/styles';
import styled from '@emotion/styled';
import style from '../../utils/styles.json';
import { SelectField } from '../../components/Common/ComponentCommonStyles';
import { IDailySummary, IDailySummariesResponse } from '../../models/daily-summaries.model';
import './Summary.css';
import Loader from '../Common/Loader';

interface IWeeklySummary {
  id?: number;
  ISO_Week_Number: number;
  summary: string;
}

interface IMonthlySummary {
  id?: number;
  ISO_Month_Number: number;
  summary: string;
}

interface YearData {
  id: number;
  year: string;
}

const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '15px 25px 25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '10px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '70px',
  border: '1px solid #DDDDDD',
}));

// Combined container for both filter and table
const CombinedContainer = styled(Box)(() => ({
  background: '#FFFFFF',
  border: '1px solid #DDDDDD',
  borderRadius: '4px',
  marginTop: '20px',
  padding: '0',
}));

const FilterContainer = styled(Box)(() => ({
  width: '250px',
  padding: '20px 20px 10px 20px', // Reduced bottom padding
  marginBottom: '0',
}));

const TableContainer = styled(Box)(() => ({
  padding: '0 20px 20px 20px', // Removed top padding
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: '#f5f5f5',
  },
  // Remove any extra spacing between rows
  '& > td': {
    borderBottom: '1px solid rgba(224, 224, 224, 1)',
  }
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  padding: '8px 16px',
  '&:last-child': {
    paddingRight: '16px',
    textAlign: 'left'
  },
  '& div': {
    margin: 0,
    textAlign: 'left'
  },
  whiteSpace: 'normal',
  minWidth: '120px',
  '&:first-of-type': {
    width: '120px',
    maxWidth: '120px',
  }
}));

const StyledTableCellHeader = styled(TableCell)(({ theme }) => ({
  backgroundColor: style.PRIMARY_COLOR,
  color: 'white',
  padding: '8px 16px',
  fontFamily: style.FONT_MEDIUM,
  fontSize: '13px',
  whiteSpace: 'normal',
  minWidth: '120px',
  textAlign: 'left',
  '&:first-of-type': {
    width: '120px',
    maxWidth: '120px',
  }
}));

const PriorityLevelsInfo = styled(Box)(() => ({
  padding: '8px 16px',
  //backgroundColor: '#e8ffed',
  fontSize: '14px',
  fontStyle: 'italic',
  color: '#333',
}));

const StyledMenuItem = styled(MenuItem)(() => ({
  '&.MuiMenuItem-root': {
    fontSize: '14px',
    fontFamily: style.FONT_MEDIUM,
  }
}));

// interface IDailySummary {
//   id: string;
//   user_id: string;
//   IDSR_date: string;
//   ISO_Day_Number: number;
//   summary: string;
//   status: string;
// }

interface SummaryProps {
  getUserDetails: {
    id: string;
    [key: string]: any;
  };
  selectedUserId?: string; // Add optional prop for selected user ID from ManagerView
  //  // Add prop to hide loader when used in ManagerView
   // Add prop to disable automatic API fetching when used in ManagerView
  AttendanceTimesheet: Array<{
    id: string;
    start_date: string;
    [key: string]: any;
  }>;
  dailySummariesData: IDailySummary[] | { data: IDailySummary[] } | null;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  fetchAttendanceTimesheet: (userId: string) => void;
  fetchDailySummaries: (params: { userId: string, tId: number }) => void;
  fetchAvailableYears: (userId: string) => void;
  availableYears: YearData[];
  isLoadingYears: boolean;
  isSuccessYears: boolean;
  isErrorYears: boolean;
  fetchMonthlySummaries: (params: { userId: number, yearId: number }) => void;
  monthlySummariesData: IDailySummary[] | { data: IDailySummary[] } | null;
  isLoadingMonthly: boolean;
  isSuccessMonthly: boolean;
  isErrorMonthly: boolean;
  fetchWeeklySummaries: (params: { userId: number, yearId: number }) => void;
  weeklySummariesData: IDailySummary[] | { data: IDailySummary[] } | null;
  isLoadingWeekly: boolean;
  isSuccessWeekly: boolean;
  isErrorWeekly: boolean;
  onTabChange?: (value: number) => void;
  initialTab?: number;
  isManagerView?: boolean; // Add this prop
}

const Summary: React.FC<SummaryProps> = ({
  getUserDetails,
  selectedUserId,
  fetchAttendanceTimesheet,
  AttendanceTimesheet,
  fetchDailySummaries,
  dailySummariesData,
  isLoading,
  isSuccess,
  isError,
  fetchAvailableYears,
  availableYears,
  isLoadingYears,
  isSuccessYears,
  isErrorYears,
  fetchMonthlySummaries,
  monthlySummariesData,
  isLoadingMonthly,
  isSuccessMonthly,
  isErrorMonthly,
  fetchWeeklySummaries,
  weeklySummariesData,
  isLoadingWeekly,
  isSuccessWeekly,
  isErrorWeekly,
  onTabChange,
  initialTab = 0,
  isManagerView = false, // Add default value
}) => {
  const [value, setValue] = useState(initialTab);
  const [selectedTimesheet, setSelectedTimesheet] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>('');
  const [initialFetchDone, setInitialFetchDone] = useState(false); // Add this line

  // Update value when initialTab changes
  useEffect(() => {
    setValue(initialTab);
  }, [initialTab]);

  // Add this helper function at the top of your component
  const getMonthName = (monthNumber: string | number) => {
    // Subtract 1 from month number since moment months are 0-based
    const monthIndex = parseInt(monthNumber.toString()) - 1;
    return moment().month(monthIndex).format('MMMM');
  };

  // Add this validation function
  const validateAndFormatDate = (dateString: string) => {
    const date = moment(dateString);
    if (!date.isValid()) return 'N/A';
    if (date.isAfter(moment())) return 'Invalid Date';
    return date.format('MM/DD/YYYY (dddd)');
  };

  // Add this helper function to get the date range for a given ISO week
  const getWeekDateRange = (weekNumber: number, year: string) => {
    // Create a new moment object starting from January 1st of the given year
    const firstDayOfYear = moment(`${year}-01-01`);

    // Set to the first day (Monday) of the specified week
    const firstDay = firstDayOfYear.isoWeek(weekNumber).startOf('isoWeek');
    // Get the last day (Sunday) of the same week
    const lastDay = firstDayOfYear.isoWeek(weekNumber).endOf('isoWeek');

    return `${firstDay.format('MM/DD/YYYY')}-${lastDay.format('MM/DD/YYYY')}`;
  };

  // Combine the initial data fetching into a single useEffect with proper dependencies
  useEffect(() => {
    // Skip API calls if in manager view
    if (isManagerView) {
      setInitialFetchDone(true);
      return;
    }

    const userId = selectedUserId || getUserDetails?.id;
    if (userId && !initialFetchDone) {
      fetchAttendanceTimesheet(userId);
      fetchAvailableYears(userId.toString());
      setInitialFetchDone(true);
    }
  }, [selectedUserId, getUserDetails?.id, initialFetchDone, fetchAttendanceTimesheet, fetchAvailableYears, isManagerView]);

  // Modify the useEffect for initial year selection and data fetching
  useEffect(() => {
    if (availableYears?.length > 0 && !selectedYear) {
      const currentYear = moment().year();
      const availableYear = availableYears
        .find(y => parseInt(y.year.toString()) <= currentYear) || availableYears[0];
      
      if (availableYear) {
        setSelectedYear(availableYear.year.toString());
        
        // If we're on weekly or monthly view, fetch the data immediately
        if ((value === 1 || value === 2) && !isManagerView) {
          const params = {
            userId: parseInt(selectedUserId || getUserDetails?.id),
            yearId: availableYear.id
          };

          if (value === 1) {
            fetchWeeklySummaries(params);
          } else if (value === 2) {
            fetchMonthlySummaries(params);
          }
        }
      }
    }
  }, [availableYears, value, selectedUserId, getUserDetails?.id, isManagerView]);

  // Remove the separate data fetching effect since it's now handled in the above effect

  // Modify timesheet selection effect
  useEffect(() => {
    // Skip API calls if in manager view
    if (isManagerView) return;

    const userId = selectedUserId || getUserDetails?.id;
    if (
      AttendanceTimesheet?.length > 0 &&
      userId &&
      !selectedTimesheet &&
      value === 0
    ) {
      const firstTimesheet = AttendanceTimesheet[0]?.id;
      setSelectedTimesheet(firstTimesheet);

      if (firstTimesheet) {
        fetchDailySummaries({
          userId: userId.toString(),
          tId: parseInt(firstTimesheet)
        });
      }
    }
  }, [AttendanceTimesheet, selectedUserId, getUserDetails?.id, selectedTimesheet, value, fetchDailySummaries, isManagerView]);

  const handleTimesheetChange = useCallback((event: any) => {
    const selectedId = event.target.value as string;
    setSelectedTimesheet(selectedId);

    // Skip API calls if selectedUserId is true
    if (selectedUserId) return;

    // Use selectedUserId from props if available, otherwise fall back to getUserDetails.id
    const userId = selectedUserId || getUserDetails?.id;
    if (selectedId && userId) {
      fetchDailySummaries({
        userId: userId.toString(),
        tId: parseInt(selectedId)
      });
    }
  }, [selectedUserId, getUserDetails?.id, fetchDailySummaries, selectedUserId]);

  const renderYearFilter = () => (
    <FormControl
      variant="outlined"
      size="small"
      sx={{ minWidth: '200px' }}
    >
      <InputLabel id="year-select-label">Select Year</InputLabel>
      <SelectField
        labelId="year-select-label"
        value={selectedYear || ''} // Add empty string fallback
        onChange={handleYearChange}
        label="Select Year"
        sx={{
          borderRadius: '22px',
          height: '35px'
        }}
      >
        {isLoadingYears ? (
          <StyledMenuItem disabled>
            Loading years...
          </StyledMenuItem>
        ) : availableYears && availableYears.length > 0 ? (
          availableYears
            .filter(yearData => parseInt(yearData.year.toString()) <= moment().year())
            .sort((a, b) => parseInt(b.year.toString()) - parseInt(a.year.toString()))
            .map((yearData) => (
              <StyledMenuItem key={yearData.id} value={yearData.year.toString()}>
                {yearData.year}
              </StyledMenuItem>
            ))
        ) : (
          <StyledMenuItem disabled>
            No years available
          </StyledMenuItem>
        )}
      </SelectField>
    </FormControl>
  );

  // Update the handleYearChange to fetch data immediately when year changes
  const handleYearChange = (event: SelectChangeEvent<unknown>) => {
    const year = event.target.value as string;
    const currentYear = moment().year();

    if (parseInt(year) > currentYear) {
      return; // Ignore future years
    }

    if (year !== selectedYear) {
      setSelectedYear(year);
      
      // Fetch data immediately when year changes
      const selectedYearObject = availableYears.find(
        yearData => yearData.year.toString() === year
      );

      if (selectedYearObject && (value === 1 || value === 2)) {
        const params = {
          userId: parseInt(selectedUserId || getUserDetails?.id),
          yearId: selectedYearObject.id
        };

        if (value === 1) {
          fetchWeeklySummaries(params);
        } else if (value === 2) {
          fetchMonthlySummaries(params);
        }
      }
    }
  };

  // Memoize the data processing to avoid unnecessary re-renders
  const processData = () => {
    let summariesArray: IDailySummary[] = [];
    let weeklySummariesArray: IWeeklySummary[] = [];
    let monthlySummariesArray: IMonthlySummary[] = [];

    if (dailySummariesData) {
      if (Array.isArray(dailySummariesData)) {
        summariesArray = dailySummariesData as unknown as IDailySummary[];
      } else if ('data' in dailySummariesData && Array.isArray(dailySummariesData.data)) {
        summariesArray = dailySummariesData.data as unknown as IDailySummary[];
      }
    }

    if (weeklySummariesData) {
      if (Array.isArray(weeklySummariesData)) {
        weeklySummariesArray = weeklySummariesData as unknown as IWeeklySummary[];
      } else if ('data' in weeklySummariesData && Array.isArray(weeklySummariesData.data)) {
        weeklySummariesArray = weeklySummariesData.data as unknown as IWeeklySummary[];
      }
    }

    if (monthlySummariesData) {
      if (Array.isArray(monthlySummariesData)) {
        monthlySummariesArray = monthlySummariesData as unknown as IMonthlySummary[];
      } else if ('data' in monthlySummariesData && Array.isArray(monthlySummariesData.data)) {
        monthlySummariesArray = monthlySummariesData.data as unknown as IMonthlySummary[];
      }
    }

    return { summariesArray, weeklySummariesArray, monthlySummariesArray };
  };

  const renderContent = () => {
    const { summariesArray, weeklySummariesArray, monthlySummariesArray } = processData();

    switch (value) {
      case 0: // Daily tab
        return (
          <>
            {isLoading && <Loader state={isLoading} />}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <StyledTableCellHeader>Date</StyledTableCellHeader>
                    <StyledTableCellHeader style={{ textAlign: 'left' }}>Summary</StyledTableCellHeader>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!dailySummariesData || summariesArray.length === 0 ? (
                    <StyledTableRow>
                      <StyledTableCell colSpan={2} style={{ textAlign: 'center' }}>
                        No data available
                      </StyledTableCell>
                    </StyledTableRow>
                  ) : (
                    summariesArray.map((summary: IDailySummary) => (
                      <StyledTableRow key={summary.id || Math.random()}>
                        <StyledTableCell>
                          {summary.IDSR_date ? validateAndFormatDate(summary.IDSR_date) : 'N/A'}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div
                            dangerouslySetInnerHTML={{ __html: summary.summary || 'No summary available' }}
                          />
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        );
      case 1: // Weekly tab
        return (
          <>
            {isLoadingWeekly && <Loader state={isLoadingWeekly} />}
            {!isManagerView && ( // Only show filter if not in manager view
              <FilterContainer>
                {renderYearFilter()}
              </FilterContainer>
            )}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <StyledTableCellHeader>Week Number</StyledTableCellHeader>
                    <StyledTableCellHeader style={{ textAlign: 'left' }}>Summary</StyledTableCellHeader>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!weeklySummariesData || weeklySummariesArray.length === 0 ? (
                    <StyledTableRow>
                      <StyledTableCell colSpan={2} style={{ textAlign: 'center' }}>
                        No data available
                      </StyledTableCell>
                    </StyledTableRow>
                  ) : (
                    weeklySummariesArray.map((summary: IWeeklySummary) => (
                      <StyledTableRow key={summary.id || Math.random()}>
                        <StyledTableCell>
                        {summary.ISO_Week_Number
                           ? `Week ${String(summary.ISO_Week_Number).padStart(2, '0')}`
                           : 'N/A'} 
                        </StyledTableCell>
                        <StyledTableCell>
                          <div
                            dangerouslySetInnerHTML={{ __html: summary.summary || 'No summary available' }}
                          />
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        );
      case 2: // Monthly tab
        return (
          <>
            {isLoadingMonthly && <Loader state={isLoadingMonthly} />}
            {!isManagerView && ( // Only show filter if not in manager view
              <FilterContainer>
                {renderYearFilter()}
              </FilterContainer>
            )}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <StyledTableCellHeader>Month</StyledTableCellHeader>
                    <StyledTableCellHeader style={{ textAlign: 'left' }}>Summary</StyledTableCellHeader>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!monthlySummariesData || monthlySummariesArray.length === 0 ? (
                    <StyledTableRow>
                      <StyledTableCell colSpan={2} style={{ textAlign: 'center' }}>
                        No data available
                      </StyledTableCell>
                    </StyledTableRow>
                  ) : (
                    monthlySummariesArray.map((summary: IMonthlySummary) => (
                      <StyledTableRow key={summary.id || Math.random()}>
                        <StyledTableCell>
                          {summary.ISO_Month_Number
                            ? getMonthName(summary.ISO_Month_Number)
                            : 'N/A'}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div
                            style={{ textAlign: 'left' }}  // Changed to left alignment
                            dangerouslySetInnerHTML={{ __html: summary.summary || 'No summary available' }}
                          />
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        );
      default:
        return null;
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    if (onTabChange) {
      onTabChange(newValue);
    }
  };

  // Add a new useEffect to handle tab changes
  useEffect(() => {
    // Skip if in manager view or if no year is selected
    if (isManagerView || !selectedYear) return;

    const userId = parseInt(selectedUserId || getUserDetails?.id);
    const yearData = availableYears.find(y => y.year.toString() === selectedYear);
    
    if (!yearData) return;

    const params = {
      userId,
      yearId: yearData.id
    };

    // Fetch appropriate data based on selected tab
    if (value === 1) { // Weekly tab
      fetchWeeklySummaries(params);
    } else if (value === 2) { // Monthly tab
      fetchMonthlySummaries(params);
    }
  }, [value, selectedYear, selectedUserId, getUserDetails?.id, isManagerView, availableYears]);

  return (
    <>
      <StyledPaper>
        <Box sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'center',
          borderBottom: 1,
          borderColor: '#1a4980',
          marginBottom: '0',
        }}>
          <Tabs
            value={value}
            onChange={handleTabChange}
            aria-label="summary tabs"
            textColor="inherit"
            TabIndicatorProps={{ style: { backgroundColor: '#1a4980' } }}
            sx={{
              minHeight: '40px',
              flex: 1,
              '& .MuiTabs-flexContainer': {
                display: 'flex',
                justifyContent: 'flex-start',
              },
              '& .MuiButtonBase-root': {
                color: 'rgb(0, 0, 0)',
                fontFamily: style.FONT_MEDIUM,
                borderRight: '1px solid rgb(255, 255, 255)',
                minHeight: '40px',
                padding: '0 24px',
                minWidth: 'auto',
                flex: '0 0 auto',
              }
            }}
          >
            <Tab
              label="Daily"
              sx={{
                textTransform: 'none',
                fontSize: '15px',
              }}
            />
            <Tab
              label="Weekly"
              sx={{
                textTransform: 'none',
                fontSize: '15px',
              }}
            />
            <Tab
              label="Monthly"
              sx={{
                textTransform: 'none',
                fontSize: '15px',
              }}
            />
          </Tabs>
        </Box>

        <CombinedContainer>
          {value === 0 && !isManagerView && ( // Use isManagerView instead of props.isManagerView
            <FilterContainer>
              <FormControl
                variant="outlined"
                size="small"
                sx={{ minWidth: '200px' }}
              >
                <InputLabel id="month-select-label">Select Month</InputLabel>
                <SelectField
                  labelId="month-select-label"
                  value={selectedTimesheet}
                  onChange={handleTimesheetChange}
                  label="Select Month"
                  sx={{
                    borderRadius: '22px',
                    height: '35px'
                  }}
                >
                  {AttendanceTimesheet?.map((timesheet: any) => (
                    <StyledMenuItem
                      key={timesheet.id}
                      value={timesheet.id}
                    >
                      {moment(new Date(timesheet.start_date).toISOString().split('T')[0]).format('MMMM YYYY')}
                    </StyledMenuItem>
                  ))}
                </SelectField>
              </FormControl>
            </FilterContainer>
          )}
          {renderContent()}
        </CombinedContainer>
      </StyledPaper>
    </>
  );
}

const mapStateToProps = (state: RootState) => {
  return {
    getUserDetails: dashboardEntity.getDashboard(state).getUserDetails,
    AttendanceTimesheet: attendanceEntity.getAllAttendance(state).getAttendanceTimesheet,
    dailySummariesData: dailySummariesEntity.getDailySummaries(state).getDailySummariesData,
    isLoading: dailySummariesUI.getDailySummariesUI(state).isGetDailySummariesLoader,
    isSuccess: dailySummariesUI.getDailySummariesUI(state).isGetDailySummariesSuccess,
    isError: dailySummariesUI.getDailySummariesUI(state).isGetDailySummariesError,
    availableYears: availableYearsEntity.getAvailableYears(state).getAvailableYearsData?.data || [],
    isLoadingYears: availableYearsUI.getAvailableYearsUI(state).isGetAvailableYearsLoader,
    isSuccessYears: availableYearsUI.getAvailableYearsUI(state).isGetAvailableYearsSuccess,
    isErrorYears: availableYearsUI.getAvailableYearsUI(state).isGetAvailableYearsError,
    weeklySummariesData: weeklySummariesEntity.getWeeklySummaries(state).getWeeklySummariesData,
    monthlySummariesData: monthlySummariesEntity.getMonthlySummaries(state).getMonthlySummariesData,
    isLoadingWeekly: weeklySummariesUI.getWeeklySummariesUI(state).isGetWeeklySummariesLoader,
    isLoadingMonthly: monthlySummariesUI.getMonthlySummariesUI(state).isGetMonthlySummariesLoader,
    isSuccessWeekly: weeklySummariesUI.getWeeklySummariesUI(state).isGetWeeklySummariesSuccess,
    isSuccessMonthly: monthlySummariesUI.getMonthlySummariesUI(state).isGetMonthlySummariesSuccess,
    isErrorWeekly: weeklySummariesUI.getWeeklySummariesUI(state).isGetWeeklySummariesError,
    isErrorMonthly: monthlySummariesUI.getMonthlySummariesUI(state).isGetMonthlySummariesError
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAttendanceTimesheet: (userId: string) =>
      dispatch(getAttendanceTimesheet.request({ userId })),
    fetchDailySummaries: (params: { userId: string, tId: number }) =>
      dispatch(getDailySummaries.request({ data: params })),
    fetchAvailableYears: (userId: string) =>
      dispatch(getAvailableYears.request({ userId })),
    fetchWeeklySummaries: (params: { userId: number, yearId: number }) =>
      dispatch(getWeeklySummaries.request({ data: params })),
    fetchMonthlySummaries: (params: { userId: number, yearId: number }) =>
      dispatch(getMonthlySummaries.request({ data: params }))
  }
}

// Wrap the component with React.memo to prevent unnecessary re-renders
const MemoizedSummary = React.memo(Summary);

export default connect(mapStateToProps, mapDispatchToProps)(MemoizedSummary)
