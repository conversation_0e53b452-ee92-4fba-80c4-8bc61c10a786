import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { useNavigate } from 'react-router-dom'
import type { SxProps, Theme } from '@mui/system'

interface BackButtonProps {
  steps?: number
  tooltip?: string
  sx?: SxProps<Theme>
}

const BackButton: React.FC<BackButtonProps> = ({ steps = -1, tooltip, sx }) => {
  const navigate = useNavigate()

  const button = (
    <IconButton
      onClick={() => navigate(steps)}
      sx={{
        backgroundColor: 'rgba(255, 255, 255, 0)',
        color: '#14325A',
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 255, 0)',
        },
        ...sx,
      }}
    >
      <ArrowBackIcon />
    </IconButton>
  )

  return tooltip ? <Tooltip title={tooltip}>{button}</Tooltip> : button
}

export default BackButton
