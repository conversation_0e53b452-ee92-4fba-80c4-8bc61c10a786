import { FormControl, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material'

interface GenericDropdownProp {
  value: { label: string; value: string }[]
  label: string
  selectedValue: string
  onChange: (event: SelectChangeEvent<string>) => void
}

function GenericDropdown({ value, label, selectedValue, onChange }: GenericDropdownProp) {
  return (
    <FormControl
      fullWidth
      size='small'
      variant='outlined'
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: '20px',
        },
        '& .MuiInputLabel-outlined': {
          backgroundColor: 'white',
          paddingX: '4px',
          transform: 'translate(14px, 8px) scale(1)',
        },
        '& .MuiInputLabel-shrink': {
          transform: 'translate(14px, -6px) scale(0.75)',
        },
      }}
    >
      <InputLabel id={`dropdown-label-${label}`}>{label}</InputLabel>
      <Select
        labelId={`dropdown-label-${label}`}
        id={`dropdown-${label}`}
        value={selectedValue}
        onChange={onChange}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 200,
            },
          },
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'left',
          },
          transformOrigin: {
            vertical: 'top',
            horizontal: 'left',
          },
        }}
      >
        {value.length > 0 ? (
          value.map((item) => (
            <MenuItem key={item.value} value={item.value}>
              {item.label}
            </MenuItem>
          ))
        ) : (
          <MenuItem disabled value=''>
            Not Found For Selected Rounds
          </MenuItem>
        )}
      </Select>
    </FormControl>
  )
}

export default GenericDropdown
