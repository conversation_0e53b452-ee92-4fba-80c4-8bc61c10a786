import React, { useState } from 'react'
import { Box, Popover, Button, TextField } from '@mui/material'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { Dayjs } from 'dayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { styled } from '@mui/material/styles'

const StyledDatePicker = styled(DatePicker)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiInputLabel-outlined': {
    backgroundColor: 'white',
    paddingX: '4px',
    transform: 'translate(14px, 16px) scale(1)',
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, -6px) scale(0.75)',
  },
}))

interface CustomDateRange {
  fromDate: Dayjs | null
  setFromDate: (date: Dayjs | null) => void
  toDate: Dayjs | null
  setToDate: (date: Dayjs | null) => void
  onApply?: () => void
}

const CustomDateRangePicker = ({
  fromDate,
  setFromDate,
  toDate,
  setToDate,
  onApply,
}: CustomDateRange) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)

  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleApply = () => {
    setAnchorEl(null)
    if (onApply !== undefined && fromDate && toDate) {
      onApply()
    }
  }

  const open = Boolean(anchorEl)
  const formattedRange =
    fromDate && toDate
      ? `${fromDate.format('MM-DD-YYYY')} - ${toDate.format('MM-DD-YYYY')}`
      : 'Select Date Range'

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TextField
        value={formattedRange}
        onClick={handleOpen}
        variant='outlined'
        size='small'
        sx={{
          marginBottom: 0,
          padding: '2px',
          width: '15rem',
          cursor: 'pointer',
          borderRadius: '20px',
          backgroundColor: 'primary.main',
          color: 'white',
          '& .MuiOutlinedInput-root': {
            borderRadius: '20px',
          },
          '& .MuiInputBase-input': {
            fontSize: '14px',
            color: 'white',
            cursor: 'pointer',
            caretColor: 'transparent',
          },
          '& fieldset': {
            border: 'none',
          },
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        sx={{position:'absolute'}}
      >
        <Box p={2} display='flex' flexDirection='column' alignItems='center'>
          <StyledDatePicker
            label='Start Date'
            format='MM-DD-YYYY'
            value={fromDate}
            onChange={(newValue: any) => setFromDate(newValue)}
          />

          <StyledDatePicker
            label='End Date'
            format='MM-DD-YYYY'
            value={toDate}
            onChange={(newValue: any) => setToDate(newValue)}
            minDate={fromDate}
          />

          <Button
            onClick={handleApply}
            variant='contained'
            sx={{
              marginTop: '10px',
              borderRadius: '20px',
              fontSize: '0.9rem',
            }}
          >
            Apply
          </Button>
        </Box>
      </Popover>
    </LocalizationProvider>
  )
}

export default CustomDateRangePicker