import React from 'react';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, TableSortLabel } from '@mui/material';
import ExpandableTableCell from './ExpandableTableCell';

export interface GeneralizedTableProps<T> {
  data: T[];
  columns: (keyof T)[];
  columnMappings: Record<string, string>;
  order: 'asc' | 'desc';
  orderBy: keyof T;
  onSort: (column: keyof T) => void;
  renderActions?: (row: T) => React.ReactNode;
  onRowClick?: (row: T) => void;
  selectedRowId?: number;
}

const GeneralizedTable = <T extends { id: number }>({
  data,
  columns,
  order,
  orderBy,
  onSort,
  renderActions,
  columnMappings,
  onRowClick,
  selectedRowId,
}: GeneralizedTableProps<T>) => {
  return (
    <Box
      sx={{
        width: '100%',
        overflowX: 'auto',
        boxShadow: '0px 4px 10px rgba(0,0,0,0.15)',
        borderRadius: '8px',
      }}
    >
      <TableContainer component={Paper}>
        <Table sx={{ tableLayout: 'auto', minWidth: '600px', borderCollapse: 'separate' }}>
          <TableHead sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
            <TableRow>
              {columns.map((col) => (
                <TableCell
                  key={String(col)}
                  sx={{
                    whiteSpace: 'nowrap',
                    color: 'white !important',
                    fontWeight: 'bold',
                    fontSize: '13px',
                    fontFamily: 'Montserrat-Medium,sans-serif',
                    padding: '1px 35px',
                  }}
                >
                  <TableSortLabel
                    active={orderBy === col}
                    direction={orderBy === col ? order : 'asc'}
                    onClick={() => onSort(col)}
                    hideSortIcon={false}
                    sx={{
                      color: 'white !important',
                      '& .MuiTableSortLabel-icon': { color: 'white !important' },
                    }}
                  >
                    {columnMappings[col as string] || String(col)}
                  </TableSortLabel>
                </TableCell>
              ))}
              {renderActions && (
                <TableCell
                  sx={{
                    color: 'white !important',
                    fontWeight: 'bold',
                    fontSize: '13px',
                    padding: '12px 35px',
                    textAlign: 'center',
                    fontFamily: 'Montserrat-Medium,sans-serif',
                  }}
                >
                  Action
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.length > 0 ? (
              data.map((row) => (
                <TableRow
                  key={row.id}
                  onClick={() => onRowClick && onRowClick(row)}
                  sx={{
                    backgroundColor: row.id === selectedRowId ? 'rgba(0, 0, 0, 0.1)' : 'white',
                    transition: 'all 0.3s ease-in-out',
                    boxShadow: 'rgba(0, 0, 0, 0.1) 0px 4px 6px',
                    cursor: onRowClick ? 'pointer' : 'default',
                  }}
                >
                  {columns.map((col) => (
                    <ExpandableTableCell key={String(col)} text={String(row[col])} />
                  ))}
                  {renderActions && (
                    <TableCell
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '6px 0px',
                      }}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {renderActions(row)}
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + (renderActions ? 1 : 0)} align="center">
                  No matching records found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default GeneralizedTable;
