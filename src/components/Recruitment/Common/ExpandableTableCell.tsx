import React, { useEffect, useRef, useState } from "react";
import { TableCell, Tooltip, Typography } from "@mui/material";

interface ExpandableTableCellProps {
  text: string
}

const ExpandableTableCell: React.FC<ExpandableTableCellProps> = ({ text }) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowed, setIsOverflowed] = useState(false);

  const checkOverflow = () => {
    if (textRef.current) {
      setIsOverflowed(textRef.current.scrollWidth > textRef.current.clientWidth);
    }
  };

  useEffect(() => {
    checkOverflow();
    window.addEventListener("resize", checkOverflow);
    return () => {
      window.removeEventListener("resize", checkOverflow);
    };
  }, [text]);

  return (
    <TableCell
      sx={{
        maxWidth: "200px",
        overflow: "hidden",
        whiteSpace: "nowrap",
        textOverflow: "ellipsis",
        padding: "6px",
        paddingLeft: "35px"
      }}
    >
      <Tooltip
        title={
          <Typography
            sx={{
              fontFamily: "Montserrat-Medium, sans-serif",
              fontSize: "12px",
              fontWeight: "normal",
              textAlign: "justify",
              color: "rgb(72, 63, 63)",
            }}
          >
            {text}
          </Typography>
        }
        disableHoverListener={!isOverflowed}
        componentsProps={{
          tooltip: {
            sx: {
              backgroundColor: "white",
              color: "black",
              fontSize: "12px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.36)",
              padding: "6px 12px",
              borderRadius: "6px",
              fontFamily: "Montserrat-Medium, sans-serif",
              fontWeight: "normal",
              maxHeight: "300px",
              overflowY: "auto",
            },
          },
        }}
      >
        <Typography
          ref={textRef}
          variant="body2"
          sx={{
            overflow: "hidden",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            display: "block",
            fontFamily: "Montserrat-Medium, sans-serif",
            fontWeight: "normal",
            color: "rgb(72, 63, 63)",
            fontSize: "12px",
          }}
        >
          {text}
        </Typography>
      </Tooltip>
    </TableCell>
  );
};

export default ExpandableTableCell;
