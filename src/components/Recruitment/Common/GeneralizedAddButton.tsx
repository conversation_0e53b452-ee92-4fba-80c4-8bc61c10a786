import React from 'react';
import { Button, ButtonProps } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';

interface GeneralizedAddButtonProps extends ButtonProps {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
}

const GeneralizedAddButton: React.FC<GeneralizedAddButtonProps> = ({
    label,
    onClick,
    icon,
    sx,
    ...rest
}) => {
    return (
        <Button
            variant="contained"
            onClick={onClick}
            sx={{
                backgroundColor: 'rgb(25, 60, 109)',
                color: '#fff',
                fontWeight: 'bold',
                textTransform: 'none',
                fontSize: '13px',
                height: '35px',
                fontFamily: 'Montserrat, sans-serif',
                marginLeft: 'auto',
                border: '1px solid rgba(25, 60, 109, 0.5)',
                borderRadius: '20px',
                padding: '5px 20px',
                '&:hover': { backgroundColor: 'rgb(25, 60, 109)' },
                ...sx,
            }}
            {...rest}
        >
            {icon ? (
                <>{icon}</>
            ) : (
                "Add "
            )}
            {label}
        </Button>
    );
};

export default GeneralizedAddButton;
