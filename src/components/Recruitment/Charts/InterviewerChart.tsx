import React from 'react'
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart'
import { Typography, Box, Grid, Paper } from '@mui/material'
import { InterviewPieChartProps, PALETTE } from './utils'
import { useChartContext } from './ChartContext'

const InterviewPieChart: React.FC<InterviewPieChartProps> = ({ data }) => {
  const chartData = data[0] || []
  const COLORS = chartData.map((_, index) => PALETTE[index % PALETTE.length])
  const { state } = useChartContext()

  return (
    <Paper sx={{ m: '1rem', p: '1rem' }}>
      <Box textAlign='center' mt={2}>
        <Typography variant='h6' fontWeight='bold'>
          Interviews from {state.fromDate?.format('DD MMM YYYY')} to{' '}
          {state.toDate?.format('DD MMM YYYY')}
        </Typography>
      </Box>

      <Box display='flex' flexDirection='column' alignItems='center' mt={2}>
        {chartData.length > 0 ? (
          <PieChart
            series={[
              {
                data: chartData.map((entry, index) => ({
                  id: index,
                  value: entry.count,
                  color: COLORS[index],
                })),
                highlightScope: { fade: 'global', highlight: 'item' },
                faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },

                valueFormatter: (value, context) => {
                  return `${chartData[context.dataIndex].recruiter_name}: ${value.value} interviews`
                },
              },
            ]}
            width={600}
            height={400}
          />
        ) : (
          <Typography variant='body1' color='textSecondary'>
            No interview data available
          </Typography>
        )}

        <Grid
          container
          spacing={2}
          justifyContent='center'
          alignItems='center'
          mt={2}
          sx={{ width: '60%' }}
        >
          {chartData.map((entry, index) => (
            <Grid
              item
              key={index}
              xs={6}
              sm={4}
              md={3}
              display='flex'
              alignItems='center'
              justifyContent='center'
            >
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: COLORS[index % COLORS.length],
                  borderRadius: '50%',
                  marginRight: 1,
                }}
              />
              <Typography variant='body2'>{entry.recruiter_name}</Typography>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Paper>
  )
}

export default InterviewPieChart
