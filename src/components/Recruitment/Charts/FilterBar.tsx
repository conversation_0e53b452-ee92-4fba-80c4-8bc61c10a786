import { useCallback, useEffect } from 'react'
import { useChartContext } from './ChartContext'
import { toast } from 'react-toastify'
import { Box, Grid, Tooltip } from '@mui/material'
import { MultiSelectDropdown } from '../Common'
import CustomDateRangePicker from '../Common/GenericDateRangePicker'
import { ActionButton } from '../../HolidayList/HolidaysStyles'
import RestartAltIcon from '@mui/icons-material/RestartAlt'
import DoneIcon from '@mui/icons-material/Done'
import { formatForMultiSelect } from '../Candidates/Dashboard/utils'
import { recruitmentEntity } from '../../../reducers'
import { RootState } from '../../../configureStore'
import { Dispatch } from 'redux'
import { fetchInterviewerPannel, fetchRecruiters } from '../../../actions'
import { connect } from 'react-redux'
import { FilterBarProp, getFirstAndLastDayOfMonth, sizes } from './utils'

const FilterBar = (props: FilterBarProp) => {
  const { fetchRecruitersData, interviewerOptions, recruiterOptions, fetchInterviewerPannel } =
    props

  const { state, dispatch } = useChartContext()
  const { fromDate, toDate } = getFirstAndLastDayOfMonth()

  useEffect(() => {
    const fetchAllData = async () => {
      try {
        await Promise.all([fetchRecruitersData({}), fetchInterviewerPannel()])
      } catch (error) {
        toast.error(`Unable to load filter's dropdown data`)
      }
    }
    fetchAllData()
  }, [fetchRecruitersData, fetchInterviewerPannel])

  const handleFilterChange = useCallback(
    (filterName: keyof typeof state.filters, selectedValues: (string | number)[]) => {
      dispatch({
        type: 'SET_FILTERS',
        payload: { ...state.filters, [filterName]: selectedValues },
      })
    },
    [dispatch, state.filters],
  )

  const handleReset = useCallback(() => {
    dispatch({
      type: 'SET_FILTERS',
      payload: { interviewers: [], recruiters: [] },
    })
    dispatch({ type: 'SET_FROM_DATE', payload: fromDate })
    dispatch({ type: 'SET_TO_DATE', payload: toDate })
    dispatch({ type: 'SET_FETCH_TRIGGER', payload: 0 })
  }, [])

  const handleShow = () => {
    dispatch({ type: 'SET_FETCH_TRIGGER', payload: state.fetchDataTrigger + 1 })
  }

  return (
    <Box
      sx={{
        my: '1rem',
      }}
    >
      <Grid container spacing={2} alignItems='center' justifyContent='space-evenly'>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Interviewer'
            options={formatForMultiSelect(interviewerOptions, 'name', 'id', 0)}
            selectedOptions={state.filters.interviewers}
            setSelectedOptions={(values) => handleFilterChange('interviewers', values)}
          />
        </Grid>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Select Recruiters'
            options={formatForMultiSelect(recruiterOptions, 'name', 'id', 0)}
            selectedOptions={state.filters.recruiters}
            setSelectedOptions={(values) => handleFilterChange('recruiters', values)}
          />
        </Grid>
        <Grid item {...sizes}>
          <CustomDateRangePicker
            fromDate={state.fromDate}
            setFromDate={(value) => dispatch({ type: 'SET_FROM_DATE', payload: value })}
            toDate={state.toDate}
            setToDate={(value) => dispatch({ type: 'SET_TO_DATE', payload: value })}
          />
        </Grid>

        <Grid item lg={1.5} md={4} sm={6} xs={12}>
          <Tooltip title='Select Date Range and Click' arrow>
            <ActionButton
              variant='outlined'
              onClick={handleShow}
              startIcon={<DoneIcon />}
              fullWidth
            >
              Show
            </ActionButton>
          </Tooltip>
        </Grid>

        <Grid item lg={1.5} md={4} sm={6} xs={12}>
          <ActionButton
            variant='outlined'
            onClick={handleReset}
            startIcon={<RestartAltIcon />}
            fullWidth
          >
            Reset
          </ActionButton>
        </Grid>
      </Grid>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    interviewerOptions: recruitmentEntity.getRecruitment(state).getInterviewerPannel,
    recruiterOptions: recruitmentEntity.getRecruitment(state).getRecruiterData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchInterviewerPannel: () => dispatch(fetchInterviewerPannel.request()),
    fetchRecruitersData: (data: {}) => dispatch(fetchRecruiters.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(FilterBar)
