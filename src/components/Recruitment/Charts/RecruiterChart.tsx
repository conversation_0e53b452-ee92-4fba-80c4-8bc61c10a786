import React from 'react'
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/BarChart'
import { Typography, Paper, Grid, Card } from '@mui/material'
import { PALETTE, processBarData, RecruitmentChartProps } from './utils'

const RecruitmentChart: React.FC<RecruitmentChartProps> = ({ data }) => {
  if (!Array.isArray(data)) {
    return (
      <Typography variant='h6' fontWeight='bold' textAlign='center' color='text.secondary'>
        No Data Available
      </Typography>
    )
  }
  const { dates, joined, notJoined } = processBarData(data)
  const noData = dates.length === 0 || Object.keys(joined).length === 0

  return (
    <Paper sx={{ m: '1rem', p: '1rem' }}>
      {noData ? (
        <Typography variant='h6' fontWeight='bold' textAlign='center' color='text.secondary'>
          No Data Available
        </Typography>
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Card sx={{ m: 1, width: '100%', flexGrow: 1 }} elevation={2}>
              <Typography variant='h6' fontWeight='bold' textAlign='center'>
                Selected and Joined Candidates
              </Typography>
              <BarChart
                xAxis={[{ scaleType: 'band', data: dates }]}
                series={Object.keys(joined).map((name, index) => ({
                  data: joined[name],
                  label: name,
                  color: PALETTE[index % PALETTE.length],
                }))}
                height={300}
              />
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card sx={{ m: 1, width: '100%', flexGrow: 1 }} elevation={2}>
              <Typography variant='h6' fontWeight='bold' textAlign='center'>
                Selected and Did NOT Join Candidates
              </Typography>
              <BarChart
                xAxis={[{ scaleType: 'band', data: dates }]}
                series={Object.keys(notJoined).map((name, index) => ({
                  data: notJoined[name],
                  label: name,
                  color: PALETTE[index % PALETTE.length],
                }))}
                height={300}
              />
            </Card>
          </Grid>
        </Grid>
      )}
    </Paper>
  )
}

export default RecruitmentChart
