import { Box, Collapse, Paper, Tab, Tabs } from '@mui/material'
import { ChartProvider } from './ChartContext'
import FilterBar from './FilterBar'
import useOpenable from '../../../hooks/useOpenable'
import { ActionButton } from '../../HolidayList/HolidaysStyles'
import FilterListIcon from '@mui/icons-material/FilterList'
import ChartArea from './ChartArea'

const Charts: React.FC = () => {
  const { isOpen, onOpenChange } = useOpenable()
  return (
    <ChartProvider>
      <Paper sx={{ mx: '1rem', p: '1rem', marginTop: '1rem' }}>
        <Box display='flex'>
          <Tabs value={0} sx={{ flexGrow: 1 }} aria-label='Charts Dashboard'>
            <Tab sx={{ fontSize: 'large' }} label='Charts' />
          </Tabs>
          <ActionButton
            sx={{ mx: '1rem' }}
            variant='outlined'
            startIcon={<FilterListIcon sx={{ width: 24, height: 24 }} />}
            onClick={onOpenChange}
          >
            Filters
          </ActionButton>
        </Box>

        <Collapse in={isOpen} timeout='auto'>
          <FilterBar />
        </Collapse>
      </Paper>
      <ChartArea />
    </ChartProvider>
  )
}

export default Charts
