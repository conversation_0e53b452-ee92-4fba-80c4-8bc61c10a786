import { dataPayloadType } from 'actions/Types'
import { CandidateCount, Interviewer<PERSON>ork, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'components/Types'
import dayjs, { Dayjs } from 'dayjs'

export const sizes = { lg: 3, md: 4, sm: 6, xs: 12 }

export const PALETTE = [
  '#FF6384',
  '#36A2EB',
  '#FFCE56',
  '#4BC0C0',
  '#9966FF',
  '#FF9F40',
  '#F56991',
  '#C9CBCF',
  '#61D936',
  '#B04FE6',
]

export type CandidateData = {
  name: string
  joined_candidates_count: number
  not_joined_candidates_count: number
}

export type ResponseData = Record<string, CandidateData[]>

export interface RecruitmentChartProps {
  data: JoinedCandidateResponse[]
}

export interface ApiResponseItem {
  id: number
  round_name: string
}

export interface InterviewPieChartProps {
  data: InterviewerResponse[][]
}

export const getFirstAndLastDayOfMonth = (): { fromDate: Dayjs; toDate: Dayjs } => {
  const now = dayjs()
  const firstDay = now.startOf('month')
  const lastDay = now.endOf('month')
  return { fromDate: firstDay, toDate: lastDay }
}

export interface ChartAreaProp {
  getInterviewerWork: (data: InterviewerWork) => void
  getCandidateByRound: (data: CandidateCount) => void
  getJoinedCandidates: (data: JoinedCandidates) => void
  interviewerWork: InterviewerResponse[][]
  candidateCount?: CandidateCountResponse[]
  joinedCandidates: JoinedCandidateResponse[]
  interviewerOptions: ApiResponseItem[][]
  recruiterOptions: ApiResponseItem[][]
  isInterviewerWorkLoaded: Boolean
  isJoinedCandidatesLoaded: Boolean
}

export interface InterviewerResponse {
  recruiter_id: string
  recruiter_name: string
  count: number
}

export interface CandidateCountResponse {
  round_name: string
  count: number
}

export interface JoinedCandidateResponse {
  [date: string]: {
    ruid: number
    name: string
    joined_candidates_count: number
    not_joined_candidates_count: number
  }[]
}

export interface FilterBarProp {
  fetchRecruitersData: ({}) => {}
  interviewerOptions: ApiResponseItem[][]
  recruiterOptions: ApiResponseItem[][]
  fetchInterviewerPannel: () => {}
}

export const processBarData = (data: JoinedCandidateResponse[]) => {
  const dates = new Set<string>()
  const joined: Record<string, number[]> = {}
  const notJoined: Record<string, number[]> = {}

  data.forEach((entry) => {
    Object.entries(entry).forEach(([date, candidates]) => {
      if (candidates.length === 0) return
      dates.add(date)
      candidates.forEach(({ name, joined_candidates_count, not_joined_candidates_count }) => {
        if (!joined[name]) joined[name] = Array.from(dates).map(() => 0)
        if (!notJoined[name]) notJoined[name] = Array.from(dates).map(() => 0)

        const index = Array.from(dates).indexOf(date)
        joined[name][index] = joined_candidates_count
        notJoined[name][index] = not_joined_candidates_count
      })
    })
  })

  return { dates: Array.from(dates), joined, notJoined }
}

export interface PerformanceChartProp {
  recruiterOptions: ApiResponseItem[][]
  viewRecruitersCalls: (data: dataPayloadType) => void
  recruitersCallsResponse: RecruiterResponseData
}

type RecruiterResponseData = {
  [key: string]: Record<string, { name: string; count: number }[]>[]
}[]

interface ChartData {
  date: string
  [key: string]: number | string
}

type ProcessedData = {
  chartData: ChartData[]
  categories: string[]
}

export const processChartData = (
  data: Record<string, { name: string; count: number }[]>[],
): ProcessedData => {
  let chartData: ChartData[] = []
  let categories = new Set<string>()

  data.forEach((entry) => {
    Object.entries(entry).forEach(([date, stats]) => {
      let dataPoint: ChartData = { date }
      stats.forEach(({ name, count }) => {
        dataPoint[name] = count
        categories.add(name)
      })
      chartData.push(dataPoint)
    })
  })

  return { chartData, categories: Array.from(categories) }
}
