import React, { createContext, useReducer, useContext } from 'react'
import { Dayjs } from 'dayjs'
import { getFirstAndLastDayOfMonth } from './utils'

type Option = string | number

interface Filters {
  interviewers: Option[]
  recruiters: Option[]
}

interface State {
  searchQuery: string
  roundFilter: string
  templateFilter: string
  scheduleFilter: string
  fromDate: Dayjs | null
  toDate: Dayjs | null
  filters: Filters
  selectedRows: string[]
  fetchDataTrigger: number
  recruiterSingle: string
}

type Action =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_ROUND_FILTER'; payload: string }
  | { type: 'SET_TEMPLATE_FILTER'; payload: string }
  | { type: 'SET_SCHEDULE_FILTER'; payload: string }
  | { type: 'SET_FROM_DATE'; payload: Dayjs | null }
  | { type: 'SET_TO_DATE'; payload: Dayjs | null }
  | { type: 'SET_FILTERS'; payload: Filters }
  | { type: 'SET_SELECTED_ROWS'; payload: string[] }
  | { type: 'SET_FETCH_TRIGGER'; payload: number }
  | { type: 'SET_RECRUITER_SINGLE'; payload: string }

const { fromDate, toDate } = getFirstAndLastDayOfMonth()

const initialState: State = {
  searchQuery: '',
  roundFilter: '',
  templateFilter: '',
  scheduleFilter: '',
  fromDate: fromDate,
  toDate: toDate,
  filters: { interviewers: [], recruiters: [] },
  selectedRows: [],
  fetchDataTrigger: 0,
  recruiterSingle: '',
}

function chartReducer(state: State, action: Action) {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
    case 'SET_ROUND_FILTER':
      return { ...state, roundFilter: action.payload }
    case 'SET_TEMPLATE_FILTER':
      return { ...state, templateFilter: action.payload }
    case 'SET_SCHEDULE_FILTER':
      return { ...state, scheduleFilter: action.payload }
    case 'SET_FROM_DATE':
      return { ...state, fromDate: action.payload }
    case 'SET_TO_DATE':
      return { ...state, toDate: action.payload }
    case 'SET_FILTERS':
      return { ...state, filters: action.payload }
    case 'SET_SELECTED_ROWS':
      return { ...state, selectedRows: action.payload }
    case 'SET_FETCH_TRIGGER':
      return { ...state, fetchDataTrigger: action.payload }
    case 'SET_RECRUITER_SINGLE':
      return { ...state, recruiterSingle: action.payload }
    default:
      return state
  }
}

const ChartContext = createContext<{
  state: State
  dispatch: React.Dispatch<Action>
}>({
  state: initialState,
  dispatch: () => {},
})

export const ChartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(chartReducer, initialState)
  return <ChartContext.Provider value={{ state, dispatch }}>{children}</ChartContext.Provider>
}

export const useChartContext = () => useContext(ChartContext)
