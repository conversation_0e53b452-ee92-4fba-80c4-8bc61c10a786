import React, { useEffect } from 'react'
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/BarChart'
import { Box, Paper, Typography } from '@mui/material'
import { useChartContext } from './ChartContext'
import { RootState } from 'configureStore'
import { connect } from 'react-redux'
import { recruitmentEntity } from 'reducers'
import { PALETTE, PerformanceChartProp, processChartData } from './utils'
import { formatForMultiSelect } from '../Candidates/Dashboard/utils'
import { Dispatch } from 'redux'
import { viewRecruitersCalls } from 'actions'
import { dataPayloadType } from 'actions/Types'
import { LineChart } from '@mui/x-charts'
import GenericDropdown from '../Common/GenericDropdown'

const PerformanceChart: React.FC<PerformanceChartProp> = React.memo(
  ({ recruiterOptions, viewRecruitersCalls, recruitersCallsResponse }) => {
    const { state, dispatch } = useChartContext()
    const firstEntry = recruitersCallsResponse[0] || []
    const recruiterName = Object.keys(firstEntry)[0] || ''
    const responseData = firstEntry[recruiterName] || []

    const { chartData, categories } = processChartData(responseData)
    useEffect(() => {
      if (state.recruiterSingle) {
        viewRecruitersCalls({
          recruiter: state.recruiterSingle,
          from_date: state.fromDate,
          to_date: state.toDate,
        })
      }
    }, [state.recruiterSingle, state.fetchDataTrigger])
    return (
      <Paper sx={{ m: '1rem', p: '1rem' }}>
        <Box display='flex' justifyContent='flex-end'>
          <Box width='25%'>
            <GenericDropdown
              value={formatForMultiSelect(recruiterOptions, 'name', 'id', 0)}
              label='Select Recruiter'
              selectedValue={state.recruiterSingle}
              onChange={(event) =>
                dispatch({ type: 'SET_RECRUITER_SINGLE', payload: event.target.value })
              }
            />
          </Box>
        </Box>

        <Typography variant='h6' fontWeight='bold' textAlign='center' my='2rem'>
          Productivity Metrics of {recruiterName} ({state.fromDate?.format('DD MMM YYYY')} to{' '}
          {state.toDate?.format('DD MMM YYYY')})
        </Typography>

        {categories.length > 0 ? (
          <>
            <LineChart
              xAxis={[{ scaleType: 'band', dataKey: 'date' }]}
              series={categories.map((category, index) => ({
                dataKey: category,
                label: category,
                showMark: true,
                markerShape: 'circle',
                color: PALETTE[index],
              }))}
              dataset={chartData}
              height={300}
            />
            <BarChart
              xAxis={[{ scaleType: 'band', dataKey: 'date' }]}
              series={categories.map((category) => ({ dataKey: category, label: category }))}
              dataset={chartData}
              height={500}
            />
          </>
        ) : (
          <Typography variant='h6' fontWeight='bold' textAlign='center' color='text.secondary'>
            {state.recruiterSingle
              ? 'No Data Available For Selected Date Range and Recruiter'
              : 'Please Select Recruiter'}
          </Typography>
        )}
      </Paper>
    )
  },
)

const mapStateToProps = (state: RootState) => {
  return {
    recruiterOptions: recruitmentEntity.getRecruitment(state).getRecruiterData,
    recruitersCallsResponse: recruitmentEntity.getRecruitment(state).viewRecruitersCalls,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    viewRecruitersCalls: (data: dataPayloadType) => dispatch(viewRecruitersCalls.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(PerformanceChart)
