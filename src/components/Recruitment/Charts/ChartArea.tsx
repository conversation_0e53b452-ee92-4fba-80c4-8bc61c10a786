import { connect } from 'react-redux'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './Interviewer<PERSON><PERSON>'
import Recruitment<PERSON><PERSON> from './Recruiter<PERSON><PERSON>'
import { ChartAreaProp } from './utils'
import { RootState } from 'configureStore'
import { recruitmentEntity, recruitmentStateUI } from 'reducers'
import { Dispatch } from 'redux'
import { CandidateCount, InterviewerWork, JoinedCandidates } from 'components/Types'
import { candidateCount, interviewerWork, joinedCandidates } from 'actions'
import { useEffect } from 'react'
import { useChartContext } from './ChartContext'
import Loader from 'components/Common/Loader'
import PerformanceChart from './PerformanceChart'

const ChartArea: React.FC<ChartAreaProp> = ({
  getInterviewerWork,
  getCandidateByRound,
  getJoinedCandidates,
  interviewerWork,
  candidateCount,
  joinedCandidates,
  interviewerOptions,
  recruiterOptions,
  isInterviewerWorkLoaded,
  isJoinedCandidatesLoaded,
}) => {
  const { state } = useChartContext()

  useEffect(() => {
    if (Array.isArray(interviewerOptions[0]) && Array.isArray(recruiterOptions[0])) {
      getInterviewerWork({
        from_date: state.fromDate,
        to_date: state.toDate,
        interviewers:
          state.filters.interviewers.length > 0
            ? state.filters.interviewers
            : interviewerOptions[0]?.map((user) => user.id),
      })

      getJoinedCandidates({
        recruiters:
          state.filters.recruiters.length > 0
            ? state.filters.recruiters
            : recruiterOptions[0]?.map((user) => user.id),
        from_date: state.fromDate,
        to_date: state.toDate,
        interviewers:
          state.filters.interviewers.length > 0
            ? state.filters.interviewers
            : interviewerOptions[0]?.map((user) => user.id),
      })
    }
  }, [
    state.fetchDataTrigger,
    state.filters.interviewers,
    interviewerOptions,
    recruiterOptions,
    state.filters.recruiters,
  ])

  return (
    <>
      <Loader state={!isInterviewerWorkLoaded} />
      <PerformanceChart/>
      <InterviewPieChart data={interviewerWork} />
      <RecruitmentChart data={joinedCandidates} />
    </>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  getInterviewerWork: (data: InterviewerWork) => dispatch(interviewerWork.request(data)),
  getCandidateByRound: (data: CandidateCount) => dispatch(candidateCount.request(data)),
  getJoinedCandidates: (data: JoinedCandidates) => dispatch(joinedCandidates.request(data)),
})

const mapStateToProps = (state: RootState) => {
  return {
    interviewerWork: recruitmentEntity.getRecruitment(state).fetchInterviewerWork,
    candidateCount: recruitmentEntity.getRecruitment(state).fetchCandidateCount,
    joinedCandidates: recruitmentEntity.getRecruitment(state).fetchJoinedCandidates,
    interviewerOptions: recruitmentEntity.getRecruitment(state).getInterviewerPannel,
    recruiterOptions: recruitmentEntity.getRecruitment(state).getRecruiterData,
    isInterviewerWorkLoaded: recruitmentStateUI.getRecruitment(state).isInterviewerWorkLoaded,
    isJoinedCandidatesLoaded: recruitmentStateUI.getRecruitment(state).isJoinedCandidatesLoaded,
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ChartArea)
