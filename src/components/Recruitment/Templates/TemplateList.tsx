import React, { useEffect } from 'react'
import { Box, Pagination, Tab, Tabs } from '@mui/material'
import TemplateTable from './TemplateTable'
import SearchComponent from './SearchComponent'
import { useTemplatesLogic, TemplatesProps } from './utils'
import GeneralizedAddButton from '../Common/GeneralizedAddButton'
import Loader from "../../Common/Loader"
import { toast } from 'react-toastify'
import { handleToasts } from '../Common/notifications'
import BackButton from '../Common/GeneralizedBackButton'

const Templates: React.FC<TemplatesProps> = (props) => {
  const {
    page,
    searchQuery,
    rowsPerPage,
    handleSearchChange,
    clearSearch,
    handlePageChange,
    filteredData,
    currentData,
    navigate,
  } = useTemplatesLogic(props)
  const { isTemplateDeleting, getTemplates, deleteRecruitmentTemplate, isGetTemplateData, isTemplateDeleted, resetTemplateDeleted } = props

  useEffect(() => {
    getTemplates()
  }, [isTemplateDeleted, page])

  useEffect(() => {
    handleToasts([
      { condition: isTemplateDeleted, message: 'Template deleted successfully', reset: resetTemplateDeleted }
    ]);
  }, [isTemplateDeleted, resetTemplateDeleted]);

  const isLoading = isGetTemplateData || isTemplateDeleting;
  return (
    <>
      {isLoading && <Loader state={true} />}
      <Box
        sx={{
          width: { xs: '90%', sm: '90%', lg: '93%' },
          margin: '25px auto 70px auto',
          backgroundColor: '#fff',
          padding: { xs: '1rem', sm: '1.5rem', lg: '25px' },
          paddingTop: { lg: '0.1rem' },
          borderRadius: { sm: '6px', lg: '4px' },
          boxShadow:
            '0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)',
          border: '1px solid #DDDDDD',
          opacity: 1,
        }}>
        <Box display='flex' alignItems='center' justifyContent='space-between' className='templates-page-tabs'>
          <Tabs value={0} aria-label='Tabs for different tables'>
            <Tab label='Templates' />
          </Tabs>
          <BackButton tooltip='Go Back' />
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            alignItems: 'center',
            marginBottom: '0.1rem',
            marginTop: '0.5rem',
            gap: '1rem',
          }}
        >
          <SearchComponent
            clearSearch={clearSearch}
            searchQuery={searchQuery}
            handleSearchChange={handleSearchChange}
            placeholder='Search by template name and round name'
          />
          <GeneralizedAddButton label='Template' onClick={() => navigate('template')} />
        </Box>
        <TemplateTable data={currentData} deleteRecruitmentTemplate={deleteRecruitmentTemplate} />
        {filteredData.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
            <Pagination
              count={Math.ceil(filteredData.length / rowsPerPage)}
              page={page}
              onChange={handlePageChange}
              color='primary'
              sx={{
                '& .MuiPaginationItem-root.Mui-selected': {
                  backgroundColor: 'rgb(25, 60, 109)',
                  border: '1px solid rgb(25, 60, 109)',
                  color: '#fff',
                },
              }}
            />
          </Box>
        )}
      </Box>
    </>
  )
}

export default Templates
