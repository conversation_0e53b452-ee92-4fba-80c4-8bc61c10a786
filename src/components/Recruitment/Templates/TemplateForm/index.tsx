import { fetchRoundsForTemplate, addEmailTemplateData, editTemplateDetails } from "../../../../actions";
import { connect } from "react-redux";
import { Dispatch } from "redux";
import { RootState } from "../../../../configureStore";
import { recruitmentEntity, recruitmentStateUI } from "../../../../reducers";
import TemplateForm from './Form'
import { TemplatePayload } from "components/Types";

const mapDispatchToProps = (dispatch: Dispatch) => ({
    fetchRoundsForTemplate: () => dispatch(fetchRoundsForTemplate.request()),
    addEmailTemplateData: (data: any) => dispatch(addEmailTemplateData.request(data)),
    editTemplateDetails: (data: TemplatePayload) => dispatch(editTemplateDetails.request(data)),
    resetTemplateAdded: () => dispatch(addEmailTemplateData.reset()),
    resetTemplateEdited: () => dispatch(editTemplateDetails.reset()),
})

const mapStateToProps = (state: RootState) => ({
    roundOptions: recruitmentEntity.getRecruitment(state).getRoundsForTemplate,
    responseEmailTemplate: recruitmentEntity.getRecruitment(state).postEmailTemplateData,
    isTemplateAdded: recruitmentStateUI.getRecruitment(state).isTemplateAdded,
    isTemplateEdited: recruitmentStateUI.getRecruitment(state).isTemplateEdited,
    isTemplateAdding: recruitmentStateUI.getRecruitment(state).isTemplateAdding,
    isTemplateEditing: recruitmentStateUI.getRecruitment(state).isTemplateEditing,
    isRoundsFetching: recruitmentStateUI.getRecruitment(state).isRoundsFetching,
})

const TemplateFormDataMapped = connect(mapStateToProps, mapDispatchToProps)(TemplateForm)

const TemplateFormRender = () => (
    <>
        <TemplateFormDataMapped />
    </>
)

export default TemplateFormRender

