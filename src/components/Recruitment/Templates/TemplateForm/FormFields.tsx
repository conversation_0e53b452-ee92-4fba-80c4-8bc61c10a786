import React from "react";
import { Box, MenuItem } from "@mui/material";
import { styled } from "@mui/system";
import { useField } from "formik";
import { TextField } from "@mui/material";
import styles from '../../../../utils/styles.json'

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
    lineHeight: "1.4375em"
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiInputLabel-root': {
    transform: 'translate(14px, 50%) scale(1)', 
    transformOrigin: 'left top', 
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, -10px) scale(0.75)', 
    transformOrigin: 'left top',
  }
}))
const FormikTextField = ({
  name,
  label,
  select = false,
  options = [],
  ...props
}: any) => {
  const [field, meta] = useField(name);
  return (
    <InputField
      {...field}
      {...props}
      label={label}
      select={select}
      variant="outlined"
      error={meta.touched && Boolean(meta.error)}
      helperText={meta.touched && meta.error}
      sx={{ minWidth: "200px", flex: 1 }}
    >
      {select &&
        options.map((option: string, index: number) => (
          <MenuItem key={index} value={option}>
            {option}
          </MenuItem>
        ))}
    </InputField>
  );
};

const FormFields2: React.FC<{ roundOptions: string[] }> = ({ roundOptions }) => {
  return (
    <Box
      display="flex"
      gap={1}
      justifyContent="center"
      flexWrap="wrap"
      width="95%"
      height="70px"
      margin="auto"
    >
      <FormikTextField name="name" label="Name" />
      <FormikTextField name="round_name" label="Round" select options={roundOptions} />
      <FormikTextField name="subject" label="Subject" sx={{ minWidth: "220px" }} />
    </Box>
  );
};

export default FormFields2;
