import React from "react";
import { TextField, Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useField } from "formik";

export const InputField = styled(TextField)(() => ({
  marginTop: "5px",
  marginBottom: "5px",
  fontFamily: 'Montserrat-Medium !important',
  "& .MuiOutlinedInput-input": {
    padding: "13px 0px",
    fontSize: "13px",
    height: "20px",
    fontWeight:"bold",
    letterSpacing: "0rem !important",
    fontFamily: 'Montserrat-Medium !important',
  },
  "& .MuiFormLabel-asterisk": {
    color: "red",
  },
  "& .MuiInputBase-root.MuiOutlinedInput-root": {
    borderRadius: "20px",
  },
  "& .MuiFormLabel-root.MuiInputLabel-root": {
    fontSize: "16px",
  },
}));

const DescriptionInput: React.FC = () => {
  const [field, meta] = useField("description");
  return (
    <Box justifyContent="center" fontFamily="Montserrat-Medium" sx={{ height: "100px" }}>
      <InputField
        {...field}
        required
        id="outlined-required"
        label="Description"
        size="small"
        fullWidth

        multiline
        maxRows={2}
        error={meta.touched && Boolean(meta.error)}
        helperText={meta.touched && meta.error}
      />
    </Box>
  );
};

export default DescriptionInput;
