import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Container, Typography, Box, Paper, Button } from "@mui/material";
import { Formik, Form } from "formik";
import Loader from "components/Common/Loader";
import FormFields2 from "./FormFields";
import Editor from "./Editor";
import DescriptionInput from "./DescriptionInput";
import {
  RowData,
  validationSchema,
  useFetchRoundsEffect,
  useTemplateStatusEffect,
  useTemplateFormFields,
} from "./utils";

interface TemplateFormProps {
  resetTemplateAdded: () => void;
  resetTemplateEdited: () => void;
  fetchRoundsForTemplate: () => void;
  roundOptions: any;
  addEmailTemplateData: (data: any) => void;
  editTemplateDetails: (data: any) => void;
  isRoundsFetching: boolean;
  isTemplateAdded: boolean;
  isTemplateEdited: boolean;
  isTemplateAdding: boolean;
  isTemplateEditing: boolean;
}

const TemplateForm: React.FC<TemplateFormProps> = (props) => {
  const {
    resetTemplateAdded,
    resetTemplateEdited,
    fetchRoundsForTemplate,
    roundOptions,
    addEmailTemplateData,
    editTemplateDetails,
    isRoundsFetching,
    isTemplateAdded,
    isTemplateEdited,
    isTemplateAdding,
    isTemplateEditing,
  } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const rowData = location.state?.Template as RowData | undefined;
  useFetchRoundsEffect(fetchRoundsForTemplate);
  useTemplateStatusEffect(isTemplateAdded, isTemplateEdited, resetTemplateAdded, resetTemplateEdited, navigate);
  const { options, initialValues, onSubmit } = useTemplateFormFields(
    roundOptions,
    addEmailTemplateData,
    editTemplateDetails
  );
  const isLoading = isTemplateAdding || isTemplateEditing || isRoundsFetching;
  return (
    <>
      {isLoading && <Loader state={true} />}
      <Container
        maxWidth="lg"
        sx={{
          padding: 2,
          borderRadius: "4px",
          fontFamily: "Montserrat-Medium",
        }}
      >
        <Paper
          elevation={3}
          sx={{
            width: "1000px",
            padding: 0,
            margin: "auto",
            borderRadius: "4px",
            backgroundColor: "white",
            boxShadow: "1px 4px 10px rgba(0, 0, 0, 0.1)",
          }}
        >
          <Typography variant="h5" sx={{ p: 2, textAlign: "center", fontFamily: "Montserrat-Medium", paddingY: "2rem" }}>
            {rowData ? "Edit Email Template" : "Add Email Template"}
          </Typography>
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
            validateOnMount
          >
            {({ isSubmitting, isValid }) => (
              <Form>
                <FormFields2 roundOptions={options} />
                <Box margin="auto" justifyContent="center" width="94.5%" marginY="1.5rem">
                  <DescriptionInput />
                </Box>
                  <Editor name="content" />
                <Box display="flex" justifyContent="end" padding="15px" gap={2} sx={{ marginTop: "0rem", paddingRight: "30px" }}>
                  <Button
                    variant="contained"
                    sx={{
                      backgroundColor: "rgb(226, 226, 226)",
                      color: "black",
                      padding: "8px 16px",
                      borderRadius: "25px",
                      fontFamily: "Montserrat, sans-serif",
                      fontSize: "14px",
                      minWidth: "100px",
                      "&:hover": {
                        backgroundColor: "rgb(226, 226, 226)",
                        color: "black",
                      },
                    }}
                    onClick={() => {
                      navigate(-1);
                    }}
                    type="button"
                  >
                    CANCEL
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{
                      backgroundColor: "rgb(25, 60, 109)",
                      color: "white",
                      borderRadius: "25px",
                      fontFamily: "Montserrat, sans-serif",
                      fontSize: "14px",
                      minWidth: "100px",
                    }}
                    disabled={isSubmitting || !isValid}
                  >
                    {rowData ? "UPDATE" : "CREATE"}
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </Paper>
      </Container>
    </>
  );
};

export default TemplateForm;
