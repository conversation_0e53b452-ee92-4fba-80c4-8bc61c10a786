import React from "react";
import { Grid, FormHelperText } from "@mui/material";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import { useFormikContext } from "formik";

interface EditorProps {
  name: string;
}

const EditorComponent: React.FC<EditorProps> = ({ name }) => {
  const { values, setFieldValue, touched, errors, setFieldTouched } = useFormikContext<any>();

  return (
    <Grid
      item
      xs={12}
      sm={12}
      sx={{
        width: "950px",
        backgroundColor: "white",
        p: 1,
        margin: "auto",
      }}
    >
      <CKEditor
        editor={ClassicEditor}
        data={values[name] || ""}
        onReady={(editor) => {
          const styleElement = document.createElement('style');
          styleElement.innerHTML = `
            .ck-powered-by { display: none !important; }
            .ck-editor .ck-content { 
              font-family: 'Times New Roman', Times, serif !important; 
            }
            .ck-editor .ck-content * { 
              font-family: 'Times New Roman', Times, serif !important; 
            }
          `;
          document.head.appendChild(styleElement);

          const editableElement = editor.ui.view.editable.element;
          if (editableElement) {
            editableElement.style.maxHeight = "150px";
            editableElement.style.overflowY = "auto";
          }
        }}
        onChange={(event, editor) => {
          const data = editor.getData();
          setFieldValue(name, data);
        }}
        onBlur={() => setFieldTouched(name, true)}
      />
      {touched[name] && errors[name] && (
        <FormHelperText error sx={{ marginLeft: "8px" }}>
          {errors[name] as string}
        </FormHelperText>
      )}
    </Grid>
  );
};

const Editor = React.memo(EditorComponent);
export default Editor;