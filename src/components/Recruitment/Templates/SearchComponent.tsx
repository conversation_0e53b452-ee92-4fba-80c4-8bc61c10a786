import React, { useState } from "react";
import { Search as SearchIcon, Close as CloseIcon } from "@mui/icons-material";
import { TextField, InputAdornment } from "@mui/material";

interface SearchComponentProps {
  searchQuery: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  clearSearch: () => void;
  placeholder: string;
}

const SearchComponent: React.FC<SearchComponentProps> = ({
  searchQuery,
  handleSearchChange,
  clearSearch,
  placeholder,
}) => {
  const [focused, setFocused] = useState(false);

  return (
    <TextField
      label={focused ? "Search" : ""}
      variant="outlined"
      size="small"
      placeholder={focused ? "" : placeholder}
      value={searchQuery}
      onChange={handleSearchChange}
      onFocus={() => setFocused(true)}
      onBlur={() => setFocused(false)}
      required={false}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
        endAdornment: searchQuery ? (
          <InputAdornment position="end">
            <CloseIcon fontSize="small" onMouseDown={clearSearch} style={{ cursor: "pointer" }} />
          </InputAdornment>
        ) : null,
      }}
      sx={{
        minWidth: "350px",
        maxWidth: "350px",
        fontSize: "13px",
        fontFamily: "Montserrat-Medium, sans-serif",
        borderRadius: "20px",
        "& .MuiOutlinedInput-root": {
          borderRadius: "20px",
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: "rgba(25,60,109,0.5)",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: "rgba(25,60,109,0.8)",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: "rgb(25,60,109)",
            borderWidth: "2px",
          },
        },
        "& .MuiInputBase-input": {
          fontSize: "13px",
          fontFamily: "Montserrat-Medium, sans-serif",
          height: "35px",
          padding: "0 0px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
          flexGrow: 1,
          textAlign: "left",
        },
      }}
    />
  );
};

export default SearchComponent;
