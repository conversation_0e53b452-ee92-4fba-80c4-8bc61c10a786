import { Checkbox, TableCell, tableCellClasses } from '@mui/material'
import styled from 'styled-components'
import style from '../../../../utils/styles.json'
import { ApiResponseItem, ColumnType } from './types'

type order = 'asc' | 'desc'
interface props {
  property: string
  orderBy: string
  order: order
  setOrder: React.Dispatch<React.SetStateAction<'asc' | 'desc'>>
  setOrderBy: React.Dispatch<React.SetStateAction<string>>
}

export function handleRequestSort({ property, order, orderBy, setOrder, setOrderBy }: props) {
  const isAsc = orderBy === property && order === 'asc'
  setOrder(isAsc ? 'desc' : 'asc')
  setOrderBy(property)
}

export function formatForMultiSelect(
  apiResponse: ApiResponseItem[][],
  label: string,
  value: string,
  index: number,
) {
  if (!Array.isArray(apiResponse) || apiResponse.length === 0) return []

  return apiResponse[index].map((item: any) => ({
    label: item[label],
    value: item[value],
  }))
}

export const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    textWrap: 'nowrap',
    padding: '6px 16px',
    '& .MuiTableSortLabel-root': {
      color: 'white',
      '&:hover': {
        color: 'white',
      },
      '&.Mui-active': {
        color: 'white',
      },
    },
    '& .MuiTableSortLabel-icon': {
      fill: 'white !important',
    },
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    padding: '5px',
  },
}))

export const StyledCheckbox = styled(Checkbox)({
  color: 'white',
  '&.Mui-checked, &.MuiCheckbox-indeterminate': {
    color: 'white',
  },
  '&.Mui-checked.MuiCheckbox-root svg, &.MuiCheckbox-indeterminate.MuiCheckbox-root svg': {
    fill: 'white',
  },
})

export const columns: ColumnType[] = [
  { label: 'Name', key: 'name' },
  { label: 'Phone', key: 'phone_no' },
  { label: 'Experience', key: 'experience' },
  { label: 'Email', key: 'email' },
  { label: 'Position', key: 'position' },
  { label: 'Status', key: 'stage' },
  { label: 'Grade', key: 'grade' },
  { label: 'Qualification', key: 'qualification' },
  { label: 'Schedule', key: 'date_time' },
  { label: 'Action', key: 'action' },
]

export interface Option {
  label: string
  value: string | number
}

export interface Filters {
  positions: Option[]
  rounds: Option[]
  tags: Option[]
  recruiters: Option[]
}

export const EditRowFieldMappings = [
  { name: 'name', label: 'Full Name' },
  { name: 'email', label: 'Email Address', type: 'email' },
  { name: 'phone_no', label: 'Phone Number', maxlength: 10 },
  { name: 'marks_obtained', label: 'Marks Obtained' },
  { name: 'marks_total', label: 'Total Marks' },
]
