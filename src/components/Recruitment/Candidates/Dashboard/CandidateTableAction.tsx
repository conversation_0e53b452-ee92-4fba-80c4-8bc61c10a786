import { <PERSON><PERSON><PERSON><PERSON>on, Menu, MenuItem } from '@mui/material'
import {
  Search as SearchIcon,
  Videocam as VideocamIcon,
  Feedback as FeedbackIcon,
  TextSnippet as TextSnippetIcon,
  AccountCircle as AccountCircleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'
import { cloneElement, useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { RootState } from '../../../../configureStore'
import { Dispatch } from 'redux'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import {
  deleteCandidateById,
  fetchVideoUrl,
  fetchViewAttachmentsCandidate,
} from '../../../../actions'
import { recruitmentEntity } from '../../../../reducers'
import useOpenable from '../../../../hooks/useOpenable'
import DeleteConfirmationDialog from '../../Common/DeleteConfirmationDialog'
import { dataPayloadType } from '../../../../actions/Types'
import { candidateProps } from './types'
import EditRow from './EditRow'

function CandidateTableAction({
  request,
  deleteCandidateById,
  fetchViewAttachmentsCandidate,
  viewAttachment,
  fetchVideoUrl,
  getVideoUrl,
}: candidateProps) {
  const deleteDailog = useOpenable()
  const editDialog = useOpenable()
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)
  const [resumeRequested, setResumeRequested] = useState(false)
  const [videoRequested, setVideoRequested] = useState(false)
  const [selectedCandidateId, setSelectedCandidateId] = useState<string>('')

  const navigate = useNavigate()
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  useEffect(() => {
    if (!resumeRequested || !Array.isArray(viewAttachment) || viewAttachment.length === 0) return

    const nestedArray = viewAttachment[0]

    if (Array.isArray(nestedArray) && nestedArray.length > 0 && nestedArray[0]?.signed_url) {
      const resumeUrl = nestedArray[nestedArray.length - 1].signed_url
      window.open(resumeUrl, '_blank')

      setResumeRequested(false)
    } else {
      toast.error('Resume not found!', { position: 'top-right', autoClose: 3000 })
    }
    setResumeRequested(false)
  }, [viewAttachment])

  useEffect(() => {
    if (!videoRequested || Object.keys(getVideoUrl).length === 0) return

    if (getVideoUrl?.candidateVideoUrl) {
      navigate('candidate/video-preview', {
        state: {
          videoURL: getVideoUrl.candidateVideoUrl,
          questions: getVideoUrl.questions,
        },
      })
      setVideoRequested(false)
    } else {
      toast.error('Video not found!', { position: 'top-right', autoClose: 3000 })
    }
    setVideoRequested(false)
  }, [getVideoUrl])

  const handleDeleteConfirm = () => {
    if (selectedCandidateId) {
      deleteCandidateById({ id: selectedCandidateId })
      toast.success('Candidate deleted successfully', { position: 'top-right', autoClose: 2000 })
      deleteDailog.onClose()
    }
  }

  const actionHandler = (action: string) => {
    switch (action) {
      case 'feedback':
        window.open(`/home/<USER>/candidate/feedback/${request.id}`, '_blank')
        break

      case 'edit':
        window.open(`/home/<USER>/candidates/editcandidate/${request.id}`, '_blank')
        break

      case 'editrow':
        editDialog.onOpen()
        break

      case 'resume':
        setResumeRequested(false)
        setResumeRequested(true)
        fetchViewAttachmentsCandidate({ email: request.email })
        break

      case 'delete':
        setSelectedCandidateId(request.id)
        deleteDailog.onOpen()
        break

      case 'video':
        setVideoRequested(true)
        fetchVideoUrl({ userId: request.id })
        break

      case 'searchMail':
        if (request.name) {
          window.open(
            `https://mail.google.com/mail/u/0/#search/${encodeURIComponent(request.name)}`,
            '_blank',
          )
        }
        break

      default:
        toast.error('Unknown action', { position: 'top-right', autoClose: 2000 })
    }
    handleMenuClose()
  }

  const menuItems = [
    { label: 'Feedback', icon: <FeedbackIcon />, action: 'feedback' },
    { label: 'Resume', icon: <TextSnippetIcon />, action: 'resume' },
    { label: 'Edit Row', icon: <EditIcon />, action: 'editrow' },
    { label: 'Edit Candidate', icon: <AccountCircleIcon />, action: 'edit' },
    { label: 'Search In Mail', icon: <SearchIcon />, action: 'searchMail' },
    { label: 'Delete', icon: <DeleteIcon sx={{ color: '#db3700' }} />, action: 'delete' },
    { label: 'Video', icon: <VideocamIcon />, action: 'video' },
  ]

  return (
    <>
      <IconButton size='small' onClick={handleMenuClick}>
        <SettingsIcon color='primary' />
      </IconButton>
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        {menuItems.map(({ label, icon, action }) => (
          <MenuItem sx={{ py: 1 }} key={action} onClick={() => actionHandler(action)}>
            {cloneElement(icon, { sx: { px: 1, ...icon.props.sx } })} {label}
          </MenuItem>
        ))}
      </Menu>

      <DeleteConfirmationDialog
        open={deleteDailog.isOpen}
        handleClose={() => deleteDailog.onClose()}
        handleConfirm={handleDeleteConfirm}
        message='Are you sure you want to delete this candidate? This action cannot be undone.'
      />

      <EditRow isOpen={editDialog.isOpen} onClose={editDialog.onClose} formData={request} />
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  viewAttachment: recruitmentEntity.getRecruitment(state).viewAttachments,
  getVideoUrl: recruitmentEntity.getRecruitment(state).getVideoUrl,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  deleteCandidateById: (data: dataPayloadType) => dispatch(deleteCandidateById.request({ data })),
  fetchVideoUrl: (data: dataPayloadType) => dispatch(fetchVideoUrl.request({ data })),
  fetchViewAttachmentsCandidate: (data: dataPayloadType) =>
    dispatch(fetchViewAttachmentsCandidate.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(CandidateTableAction)
