import { useLocation, useNavigate } from 'react-router-dom'
import { Typo<PERSON>, <PERSON><PERSON>, Card, CardContent, Box } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

const VideoPreviewPage: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { videoURL, questions } = location.state || {}

  if (!videoURL) {
    return <Typography variant='h6'>No video available.</Typography>
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 3 }}>
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '900px',
          aspectRatio: '16 / 9',
          backgroundColor: 'black',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <video controls style={{ width: '100%', height: '100%', borderRadius: '10px' }}>
          <source src={videoURL} type='video/mp4' />
          Your browser does not support the video tag.
        </video>
      </Box>

      <Card sx={{ mt: 3, width: '100%' }}>
        <CardContent>
          <Typography variant='h6' className='mb-2'>
            Questions
          </Typography>
          <ul style={{ listStyleType: 'disc', paddingLeft: '1.5rem' }}>
            {questions?.map((question: { questionId: string; questionName: string }) => (
              <li key={question.questionId} style={{ marginBottom: '0.5rem' }}>
                {question.questionName}
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      <ActionButton variant='contained' onClick={() => navigate(-1)} sx={{ mt: 2 }}>
        Go Back
      </ActionButton>
    </Box>
  )
}

export default VideoPreviewPage
