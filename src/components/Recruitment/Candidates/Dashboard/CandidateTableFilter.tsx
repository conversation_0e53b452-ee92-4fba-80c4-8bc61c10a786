import { memo, useEffect } from 'react'
import { Card, CardContent, Typography, Grid, Box, SelectChangeEvent, Tooltip } from '@mui/material'
import Divider from '@mui/material/Divider'
import { ActionButton } from '../../../HolidayList/HolidaysStyles'
import CandidateTable from './CandidateTable'
import { formatForMultiSelect } from './utils'
import FieldWrap from './FieldWrap'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import {
  fetchDateTimeByRound,
  FetchTemplateByRound,
  sendmailCandidateByIds,
} from '../../../../actions'
import { toast } from 'react-toastify'
import { dataPayloadType } from '../../../../actions/Types'
import { useCandidateContext } from '../CandidateContext'
import { candidateTableFilterProp, SendMailCandidateParams } from './types'
import GenericDropdown from 'components/Recruitment/Common/GenericDropdown'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'

const CandidateTableFilter = memo(
  ({
    roundOptions,
    fetchDateTimeByRoundData,
    scheduleOptions,
    fetchTemplateByRoundData,
    templateOptions,
    sendmailCandidateByIds,
    isCandidateMailSent,
  }: candidateTableFilterProp) => {
    const { state, dispatch } = useCandidateContext()
    const handleRoundChange = (event: SelectChangeEvent) => {
      dispatch({ type: 'SET_ROUND_FILTER', payload: event.target.value })
      dispatch({ type: 'SET_TEMPLATE_FILTER', payload: '' })
      dispatch({ type: 'SET_SCHEDULE_FILTER', payload: '' })
      fetchTemplateByRoundData({ id_round: event.target.value })
      fetchDateTimeByRoundData({ id_round: event.target.value })
    }

    dayjs.extend(customParseFormat)

    useEffect(() => {
      if (isCandidateMailSent) {
        toast.success('Candidate Updated Successfully')
      }
    }, [isCandidateMailSent])

    const handleOnlyUpdateClick = () => {
      sendmailCandidateByIds({
        emailList: state.selectedRows,
        id_round: state.roundFilter,
        templateId: state.templateFilter,
        dateTime: state.scheduleFilter,
        mute: 'mute',
      })
      dispatch({ type: 'SET_SELECTED_ROWS', payload: [] })
    }

    const handleUpdateClick = () => {
      sendmailCandidateByIds({
        emailList: state.selectedRows,
        id_round: state.roundFilter,
        templateId: state.templateFilter,
        dateTime: state.scheduleFilter,
      })
      dispatch({ type: 'SET_SELECTED_ROWS', payload: [] })
    }

    const sortedScheduleOptions = [
      [...(scheduleOptions[0] || [])].sort(
        (a, b) =>
          dayjs((b as any).date_time, 'DD-MM-YYYY [at] hh:mm A').valueOf() -
          dayjs((a as any).date_time, 'DD-MM-YYYY [at] hh:mm A').valueOf(),
      ).slice(0, 30),
    ]

    return (
      <Grid>
        <Card variant='outlined' sx={{ mt: 2, mx: 2 }}>
          <CardContent>
            <Box display={'flex'} justifyContent={'space-between'} sx={{ mb: 2 }}>
              <Typography gutterBottom>Candidates Action</Typography>
            </Box>
            <Grid container spacing={2} sx={{ pb: '1rem' }}>
              <FieldWrap>
                <GenericDropdown
                  value={formatForMultiSelect(roundOptions, 'round_name', 'id', 0)}
                  label='Select Round'
                  selectedValue={state.roundFilter}
                  onChange={handleRoundChange}
                />
              </FieldWrap>
              <FieldWrap>
                <GenericDropdown
                  value={formatForMultiSelect(templateOptions, 'name', 'id', 0)}
                  label='Select Template'
                  selectedValue={state.templateFilter}
                  onChange={(event) =>
                    dispatch({ type: 'SET_TEMPLATE_FILTER', payload: event.target.value })
                  }
                />
              </FieldWrap>
              <FieldWrap>
                <GenericDropdown
                  value={formatForMultiSelect(sortedScheduleOptions, 'date_time', 'date_time', 0)}
                  label='Select Schedule'
                  selectedValue={state.scheduleFilter}
                  onChange={(event) =>
                    dispatch({ type: 'SET_SCHEDULE_FILTER', payload: event.target.value })
                  }
                />
              </FieldWrap>
              <Grid item lg={1.5} md={3} sm={6} xs={12}>
                <Tooltip
                  title='Select rows and template to enable button'
                  arrow
                  placement='top-start'
                >
                  <span>
                    <ActionButton
                      variant='outlined'
                      sx={{
                        marginTop: 0,
                        '&.Mui-disabled': {
                          color: 'white',
                          cursor: 'not-allowed',
                          pointerEvents: 'auto',
                          bgcolor: 'grey',
                        },
                      }}
                      fullWidth
                      disabled={state.selectedRows.length === 0 || state.roundFilter.length === 0}
                      onClick={handleOnlyUpdateClick}
                    >
                      Only Update
                    </ActionButton>
                  </span>
                </Tooltip>
              </Grid>
              <Grid item lg={2} md={3} sm={6} xs={12}>
                <Tooltip title='Select rows and template to enable button' arrow placement='top'>
                  <span style={{ display: 'inline-block', width: '100%' }}>
                    <ActionButton
                      variant='outlined'
                      sx={{
                        marginTop: 0,
                        '&.Mui-disabled': {
                          color: 'white',
                          cursor: 'not-allowed',
                          pointerEvents: 'auto',
                          bgcolor: 'grey',
                        },
                      }}
                      fullWidth
                      disabled={state.selectedRows.length === 0 || state.roundFilter.length === 0}
                      onClick={handleUpdateClick}
                    >
                      Update and Notify
                    </ActionButton>
                  </span>
                </Tooltip>
              </Grid>
            </Grid>
            <Divider sx={{ mb: '1rem' }} />
            <CandidateTable />
          </CardContent>
        </Card>
      </Grid>
    )
  },
)

const mapStateToProps = (state: RootState) => {
  return {
    roundOptions: recruitmentEntity.getRecruitment(state).getRoundData,
    scheduleOptions: recruitmentEntity.getRecruitment(state).getDateTimeByRound,
    templateOptions: recruitmentEntity.getRecruitment(state).getTemplateByRound,
    isCandidateMailSent: recruitmentStateUI.getRecruitment(state).isCandidateMailSent,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchDateTimeByRoundData: (data: dataPayloadType) =>
      dispatch(fetchDateTimeByRound.request({ data })),
    fetchTemplateByRoundData: (data: dataPayloadType) =>
      dispatch(FetchTemplateByRound.request({ data })),
    sendmailCandidateByIds: (data: SendMailCandidateParams) =>
      dispatch(sendmailCandidateByIds.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CandidateTableFilter)
