import { Box, Grid } from '@mui/material'
import FilterListIcon from '@mui/icons-material/FilterList'
import { ActionButton } from '../../../HolidayList/HolidaysStyles'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import DebouncedSearchedBox from '../../../Common/DebouncedSearchBox'
import { useCandidateContext } from '../CandidateContext'
import { useNavigate } from 'react-router-dom'
import RefreshIcon from '@mui/icons-material/Refresh'

interface CardHeader {
  onOpenChange: () => void
}

function CandidateHeader({ onOpenChange }: CardHeader) {
  const { state, dispatch } = useCandidateContext()

  const handleSearchChange = (query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query })
    dispatch({ type: 'SET_PAGE', payload: 1 })
  }

  const handleRefresh = () => {
    dispatch({ type: 'SET_APPLY_TRIGGER', payload: state.applyTrigger + 1 })
  }

  const navigate = useNavigate()
  return (
    <Box display='flex' sx={{ my: '1rem' }} flexDirection={{ xs: 'column', sm: 'row' }}>
      <DebouncedSearchedBox
        placeHolder='Search Candidate'
        setSearchQuery={(query) => handleSearchChange(query as string)}
      />
      <Box flexGrow={1}>
        <ActionButton
          variant='outlined'
          startIcon={<AddTwoToneIcon sx={{ width: 24, height: 24 }} />}
          onClick={() => navigate(`/home/<USER>/candidates/addcandidate`)}
        >
          Add Candidate
        </ActionButton>
        <ActionButton
          sx={{ mx: '1rem' }}
          variant='outlined'
          startIcon={<RefreshIcon sx={{ width: 24, height: 24 }} />}
          onClick={handleRefresh}
        >
          Refresh
        </ActionButton>
        <ActionButton
          sx={{ mx: '1rem' }}
          variant='outlined'
          startIcon={<FilterListIcon sx={{ width: 24, height: 24 }} />}
          onClick={onOpenChange}
        >
          Filters
        </ActionButton>
      </Box>
    </Box>
  )
}

export default CandidateHeader
