import React from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  SelectChangeEvent,
} from '@mui/material'
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { Dayjs } from 'dayjs'
interface AdditionalDetailsProps {
  positionOptions: { id: string; name: string }[]
  experienceOptions: { id: string; name: string }[]
}
interface AdditionalDetailsProps {
  formData: {
    position: string
    experience: string
    dob: Dayjs | null
  }
  errors: {
    position?: string
    experience?: string
    dob?: string
  }
  onChange: (e: React.ChangeEvent<HTMLInputElement> | SelectChangeEvent<string>) => void
  onDateChange: (date: Dayjs | null) => void
  positionOptions: { id: string; name: string }[]
  experienceOptions: { id: string; name: string }[]
}

const AdditionalDetails: React.FC<AdditionalDetailsProps> = ({
  formData,
  errors,
  onChange,
  onDateChange,
  positionOptions,
  experienceOptions,
}) => {
  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          sm: 'repeat(2, minmax(200px, 1fr))',
          md: 'repeat(3, minmax(250px, 1fr))',
        },
        gap: 2,
        width: '100%',
        alignItems: 'center',
      }}
    >
      <FormControl
        required
        size='small'
        sx={{
          width: '100% ',
          height: '56px',
          '& .MuiInputBase-root': {
            height: '56px',
            paddingLeft: '8px',
            paddingRight: '8px',
          },
          '& .MuiFormLabel-asterisk': {
            color: 'red',
          },
          '& .MuiOutlinedInput-root': { borderRadius: '50px' },
          '& .MuiInputLabel-root': {
            top: '14px',
            left: '16px',
            transform: 'translateY(0)',
          },
          '& .MuiInputLabel-shrink': {
            top: '-5px',
            left: '16px',
            transform: 'scale(0.75)',
          },
        }}
        error={!!errors.position}
      >
        <InputLabel>Position</InputLabel>
        <Select
          name='position'
          value={formData.position || ''}
          onChange={onChange}
          label='Position'
        >
          {positionOptions.map((option) => (
            <MenuItem key={option.id} value={option.id}>
              {option.name}
            </MenuItem>
          ))}
        </Select>
        {errors.position && <FormHelperText>{errors.position}</FormHelperText>}
      </FormControl>

      <FormControl
        required
        size='small'
        sx={{
          width: '100% ',
          height: '56px',
          '& .MuiInputBase-root': {
            height: '56px',
            paddingLeft: '8px',
            paddingRight: '8px',
          },
          '& .MuiFormLabel-asterisk': {
            color: 'red',
          },
          '& .MuiOutlinedInput-root': { borderRadius: '50px' },
          '& .MuiInputLabel-root': {
            top: '14px',
            left: '16px',
            transform: 'translateY(0)',
          },
          '& .MuiInputLabel-shrink': {
            top: '-5px',
            left: '16px',
            transform: 'scale(0.75)',
          },
        }}
        error={!!errors.experience}
      >
        <InputLabel>Experience</InputLabel>
        <Select
          name='experience'
          value={formData.experience || ''}
          onChange={onChange}
          label='Experience'
        >
          {experienceOptions.map((option) => (
            <MenuItem key={option.id} value={option.id}>
              {option.name}
            </MenuItem>
          ))}
        </Select>

        {errors.experience && <FormHelperText>{errors.experience}</FormHelperText>}
      </FormControl>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="Date of Birth"
          value={formData.dob}
          onChange={onDateChange}
          maxDate={dayjs().subtract(18, 'year')}
          slotProps={{
            textField: {
              fullWidth: true,
              error: !!errors.dob,
              helperText: errors.dob,
              sx: {
                '& .MuiOutlinedInput-root': { borderRadius: '50px' },
                '& .MuiFormLabel-asterisk': {
                  display: 'none',
                },
              },
            },
          }}
        />

      </LocalizationProvider>
    </Box>
  )
}

export default AdditionalDetails
