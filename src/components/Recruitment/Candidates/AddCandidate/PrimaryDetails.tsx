import React from "react";
import { TextField, Box, FormControl, InputLabel, Select, MenuItem, FormHelperText, SelectChangeEvent, } from "@mui/material";

interface PrimaryDetailsProps {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    contact: string;
    highestQualification: string;
    subject: string;
  };
  errors: {
    firstName?: string;
    lastName?: string;
    email?: string;
    contact?: string;
    highestQualification?: string;
    subject?: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => void;
  qualificationOptions: { id: string, name: string }[];
  subjectOptions: string[];
}

const PrimaryDetails: React.FC<PrimaryDetailsProps> = ({
  formData,
  errors,
  onChange,
  qualificationOptions,
  subjectOptions,
}) => {
  return (
    <Box sx={{
      display: "grid",
      gridTemplateColumns: { xs: "1fr", sm: "repeat(2, minmax(200px, 1fr))", md: "repeat(3, minmax(250px, 1fr))" },
      gap: 2,
      width: "100%",
      alignItems: "center"
    }}>

      {[
        { label: "First Name", name: "firstName" },
        { label: "Last Name", name: "lastName" },
        { label: "Email", name: "email" },
        { label: "Contact Number", name: "contact" },
      ].map((field) => (
        <FormControl sx={{ height: "80px" }}>
          <TextField
            key={field.name}
            label={field.label}
            name={field.name}
            value={formData[field.name as keyof typeof formData]}
            onChange={onChange}
            variant="outlined"
            fullWidth
            size="small"
            error={!!errors[field.name as keyof typeof errors]}
            helperText={errors[field.name as keyof typeof errors] || ""}
            sx={{
              width: "100%",
              "& .MuiInputBase-root": {
                height: "56px",
                paddingLeft: "8px",
                paddingRight: "8px",
              },
              '& .MuiFormLabel-asterisk': {
                color: "red"
              },
              "& .MuiOutlinedInput-root": { borderRadius: "50px" },
              "& .MuiInputLabel-root": {
                top: "14px",
                left: "16px",
                transform: "translateY(0)",
              },
              "& .MuiInputLabel-shrink": {
                top: "-5px",
                left: "16px",
                transform: "scale(0.75)",
              },
            }}
          />
        </FormControl>

      ))}
      {[
        {
          label: "Highest Qualification",
          name: "highestQualification",
          options: qualificationOptions,
          required: true,
        },
        { label: "11th/12th Subject", name: "subject", options: subjectOptions, required: false },
      ].map((selectField) => (
        <FormControl
          required={selectField.required}
          key={selectField.name}
          size="small"
          sx={{
            width: "100% ",
            height: "56px",
            "& .MuiInputBase-root": {
              height: "56px",
              paddingLeft: "8px",
              paddingRight: "8px",
            },
            ...(selectField.required && {
              '& .MuiFormLabel-asterisk': {
                color: "red"
              }
            }),
            "& .MuiOutlinedInput-root": { borderRadius: "50px" },
            "& .MuiInputLabel-root": {
              top: "14px",
              left: "16px",
              transform: "translateY(0)",
            },
            "& .MuiInputLabel-shrink": {
              top: "-5px",
              left: "16px",
              transform: "scale(0.75)",
            },
          }}
          error={!!errors[selectField.name as keyof typeof errors]}
        >
          <InputLabel>{selectField.label}</InputLabel>
          <Select
            name={selectField.name}
            value={formData[selectField.name as keyof typeof formData] || ""}
            onChange={onChange}
            label={selectField.label}
          >
            {selectField.name === 'subject'
              ? subjectOptions.map((option: string) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))
              : selectField.options.map((option: any) => (
                <MenuItem key={option.id} value={option.id}>
                  {option.name}
                </MenuItem>
              ))
            }
          </Select>
          {errors[selectField.name as keyof typeof errors] && (
            <FormHelperText>
              {errors[selectField.name as keyof typeof errors]}
            </FormHelperText>
          )}
        </FormControl>
      ))}
    </Box>
  );
};

export default PrimaryDetails;