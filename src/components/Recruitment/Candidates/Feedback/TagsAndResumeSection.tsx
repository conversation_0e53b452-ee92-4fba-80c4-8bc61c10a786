import React from 'react'
import { Box, Button, IconButton, FormHelperText, Typography, Text<PERSON>ield, Chip } from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import CloseIcon from '@mui/icons-material/Close'
import Autocomplete from '@mui/material/Autocomplete'
import { styled } from '@mui/material/styles'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

interface TagsAndResumeSectionProps {
  tags: { name: string; id: number }[]
  onTagsChange: (tags: { name: string; id: number }[]) => void
  tagOptions: string[]
  resume: File | null
  onResumeUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  onResumeRemove: () => void
  resumeError?: string
  addResume?: (data: any) => void
  isUploadingResume?: boolean
  candidateName?: string
}

const MAX_FILE_SIZE_MB = 5
const MAX_FILENAME_LENGTH = 15

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
})

const truncateFilename = (filename: string) => {
  return filename.length > MAX_FILENAME_LENGTH
    ? filename.substring(0, MAX_FILENAME_LENGTH) + '...'
    : filename
}

const TagsAndResumeSection: React.FC<TagsAndResumeSectionProps> = ({
  tags,
  onTagsChange,
  tagOptions,
  resume,
  onResumeUpload,
  onResumeRemove,
  resumeError,
  addResume,
  isUploadingResume = false,
  candidateName,
}) => {
  const { id } = useParams()

  const handleTagChange = (event: any, newValue: string[]) => {
    const newTags = newValue.map((tagName) => {
      const existingTag = tags.find((tag) => tag.name === tagName)
      if (existingTag) {
        return existingTag
      }
      return {
        name: tagName,
        id: Math.floor(Math.random() * -1000000),
      }
    })

    onTagsChange(newTags)
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const maxSize = MAX_FILE_SIZE_MB * 1024 * 1024
    if (file.size > maxSize) {
      toast.error(`File size must be less than ${MAX_FILE_SIZE_MB}MB`, {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }

    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ]

    if (!allowedTypes.includes(file.type)) {
      toast.error('Only PDF or Word files are allowed', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }

    onResumeUpload(e)

    if (addResume) {
      try {
        addResume({ file })
      } catch (error) {
        toast.error('Failed to upload resume. Please try again.', {
          position: 'top-right',
          autoClose: 3000,
        })
      }
    }
  }

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        gap: 3,
        alignItems: 'flex-start',
      }}
    >
      <Box
        sx={{
          width: { xs: '100%', sm: '50%' },
          minWidth: 0,
        }}
      >
        <Autocomplete
          fullWidth
          multiple
          freeSolo
          options={tagOptions}
          value={tags.map((x) => x.name)}
          onChange={handleTagChange}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant='outlined' label={option} {...getTagProps({ index })} />
            ))
          }
          renderInput={(params) => (
            <TextField
              required={false}
              {...params}
              label='Enter a New Tag'
              variant='standard'
              size='small'
              sx={{
                margin: 0,
                width: '100%',
                '& .MuiInputBase-root': {
                  paddingLeft: '10px',
                  paddingRight: '8px',
                  width: '95%',
                },
                '& .MuiInputLabel-shrink': {
                  transform: 'translate(12px, -5px) scale(0.75)',
                },
              }}
            />
          )}
        />
      </Box>

      <Box
        sx={{
          width: { xs: '100%', sm: '50%' },
          minWidth: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Button
            component='label'
            variant='contained'
            startIcon={<CloudUploadIcon />}
            disabled={isUploadingResume}
            sx={{
              backgroundColor: '#193c6d',
              color: 'white',
              padding: '10px 16px',
              fontSize: '14px',
              borderRadius: '24px',
              whiteSpace: 'nowrap',
              '&:hover': { backgroundColor: '#152a4e' },
              '&:disabled': {
                backgroundColor: '#cccccc',
                color: '#666666',
              },
            }}
          >
            {isUploadingResume ? 'Uploading...' : 'Upload Resume'}
            <VisuallyHiddenInput
              type='file'
              onChange={handleFileUpload}
              accept='.pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
              disabled={isUploadingResume}
            />
          </Button>

          {resume && resume.size / (1024 * 1024) <= MAX_FILE_SIZE_MB && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'white',
                padding: '8px 12px',
                borderRadius: '8px',
              }}
            >
              <Typography variant='body2' sx={{ marginRight: '8px' }}>
                {truncateFilename(resume.name)}
              </Typography>
              <IconButton onClick={onResumeRemove} size='small'>
                <CloseIcon fontSize='small' />
              </IconButton>
            </Box>
          )}
        </Box>

        {resumeError && (
          <FormHelperText error sx={{ ml: 2 }}>
            {resumeError}
          </FormHelperText>
        )}
      </Box>
    </Box>
  )
}

export default TagsAndResumeSection
