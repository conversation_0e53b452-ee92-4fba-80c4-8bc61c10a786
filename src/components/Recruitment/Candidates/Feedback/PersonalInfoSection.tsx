import React from 'react'
import { Box } from '@mui/material'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './FirstNameField'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './LastNameField'
import <PERSON><PERSON><PERSON><PERSON> from './EmailField'
import <PERSON><PERSON><PERSON> from './ContactField'
import Email<PERSON><PERSON><PERSON><PERSON> from './EmailBlockField'
import { SelectChangeEvent } from '@mui/material'

interface PersonalInfoSectionProps {
  formData: {
    Name: string

    email: string
    contact: string
    blockemail: string
  }
  errors: {
    Name?: string
    firstName?: string
    lastName?: string
    email?: string
    contact?: string
    blockemail?: string
  }
  onChange: (e: React.ChangeEvent<HTMLInputElement> | SelectChangeEvent) => void
  blockEmailOptions: string[]
}

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  formData,
  errors,
  onChange,
  blockEmailOptions,
}) => {
  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', width: '100%', gap: 2 }}>
      <FirstNameField value={formData.Name} error={errors.Name} onChange={onChange} />
      <EmailField value={formData.email} error={errors.email} onChange={onChange} />
      <ContactField value={formData.contact} error={errors.contact} onChange={onChange} />
      <EmailBlockField
        value={formData.blockemail}
        error={errors.blockemail}
        onChange={onChange}
        blockEmailOptions={blockEmailOptions}
      />
    </Box>
  )
}

export default PersonalInfoSection
