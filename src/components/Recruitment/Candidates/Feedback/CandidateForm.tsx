import React, { useState, useCallback, useEffect } from 'react'
import { Typography, Box } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { useDispatch } from 'react-redux'
import { viewCandidateTags } from '../../../../actions'
import PersonalInfoSection from './PersonalInfoSection'
import EducationSection from './EducationSection'
import ProfessionalSection from './ProfessionalSection'
import DateField from './DateField'
import StatusSection from './StatusSection'
import TagsInput from './TagsInput'
import ResumeUpload from './ResumeUpload'
import FormButtons from './FormButtons'
import TagsAndResumeSection from './TagsAndResumeSection'
import customParseFormat from 'dayjs/plugin/customParseFormat'

dayjs.extend(customParseFormat)

interface CandidateData {
  id: number
  name: string
  email: string
  phone: string
  experience: string
  jobRole: string
  blockemail?: string
  highestQualification?: string
  subject?: string
  status?: string
  dob?: string
  tags?: { name: string; id: number }[]
  resume?: File | null
  qualification: string
  round: string
}

interface Qualification {
  id: number
  qualification: string
}

interface RoundOption {
  id: number
  round_name: string
}

interface CandidateFormProps {
  open: boolean
  handleClose: () => void
  handleSave: (data: any) => void
  positionOptions: Array<{ id: number; name: string }>
  experiencesOptions: Array<{ id: number; experience: string }>
  qualificationOptions?: Qualification[]
  tagsOptions: any
  roundOptions?: RoundOption[]
  initialData?: CandidateData
  editCandidate: (data: any) => void
  uploadResume: (file: File) => void
  addResume?: (data: any) => void
  isUploadingResume?: boolean
  uploadedResumeFilename?: string
  viewResume?: string
}

export default function CandidateForm({
  open,
  handleClose,
  handleSave,
  positionOptions = [],
  experiencesOptions = [],
  qualificationOptions = [],
  tagsOptions,
  roundOptions = [],
  initialData,
  editCandidate,
  uploadResume,
  addResume,
  isUploadingResume = false,
  uploadedResumeFilename,
  viewResume,
}: CandidateFormProps) {
  const [formData, setFormData] = useState({
    Name: initialData?.name || '',
    email: initialData?.email || '',
    blockemail: initialData?.blockemail || '',
    contact: initialData?.phone || '',
    highestQualification: initialData?.qualification || '',
    subject: initialData?.subject || '',
    position: initialData?.jobRole || '',
    experience: initialData?.experience || '',
    status: initialData?.status || '',
    dob:
      initialData?.dob && dayjs(initialData.dob, 'DD-MM-YYYY').isValid()
        ? dayjs(initialData.dob, 'DD-MM-YYYY')
        : null,
    tags: initialData?.tags?.join(', ') || '',
    resume: initialData?.resume || null,
  })

  const [initialFormData, setInitialFormData] = useState<typeof formData | null>(null)
  const [tags, setTags] = useState<{ name: string; id: number }[]>(initialData?.tags || [])
  const [initialTags, setInitialTags] = useState<{ name: string; id: number }[]>(
    initialData?.tags || [],
  )
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isFormValid, setIsFormValid] = useState(false)
  const [uploadedResumeFile, setUploadedResumeFile] = useState<string | null>(null)

  const qualificationList = qualificationOptions.map((qual) => ({
    id: qual.id,
    name: qual.qualification,
  }))

  const subjectOptions = ['Commerce', 'Arts', 'PCM', 'PCB', 'Diploma', 'Other']

  const statusOptions = roundOptions.map((round) => round.round_name)
  let tagOptions: string[] = []
  let tagMap: { [key: string]: number } = {}

  if (tagsOptions && Array.isArray(tagsOptions)) {
    if (Array.isArray(tagsOptions[0])) {
      const tagArray = tagsOptions[0]
      tagOptions = tagArray?.map((option: any) => option.name) || []
      tagArray?.forEach((option: any) => {
        if (option.name && option.id) {
          tagMap[option.name] = option.id
        }
      })
    } else if (tagsOptions.length > 0 && typeof tagsOptions[0] === 'object') {
      tagOptions = tagsOptions.map((option: any) => option.name).filter(Boolean)
      tagsOptions.forEach((option: any) => {
        if (option.name && option.id) {
          tagMap[option.name] = option.id
        }
      })
    } else {
      tagOptions = tagsOptions.filter((option: any) => typeof option === 'string')
    }
  }

  const convertTagToName = (tag: string, availableOptions: any): string => {
    if (isNaN(Number(tag))) {
      return tag
    }

    let foundTagName = null

    if (availableOptions && Array.isArray(availableOptions)) {
      if (Array.isArray(availableOptions[0])) {
        foundTagName = availableOptions[0]?.find((t: any) => t.id === Number(tag))?.name
      } else if (availableOptions.length > 0 && typeof availableOptions[0] === 'object') {
        foundTagName = availableOptions.find((t: any) => t.id === Number(tag))?.name
      }
    }

    return foundTagName || tag
  }

  const blockEmailOptions = ['Yes', 'No']
  const experienceOptions = experiencesOptions.map((option) => option.experience)

  useEffect(() => {
    if (initialData) {
      const newFormData = {
        Name: initialData.name || '',
        email: initialData.email || '',
        blockemail: initialData.blockemail || '',
        contact: initialData.phone || '',
        highestQualification: initialData.qualification || '',
        subject: initialData.subject || '',
        position: initialData.jobRole || '',
        experience: initialData.experience || '',
        status: initialData.status || '',
        dob:
          initialData?.dob && dayjs(initialData.dob, 'DD-MM-YYYY').isValid()
            ? dayjs(initialData.dob, 'DD-MM-YYYY')
            : null,
        tags: initialData.tags?.join(', ') || '',
        resume: initialData.resume || null,
      }
      setFormData(newFormData)
      setInitialFormData(newFormData)
      const convertedTags =
        initialData.tags?.map((tag) => ({
          name: convertTagToName(tag.name, tagsOptions),
          id: tag.id,
        })) || []
      setTags(convertedTags)
      setInitialTags(convertedTags)
    }
  }, [initialData, tagsOptions])

  const handleTagsChange = (newTags: { name: string; id: number }[]) => {
    if (newTags.length > tags.length + 1) {
      const addedTags = newTags.filter((tag) => !tags.includes(tag))

      if (addedTags.length > 1) {
        const lastAddedTag = addedTags[addedTags.length - 1]
        const saferTags = [...tags, lastAddedTag]
        setTags(saferTags)
        return
      }
    }

    setTags(newTags)
  }

  useEffect(() => {
    validateForm()
  }, [formData, tags])

  useEffect(() => {
    if (uploadedResumeFilename) {
      setUploadedResumeFile(uploadedResumeFilename)
    }
  }, [uploadedResumeFilename])

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, Name: e.target.value })

    setErrors((prev) => ({ ...prev, Name: '' }))
  }

  const handleChange = (e: { target: { name: string; value: string } }) => {
    const { name, value } = e.target

    if (name === 'contact') {
      if (value === ' ' && /[^a-zA-Z0-9]/.test(value)) {
        return
      }

      const digitsOnly = value.replace(/\D/g, '')
      if (digitsOnly.length > 10) return

      setFormData((prev) => ({ ...prev, [name]: digitsOnly }))
      if (value.length < 10) return
      if (errors[name]) {
        setErrors((prev) => {
          const newErrors = { ...prev }
          delete newErrors[name]
          return newErrors
        })
      }

      return
    }

    if (name === 'Name' && value === ' ') {
      return
    }

    setFormData((prev) => ({ ...prev, [name]: value }))

    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const handleDateChange = (date: Dayjs | null) => {
    setFormData((prev) => ({ ...prev, dob: date }))
    if (errors.dob) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.dob
        return newErrors
      })
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setFormData((prev) => ({ ...prev, resume: file }))
    if (errors.resume) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.resume
        return newErrors
      })
    }
  }

  const removeResume = () => {
    setFormData((prev) => ({ ...prev, resume: null }))
    if (errors.resume) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.resume
        return newErrors
      })
    }
  }

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}
    let isDirty = false

    if (!formData.Name.trim()) {
      newErrors.Name = 'Name is required'
    }

    if (initialFormData && formData.Name !== initialFormData.Name) {
      const nameRegex = /^[A-Za-z\s]+$/
      if (!nameRegex.test(formData.Name)) {
        newErrors.Name = 'Enter a valid name'
      }
    }

    if (initialFormData && formData.email !== initialFormData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Enter a valid email address'
      }
    }

    if (initialFormData && formData.contact !== initialFormData.contact) {
      const phoneRegex = /^\d{10}$/

      if (!phoneRegex.test(formData.contact)) {
        newErrors.contact = 'Enter a valid 10-digit phone number (no spaces or special characters)'
      }
    }

    if (initialFormData && formData.dob !== initialFormData.dob) {
      if (formData.dob) {
        const eighteenYearsAgo = dayjs().subtract(18, 'year')
        if (dayjs(formData.dob).isAfter(eighteenYearsAgo)) {
          newErrors.dob = 'Candidate must be at least 18 years old'
        }
      }
    }

    if (initialFormData) {
      const formDirty = Object.keys(formData).some((key) => {
        const formKey = key as keyof typeof formData
        if (formKey === 'dob') {
          const current = formData.dob
          const initial = initialFormData.dob
          if (current === null && initial === null) return false
          if (current === null || initial === null) return true
          return !current.isSame(initial, 'day')
        }
        if (formKey === 'resume') {
          return formData.resume !== initialFormData.resume || uploadedResumeFile !== null
        }
        return formData[formKey] !== initialFormData[formKey]
      })

      const tagsDirty = JSON.stringify(tags) !== JSON.stringify(initialTags)
      isDirty = formDirty || tagsDirty
    }

    setErrors(newErrors)
    setIsFormValid(isDirty && Object.keys(newErrors).length === 0)
  }, [formData, initialFormData, tags, initialTags, uploadedResumeFile])

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault()

    if (!isFormValid) return

    const positionId =
      positionOptions.find((pos) => pos.name === formData.position)?.id.toString() || ''
    const experienceId =
      experiencesOptions.find((exp) => exp.experience === formData.experience)?.id.toString() || ''

    const deletedTags = initialTags.filter((initialTag) => !tags.includes(initialTag))

    const addedTags = tags.filter((currentTag) => !initialTags.includes(currentTag))

    let resumeValue = ''
    if (uploadedResumeFile) {
      resumeValue = uploadedResumeFile
    } else if (viewResume) {
      resumeValue = Array.isArray(viewResume) ? viewResume[0] || '' : viewResume
    }

    const payload = {
      id: initialData?.id,
      name: formData.Name,
      email: formData.email,
      phone_no: formData.contact,
      status: formData.status,
      qualification: formData.highestQualification,
      stage: '',
      position: positionId,
      experience: experienceId,
      dob: formData.dob ? formData.dob.format('DD-MM-YYYY') : '',
      tag: tags.map((tag) => tag.name),
      subjects: formData.subject,
      delete_tag: deletedTags.map((tag) => tag.id),
      block_mail: formData.blockemail === 'Yes' ? '1' : '0',
      resume: resumeValue,
    }

    try {
      editCandidate(payload)

      setTimeout(() => {
        handleSave(initialData?.id)

        handleClose()
      }, 200)
    } catch (error) {
      handleSave(initialData?.id)
      handleClose()
    }
  }

  return (
    <>
      <Typography
        variant='h4'
        fontWeight='bold'
        color='#193c6d'
        sx={{ textAlign: 'center', mb: 3, fontFamily: 'Montserrat-SemiBold' }}
      >
        {`${formData.Name.trim() || 'Candidate'}'s Information`}
      </Typography>

      <Box
        component='form'
        onSubmit={handleSubmit}
        sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, maxWidth: '95%', margin: 'auto' }}
      >
        <PersonalInfoSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          blockEmailOptions={blockEmailOptions}
        />
        <EducationSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          qualificationOptions={qualificationList}
          subjectOptions={subjectOptions}
        />
        <ProfessionalSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          positionOptions={positionOptions.map((option) => option.name)}
          experienceOptions={experienceOptions}
        />
        <DateField
          label='Date of Birth'
          value={formData.dob}
          onChange={handleDateChange}
          error={errors.dob}
        />
        <StatusSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          statusOptions={statusOptions}
        />
        <TagsAndResumeSection
          tags={tags}
          onTagsChange={handleTagsChange}
          tagOptions={tagOptions}
          resume={formData.resume}
          onResumeUpload={handleFileUpload}
          onResumeRemove={removeResume}
          resumeError={errors.resume}
          addResume={addResume}
          isUploadingResume={isUploadingResume}
          candidateName={formData.Name}
        />
        <FormButtons onGoBack={handleClose} isFormValid={isFormValid} onSubmit={handleSubmit} />
      </Box>
    </>
  )
}
