import React from 'react'
import { Box, Button, IconButton, FormHelperText, Typography } from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import CloseIcon from '@mui/icons-material/Close'
import { styled } from '@mui/material/styles'

interface ResumeUploadProps {
  resume: File | null
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRemove: () => void
  error?: string
}

const MAX_FILE_SIZE_MB = 5
const MAX_FILENAME_LENGTH = 15

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
})

const truncateFilename = (filename: string) => {
  return filename.length > MAX_FILENAME_LENGTH
    ? filename.substring(0, MAX_FILENAME_LENGTH) + '...'
    : filename
}

const ResumeUpload: React.FC<ResumeUploadProps> = ({ resume, onUpload, onRemove, error }) => {
  return (
    <Box sx={{ width: '100%', display: 'flex', alignItems: 'center', gap: 2 }}>
      <Button
        component='label'
        variant='contained'
        startIcon={<CloudUploadIcon />}
        sx={{
          backgroundColor: '#193c6d',
          color: 'white',
          padding: '10px 16px',
          fontSize: '14px',
          borderRadius: '24px',
          '&:hover': { backgroundColor: '#152a4e' },
        }}
      >
        Upload Resume
        <VisuallyHiddenInput type='file' onChange={onUpload} accept='.pdf' />
      </Button>

      {resume && resume.size / (1024 * 1024) <= MAX_FILE_SIZE_MB && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'white',
            padding: '8px 12px',
            borderRadius: '8px',
          }}
        >
          <Typography variant='body2' sx={{ marginRight: '8px' }}>
            {truncateFilename(resume.name)}
          </Typography>
          <IconButton onClick={onRemove}>
            <CloseIcon />
          </IconButton>
        </Box>
      )}

      {error && <FormHelperText error>{error}</FormHelperText>}
    </Box>
  )
}

export default ResumeUpload