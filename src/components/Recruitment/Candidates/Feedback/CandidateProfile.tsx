import React, { useEffect, useState } from 'react'
import { Box, Typography, Button, Dialog, IconButton, Avatar } from '@mui/material'
import InterviewFeedback from './InterviewFeedback'
import NoteEditor from './NoteEditor'
import CandidateForm from './CandidateForm'
import DeleteConfirmation from './DeleteConfirmation'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { Dispatch } from 'redux'
import {
  addFeedbackCandidate,
  addUploadAssignment,
  addResume,
  deleteAttachment,
  deleteCandidateFeedback,
  editCandidate,
  fetchAddQualification,
  fetchCandidatePosition,
  fetchFeedback,
  fetchPositions,
  fetchRoundsByType,
  fetchTags,
  getCandidatebyID,
  resumeApplicants,
  viewCandidateFeedback,
  viewCandidateTags,
} from '../../../../actions'
import { connect } from 'react-redux'
import DocumentsTable from './DocumentsTable'
import FeedbackPopUp from './FeedbackPopUp '
import { useParams } from 'react-router-dom'
import { isObject } from 'formik'
import MarksFeedbackShow from './MarksFeedbackShow'
import NoteShow from './NoteShow'
import { toast } from 'react-toastify'
import { handleToasts } from 'components/Recruitment/Common/notifications'

interface Attachment {
  resume: string
  email: string
  id: number
}
interface NoteData {
  feedback: string
  note_data: string
}

interface CandidateData {
  id: number
  name: string
  email: string
  phone_no: string
  experience_id: number
  position_id: number
  round_id: string
  qualification_id: string
  subjects: string
  dob: string
  attachments: Attachment[]
}
interface CandidateFeedback {
  id: number
  feedback: string
  candidate_id: number
  grade: string
  created_at: string
  assignment: string
  recruiter_name: string
  recruiter_image: string
  feedback_type: number
  note_type: string
}

interface Document {
  id: number
  name: string
}

interface Experience {
  id: number
  experience: string
}

interface JobDescription {
  id: number
  name: string
}

interface CandidateTag {
  tag_name: string
  tag_id: number
}

function CandidateProfile(props: any) {
  const [openDialog, setOpenDialog] = useState(false)
  const [openEdit, setOpenEdit] = useState(false)
  const [openFeedback, setOpenFeedback] = useState(false)
  const [openNoteEditor, setOpenNoteEditor] = useState(false)
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false)
  const [marksFeedback, setMarksFeeback] = useState('')
  const [notedata, setNotedata] = useState<NoteData>()
  const [isVisible, setIsVisible] = useState(false)
  const [isNoteVisible, setNoteIsVisible] = useState(false)
  const [candidateId, setCandidateId] = useState(-1)
  const [refresh, setRefresh] = useState(false)
  const [prevUploadingState, setPrevUploadingState] = useState(false)
  const [uploadedResumeFilename, setUploadedResumeFilename] = useState<string>('')

  const [candidateName, setCandidateName] = useState('')
  const [candidateEmail, setCandidateEmail] = useState('')
  const [candidatePhone, setCandidatePhone] = useState('')
  const [experience, setExperience] = useState('')
  const [jobRole, setJobRole] = useState('')
  const [qualificationId, setQualificationId] = useState('')
  const [roundId, setRoundId] = useState('')
  const [subjects, setSubjects] = useState('')
  const [dob, setDOB] = useState('')
  const [documents, setDocuments] = useState<Document[]>([])
  const [statusData, setStatusData] = useState([])
  const [candidateTags, setCandidateTags] = useState<CandidateTag[]>([])
  const [clickedResumeName, setClickedResumeName] = useState<string>('')

  const { id } = useParams()
  const {
    fetchpositionsData,
    positionExperienceOptions,
    fetchCandidatebyID,
    CandidateByIDOptions,
    fetchAddQualification,
    AddQualificationOptions,
    fetchTag,
    TagOptions,
    fetchViewData,
    UploadResume,
    addFeedbackCandidate,
    addUploadAssignment,
    deleteAttachment,
    editCandidate,
    viewCandidateFeedback,
    CandidateFeedback,
    deleteCandidateFeedback,
    addResume,
    viewCandidateTags,
    CandidateTagsData,
    isCandidateEdited,
    resetCandidateEdited,
    isFeedbackAdded,
    resetAddFeedback,
    isAttachmentDeleted,
    resetDeleteAttachment,
    isFeedbackDeleted,
    resetDeleteFeedback,
    RoundOptions,
    fetchRoundsByType,
    isApplicantResume,
    applicantsResume,
    isUploadingResume,
    addFeedbackCandidateResponse,
  } = props

  useEffect(() => {
    if (isFeedbackAdded) {
      let message = 'Candidate Feedback Added Successfully'

      if (
        addFeedbackCandidateResponse &&
        Array.isArray(addFeedbackCandidateResponse) &&
        addFeedbackCandidateResponse.length > 0
      ) {
        const response = addFeedbackCandidateResponse[0]

        if (response && response.feedback_type === 3) {
          message = 'Candidate Notes Added Successfully'
        }
      }

      handleToasts([
        {
          condition: true,
          message: message,
          reset: resetAddFeedback,
        },
      ])
    }
  }, [isFeedbackAdded, addFeedbackCandidateResponse, resetAddFeedback])

  useEffect(() => {
    handleToasts([
      {
        condition: isCandidateEdited,
        message: 'Candidate Details Successfully Updated',
        reset: resetCandidateEdited,
      },
      {
        condition: isAttachmentDeleted,
        message: 'Candidate Attachment Successfully Deleted',
        reset: resetDeleteAttachment,
      },
    ])
  }, [isCandidateEdited, isAttachmentDeleted, resetCandidateEdited, resetDeleteAttachment])

  useEffect(() => {
    if (isFeedbackDeleted) {
      toast.success('Candidate Feedback Successfully Deleted', {
        position: 'top-right',
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })

      viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })

      setRefresh((prev) => !prev)
    }
  }, [isFeedbackDeleted, id, viewCandidateFeedback, resetDeleteFeedback])

  useEffect(() => {
    if (prevUploadingState && !isUploadingResume) {
      toast.success('Resume Uploaded Successfully', {
        position: 'top-right',
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })

      if (UploadResume) {
        setUploadedResumeFilename(UploadResume)
      }
    }
    setPrevUploadingState(isUploadingResume)
  }, [isUploadingResume, prevUploadingState, UploadResume])

  useEffect(() => {
    if (isCandidateEdited && id) {
      const performSequentialRefresh = async () => {
        try {
          await new Promise((resolve) => setTimeout(resolve, 500))

          fetchCandidatebyID(id)

          await new Promise((resolve) => setTimeout(resolve, 700))

          const maxRetries = 3
          let retryCount = 0

          const refreshTags = async (): Promise<void> => {
            try {
              viewCandidateTags({ data: { id: id } })
            } catch (error) {
              retryCount++

              if (retryCount < maxRetries) {
                await new Promise((resolve) => setTimeout(resolve, 1000))
                return refreshTags()
              } else {
                throw error
              }
            }
          }

          await refreshTags()
        } catch (error) {
          setTimeout(() => {
            fetchCandidatebyID(id)
            viewCandidateTags({ data: { id: id } })
          }, 2000)
        }
      }

      performSequentialRefresh()
    }
  }, [isCandidateEdited, id, fetchCandidatebyID, viewCandidateTags])

  useEffect(() => {
    fetchpositionsData()
    if (id) {
      fetchCandidatebyID(id)
      viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })
      viewCandidateTags({ data: { id: id } })
    }
    fetchAddQualification()
    fetchTag()
  }, [
    id,
    fetchCandidatebyID,
    fetchpositionsData,
    fetchAddQualification,
    fetchTag,
    viewCandidateFeedback,
    viewCandidateTags,
  ])

  let documentdata: any = null
  let attachmentData: any = null
  if (isObject(CandidateByIDOptions[0])) {
    documentdata = CandidateByIDOptions[0]['0']
    attachmentData = CandidateByIDOptions[0]['attachments']
  }
  const Data =
    Array.isArray(CandidateFeedback) && Array.isArray(CandidateFeedback[0])
      ? CandidateFeedback[0]
      : []

  useEffect(() => {
    if (CandidateByIDOptions && CandidateByIDOptions.length > 0) {
      const candidateData = CandidateByIDOptions[0][0] as CandidateData
      setCandidateName(candidateData.name)
      setCandidateId(candidateData.id)
      setCandidateEmail(candidateData.email)
      setCandidatePhone(candidateData.phone_no)
      setSubjects(candidateData.subjects)
      setQualificationId(candidateData.qualification_id)
      setRoundId(candidateData.round_id)
      setDOB(candidateData.dob)

      const experienceData = positionExperienceOptions[0]?.find(
        (exp: Experience) => exp.id === candidateData.experience_id,
      )
      const jobDescriptionData = positionExperienceOptions[1]?.find(
        (job: JobDescription) => job.id === candidateData.position_id,
      )

      if (experienceData) {
        setExperience(experienceData.experience)
      }

      if (jobDescriptionData) {
        setJobRole(jobDescriptionData.name)
      }

      const attachments = CandidateByIDOptions[0].attachments
      if (attachments && attachments.length > 0) {
        setDocuments(attachments.map((att: any) => ({ id: att.id, name: att.resume })))
      }
    }
  }, [CandidateByIDOptions, positionExperienceOptions])

  useEffect(() => {
    if (CandidateTagsData && CandidateTagsData.length > 0) {
      let extractedTags: CandidateTag[] = []

      if (Array.isArray(CandidateTagsData) && Array.isArray(CandidateTagsData[0])) {
        const rawTags = CandidateTagsData[0]

        extractedTags = rawTags.map((tag: any, index: number) => {
          if (typeof tag === 'object' && tag.tag_name && tag.tag_id) {
            return { tag_name: tag.tag_name, tag_id: tag.tag_id }
          } else if (typeof tag === 'string' || typeof tag === 'number') {
            const tagId = typeof tag === 'number' ? tag : parseInt(tag)

            let tagName = tag.toString()
            if (TagOptions && Array.isArray(TagOptions)) {
              const foundTag = Array.isArray(TagOptions[0])
                ? TagOptions[0]?.find((t: any) => t.id === tagId)
                : TagOptions.find((t: any) => t.id === tagId)

              if (foundTag) {
                tagName = foundTag.name
              }
            }

            return { tag_name: tagName, tag_id: tagId }
          } else {
            return { tag_name: 'Unknown Tag', tag_id: index }
          }
        })
      } else if (Array.isArray(CandidateTagsData)) {
        const rawTags = CandidateTagsData

        extractedTags = rawTags.map((tag: any, index: number) => {
          if (typeof tag === 'object' && tag.tag_name && tag.tag_id) {
            return { tag_name: tag.tag_name, tag_id: tag.tag_id }
          } else if (typeof tag === 'string' || typeof tag === 'number') {
            const tagId = typeof tag === 'number' ? tag : parseInt(tag)

            let tagName = tag.toString()
            if (TagOptions && Array.isArray(TagOptions)) {
              const foundTag = Array.isArray(TagOptions[0])
                ? TagOptions[0]?.find((t: any) => t.id === tagId)
                : TagOptions.find((t: any) => t.id === tagId)

              if (foundTag) {
                tagName = foundTag.name
              }
            }

            return { tag_name: tagName, tag_id: tagId }
          } else {
            return { tag_name: 'Unknown Tag', tag_id: index }
          }
        })
      }

      const uniqueTags = extractedTags.filter(
        (tag, index, array) =>
          array.findIndex((t) => t.tag_name === tag.tag_name && t.tag_id === tag.tag_id) === index,
      )

      setCandidateTags(uniqueTags)
    } else {
      setCandidateTags([])
    }
  }, [CandidateTagsData, TagOptions])

  useEffect(() => {
    fetchRoundsByType({ round_type: 1 })
  }, [fetchRoundsByType])

  useEffect(() => {
    if (isApplicantResume.length > 0) {
      if (!clickedResumeName) {
        const url = isApplicantResume[0][0].signed_url ? isApplicantResume[0][0].signed_url : null
        if (url) {
          window.open(isApplicantResume[0][0].signed_url, '_blank')
        }
      } else {
        let targetResume = null

        for (const resumeArray of isApplicantResume) {
          if (Array.isArray(resumeArray)) {
            for (const resume of resumeArray) {
              const resumeName =
                resume.resume || resume.name || resume.file_name || resume.original_name
              if (resumeName && resumeName === clickedResumeName) {
                targetResume = resume
                break
              }
            }
          }
          if (targetResume) break
        }

        const url = targetResume?.signed_url || isApplicantResume[0][0]?.signed_url
        if (url) {
          window.open(url, '_blank')
        }

        setClickedResumeName('')
      }
    }
  }, [isApplicantResume, clickedResumeName])

  const handleDelete = (id: number) => {
    deleteCandidateFeedback({ id: id })
  }

  const handleAttachmentDelete = (data: any) => {
    deleteAttachment(data)
    setDocuments([])
    setTimeout(() => fetchCandidatebyID(id), 100)
  }

  const handleResumeUpload = (file: File) => {
    if (file.type !== 'application/pdf') {
      toast.error('Only PDF files are allowed', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size should not exceed 5MB', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }

    addResume({ file })
    setDocuments([{ id: Date.now(), name: file.name }])
    setTimeout(() => {
      fetchCandidatebyID(id)
      viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })
      viewCandidateTags({ data: { id: id } })
    }, 500)
  }

  const handleSave = (data: any) => {
    setOpenEdit(false)
  }

  useEffect(() => {
    if (id && refresh) {
      setTimeout(() => {
        fetchCandidatebyID(id)
        viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })
        viewCandidateTags({ data: { id: id } })
      }, 100)
    }
  }, [refresh, id, fetchCandidatebyID, viewCandidateFeedback, viewCandidateTags])

  const experienceOptions =
    positionExperienceOptions[0]?.map((exp: any) => ({
      id: exp.id,
      experience: exp.experience,
    })) || []
  const positionOptions =
    positionExperienceOptions[1]?.map((pos: any) => ({
      id: pos.id,
      name: pos.name,
    })) || []

  const handleResumeClick = (resumeName: string) => {
    setClickedResumeName(resumeName)
    applicantsResume({ email: candidateEmail })
  }
  return (
    <div
      style={{
        padding: '20px 15px',
        backgroundColor: 'rgb(231,235,240)',
        fontFamily: 'Montserrat-SemiBold',
      }}
    >
      <Box sx={{ minHeight: '80vh', bgcolor: 'white', padding: 2 }}>
        <Box
          sx={{
            color: '#193C6D',
            padding: 3,
            borderRadius: 1,
            border: '1px solid #c8cee3',
          }}
        >
          <Typography
            variant='h2'
            fontWeight='bold'
            sx={{ fontSize: '28px', fontFamily: 'Montserrat-Medium' }}
          >
            {candidateName}
          </Typography>
          <Typography
            variant='body1'
            sx={{
              fontSize: '18px',
              color: '#193C6D',
              marginTop: '10px',
              fontFamily: 'Montserrat-Medium',
            }}
          >
            ({candidateEmail}, {candidatePhone}, {experience}, {jobRole})
          </Typography>
          {candidateTags && candidateTags.length > 0 && (
            <Box sx={{ marginTop: '10px' }}>
              <Typography
                variant='body2'
                sx={{
                  fontSize: '16px',
                  color: '#193C6D',
                  fontFamily: 'Montserrat-Medium',
                  marginBottom: '5px',
                }}
              >
                Tags:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {candidateTags.map((tag: CandidateTag, index: number) => (
                  <Box
                    key={tag.tag_id || index}
                    sx={{
                      backgroundColor: '#e3f2fd',
                      color: '#193C6D',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '14px',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    {tag.tag_name || 'Unknown Tag'}
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            height: '45px',
            alignItems: 'space-evenly',
            marginTop: 4,
            width: '100%',
            gap: 5,
            backgroundColor: '',
          }}
        >
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenFeedback(true)}
          >
            Interview Feedback
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              width: '80%',
              borderRadius: '35px',
              fontSize: '16px',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenDialog(true)}
          >
            Written
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenNoteEditor(true)}
          >
            Notes
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              padding: '0px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => {
              const tagsToPass = candidateTags.map((tag) => tag.tag_name)

              const uniqueTags = Array.from(new Set(tagsToPass))

              setOpenEdit(true)
            }}
          >
            Edit
          </Button>
        </Box>

        <DocumentsTable
          documents={documents}
          onDelete={handleDelete}
          documentdata={documentdata}
          attachmentData={attachmentData}
          deleteAttachment={handleAttachmentDelete}
          handleResumeClick={handleResumeClick}
        />

        <FeedbackPopUp
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          candidateId={id || ''}
          refresh={refresh}
          setRefresh={setRefresh}
          setMarksFeeback={setMarksFeeback}
          setIsVisible={setIsVisible}
        />
        <MarksFeedbackShow
          Data={Data}
          handleDelete={handleDelete}
          candidateData={CandidateByIDOptions}
          CandidateTagsData={CandidateTagsData}
          editCandidate={editCandidate}
        />

        <DeleteConfirmation open={openDeleteConfirm} onClose={() => setOpenDeleteConfirm(false)} />
        <InterviewFeedback
          refresh={refresh}
          setRefresh={setRefresh}
          open={openFeedback}
          handleClose={() => setOpenFeedback(false)}
          candidateId={id || ''}
        />
        <Dialog open={openEdit} onClose={() => setOpenEdit(false)} maxWidth='lg' fullWidth>
          <Box p={2}>
            <CandidateForm
              open={openEdit}
              handleClose={() => setOpenEdit(false)}
              handleSave={handleSave}
              positionOptions={positionOptions}
              experiencesOptions={experienceOptions}
              qualificationOptions={AddQualificationOptions?.[0] || []}
              tagsOptions={TagOptions}
              roundOptions={RoundOptions}
              initialData={{
                id: candidateId,
                name: candidateName,
                email: candidateEmail,
                phone: candidatePhone,
                experience: experience,
                jobRole: jobRole,
                qualification: qualificationId,
                round: roundId,
                subject: subjects,
                dob: dob,
                blockemail: documentdata?.block_mail === '1' ? 'Yes' : 'No',
                status: documentdata?.status || '',
                tags: candidateTags.map((tag) => ({ name: tag.tag_name, id: tag.tag_id })),
              }}
              editCandidate={editCandidate}
              uploadResume={handleResumeUpload}
              addResume={addResume}
              isUploadingResume={isUploadingResume}
              uploadedResumeFilename={uploadedResumeFilename}
              viewResume={UploadResume}
            />
          </Box>
        </Dialog>

        <Dialog open={openNoteEditor} onClose={() => setOpenNoteEditor(false)} maxWidth='lg'>
          <Box p={2} sx={{ width: '750px' }}>
            <NoteEditor
              onChange={setOpenNoteEditor}
              setNoteIsVisible={setNoteIsVisible}
              setNotedata={setNotedata}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          </Box>
        </Dialog>
      </Box>
    </div>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    positionExperienceOptions: recruitmentEntity.getRecruitment(state).getPositionData || [[], []],
    CandidateByIDOptions: recruitmentEntity.getRecruitment(state).getCandidateByID,
    AddQualificationOptions: recruitmentEntity.getRecruitment(state).getAddQualificationData,
    TagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    CandidateFeedback: recruitmentEntity.getRecruitment(state).viewCandidateFeedback,
    CandidateTagsData: recruitmentEntity.getRecruitment(state).viewCandidateTags,
    UploadResume: recruitmentEntity.getRecruitment(state).addResume,
    UploadAssignment: recruitmentEntity.getRecruitment(state).addUploadAssignment,
    deleteCandidateFeedbackResponse:
      recruitmentEntity.getRecruitment(state).deleteCandidateFeedback,
    isCandidateEdited: recruitmentStateUI.getRecruitment(state).isCandidateEdited,
    isFeedbackAdded: recruitmentStateUI.getRecruitment(state).isFeedbackAdded,
    isAttachmentDeleted: recruitmentStateUI.getRecruitment(state).isAttachmentDeleted,
    isFeedbackDeleted: recruitmentStateUI.getRecruitment(state).isFeedbackDeleted,
    RoundOptions: recruitmentEntity.getRecruitment(state).getRoundsByTypeData,
    isApplicantResume: recruitmentEntity.getRecruitment(state).resumeApplicants,
    isUploadingResume: recruitmentStateUI.getRecruitment(state).isAddResume,
    addFeedbackCandidateResponse: recruitmentEntity.getRecruitment(state).addFeedbackCandidate,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchpositionsData: () => dispatch(fetchPositions.request()),
    fetchCandidatebyID: (id: string) => dispatch(getCandidatebyID.request({ id })),
    fetchAddQualification: () => dispatch(fetchAddQualification.request()),
    fetchTag: () => dispatch(fetchTags.request()),
    fetchViewData: () => dispatch(fetchCandidatePosition.request()),
    addFeedbackCandidate: (data: any) => dispatch(addFeedbackCandidate.request(data)),
    deleteAttachment: (data: any) => dispatch(deleteAttachment.request(data)),
    editCandidate: (data: any) => dispatch(editCandidate.request(data)),
    viewCandidateFeedback: (data: any) => dispatch(viewCandidateFeedback.request(data)),
    viewCandidateTags: (data: any) => dispatch(viewCandidateTags.request(data)),
    deleteCandidateFeedback: (data: any) => dispatch(deleteCandidateFeedback.request(data)),
    addResume: (data: any) => dispatch(addResume.request(data)),
    addUploadAssignment: (data: any) => dispatch(addUploadAssignment.request(data)),
    resetCandidateEdited: () => dispatch(editCandidate.reset()),
    resetAddFeedback: () => dispatch(addFeedbackCandidate.reset()),
    resetDeleteAttachment: () => dispatch(deleteAttachment.reset()),
    resetDeleteFeedback: () => dispatch(deleteCandidateFeedback.reset()),
    fetchRoundsByType: (data: {}) => dispatch(fetchRoundsByType.request({ data })),
    applicantsResume: (data: {}) => dispatch(resumeApplicants.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CandidateProfile)
