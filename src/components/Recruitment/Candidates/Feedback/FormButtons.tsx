import React from 'react'
import { But<PERSON>, <PERSON> } from '@mui/material'

interface FormButtonsProps {
  onGoBack: () => void
  isFormValid: boolean
  onSubmit: (e: React.FormEvent) => void
}

const FormButtons: React.FC<FormButtonsProps> = ({ onGoBack, isFormValid, onSubmit }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        marginLeft: 'auto',
        gap: 2,
      }}
    >
      <Button
        variant='contained'
        onClick={onGoBack}
        sx={{
          backgroundColor: '#E0E0E0',
          color: 'black',
          borderRadius: '24px',
          padding: '6px 20px',
          width: '120px',
          fontSize: '20px',
          marginBottom: '20px',
          marginright: '100px',

          ' &:hover': {
            backgroundColor: '#E0E0E0',
            color: 'black',
            boxShadow: '0 0 0 0.2',
          },
        }}
      >
        Cancel
      </Button>

      <Button
        variant='contained'
        sx={{
          backgroundColor: isFormValid ? '#1A3A6F' : '#a2b1c6',
          color: 'white',
          borderRadius: '24px',
          padding: '6px 20px',
          fontSize: '20px',
          fontWeight: 'bold',
          marginBottom: '20px',

          fontFamily: 'Montserrat-SemiBold',
          '&:hover': {
            backgroundColor: isFormValid ? '#152a4e' : '#a2b1c6',
          },
          '&.Mui-disabled': {
            backgroundColor: '#a2b1c6',
            color: 'white',
          },
        }}
        type='submit'
        onClick={onSubmit}
      >
        Update
      </Button>
    </Box>
  )
}

export default FormButtons
