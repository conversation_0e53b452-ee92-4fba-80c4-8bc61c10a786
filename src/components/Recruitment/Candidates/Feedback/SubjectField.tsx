import React from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material'

interface SubjectFieldProps {
  value: string
  error?: string
  onChange: (e: SelectChangeEvent) => void
  options: string[]
}
const SubjectField: React.FC<SubjectFieldProps> = ({ value, error, onChange, options }) => {
  return (
    <FormControl
      size='small'
      fullWidth
      error={!!error}
      sx={{
        margin: '0px 0px 8px 0px',
        width: { xs: '100%', sm: '48%' },
        '& .MuiOutlinedInput-root': { borderRadius: '50px', height: '50px' },
      }}
    >
      <InputLabel>11th/12th Subject</InputLabel>
      <Select name='subject' value={value} label='11th/12th Subject ' onChange={onChange}>
        {options.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>
      {error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}

export default SubjectField
