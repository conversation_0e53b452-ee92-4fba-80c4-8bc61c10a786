import React, { useState } from 'react'
import { Box, Typography, Avatar, IconButton } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'

interface NoteShowProps {
  feedback: string
  note_data: string
}

const NoteShow: React.FC<NoteShowProps> = ({ feedback, note_data }) => {
  const [isVisible, setIsVisible] = useState(true)

  const handleDelete = () => {
    setIsVisible(false)
  }

  const currentDateTime = new Date().toLocaleString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short',
  })

  if (!isVisible) return null

  return (
    <Box
      sx={{
        mt: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '10px',
        borderRadius: '10px',
        border: '1px solid #ddd',
        boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
        backgroundColor: '#fff',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Avatar
          src='https://via.placeholder.com/40'
          alt='User Avatar'
          sx={{ width: 40, height: 40, marginRight: '10px' }}
        />
        <Box>
          <Typography sx={{ fontWeight: 'bold', fontSize: '14px' }}>Shrukrishn Badade</Typography>
          <Typography sx={{ fontSize: '12px', color: 'gray' }}>{currentDateTime}</Typography>
          <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
            <strong>Feedback:</strong> <span dangerouslySetInnerHTML={{ __html: feedback || '' }} />
          </Typography>
          <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
            <strong>Note:</strong> <span dangerouslySetInnerHTML={{ __html: note_data || '' }} />
          </Typography>
        </Box>
      </Box>
      <IconButton sx={{ color: 'red', marginRight: '100px' }} onClick={handleDelete}>
        <DeleteIcon />
      </IconButton>
    </Box>
  )
}

export default NoteShow
