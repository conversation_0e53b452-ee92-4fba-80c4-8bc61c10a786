import React from "react";
import { TextField } from "@mui/material";

interface FirstNameFieldProps {
  value: string;
  error?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FirstNameField: React.FC<FirstNameFieldProps> = ({ value, error, onChange }) => {
  return (
    <TextField
      label="Name"
      name="Name"
      type=""
      value={value}
      onChange={onChange}
      variant="outlined"
      fullWidth
      size="small"
      error={!!error}
      helperText={error || ""}
      sx={{
        margin: "0px 0px 8px 0px",
        fontFamily:"Montserrat-Medium",
      
        width: { xs: "100%", sm: "48%" },
        "& .MuiOutlinedInput-root": { borderRadius: "50px", height: "50px" , },
      }}
    />
  );
};

export default FirstNameField;