
import React from 'react';
import { TextField } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Chip from '@mui/material/Chip';

interface TagsInputProps {
  tags: string[];
  onChange: (tags: string[]) => void;
  options: string[];
}
const TagsInput: React.FC<TagsInputProps> = ({ tags, onChange, options }) => {
  return (
    <Autocomplete
      fullWidth
      multiple  
      freeSolo
      options={options}
      value={tags}
      onChange={(_, newValue) => onChange(newValue)}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip variant="outlined" label={option} {...getTagProps({ index })} />
        ))
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label="Enter a New Tag"
          variant="standard"
          size="small"
           sx={{
            margin: 0,
                width: "40%", 
                "& .MuiInputBase-root": {
                    paddingLeft: "1px",
                    paddingRight: "8px",
                    width:"50%"
                },
                
                "& .MuiInputLabel-shrink": {
                    transform: 'translate(12px, -5px) scale(0.75)'
                }
              }}
        />
        
      )}
    />
  );
};

export default TagsInput;