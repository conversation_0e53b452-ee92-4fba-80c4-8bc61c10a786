import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Checkbox,
  FormControlLabel,
  IconButton,
  Box,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'

interface AddTagPopupProps {
  open: boolean
  onClose: () => void
}
const AddTagPopup: React.FC<AddTagPopupProps> = ({ open, onClose }) => {
  const [tag, setTag] = useState('')
  const [shortcutTag, setShortcutTag] = useState(false)

  return (
    <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth>
      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='center'
        p={2}
        sx={{ backgroundColor: '#193C6D' }}
      >
        <Typography variant='h6' sx={{ color: 'white', fontWeight: 'bold' }}>
          Add Tag
        </Typography>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent sx={{ p: 3 }}>
        <TextField
          label='Tag'
          variant='outlined'
          fullWidth
          value={tag}
          onChange={(e) => setTag(e.target.value)}
          sx={{ backgroundColor: 'white' }}
        />
        <FormControlLabel
          control={
            <Checkbox checked={shortcutTag} onChange={(e) => setShortcutTag(e.target.checked)} />
          }
          label={<Typography fontWeight='bold'>Shortcut Tag</Typography>}
          sx={{ mt: 2 }}
        />
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          variant='outlined'
          sx={{ borderRadius: '6px', padding: '6px 20px' }}
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button
          variant='contained'
          sx={{ borderRadius: '6px', padding: '6px 20px' }}
          disabled={!tag.trim()}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default AddTagPopup
