import React from "react";
import { TextField } from "@mui/material";

interface EmailFieldProps {
  value: string;
  error?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const EmailField: React.FC<EmailFieldProps> = ({ value, error, onChange }) => {
  return (
    <TextField
      label="Email"
      name="email"
      type="email"
      value={value}
      onChange={onChange}
      variant="outlined"
      fullWidth
      size="small"
      error={!!error}
      helperText={error || ""}
      sx={{
        margin: "0px 0px 8px 0px",
        width: { xs: "100%", sm: "48%" },
        "& .MuiOutlinedInput-root": { borderRadius: "50px" , height: "50px",  },
      }}
    />
  );
};

export default EmailField;