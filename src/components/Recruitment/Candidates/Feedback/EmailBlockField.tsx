import React from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material'

interface EmailBlockFieldProps {
  value: string
  error?: string
  onChange: (e: SelectChangeEvent) => void
  blockEmailOptions: string[]
}

const EmailBlockField: React.FC<EmailBlockFieldProps> = ({
  value,
  error,
  onChange,
  blockEmailOptions,
}) => {
  return (
    <FormControl
      size='small'
      fullWidth
      error={!!error}
      sx={{
        margin: '0px 0px 8px 0px',
        width: { xs: '100%', sm: '48%' },
        '& .MuiOutlinedInput-root': { borderRadius: '50px', height: '50px' },
      }}
    >
      <InputLabel>Block Email</InputLabel>
      <Select name='blockemail' value={value} label='Block email' onChange={onChange}>
        {blockEmailOptions.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>
      {error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}

export default EmailBlockField
