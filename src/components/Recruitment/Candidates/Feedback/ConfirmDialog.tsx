import React from "react";
import { Dialog, DialogTitle, DialogContent, DialogActions, But<PERSON> } from "@mui/material";

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, onClose, onConfirm }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle sx={{}}>Are you sure you want to delete?</DialogTitle>
      <DialogContent>This action cannot be undone.</DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">No</Button>
        <Button onClick={onConfirm} color="error" autoFocus>Yes</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDialog;