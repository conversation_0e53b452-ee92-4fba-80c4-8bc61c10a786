import React from 'react'
import { Box, SelectChangeEvent } from '@mui/material'
import HighestQualificationField from './HighestQualificationField'
import SubjectField from './SubjectField'

interface EducationSectionProps {
  formData: {
    highestQualification: string
    subject: string
  }
  errors: {
    highestQualification?: string
    subject?: string
  }
  onChange: (e: SelectChangeEvent) => void
  qualificationOptions: { id: number; name: string }[]
  subjectOptions: string[]
}

const EducationSection: React.FC<EducationSectionProps> = ({
  formData,
  errors,
  onChange,
  qualificationOptions,
  subjectOptions,
}) => {
  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', width: '100%', gap: 2 }}>
      <HighestQualificationField
        value={formData.highestQualification}
        error={errors.highestQualification}
        onChange={onChange}
        options={qualificationOptions}
      />
      <SubjectField
        value={formData.subject}
        error={errors.subject}
        onChange={onChange}
        options={subjectOptions}
      />
    </Box>
  )
}

export default EducationSection
