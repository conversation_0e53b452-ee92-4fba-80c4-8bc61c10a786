import { useFormik } from 'formik'
import * as Yup from 'yup'

interface UseFeedbackFormProps {
  candidateId: string
  setMarksFeeback: (feedback: string) => void
  setIsVisible: (value: boolean) => void
  refresh: boolean
  setRefresh: (value: boolean) => void
  addFeedbackCandidate: (data: any) => void
  onClose: () => void
}

export const useFeedbackForm = ({
  candidateId,
  setMarksFeeback,
  setIsVisible,
  refresh,
  setRefresh,
  addFeedbackCandidate,
  onClose,
}: UseFeedbackFormProps) => {
  return useFormik({
    initialValues: {
      totalMarks: '40',
      obtainedMarks: '',
      otherChecked: false,
      dropdownValue: '',
    },
    validationSchema: Yup.object({
      totalMarks: Yup.number()
        .typeError('Total marks must be a number')
        .when('otherChecked', {
          is: false,
          then: (schema) => schema.required('Total marks are required'),
          otherwise: (schema) => schema.notRequired(),
        }),
      obtainedMarks: Yup.number()
        .typeError('Obtained marks must be a number')
        .max(Yup.ref('totalMarks'), 'Cannot be greater than total marks')
        .when('otherChecked', {
          is: false,
          then: (schema) => schema.required('Obtained marks are required'),
          otherwise: (schema) => schema.notRequired(),
        }),
      dropdownValue: Yup.string().when('otherChecked', {
        is: true,
        then: (schema) => schema.required('Please select an option'),
        otherwise: (schema) => schema.notRequired(),
      }),
    }),
    onSubmit: (values) => {
      const feedback = values.otherChecked
        ? values.dropdownValue
        : `${values.obtainedMarks}/${values.totalMarks}`

      const feedback_id =
        values.dropdownValue === 'NA' ? 0 : values.dropdownValue === 'CM' ? 1 : -1

      const feedbackData = {
        candidate_id: candidateId,
        feedback: feedback,
        feedback_id: feedback_id,
        grade: '',
      }

      setIsVisible(true)
      setMarksFeeback(feedback)
      addFeedbackCandidate(feedbackData)

      values.totalMarks = '40'
      values.obtainedMarks = ''
      values.otherChecked = false
      values.dropdownValue = ''

      onClose()

    },
  })
}