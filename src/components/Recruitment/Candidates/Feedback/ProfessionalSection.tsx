import React from 'react'
import { Box } from '@mui/material'
import <PERSON>sition<PERSON>ield from './PositionField'
import ExperienceField from './ExperienceField'

interface ProfessionalSectionProps {
  formData: {
    position: string
    experience: string
  }
  errors: {
    position?: string
    experience?: string
  }
  onChange: (e: { target: { name: string; value: string } }) => void
  positionOptions: string[]
  experienceOptions: string[]
}

const ProfessionalSection: React.FC<ProfessionalSectionProps> = ({
  formData,
  errors,
  onChange,
  positionOptions,
  experienceOptions,
}) => {
  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', width: '100%', gap: 2 }}>
      <PositionField
        value={formData.position}
        error={errors.position}
        onChange={onChange}
        options={positionOptions}
      />
      <ExperienceField
        value={formData.experience}
        error={errors.experience}
        onChange={onChange}
        options={experienceOptions}
      />
    </Box>
  )
}

export default ProfessionalSection