import React from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material'

interface ExperienceFieldProps {
  value: string
  error?: string
  onChange: (e: SelectChangeEvent) => void
  options: string[]
}

const ExperienceField: React.FC<ExperienceFieldProps> = ({ value, error, onChange, options }) => {
  return (
    <FormControl
      size='small'
      fullWidth
      error={!!error}
      sx={{
        margin: '0px 0px 0px 0px',
        width: { xs: '100%', sm: '48%' },
        '& .MuiOutlinedInput-root': { borderRadius: '50px', height: '50px' },
      }}
    >
      <InputLabel>Experience</InputLabel>
      <Select name='experience' value={value} label='Experience' onChange={onChange}>
        {options.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>
      {error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}

export default ExperienceField
