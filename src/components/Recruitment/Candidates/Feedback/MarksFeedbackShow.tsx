import React, { useState } from 'react'
import { Box, Typography, Avatar, IconButton } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'
import DeleteConfirmationDialog from '../../Common/DeleteConfirmationDialog'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { aiReview } from 'services'
import { set } from 'date-fns'

interface CandidateFeedback {
  id: number
  feedback: string
  candidate_id: number
  grade: string | null
  created_at: string
  assignment: string | null
  recruiter_name: string
  recruiter_image: string
  feedback_type: number
  note_type: string
}

interface MarksFeedbackShowProps {
  Data: CandidateFeedback[]
  handleDelete: (value: number) => void
  candidateData: any
  CandidateTagsData: [{}][]
  editCandidate: (value: any) => void
}

let aiMsg = 'AI is currently unavailable. Please try again later.'

const MarksFeedbackShow: React.FC<MarksFeedbackShowProps> = ({
  Data,
  handleDelete,
  candidateData,
  CandidateTagsData,
  editCandidate,
}) => {
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const [openPopup, setOpenPopup] = useState(false)
  const candidate =
    Array.isArray(candidateData) && candidateData.length > 0 ? candidateData[0]['0'] : null
  const candidatePayload = candidate
    ? {
        id: candidate.id,
        name: candidate.name,
        email: candidate.email,
        phone_no: candidate.phone_no,
        qualification: candidate.qualification_id,
        experience: candidate.experience_id?.toString(),
        position: candidate.position_id?.toString(),
        dob: candidate.dob,
        subjects: candidate.subjects,
        status: candidate.status,
        block_mail: '0',
        resume: '',
        stage: '',
        tag:
          Array.isArray(CandidateTagsData) && CandidateTagsData.length > 0
            ? CandidateTagsData[0]
                .filter((tag: any) => tag.tag_id !== 672)
                .map((tag: any) => tag.tag_name)
            : [],
        delete_tag: [672],
      }
    : {}

  const handleOpenDialog = (id: number) => {
    setSelectedId(id)
    setOpenDialog(true)
  }
  const handleConfirmDelete = () => {
    if (selectedId !== null) {
      handleDelete(selectedId)
      setOpenDialog(false)
    }
  }

  if (Data.length === 0) {
    return (
      <Typography sx={{ textAlign: 'center', color: 'gray' }}>No Feedback Available</Typography>
    )
  }

  const handleAskAI = async (text: string) => {
    try {
      const response = await aiReview({ text })
      console.log("qwty",response.data.data)
      if (response.data.data === '1') {

        // alert('AI Response: Approved')
        aiMsg = 'AI Response: Approved'
        setOpenPopup(true)
        editCandidate({
          ...candidatePayload,
          tag: [
            ...(Array.isArray(candidatePayload.tag) ? candidatePayload.tag : []),
            'AI Approved',
          ],
        })
      } else {
        // alert('AI Response: Rejected')
         aiMsg = 'AI Response: Rejected'
        setOpenPopup(true)
        editCandidate({
          ...candidatePayload,
          tag: [
            ...(Array.isArray(candidatePayload.tag) ? candidatePayload.tag : []),
            'AI Rejected',
          ],
          status: 'On Hold For Now',
          stage: 'On Hold For Now',
        })
      }
    } catch (error) {
      // alert('AI Response: Rejected')
       aiMsg = 'AI Response: Rejected'
        setOpenPopup(true)
    }
  }

  return (
    <Box sx={{ width: '97%', margin: '20px' }}>
      {Data.map((item) => {
        const formattedDate = new Date(item.created_at).toLocaleString('en-US', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZoneName: 'short',
        })

        const isSpecialFeedback = item.feedback === 'na' || item.feedback === 'cm'

        return (
          <Box
            key={item.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '10px',
              borderRadius: '10px',
              border: '1px solid #ddd',
              boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
              backgroundColor: '#fff',
              marginBottom: '10px',
              width: '100%',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                src={item.recruiter_image ? item.recruiter_image : 'https://via.placeholder.com/40'}
                alt={item.recruiter_name}
                sx={{ width: 40, height: 40, marginRight: '10px' }}
              />
              <Box>
                <Typography sx={{ fontWeight: 'bold', fontSize: '14px' }}>
                  {item.recruiter_name}
                </Typography>
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>{formattedDate}</Typography>

                <Typography sx={{ fontSize: '14px', marginTop: '4px', color: '#555' }}>
                  {isSpecialFeedback ? (
                    item.note_type
                  ) : (
                    <span dangerouslySetInnerHTML={{ __html: item.feedback || '' }} />
                  )}
                </Typography>

                {!isSpecialFeedback && item.grade && (
                  <Typography sx={{ fontSize: '14px', marginTop: '4px', color: '#555' }}>
                    Grade: {item.grade}
                  </Typography>
                )}
              </Box>
            </Box>
            <Box display='flex' gap={2}>
              <ActionButton
                variant='outlined'
                sx={{
                  marginTop: 0,
                  '&.Mui-disabled': {
                    color: 'white',
                    bgcolor: 'grey',
                  },
                }}
                onClick={() => handleAskAI(item.feedback )}
              >
                Ask AI
              </ActionButton>
              <IconButton
                sx={{ color: 'red', marginRight: '10px' }}
                onClick={() => handleOpenDialog(item.id)}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>
        )
      })}

      <DeleteConfirmationDialog
        open={openDialog}
        handleClose={() => setOpenDialog(false)}
        handleConfirm={handleConfirmDelete}
        message='Are you sure you want to delete this feedback? This action cannot be undone.'
      />
      <DeleteConfirmationDialog
      title='AI Response'
        open={openPopup}
        handleClose={() => setOpenPopup(false)}
        message={aiMsg}
        confirm={false}
      />
    </Box>
  )
}

export default MarksFeedbackShow
