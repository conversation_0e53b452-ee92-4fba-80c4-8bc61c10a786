import React from 'react'
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { Dayjs } from 'dayjs'

interface DateFieldProps {
  value: Dayjs | null
  onChange: (date: Dayjs | null) => void
  label: string
  error?: string
}

const DateField: React.FC<DateFieldProps> = ({ value, onChange, label, error }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        label={label}
        value={value}
        onChange={onChange}
        format='MM-DD-YYYY' 
        slotProps={{
          textField: {
            fullWidth: true,
            error: !!error,
            helperText: error,
            required: false,
            sx: {
              margin: '0px 0px 0px 0px',
              mb: 0,
              width: { xs: '100%', sm: '48%', md: '48%' },
              '& .MuiOutlinedInput-root': { borderRadius: '50px', height: '50px' },
            },
          },
        }}
      />
    </LocalizationProvider>
  )
}

export default DateField
