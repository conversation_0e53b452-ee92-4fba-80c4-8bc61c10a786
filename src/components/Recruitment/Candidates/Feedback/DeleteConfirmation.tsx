import React from 'react'
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Box,
} from '@mui/material'

interface DeleteConfirmationProps {
  open: boolean
  onClose: () => void
}

const DeleteConfirmation: React.FC<DeleteConfirmationProps> = ({ open, onClose }) => {
  return (
    <Box>
      <Dialog
        open={open}
        onClose={onClose}
        fullWidth
        maxWidth='sm'
        PaperProps={{
          sx: { borderRadius: '6px', width: '500px', height: '250px' },
        }}
      >
        <Box sx={{ backgroundColor: '#193C6D' }}>
          <DialogTitle
            sx={{
              margin: 'auto ',
              fontFamily: 'Montserrat-SemiBold',
              padding: '20px',
              color: 'white',
              fontSize: '30px',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            Confirm Delete
          </DialogTitle>
        </Box>
        <DialogContent>
          <DialogContentText sx={{ marginLeft: '2px', color: '#193C6D', fontSize: '20px' }}>
            Are you sure you want to delete this document?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={onClose}
            color='primary'
            sx={{
              backgroundColor: '#E0E0E0',
              ' &:hover': {
                backgroundColor: '#E0E0E0',
                color: 'black',
                boxShadow: '0 0 0 0.2',
              },
              color: 'black',
              borderRadius: '24px',
              padding: '6px 20px',

              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',

              fontFamily: 'Montserrat-SemiBold',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onClose}
            color='error'
            sx={{
              backgroundColor: '#1A3A6F',
              color: 'white',
              borderRadius: '24px',
              padding: '6px 20px',
              marginRight: '20px',
              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',

              fontFamily: 'Montserrat-SemiBold',
            }}
            variant='contained'
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default DeleteConfirmation