import React, { useState, useCallback, useEffect } from 'react';
import { Typography, Box } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs, { Dayjs } from 'dayjs';
import { toast } from 'react-toastify';
import PrimaryDetails from './PrimaryDetails';
import AdditionalDetails from './AdditionalDetails';
import StatusTags from './StatusTags';
import Buttons from './Buttons';
import { connect } from 'react-redux';
import { RootState } from '../../../../configureStore';
import { fetchAddQualification, fetchPositions, fetchRoundsByType, fetchTags, getCandidatebyID, editCandidateForm, addResume, fetchViewAttachmentsCandidate, viewCandidateTags } from '../../../../actions';
import { Dispatch } from 'redux';
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers';
import Loader from 'components/Common/Loader';

interface OptionType {
    value: string;
    label: string;
}

const formatCandidatePayload = (
    formData: any,
    tags: string[],
    dynamicStageOptions: any[],
    id: string | undefined,
    viewResume: string | null
) => {
    return {
        id: id,
        name: `${formData.firstName.trim()} ${formData.lastName.trim()}`.trim(),
        email: formData.email.trim(),
        phone_no: formData.contact.trim(),
        status: formData.status,
        qualification: parseInt(formData.highestQualification),
        position: parseInt(formData.position),
        experience: parseInt(formData.experience),
        block_mail: "0",
        delete_tag: [],
        dob: formData.dob ? dayjs(formData.dob).format('DD-MM-YYYY') : null,
        subjects: formData.subject.trim(),
        tag: tags,
        stage: dynamicStageOptions.find((s: { id: string, name: string }) => s.name === formData.stage)?.id || "",
        resume: formData.resume ? viewResume : formData.resumeURL || '',
    };
};
function EditCandidate(props: any) {
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        contact: '',
        highestQualification: '',
        subject: '',
        position: '',
        experience: '',
        dob: null as Dayjs | null,
        status: '',
        stage: '',
        tags: '',
        resume: null as File | null,
        resumeURL: null as string | null,
    });
    const [signedResumeURL, setSignedResumeURL] = useState<string | null>(null);
    const [initialData, setInitialData] = useState<typeof formData | null>(null);
    const [initialTags, setInitialTags] = useState<string[]>([]);
    const { fetchCandidatebyID, CandidateByIDOptions, fetchAddQualification, AddQualificationOptions, fetchTag, TagOptions, fetchPosition, PositionOptions, fetchRoundsByType, RoundOptions, addResume, editCandidateForm, viewResume, isAddResume, fetchViewAttachmentsCandidate, viewAttachment,viewCandidateTags,CandidateTagsData } = props;
    const { id } = useParams();
    const [dynamicTagOptions, setDynamicTagOptions] = useState<string[]>([]);
    const [qualificationOptions, setQualificationOptions] = useState<OptionType[]>([]);
    const [experienceOptions, setExperienceOptions] = useState<OptionType[]>([]);
    const [positionOptions, setPositionOptions] = useState<OptionType[]>([]);
    const [dynamicStageOptions, setDynamicStageOptions] = useState<Array<{ id: string, name: string }>>([]);
    const [tags, setTags] = useState<string[]>([]);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});
    const [isFormValid, setIsFormValid] = useState(false);
    const navigate = useNavigate();
    const subjectOptions = ["Commerce", "Arts", "PCM", "PCB", "Diploma", "Other"];
    const statusOptions = ["Active", "Inactive"];
    const [shouldOpenPDF, setShouldOpenPDF] = useState(false);

    useEffect(() => {
        if (id) fetchCandidatebyID(id);
        fetchAddQualification();
        fetchTag();
        fetchPosition();
        fetchRoundsByType({ round_type: 1 });
    }, [id, fetchCandidatebyID, fetchRoundsByType]);
    
    useEffect(() => {
        if (viewAttachment?.data?.[0]?.[0]?.signed_url) {
            const signedUrl = viewAttachment.data[0][0].signed_url;
            setSignedResumeURL(signedUrl);
            if (shouldOpenPDF) {
                window.open(signedUrl, '_blank');
                setShouldOpenPDF(false);
            }
        }
    }, [viewAttachment, shouldOpenPDF]);
    
    useEffect(() => {
        const url = Array.isArray(viewAttachment?.[0]) && 
            viewAttachment[0].length > 0 && 
            viewAttachment[0][0]?.signed_url;
            
        if (url) {
            setSignedResumeURL(url);
            if (shouldOpenPDF) {
                window.open(url, '_blank');
                setShouldOpenPDF(false);
            }
        }
    }, [viewAttachment, shouldOpenPDF]);
    
    useEffect(() => {
        if (AddQualificationOptions?.length > 0) {
            setQualificationOptions(
                AddQualificationOptions[0].map((item: any) => ({
                    value: item.id.toString(),
                    label: item.qualification
                }))
            );
        }
        if (TagOptions?.length > 0) {
            setDynamicTagOptions(TagOptions[0].map((tagObj: any) => tagObj.name));
        }
        if (PositionOptions?.length > 1) {
            setExperienceOptions(
                PositionOptions[0].map((exp: any) => ({
                    value: exp.id.toString(),
                    label: exp.experience
                }))
            );
            setPositionOptions(
                PositionOptions[1].map((pos: any) => ({
                    value: pos.id.toString(),
                    label: pos.name
                }))
            );
        }
    }, [AddQualificationOptions, TagOptions, PositionOptions]);
    useEffect(() => {
        if (RoundOptions?.length > 0) {
            const stages = RoundOptions.map((round: any) => ({
                id: round.id.toString(),
                name: round.round_name
            }));
            setDynamicStageOptions(stages);
        }
    }, [RoundOptions])

    useEffect ( () => {
        if (id) {
          viewCandidateTags({ data: { id: id } })
        }
      }, [id, viewCandidateTags])
      
    useEffect(() => {
        if (CandidateTagsData && CandidateTagsData.length > 0) {
            let extractedTagNames: string[] = [];

            if (Array.isArray(CandidateTagsData) && Array.isArray(CandidateTagsData[0])) {
                const rawTags = CandidateTagsData[0];

                extractedTagNames = rawTags.map((tag: any) => {
                    if (typeof tag === 'object' && tag.tag_name) {
                        return tag.tag_name;
                    } else if (typeof tag === 'string' || typeof tag === 'number') {
                        const tagId = typeof tag === 'number' ? tag : parseInt(tag);

                        let tagName = tag.toString();
                        if (TagOptions && Array.isArray(TagOptions)) {
                            const foundTag = Array.isArray(TagOptions[0])
                                ? TagOptions[0]?.find((t: any) => t.id === tagId)
                                : TagOptions.find((t: any) => t.id === tagId);

                            if (foundTag) {
                                tagName = foundTag.name;
                            }
                        }

                        return tagName;
                    } else {
                        return 'Unknown Tag';
                    }
                });
            } else if (Array.isArray(CandidateTagsData)) {
                const rawTags = CandidateTagsData;

                extractedTagNames = rawTags.map((tag: any) => {
                    if (typeof tag === 'object' && tag.tag_name) {
                        return tag.tag_name;
                    } else if (typeof tag === 'string' || typeof tag === 'number') {
                        const tagId = typeof tag === 'number' ? tag : parseInt(tag);

                        let tagName = tag.toString();
                        if (TagOptions && Array.isArray(TagOptions)) {
                            const foundTag = Array.isArray(TagOptions[0])
                                ? TagOptions[0]?.find((t: any) => t.id === tagId)
                                : TagOptions.find((t: any) => t.id === tagId);

                            if (foundTag) {
                                tagName = foundTag.name;
                            }
                        }

                        return tagName;
                    } else {
                        return 'Unknown Tag';
                    }
                });
            }

            const uniqueTagNames = extractedTagNames.filter((tagName, index, array) => 
                tagName && tagName !== 'Unknown Tag' && array.indexOf(tagName) === index
            );

            setTags(uniqueTagNames);

            if (initialTags.length === 0) {
                setInitialTags(uniqueTagNames);
            }
        } else {
            if (CandidateTagsData !== undefined) {
                setTags([]);
                if (initialTags.length === 0) {
                    setInitialTags([]);
                }
            }
        }
    }, [CandidateTagsData, TagOptions, initialTags.length]);

    useEffect(() => {
        if (CandidateByIDOptions.length > 0) {
            const candidate = CandidateByIDOptions[0]["0"];
            const stageName = dynamicStageOptions.find((s: { id: string, name: string }) => s.id === candidate.stage)?.name || "";
            const resumeAttachment = CandidateByIDOptions[0].attachments.find(
                (att: any) => att.email === candidate.email
            );
            const initialFormData = {
                firstName: candidate.name.split(" ")[0] || "",
                lastName: candidate.name.split(" ")[1] || "",
                email: candidate.email || "",
                contact: candidate.phone_no || "",
                highestQualification: candidate.qualification_id?.toString() || "",
                subject: candidate.subjects || "",
                position: candidate.position_id?.toString() || "",
                experience: candidate.experience_id?.toString() || "",
                status: candidate.status || "",
                stage: stageName,
                dob: candidate.dob ? dayjs(candidate.dob, "DD-MM-YYYY") : null,
                tags: candidate.tag?.join(', ') || "",
                resume: null,
                resumeURL: resumeAttachment ? `${resumeAttachment.resume}` : null,
            };

            setFormData(initialFormData);
            setInitialData(initialFormData);
            if (!CandidateTagsData || CandidateTagsData.length === 0) {
                setTags(candidate.tag || []);
                setInitialTags(candidate.tag || []);
            }
            
            setFormData((prev) => ({
                ...prev,
                stage: stageName,
            }));
        }
    }, [CandidateByIDOptions, dynamicStageOptions, CandidateTagsData]);
    const handleChange = (e: { target: { name: string; value: string } }) => {
        const { name, value } = e.target;
        
        setTouchedFields(prev => ({ ...prev, [name]: true }));
        
        if (name === 'contact') {
            const numericValue = value.replace(/\D/g, '').slice(0, 10);
            setFormData(prev => ({ ...prev, [name]: numericValue }));
            return;
        }
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleDateChange = (date: Dayjs | null) => {
        setTouchedFields(prev => ({ ...prev, dob: true }));
        
        if (date) {
            const today = dayjs();
            const minAllowedDOB = today.subtract(18, 'year');

            if (date.isAfter(minAllowedDOB)) {
                setErrors((prev) => ({ ...prev, dob: "Candidate must be at least 18 years old." }));
                return;
            } else {
                setErrors((prev) => ({ ...prev, dob: "" }));
            }
        }
        setFormData((prev) => ({ ...prev, dob: date }));
    };
    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (file) {
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            ];

            if (!allowedTypes.includes(file.type)) {
                toast.error('Only PDF or Word files are allowed', {
                    position: 'top-right',
                    autoClose: 3000,
                });
                return;
            }

            if (file.size > 2 * 1024 * 1024) {
                setErrors((prev) => ({ ...prev, resume: 'File size should not exceed 2MB.' }));
                return;
            }
            setErrors((prev) => ({ ...prev, resume: '' }));
            setFormData((prev) => ({ ...prev, resume: file, resumeURL: URL.createObjectURL(file) }));
            addResume({ file });
        }
        e.target.value = '';
    };
    const handleResumeRemove = () => {
        setFormData((prev) => ({ ...prev, resume: null, resumeURL: '' }));
        setErrors((prev) => ({ ...prev, resume: '' }));
    };
    const validateForm = useCallback(() => {
        const newErrors: Record<string, string> = {};
        let isDirty = false;

        if (touchedFields.firstName && !formData.firstName.trim()) {
            newErrors.firstName = "First name is required";
        }
        if (touchedFields.lastName && !formData.lastName.trim()) {
            newErrors.lastName = "Last name is required";
        }
        if (touchedFields.email) {
            if (!formData.email.trim()) {
                newErrors.email = "Email is required";
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
                newErrors.email = "Invalid email format";
            }
        }
        if (touchedFields.contact) {
            if (!formData.contact.trim()) {
                newErrors.contact = "Contact number is required";
            } else if (formData.contact.length !== 10) {
                newErrors.contact = "Contact number must be 10 digits";
            }
        }
        if (touchedFields.dob && formData.dob) {
            const today = dayjs();
            const minAllowedDOB = today.subtract(18, 'year');

            if (formData.dob.isAfter(minAllowedDOB)) {
                newErrors.dob = "Candidate must be at least 18 years old.";
            }
        }
        if (initialData) {
            const formDirty = Object.keys(formData).some(key => {
                const formKey = key as keyof typeof formData;
                if (formKey === 'dob') {
                    const current = formData.dob;
                    const initial = initialData.dob;
                    if (current === null && initial === null) return false;
                    if (current === null || initial === null) return true;
                    return !current.isSame(initial, 'day');
                }
                if (formKey === 'resume') return formData.resume !== null;
                return formData[formKey] !== initialData[formKey];
            });
            const tagsDirty = JSON.stringify(tags) !== JSON.stringify(initialTags);
            isDirty = formDirty || tagsDirty;
        }
        setErrors(newErrors);
        setIsFormValid(
            isDirty &&
            Object.keys(newErrors).length === 0 &&
            formData.firstName.trim() !== "" &&
            formData.lastName.trim() !== "" &&
            formData.email.trim() !== "" &&
            formData.contact.trim() !== "" &&
            (!formData.dob || !newErrors.dob)
        );
    }, [formData, initialData, tags, initialTags, touchedFields]);
    useEffect(() => {
        validateForm();
    }, [formData, validateForm, tags]);
    const validateAllFields = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.firstName.trim()) {
            newErrors.firstName = "First name is required";
        }
        if (!formData.lastName.trim()) {
            newErrors.lastName = "Last name is required";
        }
        if (!formData.email.trim()) {
            newErrors.email = "Email is required";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Invalid email format";
        }
        if (!formData.contact.trim()) {
            newErrors.contact = "Contact number is required";
        } else if (formData.contact.length !== 10) {
            newErrors.contact = "Contact number must be 10 digits";
        }
        if (formData.dob) {
            const today = dayjs();
            const minAllowedDOB = today.subtract(18, 'year');

            if (formData.dob.isAfter(minAllowedDOB)) {
                newErrors.dob = "Candidate must be at least 18 years old.";
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: { preventDefault: () => void }) => {
        e.preventDefault();
        
        setTouchedFields({
            firstName: true,
            lastName: true,
            email: true,
            contact: true,
            dob: true,
        });
        
        const isValid = validateAllFields();
        if (!isValid || !isFormValid) return;
        
        const formattedData = formatCandidatePayload(formData, tags, dynamicStageOptions, id, props.viewResume);
        editCandidateForm(formattedData);
        navigate(`/home/<USER>
    };
    return (
        <>
            <Loader state={isAddResume} />
            <Box
                component='form'
                onSubmit={handleSubmit}
                sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 2,
                    padding: '24px',
                    maxWidth: '75%',
                    margin: 'auto',
                    backgroundColor: '#fff',
                    marginTop: '40px',
                    marginBottom: '20px',
                }}>
                <Typography
                    variant='h4'
                    color='black'
                    sx={{ textAlign: 'center', margin: 'auto' }}
                >
                    {`${formData.firstName} ${formData.lastName}'s Information`}
                </Typography>
                <PrimaryDetails
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    qualificationOptions={qualificationOptions}
                    subjectOptions={subjectOptions}
                />
                <AdditionalDetails
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    onDateChange={handleDateChange}
                    positionOptions={positionOptions}
                    experienceOptions={experienceOptions}
                />
                <StatusTags
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    statusOptions={statusOptions}
                    stageOptions={dynamicStageOptions}
                    tags={tags}
                    onTagsChange={setTags}
                    tagOptions={dynamicTagOptions}
                />
                <Buttons
                    resume={formData.resume}
                    resumeURL={signedResumeURL || formData.resumeURL}
                    onUpload={handleFileUpload}
                    onRemove={handleResumeRemove}
                    error={errors.resume}
                    onGoBack={() => navigate(`/home/<USER>
                    isFormValid={isFormValid}
                    onSubmit={handleSubmit}
                    email={formData.email}
                    fetchViewAttachmentsCandidate={(data) => {
                        setShouldOpenPDF(true);
                        fetchViewAttachmentsCandidate(data);
                    }}
                />
            </Box>
        </>
    );
}

const mapStateToProps = (state: RootState) => ({
    CandidateByIDOptions: recruitmentEntity.getRecruitment(state).getCandidateByID,
    AddQualificationOptions: recruitmentEntity.getRecruitment(state).getAddQualificationData,
    TagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    PositionOptions: recruitmentEntity.getRecruitment(state).getPositionData,
    CandidateTagsData: recruitmentEntity.getRecruitment(state).viewCandidateTags,
    RoundOptions: recruitmentEntity.getRecruitment(state).getRoundsByTypeData,
    isAddResume: recruitmentStateUI.getRecruitment(state).isAddResume,
    viewResume: recruitmentEntity.getRecruitment(state).addResume,
    viewAttachment: recruitmentEntity.getRecruitment(state).viewAttachments,
});

const mapDispatchToProps = (dispatch: Dispatch) => ({
    fetchCandidatebyID: (id: string) => dispatch(getCandidatebyID.request({ id })),
    fetchAddQualification: () => dispatch(fetchAddQualification.request()),
    fetchTag: () => dispatch(fetchTags.request()),
    fetchPosition: () => dispatch(fetchPositions.request()),
    viewCandidateTags: (data: any) => dispatch(viewCandidateTags.request(data)),
    fetchRoundsByType: (data: {}) => dispatch(fetchRoundsByType.request({ data })),
    addResume: (data: any) => dispatch(addResume.request(data)),
    editCandidateForm: (payload: any) => dispatch(editCandidateForm.request(payload)),
    fetchViewAttachmentsCandidate: (data: any) =>
        dispatch(fetchViewAttachmentsCandidate.request({ data })),
});

export default connect(mapStateToProps, mapDispatchToProps)(EditCandidate);