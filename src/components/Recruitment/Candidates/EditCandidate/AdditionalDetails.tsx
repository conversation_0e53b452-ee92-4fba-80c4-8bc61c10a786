import React from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  SelectChangeEvent,
} from "@mui/material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";

interface OptionType {
  value: string;
  label: string;
}

interface AdditionalDetailsProps {
  formData: {
    position: string;
    experience: string;
    dob: Dayjs | null;
  };
  errors: {
    position?: string;
    experience?: string;
    dob?: string;
  };
  onChange: (
    e: React.ChangeEvent<HTMLInputElement> | SelectChangeEvent<string>
  ) => void;
  onDateChange: (date: Dayjs | null) => void;
  positionOptions: OptionType[];
  experienceOptions: OptionType[];
}

const AdditionalDetails: React.FC<AdditionalDetailsProps> = ({
  formData,
  errors,
  onChange,
  onDateChange,
  positionOptions,
  experienceOptions,
}) => {
  return (
    <Box sx={{
      display: "grid",
      gridTemplateColumns: { xs: "1fr", sm: "repeat(2, minmax(200px, 1fr))", md: "repeat(3, minmax(250px, 1fr))" },
      gap: 2,
      width: "100%",
      alignItems: "center"
    }}>

      <FormControl
        size="small"
        sx={{
          width: "100% ",
          height: "56px",
          "& .MuiInputBase-root": {
            height: "56px",
            paddingLeft: "8px",
            paddingRight: "8px",
          },
          "& .MuiOutlinedInput-root": { borderRadius: "50px" },
          "& .MuiInputLabel-root": {
            top: "14px",
            left: "16px",
            transform: "translateY(0)",
          },
          "& .MuiInputLabel-shrink": {
            top: "-5px",
            left: "16px",
            transform: "scale(0.75)",
          },
        }}
        error={!!errors.position}
      >
        <InputLabel>Position</InputLabel>
        <Select
          name="position"
          value={formData.position || ""}
          onChange={(e) => onChange(e as SelectChangeEvent<string>)}
          label="Position"
        >
          {positionOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {errors.position && <FormHelperText>{errors.position}</FormHelperText>}
      </FormControl>

      <FormControl
        size="small"
        sx={{
          width: "100% ",
          height: "56px",
          "& .MuiInputBase-root": {
            height: "56px",
            paddingLeft: "8px",
            paddingRight: "8px",
          },

          "& .MuiOutlinedInput-root": { borderRadius: "50px" },
          "& .MuiInputLabel-root": {
            top: "14px",
            left: "16px",
            transform: "translateY(0)",
          },
          "& .MuiInputLabel-shrink": {
            top: "-5px",
            left: "16px",
            transform: "scale(0.75)",
          },
        }}
        error={!!errors.experience}
      >
        <InputLabel>Experience</InputLabel>
        <Select
          name="experience"
          value={formData.experience || ""}
          onChange={(e) => onChange(e as SelectChangeEvent<string>)}
          label="Experience"
        >
          {experienceOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {errors.experience && (
          <FormHelperText>{errors.experience}</FormHelperText>
        )}
      </FormControl>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="Date of Birth"
          value={formData.dob}
          onChange={onDateChange}
          maxDate={dayjs().subtract(18, 'year')}
          slotProps={{
            textField: {
              fullWidth: true,
              error: !!errors.dob,
              helperText: errors.dob,
              sx: {
                '& .MuiOutlinedInput-root': { borderRadius: '50px' },
                '& .MuiFormLabel-asterisk': {
                  display: "none",
                },
              },
            },
          }}
        />

      </LocalizationProvider>
    </Box>
  );
};

export default AdditionalDetails;