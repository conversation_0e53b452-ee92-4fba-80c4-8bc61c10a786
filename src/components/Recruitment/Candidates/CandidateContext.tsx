import React, { createContext, useReducer, useContext } from 'react'
import { Dayjs } from 'dayjs'

type Option = string | number

interface Filters {
  positions: Option[]
  rounds: Option[]
  tags: Option[]
  recruiters: Option[]
}

interface State {
  searchQuery: string
  roundFilter: string
  templateFilter: string
  scheduleFilter: string
  fromDate: Dayjs | null
  toDate: Dayjs | null
  filters: Filters
  selectedRows: string[]
  applyTrigger: number
  page: number
}

type Action =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_ROUND_FILTER'; payload: string }
  | { type: 'SET_TEMPLATE_FILTER'; payload: string }
  | { type: 'SET_SCHEDULE_FILTER'; payload: string }
  | { type: 'SET_FROM_DATE'; payload: Dayjs | null }
  | { type: 'SET_TO_DATE'; payload: Dayjs | null }
  | { type: 'SET_FILTERS'; payload: Filters }
  | { type: 'SET_SELECTED_ROWS'; payload: string[] }
  | { type: 'SET_APPLY_TRIGGER'; payload: number }
  | { type: 'SET_PAGE'; payload: number }

const initialState: State = {
  searchQuery: '',
  roundFilter: '',
  templateFilter: '',
  scheduleFilter: '',
  fromDate: null,
  toDate: null,
  filters: { positions: [], rounds: [], tags: [], recruiters: [] },
  selectedRows: [],
  applyTrigger: 0,
  page:1
}

function candidateReducer(state: State, action: Action) {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
    case 'SET_ROUND_FILTER':
      return { ...state, roundFilter: action.payload }
    case 'SET_TEMPLATE_FILTER':
      return { ...state, templateFilter: action.payload }
    case 'SET_SCHEDULE_FILTER':
      return { ...state, scheduleFilter: action.payload }
    case 'SET_FROM_DATE':
      return { ...state, fromDate: action.payload }
    case 'SET_TO_DATE':
      return { ...state, toDate: action.payload }
    case 'SET_FILTERS':
      return { ...state, filters: action.payload }
    case 'SET_SELECTED_ROWS':
      return { ...state, selectedRows: action.payload }
    case 'SET_APPLY_TRIGGER':
      return { ...state, applyTrigger: action.payload }
    case 'SET_PAGE':
      return { ...state, page: action.payload }
    default:
      return state
  }
}

const CandidateContext = createContext<{
  state: State
  dispatch: React.Dispatch<Action>
}>({
  state: initialState,
  dispatch: () => {},
})

export const CandidateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(candidateReducer, initialState)
  return (
    <CandidateContext.Provider value={{ state, dispatch }}>{children}</CandidateContext.Provider>
  )
}

export const useCandidateContext = () => useContext(CandidateContext)
