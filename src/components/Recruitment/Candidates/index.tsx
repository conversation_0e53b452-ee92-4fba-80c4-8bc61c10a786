import React from 'react'
import useOpenable from '../../../hooks/useOpenable'
import { Box, Collapse, Paper, Tab, Tabs } from '@mui/material'
import CandidateHeader from './Dashboard/CandidateHeader'
import FilterBar from './Dashboard/FilterBar'
import CandidateTableFilter from './Dashboard/CandidateTableFilter'
import { CandidateProvider } from './CandidateContext'
import BackButton from '../Common/GeneralizedBackButton'

const Candidates: React.FC = () => {
  const { isOpen, onOpenChange } = useOpenable()

  return (
    <CandidateProvider>
      <Paper sx={{ mx: '1rem', p: '1rem', marginTop: '1rem' }}>
        <Box display='flex' alignItems='center' justifyContent='space-between'>
          <Tabs value={0} aria-label='Candidate Dashboard'>
            <Tab sx={{
              fontSize: 'large', fontFamily: "Montserrat-Semibold",
              fontWeight: 600
            }} label='Candidates' />
          </Tabs>
          <BackButton tooltip='Go Back' />
        </Box>

        <CandidateHeader onOpenChange={onOpenChange} />
        <Collapse in={isOpen} timeout='auto'>
          <FilterBar />
        </Collapse>
      </Paper>
      <CandidateTableFilter />
    </CandidateProvider>
  )
}

export default Candidates
