import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import Buttons from './Buttons'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Paper from '@mui/material/Paper'
import Checkbox from '@mui/material/Checkbox'
import TableSortLabel from '@mui/material/TableSortLabel'
import IconButton from '@mui/material/IconButton'
import DeleteIcon from '@mui/icons-material/Delete'
import DescriptionIcon from '@mui/icons-material/Description'
import Tooltip from '@mui/material/Tooltip'
import FormControlLabel from '@mui/material/FormControlLabel'
import { initialRows } from './Data/ApplicantsData'
import PaginationControlled from './PaginationControlled'
import ExpandableTableCell from '../Common/ExpandableTableCell'
import {
  deleteUnapprovedCandidate,
  getSpaming,
  getUnapprovedCandidate,
  resumeApplicants,
} from '../../../actions'
import { StyledTableCell } from '../../Common/CommonStyles'
import ApplicantRow from './ApplicantsRow'
import { deleteApplicant, sortRows, filterRows } from './utils/ApplicantDetailUtils'
import DeleteConfirmationDialog from '../Common/DeleteConfirmationDialog'
import { RootState } from '../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../reducers'
import { Dispatch } from 'redux'
import Loader from '../../Common/Loader'
import { Box, Pagination } from '@mui/material'
import { toast, ToastContainer } from 'react-toastify'
import { useApplicantContext } from './ApplicantsContext'
import AttachmentsPage from './AttachmentsPage'

function ApplicantDetails(props: any) {
  const {
    fetchUnapprovedData,
    UnapprovedOptions,
    SpamingOptions,
    fetchSpamingData,
    searchQuery,
    isFetchApplicants,
    isFetchApplicantsSpam,
    deleteUnapprovedCandidate,
    isDeleteUnapprovedCandidate,
    isApplicantResume,
    applicantsResume,
    resetApplicantsResume,
  } = props

  const { dispatch } = useApplicantContext()

  const [rows, setRows] = useState<any[]>([])
  const [isSpamData, setIsSpamData] = useState(false)
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedApplicant, setSelectedApplicant] = useState<string | ''>('')
  const [selectedApplicantForResume, setSelectedApplicantForResume] = useState<any>(null)
  const [showAttachmentsPage, setShowAttachmentsPage] = useState(false)

  const dataCount = isSpamData ? SpamingOptions[1] || 0 : UnapprovedOptions[1] || 0
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const rowsPerPage = 20

  const isRowSelected = selectedRows.length > 0
  const clearSelectedRows = () => {
    setSelectedRows([])
    dispatch({ type: 'CLEAR_ALL' })
  }
  useEffect(() => {
    const handler = setTimeout(() => {
      if (isSpamData) {
        fetchSpamingData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
      } else {
        fetchUnapprovedData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
      }
    }, 500)

    return () => clearTimeout(handler)
  }, [searchQuery])

  useEffect(() => {
    const handler = setTimeout(() => {
      if (isSpamData) {
        fetchSpamingData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
      } else {
        fetchUnapprovedData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
      }
    }, 500)

    return () => clearTimeout(handler)
  }, [searchQuery])

  useEffect(() => {
    if (isSpamData) {
      fetchSpamingData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
    } else {
      fetchUnapprovedData({ limit: rowsPerPage, pageNumber: page, search_input: searchQuery })
      // applicantsResume({})
    }
  }, [page, isSpamData, isDeleteUnapprovedCandidate])

  useEffect(() => {
    setRows(isSpamData ? SpamingOptions[0] || [] : UnapprovedOptions[0] || [])
  }, [SpamingOptions, UnapprovedOptions, isSpamData])

  const handleDelete = (name: string) => {
    setSelectedApplicant(name)
    setDeleteDialogOpen(true)
  }

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelectedRows = filteredRows.map((row) => row.id)
      setSelectedRows(newSelectedRows)
      
      // Clear context first, then update with all selected applicant data
      dispatch({ type: 'CLEAR_ALL' })
      filteredRows.forEach((row) => {
        dispatch({ type: 'FETCH_ID', payload: row.id })
        dispatch({ type: 'FETCH_UNAPPROVED_EMAIL', payload: row.email })
        dispatch({ type: 'FETCH_MOBILE_NUMBER', payload: row.phone_no })
      })
    } else {
      setSelectedRows([])
      // Clear context when deselecting all
      dispatch({ type: 'CLEAR_ALL' })
    }
  }

  const handleRowSelect = (id: string) => {
    const selectedRow = filteredRows.find((row) => row.id === id)
    
    setSelectedRows((prevSelected) => {
      if (prevSelected.includes(id)) {
        // Remove from selection - remove from context too
        if (selectedRow) {
          dispatch({ type: 'REMOVE_ID', payload: selectedRow.id })
          dispatch({ type: 'REMOVE_UNAPPROVED_EMAIL', payload: selectedRow.email })
          dispatch({ type: 'REMOVE_MOBILE_NUMBER', payload: selectedRow.phone_no })
        }
        return prevSelected.filter((selectedId) => selectedId !== id)
      } else {
        // Add to selection - update context with applicant data
        if (selectedRow) {
          dispatch({ type: 'FETCH_ID', payload: selectedRow.id })
          dispatch({ type: 'FETCH_UNAPPROVED_EMAIL', payload: selectedRow.email })
          dispatch({ type: 'FETCH_MOBILE_NUMBER', payload: selectedRow.phone_no })
        }
        return [...prevSelected, id]
      }
    })
  }

  const confirmDelete = () => {
    if (selectedApplicant) {
      try {
        deleteUnapprovedCandidate({ id: selectedApplicant })
        setRows((prevRows) => prevRows.filter((row) => row.id !== selectedApplicant))
      } catch (error) {
        console.error('Error deleting applicant:', error)
      }
    }
    setDeleteDialogOpen(false)
    setSelectedApplicant('')
  }

  const handleViewResume = (email: string, applicantName: string) => {
    if (email) {
      setSelectedApplicantForResume({ email, name: applicantName })
      applicantsResume({
        email: email,
        unApproved: true,
      })
    }
  }

  useEffect(() => {
    if (isApplicantResume && Array.isArray(isApplicantResume) && isApplicantResume.length > 0) {
      // Show the attachments page instead of dialog
      setShowAttachmentsPage(true)
    }
  }, [isApplicantResume])

  const handleSort = () => {
    setOrder(order === 'asc' ? 'desc' : 'asc')
  }

  const filteredRows = sortRows(filterRows(rows, searchQuery || ''), order)

  const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
  }

  const handleToggleSpamData = () => {
    setIsSpamData((prev) => !prev)
    setPage(1)
  }

  const handleBackFromAttachments = () => {
    setShowAttachmentsPage(false)
    setSelectedApplicantForResume(null)
  }

  // Reset attachment page state when component mounts or when navigating back to applicants
  useEffect(() => {
    setShowAttachmentsPage(false)
    setSelectedApplicantForResume(null)
    // Also reset the resume data in Redux state
    resetApplicantsResume()
  }, []) // Empty dependency array means this runs only on mount

  const handleSuccessfulAction = (actionType: string, processedIds: string[]) => {
    // Remove processed candidates from the local state immediately
    setRows((prevRows) => prevRows.filter((row) => !processedIds.includes(row.id)))
    
    // Clear selected rows and context
    setSelectedRows([])
    dispatch({ type: 'CLEAR_ALL' })
    
    // Show success message
    toast.success(
      `${actionType.charAt(0).toUpperCase() + actionType.slice(1)} action successful!`,
      {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'colored',
      },
    )
  }

  // Show attachments page if requested
  if (showAttachmentsPage) {
    return (
      <>
        <ToastContainer />
        <AttachmentsPage
          resumeData={isApplicantResume?.[0] || []}
          applicantName={selectedApplicantForResume?.name || ''}
          applicantEmail={selectedApplicantForResume?.email || ''}
          onBack={handleBackFromAttachments}
        />
      </>
    )
  }

  return (
    <>
      <ToastContainer />
      <Loader state={isFetchApplicants || isFetchApplicantsSpam} />
      <Paper sx={{ p: 3, overflow: 'hidden', boxShadow: 3 }}>
        <Buttons
          fetchSpamingData={handleToggleSpamData}
          fetchUnapprovedData={fetchUnapprovedData}
          isRowSelected={isRowSelected}
          clearSelectedRows={clearSelectedRows}
          onSuccessfulAction={handleSuccessfulAction}
        />
        <TableContainer
          component={Paper}
          sx={{ backgroundColor: 'white', maxHeight: '100%', overflow: 'auto' }}
        >
          <Table sx={{ width: '100%', tableLayout: 'auto' }} aria-label='customized table'>
            <TableHead>
              <TableRow>
                <StyledTableCell>
                  <Checkbox
                    checked={filteredRows.length > 0 && selectedRows.length === filteredRows.length}
                    indeterminate={
                      selectedRows.length > 0 && selectedRows.length < filteredRows.length
                    }
                    onChange={handleSelectAll}
                    sx={{
                      color: 'white',
                      '&.Mui-checked': {
                        color: 'white',
                      },
                      '&.MuiCheckbox-indeterminate': {
                        color: 'white',
                      },
                    }}
                  />
                </StyledTableCell>
                <StyledTableCell>
                  <TableSortLabel
                    active
                    direction={order}
                    onClick={handleSort}
                    sx={{
                      color: 'white',
                      '&:hover': { color: 'white' },
                      '&.Mui-active': { color: 'white' },
                      '& .MuiTableSortLabel-icon': { color: 'white !important' },
                    }}
                  >
                    Name
                  </TableSortLabel>
                </StyledTableCell>
                <StyledTableCell>Email</StyledTableCell>
                <StyledTableCell>Mobile Number</StyledTableCell>
                <StyledTableCell>Qualification</StyledTableCell>
                <StyledTableCell>Content</StyledTableCell>
                <StyledTableCell>Applied On</StyledTableCell>
                <StyledTableCell>Actions</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredRows.length>0?filteredRows.map((row) => (
                <ApplicantRow
                  key={row.name}
                  row={row}
                  selectedRows={selectedRows}
                  handleRowSelect={handleRowSelect}
                  handleDelete={() => handleDelete(row.id)}
                  handleViewResume={() => handleViewResume(row.email, row.name)}
                />
              )):<StyledTableCell colSpan={8} sx={{height:"42px"}}>No data available</StyledTableCell>}
            </TableBody>
          </Table>
        </TableContainer>
        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          handleClose={() => setDeleteDialogOpen(false)}
          handleConfirm={confirmDelete}
        />
        {rows.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
            <Pagination
              count={Math.ceil(dataCount / rowsPerPage)}
              color='primary'
              page={page}
              onChange={handlePageChange}
              sx={{
                padding: '10px',
              }}
            />
          </Box>
        )}
      </Paper>
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  UnapprovedOptions: recruitmentEntity.getRecruitment(state).getAllUnapprovedCandidate,
  SpamingOptions: recruitmentEntity.getRecruitment(state).getAllSpaming,
  isFetchApplicants: recruitmentStateUI.getRecruitment(state).isFetchApplicants,
  isFetchApplicantsSpam: recruitmentStateUI.getRecruitment(state).isFetchApplicantsSpam,
  isApplicantResume: recruitmentEntity.getRecruitment(state).getApplicantsResume,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchUnapprovedData: (data: {}) => dispatch(getUnapprovedCandidate.request({ data })),
  fetchSpamingData: (data: {}) => dispatch(getSpaming.request({ data })),
  deleteUnapprovedCandidate: (data: {}) => dispatch(deleteUnapprovedCandidate.request({ data })),
  applicantsResume: (data: {}) => dispatch(resumeApplicants.request({ data })),
  resetApplicantsResume: () => dispatch(resumeApplicants.reset()),
})

export default connect(mapStateToProps, mapDispatchToProps)(ApplicantDetails)