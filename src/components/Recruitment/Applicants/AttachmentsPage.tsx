import React from 'react'
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Pagination,
} from '@mui/material'
import { 
  Download as DownloadIcon, 
  Delete as DeleteIcon, 
  ArrowBack as ArrowBackIcon 
} from '@mui/icons-material'
import { StyledTableCell } from '../../Common/CommonStyles'
import { ActionButton } from '../../HolidayList/HolidaysStyles'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { deleteAttachment } from '../../../actions'
import { toast } from 'react-toastify'
import DeleteConfirmationDialog from '../Common/DeleteConfirmationDialog'

interface AttachmentsPageProps {
  resumeData: any
  applicantName: string
  applicantEmail: string
  onBack: () => void
  deleteAttachment: (data: any) => void
}

const AttachmentsPage: React.FC<AttachmentsPageProps> = ({
  resumeData,
  applicantName,
  applicantEmail,
  onBack,
  deleteAttachment,
}) => {
  const [page, setPage] = React.useState(1)
  const [localResumeData, setLocalResumeData] = React.useState(resumeData)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [resumeToDelete, setResumeToDelete] = React.useState<any>(null)
  const rowsPerPage = 10
  
  // Update local data when resumeData prop changes
  React.useEffect(() => {
    setLocalResumeData(resumeData)
  }, [resumeData])
  
  const handleViewResume = (url: string) => {
    if (url) {
      window.open(url, '_blank')
    }
  }

  const handleDeleteResume = (index: number) => {
    const resumeToDelete = localResumeData[index]
    if (resumeToDelete) {
      setResumeToDelete(resumeToDelete)
      setDeleteDialogOpen(true)
    }
  }

  const handleConfirmDelete = () => {
    if (resumeToDelete && applicantEmail) {
      const resumeName = getFileName(resumeToDelete.signed_url)
      
      // Call the delete API
      deleteAttachment({ 
        email: applicantEmail, 
        resume: resumeName 
      })
      
      // Remove from local state immediately (optimistic update)
      setLocalResumeData((prevData: any[]) => 
        prevData.filter(resume => resume !== resumeToDelete)
      )
      
      // Show success message
      toast.success('Resume deleted successfully!', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
    }
    
    setDeleteDialogOpen(false)
    setResumeToDelete(null)
  }

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false)
    setResumeToDelete(null)
  }

  const getFileName = (url: string, index?: number) => {
    if (!url) return 'Resume'
    
    try {
      // First try to extract filename from URL path
      const urlObj = new URL(url)
      let fileName = urlObj.pathname.split('/').pop() || ''
      
      // Remove query parameters from filename
      if (fileName.includes('?')) {
        fileName = fileName.split('?')[0]
      }
      
      // Extract filename from query parameters if available
      const responseContentDisposition = urlObj.searchParams.get('response-content-disposition')
      if (responseContentDisposition) {
        const match = responseContentDisposition.match(/filename="([^"]*)"/)
        if (match && match[1]) {
          fileName = match[1]
        }
      }
      
      // If we still don't have a good filename, try to extract from other URL parts
      if (!fileName || fileName.length < 3) {
        // Look for filename in the URL path segments
        const pathSegments = urlObj.pathname.split('/')
        for (let i = pathSegments.length - 1; i >= 0; i--) {
          const segment = pathSegments[i]
          if (segment && (segment.includes('.') || segment.length > 5)) {
            fileName = segment
            break
          }
        }
      }
      
      // Clean up the filename
      if (fileName) {
        // Remove any remaining query parameters
        fileName = fileName.split('?')[0]
        
        // Decode URL encoding
        fileName = decodeURIComponent(fileName)
        
        // If filename is still too long or looks like a hash, create a user-friendly name
        if (fileName.length > 50 || /^[a-f0-9]{20,}/.test(fileName)) {
          const extension = fileName.split('.').pop()
          if (extension && ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'].includes(extension.toLowerCase())) {
            fileName = `Resume_${index !== undefined ? index + 1 : 1}.${extension}`
          } else {
            fileName = `Resume_${index !== undefined ? index + 1 : 1}`
          }
        }
      } else {
        // Fallback to generic name
        fileName = `Resume_${index !== undefined ? index + 1 : 1}`
      }
      
      return fileName
    } catch (error) {
      // If URL parsing fails, return a generic name
      return `Resume_${index !== undefined ? index + 1 : 1}`
    }
  }

  const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
  }

  const paginatedData = localResumeData ? localResumeData.slice((page - 1) * rowsPerPage, page * rowsPerPage) : []
  const totalPages = localResumeData ? Math.ceil(localResumeData.length / rowsPerPage) : 0

  return (
    <Box sx={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f5f5f5',
      zIndex: 1300,
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Main Content */}
      <Box sx={{ flex: 1, px: 3, py: 3, overflow: 'auto' }}>
        <Box sx={{ mb: 2, pl: 1, display: 'flex', alignItems: 'center' }}>
          <IconButton 
            onClick={onBack}
            sx={{ 
              mr: 2,
              backgroundColor: 'rgb(25, 60, 109)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgb(20, 50, 95)',
              },
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography 
            variant="h6" 
            sx={{ 
              fontFamily: 'Montserrat-Bold, sans-serif',
              fontWeight: 'bold',
              color: 'rgb(25, 60, 109)',
            }}
          >
            Attachments
          </Typography>
        </Box>
        <Paper sx={{ 
          height: 'calc(100% - 50px)', 
          overflow: 'hidden', 
          boxShadow: 3,
          display: 'flex',
          flexDirection: 'column'
        }}>
          <TableContainer sx={{ 
            backgroundColor: 'white', 
            flex: 1,
            overflow: 'auto'
          }}>
            <Table sx={{ width: '100%' }} aria-label="attachments table">
            <TableHead>
              <TableRow>
                <StyledTableCell sx={{ textAlign: 'center' }}>Actions</StyledTableCell>
                <StyledTableCell>Documents</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedData && paginatedData.length > 0 ? (
                paginatedData.map((resume: any, index: number) => (
                  <TableRow key={index} sx={{ '&:hover': { backgroundColor: '#f9f9f9' } }}>
                    <StyledTableCell sx={{ textAlign: 'center' }}>
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <Tooltip title="Download">
                          <IconButton
                            color="primary"
                            onClick={() => handleViewResume(resume.signed_url)}
                            sx={{
                              backgroundColor: '#e3f2fd',
                              '&:hover': {
                                backgroundColor: 'rgba(25, 60, 109, 0.1)',
                              },
                            }}
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            sx={{
                              color: '#d32f2f',
                              backgroundColor: '#ffebee',
                              '&:hover': {
                                backgroundColor: 'rgba(211, 47, 47, 0.1)',
                              },
                            }}
                            onClick={() => handleDeleteResume(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </StyledTableCell>
                    <StyledTableCell>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          fontFamily: 'Montserrat-Medium, sans-serif',
                          cursor: 'pointer',
                          '&:hover': {
                            textDecoration: 'underline',
                            color: 'rgb(25, 60, 109)',
                          }
                        }}
                        onClick={() => handleViewResume(resume.signed_url)}
                      >
                        {getFileName(resume.signed_url, index)}
                      </Typography>
                    </StyledTableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <StyledTableCell colSpan={2} sx={{ textAlign: 'center', padding: '60px' }}>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontFamily: 'Montserrat-Medium, sans-serif',
                        color: 'rgba(0, 0, 0, 0.6)'
                      }}
                    >
                      No attachments available
                    </Typography>
                  </StyledTableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          </TableContainer>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, backgroundColor: 'white' }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                sx={{
                  '& .MuiPaginationItem-root': {
                    fontFamily: 'Montserrat-Medium, sans-serif',
                  },
                }}
              />
            </Box>
          )}
        </Paper>
      </Box>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleCancelDelete}
        handleConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this resume? This action cannot be undone."
      />
    </Box>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  deleteAttachment: (data: any) => dispatch(deleteAttachment.request(data)),
})

export default connect(null, mapDispatchToProps)(AttachmentsPage)