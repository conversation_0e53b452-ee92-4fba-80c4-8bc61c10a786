import { Box, Stack, Dialog, DialogTitle, DialogContent, DialogActions, Typography } from '@mui/material'
import { ActionButton } from '../../HolidayList/HolidaysStyles'
import { useState } from 'react'
import { toast, ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { getApprovedApplicants, getRejectedApplicants } from 'services'
import { approveApplicants, deleteApplicants, rejectApplicants } from 'actions'
import { RootState } from 'configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux/es/exports'
import { recruitmentEntity, recruitmentStateUI} from 'reducers'
import { useApplicantContext } from './ApplicantsContext'
import { IdApplicants } from 'sagas/Types'

const Buttons = ({ fetchSpamingData, isRowSelected, fetchApprovedData, fetchRejectedData, fetchMultipleDelete, MultipleDeleteApplicants, clearSelectedRows, onSuccessfulAction }: any) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [actionType, setActionType] = useState('')

  const handleFetchData = () => {
    fetchSpamingData()
  }

  const {state, dispatch} = useApplicantContext()

  const handleActionClick = (action: string) => {
    setActionType(action)
    setDialogOpen(true)
  }

  

  const handleConfirmAction = () => {
    setDialogOpen(false)
    
    // Store the current selected IDs for updating UI after successful operation
    const currentSelectedIds = [...state.id]
  
    if (actionType === 'approve') {
      const payload = {
        emailList: state.emailList,
        status: "Active",
        tags: state.tags ? [parseInt(state.tags)] : [],
        experience: state.experience,
        position: state.position,
        qualification: state.qualification,
        subject: state.subject,
        phone_no: state.phone_no.length > 0 ? state.phone_no[0] : ""
      }
      fetchApprovedData(payload)
      
      // Update UI immediately (optimistic update)
      if (onSuccessfulAction) {
        onSuccessfulAction('approve', currentSelectedIds)
      }
  
    } 
    else if (actionType === 'reject') {
      const payload = {
        emailList: state.emailList,
        status: "Rejected",
        tags: state.tags ? [parseInt(state.tags)] : [],
        experience: state.experience,
        position: state.position,
        qualification: state.qualification,
        subject: state.subject,
        phone_no: state.phone_no.length > 0 ? state.phone_no[0] : ""
      }
      fetchRejectedData(payload)
      
      // Update UI immediately (optimistic update)
      if (onSuccessfulAction) {
        onSuccessfulAction('reject', currentSelectedIds)
      }
    } 
    else if (actionType === 'delete') {
      const payload = state.id.map((i: any) => ({ id: i }))
      fetchMultipleDelete(payload)
      
      // Update UI immediately (optimistic update)
      if (onSuccessfulAction) {
        onSuccessfulAction('delete', currentSelectedIds)
      }
    }
    
    // Clear all dropdown values and selected data after any action
    dispatch({ type: 'CLEAR_ALL' })
  }

  // Check if required dropdown fields are selected for approve/reject actions
  // Only position, experience, and highest qualification are required
  const areDropdownsSelected = state.position && state.experience && state.qualification
  
  // Delete button: only enabled when users are selected
  const isDeleteEnabled = isRowSelected
  
  // Approve/Reject buttons: enabled when users are selected AND required dropdowns are selected
  const isApproveRejectEnabled = isRowSelected && areDropdownsSelected
  
  console.log("state", state)
  console.log("state.id", state.id)
  console.log("state.emailList", state.emailList)
  console.log("areDropdownsSelected", areDropdownsSelected)
  console.log("isApproveRejectEnabled", isApproveRejectEnabled)
  
  return (
    <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end' }}>
        <Stack direction='row' spacing={2}>
          <ActionButton variant='contained' color='primary' onClick={handleFetchData}>
            Suspected Spam
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isApproveRejectEnabled}
            onClick={() => handleActionClick('approve')}
          
          >
            Approve
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isApproveRejectEnabled}
            onClick={() => handleActionClick('reject')}
          >
            Reject
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isDeleteEnabled}
            onClick={() => handleActionClick('delete')}
          >
            Delete
          </ActionButton>
        </Stack>
      </Box>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle  sx={{
          bgcolor: 'rgb(25, 60, 109)',
          color: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '1rem',
          marginBottom: '1rem',
          fontFamily: 'Montserrat-Medium, sans-serif',
          fontWeight: 'bold',
          fontSize:"16px"
        }}>Confirm {actionType}</DialogTitle>
        <DialogContent sx={{ padding: '20px', fontFamily: 'Montserrat-Medium, sans-serif', paddingBottom: '0rem' }}>
          
          <Typography sx={{ fontFamily: 'Montserrat-Medium, sans-serif', fontSize:"13px" }}>
          Are you sure you want to {actionType} the selected applicants?
        </Typography>
        </DialogContent>
        <DialogActions>
          <ActionButton onClick={() => setDialogOpen(false)} color='secondary'>
            Cancel
          </ActionButton>
          <ActionButton onClick={handleConfirmAction} color='primary'>
            Confirm
          </ActionButton>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  ApprovedOptons: recruitmentEntity.getRecruitment(state).getApproveApplicants,
  RejectedOptions: recruitmentEntity.getRecruitment(state).getRejectedApplicants,
  MultipleDeleteApplicantsOptions: recruitmentStateUI.getRecruitment(state).getMultipleDeleteApplicants,
  
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchApprovedData: (data: {}) => dispatch(approveApplicants.request({ data })),
  fetchRejectedData: (data: {}) => dispatch(rejectApplicants.request({ data })),
  fetchMultipleDelete: (data:IdApplicants[]) => dispatch(deleteApplicants.request(data)),

})

export default connect(mapStateToProps, mapDispatchToProps)(Buttons)