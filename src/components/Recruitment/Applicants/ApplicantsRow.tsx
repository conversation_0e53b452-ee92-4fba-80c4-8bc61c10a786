import React from 'react'
import { TableRow, Checkbox, IconButton, Tooltip } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'
import DescriptionIcon from '@mui/icons-material/Description'
import { StyledTableCell } from '../../Common/CommonStyles'
import ExpandableTableCell from '../Common/ExpandableTableCell'

interface ApplicantRowProps {
  row: any
  selectedRows: string[]
  handleRowSelect: (id: string) => void
  handleDelete: (id: string) => void
  handleViewResume: (applicantId: string) => void
}

const ApplicantRow = ({
  row,
  selectedRows,
  handleRowSelect,
  handleDelete,
  handleViewResume,
}: ApplicantRowProps) => (
  <TableRow key={row.id}>
    <StyledTableCell>
      <Checkbox checked={selectedRows.includes(row.id)} onChange={() => handleRowSelect(row.id)} />
    </StyledTableCell>
    <StyledTableCell>{row.name}</StyledTableCell>
    <StyledTableCell>{row.email}</StyledTableCell>
    <StyledTableCell>{row.phone_no}</StyledTableCell>
    <StyledTableCell>{row.qualification}</StyledTableCell>
    <ExpandableTableCell text={row.subject} />
    <StyledTableCell>{row.created_at}</StyledTableCell>

    <StyledTableCell>
      <Tooltip title='View Resume'>
        <IconButton color='primary' onClick={() => handleViewResume(row.email)}>
          <DescriptionIcon />
        </IconButton>
      </Tooltip>
      <Tooltip title='Delete'>
        <IconButton sx={{ color: '#db3700' }} onClick={() => handleDelete(row.id)}>
          <DeleteIcon />
        </IconButton>
      </Tooltip>
    </StyledTableCell>
  </TableRow>
)

export default ApplicantRow