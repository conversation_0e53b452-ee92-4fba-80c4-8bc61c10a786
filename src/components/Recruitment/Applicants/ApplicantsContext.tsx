import React, { createContext, useReducer, useContext } from 'react'
import { Dayjs } from 'dayjs'

type Option = string | number

interface State {
  id: string[]
  emailList: string[]
  experience: string
  phone_no:string[]
  position:string
  qualification:string
  status:string
  subject: string
  tags: string
}

type Action =
  | {type: 'FETCH_ID'; payload: string}
  | { type: 'FETCH_UNAPPROVED_EMAIL'; payload: string }
  | { type: 'FETCH_UNAPPROVED_EXPERIENCE'; payload: string }
  | { type: 'FETCH_MOBILE_NUMBER'; payload:string }
  | { type: 'FETCH_UNAPPROVED_POSITION'; payload: string }
  | { type: 'FETCH_UNAPPROVED_QUALIFICATION'; payload: string }
  | { type: 'FETCH_UNAPPROVED_STATUS'; payload: string }
  | { type: 'FETCH_UNAPPROVED_SUBJECTS'; payload: string }
  | { type: 'FETCH_UNAPPROVED_TAGS'; payload: string }
  | { type: 'REMOVE_ID'; payload: string }
  | { type: 'REMOVE_UNAPPROVED_EMAIL'; payload: string }
  | { type: 'REMOVE_MOBILE_NUMBER'; payload: string }
  | { type: 'CLEAR_ALL' }

const initialState: State = {
  id:[],
  emailList: [],
  experience: '',
  phone_no: [],
  position: '',
  qualification:'',
  status:'' ,
  subject:'',
  tags:''
}

function applicantReducer(state: State, action: Action) {
  switch (action.type) {
    case 'FETCH_ID':
      return { ...state, id: [...state.id,action.payload] }
    case 'FETCH_UNAPPROVED_EMAIL':
      return { ...state, emailList: [...state.emailList,action.payload] }
    case 'FETCH_UNAPPROVED_EXPERIENCE':
      return { ...state, experience: action.payload }
    case 'FETCH_MOBILE_NUMBER':
      return { ...state, phone_no: [...state.phone_no,action.payload] }
    case 'FETCH_UNAPPROVED_POSITION':
      return { ...state, position: action.payload }
    case 'FETCH_UNAPPROVED_QUALIFICATION':
      return { ...state, qualification: action.payload }
    case 'FETCH_UNAPPROVED_STATUS':
      return { ...state, status: action.payload }
    case 'FETCH_UNAPPROVED_SUBJECTS':
      return { ...state, subject: action.payload }
    case 'FETCH_UNAPPROVED_TAGS':
      return { ...state, tags: action.payload }
    case 'REMOVE_ID':
      return { ...state, id: state.id.filter(id => id !== action.payload) }
    case 'REMOVE_UNAPPROVED_EMAIL':
      return { ...state, emailList: state.emailList.filter(email => email !== action.payload) }
    case 'REMOVE_MOBILE_NUMBER':
      return { ...state, phone_no: state.phone_no.filter(phone => phone !== action.payload) }
    case 'CLEAR_ALL':
      return initialState
    default:
      return state
  }
}

const ApplicantsContext = createContext<{
  state: State
  dispatch: React.Dispatch<Action>
}>({
  state: initialState,
  dispatch: () => {},
})

export const ApplicantsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(applicantReducer, initialState)
  return (
    <ApplicantsContext.Provider value={{ state, dispatch }}>{children}</ApplicantsContext.Provider>
  )
}

export const useApplicantContext = () => useContext(ApplicantsContext)