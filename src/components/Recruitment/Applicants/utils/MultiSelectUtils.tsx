export const toggleOption = (selectedOptions: string[], option: string): string[] => {
  return selectedOptions.includes(option)
    ? selectedOptions.filter((item) => item !== option)
    : [...selectedOptions, option]
}

export const toggleSelectAll = (selectedOptions: string[], options: string[]): string[] => {
  return selectedOptions.length === options.length ? [] : options
}

export const filterOptions = (options: string[], searchText: string): string[] => {
  return options.filter((option) => option.toLowerCase().includes(searchText.toLowerCase()))
}