import { SelectChangeEvent } from '@mui/material/Select'


export const dropdownLabels = [
  'Position',
  'Experience',
  'Highest Qualification',
  '11th/12th Subject',
  'Tags'
]

export const initialValues = {
  box1: '',
  box2: '',
  box3: '',
  box4: '',
  box5: '',
}

export const handleDropdownChange =
  (setValues: Function) => (name: string) => (event: SelectChangeEvent) => {
    setValues((prev: any) => ({
      ...prev,
      [name]: event.target.value as string,
    }))
  }

interface ApiResponseItem {
  id: number
  round_name: string
}

export function formatForMultiSelect(
  apiResponse: ApiResponseItem[][],
  label: string,
  value: string,
  index: number,
) {
  if (!Array.isArray(apiResponse) || apiResponse.length === 0) return []

  return apiResponse[index].map((item: any) => ({
    label: item[label],
    value: item[value],
  }))
}