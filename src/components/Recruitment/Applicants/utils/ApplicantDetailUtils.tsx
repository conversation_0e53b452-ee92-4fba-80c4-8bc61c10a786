export const deleteApplicant = (rows: any[], name: string) => {
  return rows.filter((row) => row.name !== name)
}

export const toggleSelectAll = (rows: any[], isChecked: boolean) => {
  return rows.map((row) => ({ ...row, selected: isChecked }))
}

export const toggleSelectRow = (rows: any[], name: string) => {
  return rows.map((row) => (row.name === name ? { ...row, selected: !row.selected } : row))
}

export const sortRows = (rows: any[], order: 'asc' | 'desc') => {
  return [...rows].sort((a, b) =>
    order === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name),
  )
}

export const filterRows = (rows: any[], searchQuery: string) => {
  return rows.filter((row) => {
    const name = row?.name || ''
    const email = row?.email || ''
    const mobile = row?.phone_no || ''
    const qualification = row?.qualification || ''

    return (
      (name && name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (email && email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (mobile && mobile.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (qualification && qualification.toLowerCase().includes(searchQuery.toLowerCase()))
    )
  })
}