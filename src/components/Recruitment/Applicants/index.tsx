import { useState } from 'react'
import ApplicantDetails from './ApplicantDetails'
import Box from '@mui/material/Box'
import ApplicantHeader from './ApplicantHeader'
import { ApplicantsProvider } from './ApplicantsContext'

export default function Applicants() {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <ApplicantsProvider>
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 1 }}>
      <ApplicantHeader searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      <ApplicantDetails searchQuery={searchQuery} />
    </Box>
    </ApplicantsProvider>
  )
}