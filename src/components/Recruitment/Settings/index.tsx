import React, { useState } from "react";
import { Grid } from "@mui/material";
import { menuItems } from "./menuData";
import MenuCard from "./MenuCard";
import SubfieldPopover from "./SubfieldPopover";

const Settings: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [popoverContent, setPopoverContent] = useState<any[]>([]);

  const handleCardClick = (event: React.MouseEvent<HTMLDivElement>, subfields: any[]) => {
    setPopoverContent(subfields);
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => setAnchorEl(null);

  return (
    <div
      style={{
        width: "94vw", 
        minHeight: "50vh", 
        padding: "20px",
        backgroundColor: "#f4f4f4",
        boxSizing: "border-box",
      }}
    >
      <Grid container spacing={2} justifyContent="center">
        {menuItems.map((item, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <MenuCard icon={item.icon} label={item.label} onClick={(e) => handleCardClick(e, item.subfields)} />
          </Grid>
        ))}
      </Grid>

      <SubfieldPopover
        anchorEl={anchorEl}
        onClose={handleClosePopover}
        subfields={popoverContent}
        onSubfieldClick={(label) => console.log(`Clicked: ${label}`)}
      />
    </div>
  );
};

export default Settings;
