import React from 'react'
import { Grid, Button } from '@mui/material'
import { ActionButtonsProps } from './UploadCandidateType'

const ActionButtons: React.FC<ActionButtonsProps> = ({ onCancel, onSubmit, disabled }) => (
  <Grid
    container
    justifyContent='flex-end'
    spacing={1}
    sx={{ fontFamily: 'Montserrat-Semibold', marginTop: 2 }}
  >
    <Grid item>
      <Button
        variant='contained'
        onClick={onCancel}
        sx={{
          backgroundColor: '#E2E2E2',
          color: '#000000',
          height: '40px',
          fontSize: '15px',
          borderRadius: '50px',
          fontWeight: 'bold',
          minWidth: '90px',
          fontFamily: 'Montserrat-Semibold',
          '&:hover': { backgroundColor: '#E2E2E2', color: '#000000' },
        }}
      >
        Cancel
      </Button>
    </Grid>
    <Grid item>
      <Button
        variant='contained'
        onClick={onSubmit}
        disabled={disabled}
        sx={{
          backgroundColor: disabled ? '#A0A0A0' : '#193C6D',
          color: '#FFF',
          fontSize: '15px',
          height: '40px',
          borderRadius: '50px',
          fontWeight: 'bold',
          fontFamily: 'Montserrat-Semibold',
          '&:hover': {
            backgroundColor: disabled ? '#A0A0A0' : '#102F58',
          },
        }}
      >
        Submit
      </Button>
    </Grid>
  </Grid>
)

export default ActionButtons
