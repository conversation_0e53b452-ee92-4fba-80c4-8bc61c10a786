import React, { useState } from 'react'
import { Autocomplete, TextField, Chip, Box } from '@mui/material'
import { TagsInputFieldProps } from './UploadCandidateType'

const TagsInputField: React.FC<TagsInputFieldProps> = ({ tags, setTags, options }) => {
  const [inputValue, setInputValue] = useState('')
  const [open, setOpen] = useState(false)

  const handleTagsChange = (_event: React.SyntheticEvent<Element, Event>, newTags: string[]) => {
    setTags(newTags)
  }

  const handleInputChange = (
    _event: React.SyntheticEvent<Element, Event>,
    newInputValue: string,
  ) => {
    setInputValue(newInputValue)
    setOpen(newInputValue.trim() !== '')
  }

  const tagNames = options ? options.map((option) => option.name) : []

  const filterOptions = (options: string[], { inputValue }: { inputValue: string }) => {
    return options.filter((option) => option.toLowerCase().includes(inputValue.toLowerCase()))
  }

  return (
    <Autocomplete
      multiple
      freeSolo
      options={tagNames}
      value={tags}
      onChange={handleTagsChange}
      onInputChange={handleInputChange}
      filterOptions={filterOptions}
      getOptionLabel={(option) => option || ''}
      open={open}
      onClose={() => setOpen(false)}
      renderTags={(value: string[], getTagProps) => (
        <Box
          sx={{
            maxHeight: '50px',
            overflowY: 'auto',
            display: 'flex',
            flexWrap: 'wrap',
          }}
        >
          {value.map((option: string, index: number) => (
            <Chip
              label={option}
              variant='outlined'
              {...getTagProps({ index })}
              sx={{
                fontFamily: 'Montserrat-Medium',
              }}
            />
          ))}
        </Box>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder='Enter a new tag'
          variant='standard'
          InputLabelProps={{ shrink: true }}
          sx={{
            fontFamily: 'Montserrat-Medium',
            '& .MuiInputBase-root': {
              fontFamily: 'Montserrat-Medium',
            },
            '& .MuiInputLabel-root': {
              fontFamily: 'Montserrat-Medium',
            },
            '& .MuiInput-underline:before': {
              borderBottomColor: '#193C6D',
            },
            '& .MuiInput-underline:hover:not(.Mui-disabled):before': {
              borderBottomColor: '#193C6D',
              borderBottomWidth: '3px',
            },
            '& .MuiInput-underline:after': {
              borderBottomColor: '#193C6D',
            },
          }}
        />
      )}
      sx={{
        fontFamily: 'Montserrat-Medium',
        fontSize: '13px',
        '& .MuiAutocomplete-inputRoot': {
          fontFamily: 'Montserrat-Medium',
        },
        '& .MuiAutocomplete-popupIndicator': {
          color: '#193C6D',
        },
        '& .MuiAutocomplete-option': {
          fontFamily: 'Montserrat-Medium',
          fontSize: '13px',
          backgroundColor: '#f5f5f5',
          borderRadius: '4px',
          margin: '4px',
          '&:hover': {
            backgroundColor: '#e0e0e0',
          },
        },
      }}
    />
  )
}

export default TagsInputField
