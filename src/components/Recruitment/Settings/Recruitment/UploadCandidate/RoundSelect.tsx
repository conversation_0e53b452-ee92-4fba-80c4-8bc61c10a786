import React, { useState } from 'react'
import { MenuItem, TextField } from '@mui/material'
import { RoundSelectProps } from './UploadCandidateType'

const RoundSelect: React.FC<RoundSelectProps> = ({ round, setRound, options }) => {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <TextField
      fullWidth
      select
      label='Round'
      value={round}
      onChange={(e) => setRound(e.target.value)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(!!round)}
      variant='outlined'
      size='small'
      InputLabelProps={{
        shrink: isFocused || !!round,
      }}
      sx={{
        fontFamily: 'Montserrat-Medium',
        '& .MuiOutlinedInput-root': {
          height: '45px',
          borderRadius: '30px',
          transition: 'border-color 0.3s ease',
          fontFamily: 'Montserrat-Medium',
          '& fieldset': {
            borderColor: '#ccc',
          },
          '&:hover fieldset': {
            borderColor: '#000000DE !important',
          },
          '&.Mui-focused fieldset': {
            borderColor: '#193C6D',
            borderWidth: '2px',
          },
        },
        '& .MuiOutlinedInput-input': {
          fontSize: '13px',
        },
        '& .MuiInputLabel-root': {
          color: '#808080',
          fontFamily: 'Montserrat-Medium',
        },
        '& .MuiInputLabel-root.Mui-focused': {
          color: '#193C6D',
        },
        '& .MuiSelect-select': {
          fontFamily: 'Montserrat-Medium',
        },
        '& .MuiMenuItem-root': {
          fontFamily: 'Montserrat-Medium',
          fontSize: '13px',
        },
      }}
    >
      {options.length > 0 ? (
        options.map((option) => (
          <MenuItem key={option.id} value={option.id.toString()}>
            {option.round_name}
          </MenuItem>
        ))
      ) : (
        <MenuItem disabled sx={{ fontFamily: 'Montserrat-Medium' }}>
          No Rounds Available
        </MenuItem>
      )}
    </TextField>
  )
}

export default RoundSelect
