export interface TagsInputFieldProps {
  tags: string[]
  setTags: (tags: string[]) => void
  options: { id: number; name: string }[]
}

export interface RoundSelectProps {
  round: string
  setRound: React.Dispatch<React.SetStateAction<string>>
  options: { id: number; round_name: string }[]
}

export interface PositionSelectProps {
  position: string
  setPosition: React.Dispatch<React.SetStateAction<string>>
  options: { id: number; name: string }[]
}

export interface FileUploadProps {
  file: File | null
  setFile: (file: File | null) => void
}

export interface ExperienceSelectProps {
  experience: string
  setExperience: React.Dispatch<React.SetStateAction<string>>
  options: { id: number; experience: string }[]
}
export interface ActionButtonsProps {
  onCancel: () => void
  onSubmit: () => void
  disabled: boolean
}

export interface Candidate {
  name: string
  email: string
  phone: string
  branch: string
}

export interface AddMultipleCandidatesPayload {
  experience: string
  position: string
  round: string
  tags: string[]
  candidateData: string[][]
}
export interface positionstagsOpt {
  id: number
  name: string
}
export interface experiencesOpt {
  id: number
  experience: string
}
export interface roundOpt {
  id: number
  round_name: string
}
export interface UploadCandidateProps {
  fetchtagsData: () => void
  fetchpositionExperiencesData: () => void
  fetchroundsData: () => void
  isCandidateRoundData: boolean
  isCandidateTagData: boolean
  isExperiencePositionData: boolean
  multipleCandidateOptions: any
  positionOptions: positionstagsOpt[]
  roundOptions: roundOpt[]
  experiencesOptions: experiencesOpt[]
  tagOptions: positionstagsOpt[]
  addMultipleCandidates: (payload: AddMultipleCandidatesPayload) => void
}
