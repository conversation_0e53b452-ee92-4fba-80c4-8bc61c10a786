import React, { useCallback, useState } from 'react'
import { Button, Typography, Box, Tooltip } from '@mui/material'
import { CloudUpload, Download } from '@mui/icons-material'
import CloseIcon from '@mui/icons-material/Close'
import { styled } from '@mui/material/styles'
import { toast } from 'react-toastify'
import { FileUploadProps } from './UploadCandidateType'

const ALLOWED_TYPES: string[] = ['text/csv']
const MAX_FILE_SIZE_MB = 5

const VisuallyHiddenInput = styled('input')({
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
})

const FileUpload: React.FC<FileUploadProps> = ({ file, setFile }) => {
  const [isFileSelected, setIsFileSelected] = useState<boolean>(false)

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFile = event.target.files?.[0]

      if (!selectedFile) return

      if (!ALLOWED_TYPES.includes(selectedFile.type)) {
        toast.error('Invalid file type! Please upload a CSV file.', { position: 'top-right' })
        setIsFileSelected(false)
        return
      }

      if (selectedFile.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
        toast.error(`File size exceeds ${MAX_FILE_SIZE_MB}MB. Please upload a smaller file.`, {
          position: 'top-right',
        })
        setIsFileSelected(false)
        return
      }

      setFile(selectedFile)
      setIsFileSelected(true)
    },
    [setFile],
  )

  const handleRemoveFile = useCallback(() => {
    setFile(null)
    setIsFileSelected(false)
  }, [setFile])

  const handleDownloadTemplate = useCallback(() => {
    const csvContent = 'Name,Email,Phone,Branch\n'
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    link.setAttribute('href', url)
    link.setAttribute('download', 'candidate_template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success('Template downloaded successfully!', { position: 'top-right' })
  }, [])

  const truncateFileName = (name: string, maxLength: number = 15): string => {
    if (name.length > maxLength) {
      const extension = name.split('.').pop()
      const nameWithoutExt = name.substring(0, name.lastIndexOf('.')) || name

      return nameWithoutExt.substring(0, maxLength) + '....' + extension
    }
    return name
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        margin: '8px 0px 0px 0px',
        flexWrap: 'wrap',
        fontFamily: 'Montserrat-Medium',
        gap: '12px',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <Button
          component='label'
          variant='contained'
          sx={{
            backgroundColor: '#193C6D',
            color: '#FFFFFF',
            fontSize: '15px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '10px 25px',
            borderRadius: '50px',
            fontWeight: 'bold',
            height: '40px',
            fontFamily: 'Montserrat-Semibold',
          }}
          startIcon={<CloudUpload />}
        >
          Upload Data
          <VisuallyHiddenInput type='file' accept='.csv' onChange={handleFileUpload} />
        </Button>

        {isFileSelected && file && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: '#FFFFFF',
              padding: '0px 10px',
              fontFamily: 'Montserrat-Medium',
            }}
          >
            <Tooltip title={file.name} arrow>
              <Typography
                variant='body2'
                noWrap
                sx={{
                  color: '#193C6D',
                  maxWidth: '120px',
                  overflow: 'hidden',
                  fontSize: '13px',
                  textOverflow: 'ellipsis',
                  fontFamily: 'Montserrat-Medium',
                }}
              >
                {truncateFileName(file.name)}
              </Typography>
            </Tooltip>
            <Button
              sx={{
                minWidth: '20px',
                color: '#E2E2E2',
                backgroundColor: '#FFF',
                fontFamily: 'Montserrat-Medium',
                '&:hover': { color: 'darkred', backgroundColor: '#FFF' },
              }}
              onClick={handleRemoveFile}
            >
              <CloseIcon sx={{ color: '#848a79', backgroundColor: '#FFF' }} />
            </Button>
          </Box>
        )}
      </Box>

      <Button
        variant='contained'
        onClick={handleDownloadTemplate}
        sx={{
          backgroundColor: '#193C6D',
          color: '#FFFFFF',
          fontSize: '15px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '10px 25px',
          borderRadius: '50px',
          fontWeight: 'bold',
          height: '40px',
          fontFamily: 'Montserrat-Semibold',
          '&:hover': {
            backgroundColor: '#1a3a68',
          },
        }}
        startIcon={<Download />}
      >
        Sample CSV
      </Button>
    </Box>
  )
}

export default FileUpload
