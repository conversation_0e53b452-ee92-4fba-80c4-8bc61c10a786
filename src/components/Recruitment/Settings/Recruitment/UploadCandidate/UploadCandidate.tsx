import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { DialogTitle, DialogContent, Box, Grid, Container } from '@mui/material'
import PositionSelect from './PositionSelect'
import ExperienceSelect from './ExperienceSelect'
import RoundSelect from './RoundSelect'
import TagsInputField from './TagsInputField'
import FileUpload from './FileUpload'
import ActionButtons from './ActionButtons'
import {
  addMultipleCandidates,
  fetchCandidateRounds,
  fetchCandidateTags,
  fetchPositions,
} from '../../../../../actions'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import Loader from 'components/Common/Loader'
import { AddMultipleCandidatesPayload, UploadCandidateProps } from './UploadCandidateType'

const UploadCandidate: React.FC<UploadCandidateProps> = ({
  fetchtagsData,
  fetchpositionExperiencesData,
  fetchroundsData,
  isCandidateRoundData,
  isCandidateTagData,
  isExperiencePositionData,
  multipleCandidateOptions,
  positionOptions,
  roundOptions,
  experiencesOptions,
  tagOptions,
  addMultipleCandidates,
}) => {
  const [position, setPosition] = useState<string>('')
  const [experience, setExperience] = useState<string>('')
  const [round, setRound] = useState<string>('')
  const [file, setFile] = useState<File | null>(null)
  const [tags, setTags] = useState<string[]>([])
  const navigate = useNavigate()

  const isSubmitDisabled = !position || !experience || !round || !file

  const handleSubmit = () => {
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      if (!event.target?.result) return

      const csvData = event.target.result as string
      const rows = csvData.trim().split('\n')

      if (rows.length < 2) return

      const headers = rows[0].split(',').map((header) => header.trim())
      const candidateData: string[][] = []

      rows.slice(1).forEach((row) => {
        const values = row.split(',').map((value) => value.trim())
        const rowData = headers.reduce((acc, key, i) => {
          acc[key] = values[i] || ''
          return acc
        }, {} as Record<string, string>)
        candidateData.push([rowData.Name, rowData.Email, rowData.Phone, rowData.Branch])
      })

      if (candidateData.length === 0) return

      const payload: AddMultipleCandidatesPayload = {
        experience,
        position,
        round,
        tags,
        candidateData,
      }

      addMultipleCandidates(payload)
      if (multipleCandidateOptions) {
        navigate('/home/<USER>')
      }
    }

    reader.readAsText(file)
  }

  useEffect(() => {
    fetchtagsData()
    fetchpositionExperiencesData()
    fetchroundsData()
  }, [])

  const isLoading = isCandidateRoundData || isCandidateTagData || isExperiencePositionData

  return (
    <>
      {!isLoading && <Loader state={true} />}
      <Container maxWidth='md' sx={{ display: 'flex', padding: '20px', height: '80%' }}>
        <Box
          sx={{
            borderRadius: '4px',
            backgroundColor: '#FFFFFF',
            padding: '20px 20px 5px 20px',
            boxShadow: 2,
            width: '100%',
          }}
        >
          <DialogTitle
            sx={{
              textAlign: 'center',
              color: '#193C6D',
              fontSize: '28px',
              fontFamily: 'Montserrat-Semibold',
            }}
          >
            Upload Candidate
          </DialogTitle>
          <DialogContent>
            <Box>
              <PositionSelect
                position={position}
                setPosition={setPosition}
                options={positionOptions || []}
              />
              <ExperienceSelect
                experience={experience}
                setExperience={setExperience}
                options={experiencesOptions || []}
              />
              <RoundSelect round={round} setRound={setRound} options={roundOptions || []} />
              <TagsInputField tags={tags} setTags={setTags} options={tagOptions || []} />
              <FileUpload file={file} setFile={setFile} />
              <ActionButtons
                onCancel={() => navigate(-1)}
                onSubmit={handleSubmit}
                disabled={isSubmitDisabled}
              />
            </Box>
          </DialogContent>
        </Box>
      </Container>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  const positionExperienceData = recruitmentEntity.getRecruitment(state).getPositionData || []
  return {
    tagOptions: recruitmentEntity.getRecruitment(state).CandidateTagsData || [],
    roundOptions: recruitmentEntity.getRecruitment(state).CandidateRoundsData?.flat() || [],
    positionOptions: positionExperienceData[1] || [],
    experiencesOptions: positionExperienceData[0] || [],
    multipleCandidateOptions: recruitmentEntity.getRecruitment(state).addMultipleCandidates,
    isCandidateRoundData: recruitmentStateUI.getRecruitment(state).isCandidateRoundsData,
    isCandidateTagData: recruitmentStateUI.getRecruitment(state).isCandidateTagsData,
    isExperiencePositionData: recruitmentStateUI.getRecruitment(state).isPositionData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchtagsData: () => dispatch(fetchCandidateTags.request()),
  fetchroundsData: () => dispatch(fetchCandidateRounds.request()),
  fetchpositionExperiencesData: () => dispatch(fetchPositions.request()),
  addMultipleCandidates: (payload: AddMultipleCandidatesPayload) =>
    dispatch(addMultipleCandidates.request(payload)),
})

export default connect(mapStateToProps, mapDispatchToProps)(UploadCandidate)
