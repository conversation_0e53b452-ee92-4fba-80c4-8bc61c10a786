import React, { useState } from 'react'
import { MenuItem, TextField } from '@mui/material'
import { ExperienceSelectProps } from './UploadCandidateType'

const ExperienceSelect: React.FC<ExperienceSelectProps> = ({
  experience,
  setExperience,
  options,
}) => {
  const [isFocused, setIsFocused] = useState(false)

  const sortedOptions = [...options].sort(
    (a, b) => parseInt(a.experience, 10) - parseInt(b.experience, 10),
  )

  return (
    <TextField
      fullWidth
      select
      label='Experience'
      value={experience}
      onChange={(e) => setExperience(e.target.value)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(!!experience)}
      variant='outlined'
      size='small'
      InputLabelProps={{
        shrink: isFocused || !!experience,
      }}
      sx={{
        fontFamily: 'Montserrat-Medium',
        '& .MuiOutlinedInput-root': {
          fontFamily: 'Montserrat-Medium',
          height: '45px',
          borderRadius: '30px',
          transition: 'border-color 0.3s ease',
          '& fieldset': {
            borderColor: '#ccc',
          },
          '&:hover fieldset': {
            borderColor: '#000000DE !important',
          },
          '&.Mui-focused fieldset': {
            borderColor: '#193C6D',
            borderWidth: '2px',
          },
        },
        '& .MuiOutlinedInput-input': {
          fontSize: '13px',
        },
        '& .MuiInputLabel-root': {
          fontFamily: 'Montserrat-Medium',
          color: '#808080',
        },
        '& .MuiInputLabel-root.Mui-focused': {
          color: '#193C6D',
        },
        '& .MuiMenuItem-root': {
          fontFamily: 'Montserrat-Medium',
          fontSize: '13px',
        },
      }}
    >
      {sortedOptions.length > 0 ? (
        sortedOptions.map((option) => (
          <MenuItem key={option.id} value={option.id.toString()}>
            {option.experience}
          </MenuItem>
        ))
      ) : (
        <MenuItem disabled sx={{ fontFamily: 'Montserrat-Medium' }}>
          No Experiences Available
        </MenuItem>
      )}
    </TextField>
  )
}

export default ExperienceSelect
