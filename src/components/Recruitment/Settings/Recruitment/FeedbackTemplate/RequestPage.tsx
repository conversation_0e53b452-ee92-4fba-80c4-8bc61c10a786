import React, { FC, useEffect, useState } from 'react'
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import SwapVertIcon from '@mui/icons-material/SwapVert'

import CustomPagination from './CustomPagination'
import TableRowComponent from './TableRowComponent'
import AddFeedbackModal from './AddFeedbackModal'
import TopHeading from './TopHeading'
import ButtonComponent from './ButtonComponent'
import SearchBox from './SearchBox'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
import {
  DeleteFeedback,
  EditFeedback,
  fetchFeedback,
  fetchPositions,
  roundfeedbackDetails,
  AddFeedback,
  SubEditFeedback,
} from '../../../../../actions'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

interface SortConfig {
  key: string
  direction: 'asc' | 'desc'
}

interface FeedbackData {
  id: number
  feedback_type: string
  feedback_data: string
  round_name: string
  position_name: string
  round: string
  position: string
}

interface Props {
  fetchFeedbackData: () => void
  feedbackOptions: FeedbackData[]
  deleteFeedback: (data: { data: { feedback_id: number } }) => void
  roundfeedbackDetails: (data: { round_type: number }) => void
  fetchPosition: () => void
  roundfeedbackDetailsOptions: any[]
  PositionOptions: any[]
  addfeedbackDetails: (data: any) => void
  subeditFeedback: (data: any) => void
}

const RequestPage: FC<Props> = ({
  fetchFeedbackData,
  feedbackOptions,
  deleteFeedback,
  roundfeedbackDetails,
  fetchPosition,
  roundfeedbackDetailsOptions,
  PositionOptions,
  addfeedbackDetails,
  subeditFeedback,
}) => {
  const [tableData, setTableData] = useState<FeedbackData[]>([])
  const [filteredData, setFilteredData] = useState<FeedbackData[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const entriesPerPage = 10
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [feedbackToDelete, setFeedbackToDelete] = useState<number | null>(null)
  const [openModal, setOpenModal] = useState(false)
  const [currentEditData, setCurrentEditData] = useState<FeedbackData | null>(null)

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: '',
    direction: 'asc',
  })

  const handleOpenModal = () => {
    // Only set currentEditData to null when explicitly adding new feedback
    // This allows handleEdit to set currentEditData for editing
    if (!currentEditData) {
      setCurrentEditData(null);
    }
    setOpenModal(true);
  }

  const handleCloseModal = () => {
   
    setCurrentEditData(null);
    setOpenModal(false);
    
    
    setTimeout(() => {
      fetchFeedbackData();
    }, 300);
  }

  const tableColumns = ['Feedback Type', 'Feedback Data', 'Round', 'Position']

  const safeSearchString = (str: string | null | undefined): string => {
    return str ? str.toLowerCase() : ''
  }

  useEffect(() => {
    fetchFeedbackData()
  }, [])

  useEffect(() => {
    if (feedbackOptions) {
      const formattedData = feedbackOptions.map((item: any) => ({
        id: item.id || 0,
        feedback_type: item.feedback_type || '',
        feedback_data: item.feedback_data || '',
        round_name: item.round_name || '',
        position_name: item.position_name || '',
        round: item.round || '',
        position: item.position || '',
      }))
      setTableData(formattedData)
      setFilteredData(formattedData)
    }
  }, [feedbackOptions])

  const sortedData = [...filteredData].sort((a, b) => {
    const { key, direction } = sortConfig
    if (!key) return 0

    const aValue = a[key as keyof FeedbackData] || ''
    const bValue = b[key as keyof FeedbackData] || ''

    if (direction === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  const startIndex = (currentPage - 1) * entriesPerPage
  const endIndex = startIndex + entriesPerPage
  const currentData = sortedData.slice(startIndex, endIndex)

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setCurrentPage(value)
  }

  const handleSort = (column: string) => {
    const direction = sortConfig.key === column && sortConfig.direction === 'asc' ? 'desc' : 'asc'
    setSortConfig({ key: column, direction })
  }

  const handleEdit = (rowData: FeedbackData) => {
    console.log("Editing feedback:", rowData);
    setCurrentEditData(rowData);
    setOpenModal(true);
  }

  const handleDeleteClick = (feedbackId: number) => {
    setFeedbackToDelete(feedbackId)
    setDeleteDialogOpen(true)
  }

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false)
    setFeedbackToDelete(null)
  }

  const handleDeleteConfirm = () => {
    if (feedbackToDelete) {
      deleteFeedback({ data: { feedback_id: feedbackToDelete } })
      toast.success('Feedback deleted successfully!', {
        position:'top-right',
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })
      setDeleteDialogOpen(false)
      fetchFeedbackData()
    }
  }

  const handleSearch = (value: string) => {
    if (!value) {
      setFilteredData(tableData)
      setCurrentPage(1)
      return
    }

    const searchTerm = value.toLowerCase()
    const filtered = tableData.filter((item) => {
      return (
        safeSearchString(item.feedback_data).includes(searchTerm) ||
        safeSearchString(item.feedback_type).includes(searchTerm) ||
        safeSearchString(item.round_name).includes(searchTerm) ||
        safeSearchString(item.position_name).includes(searchTerm)
      )
    })
    setFilteredData(filtered)
    setCurrentPage(1)
  }

  const stripHtml = (html: string) => {
    if (!html) return ''
    return html.replace(/<[^>]*>?/gm, '')
  }

  return (
    <Box
      sx={{
        position: 'absolute',
        top: '75px',
        left: '20px',
        bottom: '75px',
        right: '0',
        marginTop: '20px',
        marginLeft: '0px',
        marginRight: '20px',
        backgroundColor: 'rgb(255, 255, 255)',
        padding: 3,
        overflow: 'auto',
        boxShadow: 10,
      }}
    >
      <TopHeading />

      <AddFeedbackModal
        open={openModal}
        onClose={handleCloseModal}
        currentEditData={currentEditData}
      />

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          my: 2,
        }}
      >
        <SearchBox placeholder='Search by data' onSearch={handleSearch} />

        <ButtonComponent label='Add Feedback' onClick={handleOpenModal} variant='contained' />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
              {tableColumns.map((column, index) => (
                <TableCell
                  key={index}
                  sx={{
                    color: 'white',
                    fontWeight: 'bold',
                    padding: '12px',
                    textAlign: 'center',
                    width: index === 0 ? '150px' : '250px',
                  }}
                  onClick={() => handleSort(column)}
                >
                  {column}
                  {(column === 'Round' || column === 'Position') && sortConfig.key === column && (
                    <SwapVertIcon sx={{ fontSize: 16, marginLeft: 1 }} />
                  )}
                </TableCell>
              ))}

              <TableCell
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  padding: '10px',
                  textAlign: 'center',
                  width: '100px',
                }}
                scope='col'
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {currentData.length > 0 ? (
              currentData.map((row, index) => (
                <TableRowComponent
                  key={index}
                  data={[
                    row.feedback_type,
                    stripHtml(row.feedback_data),
                    row.round_name,
                    row.position_name,
                  ]}
                  feedbackId={row.id}
                  onEdit={() => handleEdit(row)}
                  onDelete={handleDeleteClick}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={tableColumns.length + 1} align='center'>
                  <Typography variant='body1' sx={{ py: 2 }}>
                    No matching records found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredData.length > 0 && (
        <CustomPagination
          count={Math.ceil(filteredData.length / entriesPerPage)}
          page={currentPage}
          onChange={handlePageChange}
        />
      )}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleDeleteDialogClose}
        handleConfirm={handleDeleteConfirm}
        message='Are you sure you want to delete this entry?'
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    feedbackOptions: recruitmentEntity.getRecruitment(state).getFeedbackData,
    deleteFeedback: recruitmentEntity.getRecruitment(state).DeleteFeedbackDetails || {},
    editFeedback: recruitmentEntity.getRecruitment(state).EditFeedbackDetails || {},
    roundfeedbackDetailsOptions: recruitmentEntity.getRecruitment(state).roundfeedbackDetails || {},
    PositionOptions: recruitmentEntity.getRecruitment(state).getPositionData || {},
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchFeedbackData: () => dispatch(fetchFeedback.request()),
    deleteFeedback: (data: { data: {} }) => dispatch(DeleteFeedback.request(data)),
    editFeedback: (data: { data: {} }) => dispatch(EditFeedback.request(data)),
    roundfeedbackDetails: (data: { round_type: number }) =>
      dispatch(roundfeedbackDetails.request({ data })),
    fetchPosition: () => dispatch(fetchPositions.request()),
    addfeedbackDetails: (data: {
      feedback_type: string
      round: string
      position: string
      feedback_data: string
    }) => dispatch(AddFeedback.request({ data })),
    subeditFeedback: (data: { data: {} }) => dispatch(SubEditFeedback.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(RequestPage)
