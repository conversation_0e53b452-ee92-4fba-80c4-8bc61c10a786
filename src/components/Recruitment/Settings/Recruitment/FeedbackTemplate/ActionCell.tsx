import React from 'react'
import { Box, IconButton, Tooltip } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'

type Props = {
  onEdit: () => void
  onDelete: () => void
}

const ActionCell: React.FC<Props> = ({ onEdit, onDelete }) => {
  return (
    <Box
      display='flex'
      alignItems='center'
      justifyContent='center'
      overflow='hidden'
      width='100px'
      height='40px'
      sx={{
        transition: 'background-color 0.3s ease',
      }}
    >
      <Tooltip title='Edit' arrow>
        <IconButton
          size='small'
          onClick={onEdit}
          sx={{
            flex: 1,
            color: 'rgb(25,60,109)',
            transition: 'background-color 0.3s ease',
            '&:hover': { backgroundColor: 'rgba(25,60,109,0.1)' },
          }}
        >
          <EditIcon fontSize='small' />
        </IconButton>
      </Tooltip>

      <Tooltip title='Delete' arrow>
        <IconButton
          size='small'
          onClick={onDelete}
          sx={{
            flex: 1,
            color: 'red',
            transition: 'background-color 0.3s ease',
            '&:hover': { backgroundColor: 'rgba(255,0,0,0.1)' },
          }}
        >
          <DeleteIcon fontSize='small' />
        </IconButton>
      </Tooltip>
    </Box>
  )
}

export default ActionCell
