import React from 'react'
import { Button } from '@mui/material'

interface ButtonComponentProps {
  label: string

  onClick?: () => void
  variant?: 'text' | 'outlined' | 'contained'
}

const ButtonComponent: React.FC<ButtonComponentProps> = ({
  label,
  onClick,
  variant = 'outlined',
}) => {
  return (
    <Button
      variant={variant}
      onClick={onClick}
      sx={{
        color: 'white',
        backgroundColor: 'rgb(25, 60, 109)',
        borderColor: 'rgb(25, 60, 109)',
        borderRadius: '20px',
        fontSize: '16px',
        marginTop: '2px',
        marginBottom: '2px',
        '&:hover': { backgroundColor: 'rgb(20, 50, 90)' },
        textTransform: 'none',
        marginLeft: 'auto',
      }}
    >
      {label}
    </Button>
  )
}

export default ButtonComponent
