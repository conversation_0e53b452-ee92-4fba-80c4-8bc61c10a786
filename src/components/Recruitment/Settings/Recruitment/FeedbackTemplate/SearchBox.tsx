import React, { useState } from 'react'
import { Box, IconButton, InputAdornment, OutlinedInput } from '@mui/material'
import SearchIcon from '@mui/icons-material/Search'
import ClearIcon from '@mui/icons-material/Clear'

interface SearchBoxProps {
  placeholder?: string
  onSearch: (value: string) => void
}

const SearchBox: React.FC<SearchBoxProps> = ({ placeholder = 'Search', onSearch }) => {
  const [value, setValue] = useState('')

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value)
    onSearch(event.target.value)
  }

  const handleClear = () => {
    setValue('')
    onSearch('')
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
      }}
    >
     
     <OutlinedInput
  id="outlined-basic"
  placeholder={placeholder}
  size="small"
  value={value}
  onChange={handleChange}
  fullWidth
  sx={{
    width: '240px',
    borderRadius: '30px',
    '& .MuiOutlinedInput-root': {
      borderRadius: '20px',
      '& fieldset': { borderColor: '#777777' },
      '&:hover fieldset': { borderColor: 'rgb(25, 60, 109)' },
      '&.Mui-focused fieldset': { borderColor: 'rgb(25, 60, 109)' },
    },
  }}
  startAdornment={
    <InputAdornment position="start">
      <SearchIcon sx={{ color: 'gray' }} />
    </InputAdornment>
  }
  endAdornment={
    value && (
      <InputAdornment position="end">
        <IconButton onClick={handleClear}>
          <ClearIcon />
        </IconButton>
      </InputAdornment>
    )
  }
/>

    </Box>
  )
}

export default SearchBox
