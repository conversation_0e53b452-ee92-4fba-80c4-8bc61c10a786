import React, { useState } from 'react'
import { TableCell, TableRow } from '@mui/material'
import ActionCell from './ActionCell'

interface TableRowComponentProps {
  data: any
  onEdit: () => void
  onDelete: (feedbackId: number) => void
  feedbackId?: number
}

const TableRowComponent: React.FC<TableRowComponentProps> = ({
  data,
  onEdit,
  onDelete,
  feedbackId,
}) => {
  const [expanded, setExpanded] = useState(false)

  const handleDeleteClick = () => {
    if (feedbackId !== undefined) {
      onDelete(feedbackId)
    }
  }

  return (
    <TableRow>
      <TableCell sx={{ textAlign: 'left', verticalAlign: 'baseline' }}>{data[0]}</TableCell>

      <TableCell
        sx={{
          textAlign: 'left',
          maxWidth: '300px',
          whiteSpace: expanded ? 'normal' : 'nowrap',
          overflow: expanded ? 'visible' : 'hidden',
          textOverflow: expanded ? 'clip' : 'ellipsis',
          wordBreak: 'break-word',
          cursor: 'pointer',
        }}
        onClick={() => setExpanded(!expanded)}
      >
        {expanded ? data[1] : data[1].length > 100 ? `${data[1].substring(0, 100)}...` : data[1]}
      </TableCell>

      <TableCell sx={{ textAlign: 'center', verticalAlign: 'baseline' }}>{data[2]}</TableCell>
      <TableCell sx={{ textAlign: 'center', verticalAlign: 'baseline' }}>{data[3]}</TableCell>

      <TableCell sx={{ textAlign: 'center', verticalAlign: 'baseline' }}>
        <ActionCell onEdit={onEdit} onDelete={handleDeleteClick} />
      </TableCell>
    </TableRow>
  )
}

export default TableRowComponent
