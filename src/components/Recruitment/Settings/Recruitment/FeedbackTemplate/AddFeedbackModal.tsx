import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Box,
  Button,
  IconButton,
  Grid,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  FormControlLabel,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { AddFeedback, fetchPositions, roundfeedbackDetails, SubEditFeedback } from 'actions'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

interface AddFeedbackModalProps {
  open: boolean
  onClose: () => void
  currentEditData: any
  roundfeedbackDetailsOptions: {
    round_name: string
    id: string
    label: string
  }[][]
  roundfeedbackDetails: (data: { round_type: number }) => void
  fetchPosition: () => void
  PositionOptions: any[][]
  addfeedbackDetails: (data: {
    feedback_type: string
    round: string
    position: string
    feedback_data: string
    is_default: false
  }) => void
  subeditFeedback: (data: { data: any }) => void
}

const AddFeedbackModal: React.FC<AddFeedbackModalProps> = ({
  open,
  onClose,
  currentEditData,
  roundfeedbackDetailsOptions,
  roundfeedbackDetails,
  fetchPosition,
  PositionOptions,
  addfeedbackDetails,
  subeditFeedback,
}) => {
  const [feedbackType, setFeedbackType] = useState('')
  const [round, setRound] = useState('')
  const [position, setPosition] = useState('')
  const [feedbackData, setFeedbackData] = useState('')
  const [autoFill, setAutoFill] = useState(false)

  useEffect(() => {
    if (open) {
      roundfeedbackDetails({ round_type: 1 })
      fetchPosition()
    }
  }, [open, roundfeedbackDetails, fetchPosition])
  useEffect(() => {
    if (open && currentEditData) {
      console.log('Current edit data:', currentEditData);
      const formattedFeedbackType = currentEditData.feedback_type 
      ? currentEditData.feedback_type.charAt(0).toUpperCase() + currentEditData.feedback_type.slice(1)
      : '';
      setFeedbackType(formattedFeedbackType);
      //setFeedbackType(currentEditData.feedback_type || '')
      setRound(currentEditData.round_name || '')
      setPosition(currentEditData.position_name || '')
      setFeedbackData(currentEditData.feedback_data || '')
    } else if (open) {
      setFeedbackType('')
      setRound('')
      setPosition('')
      setFeedbackData('')
      setAutoFill(false)
    }
  }, [open, currentEditData])

  const handleAutoFill = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked
    setAutoFill(checked)

    if (checked) {
      setFeedbackType('Interview')
      setRound('Round I')
      if (PositionOptions && PositionOptions[1] && PositionOptions[1].length > 0) {
        setPosition(PositionOptions[1][0].name || '')
      }
    } else {
      setFeedbackType('')
      setRound('')
      setPosition('')
    }
  }

  const handleSubmit = () => {
    if (!isFormValid) return

    const selectedRound = roundfeedbackDetailsOptions[0]?.find(
      (option) => option.round_name === round,
    )
    const selectedPosition = PositionOptions[1]?.find((option) => option.name === position)

    if (currentEditData) {
      subeditFeedback({
        data: {
          feedback_id: currentEditData.id,
          feedback_data: feedbackData,
          round: selectedRound ? selectedRound.id : 0,
          position: selectedPosition ? selectedPosition.id : 0,
          feedback_type: feedbackType.toLowerCase(),
          is_default: 0
        }
      })
      toast.success('Feedback Template Updated Successfully', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })
    } else {
      addfeedbackDetails({
        feedback_type: feedbackType.toLowerCase(),
        round: selectedRound ? `${selectedRound.id}` : '',
        position: selectedPosition ? `${selectedPosition.id}` : '',
        feedback_data: feedbackData,
        is_default: false,
      })
      toast.success('Feedback Template Added Successfully', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })
    }
    
    
    setTimeout(() => {
      onClose(); 
    }, 500);
  }

  const handleClose = () => {
    setFeedbackType('')
    setRound('')
    setPosition('')
    setFeedbackData('')
    setAutoFill(false)
    onClose()
  }

  console.log(feedbackType,"llllll")
  const isFormValid =
    feedbackType.trim() !== '' &&
    round.trim() !== '' &&
    position.trim() !== '' &&
    feedbackData.trim() !== ''

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='md'>
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'rgb(25,60,109)',
          color: '#ffffff',
        }}
      >
        <Typography variant='h5' sx={{ fontWeight: 'bold' }}>
          {currentEditData ? 'Edit Feedback Template' : 'Add Feedback Template'}
        </Typography>
        <IconButton onClick={handleClose} aria-label='close' sx={{ color: '#ffffff' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ padding: 3 }}>
        <Box sx={{ padding: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant='body1' sx={{ fontWeight: 'bold', mb: 1 }}>
                Feedback Data
              </Typography>
              <Box
                sx={{
                  height: '90px',
                  overflowY: 'auto',
                  border: '1px solid #ccc',
                  borderRadius: 1,
                  padding: 1,
                  mb: 2,
                }}
              >
                <CKEditor
                  editor={ClassicEditor}
                  data={feedbackData}
                  onChange={(_event: unknown, editor: any) => {
                    const data = editor.getData()
                    setFeedbackData(data)
                  }}
                  config={{
                    toolbar: [
                      'undo',
                      'redo',
                      '|',
                      'paragraph',
                      '|',
                      'bold',
                      'italic',
                      '|',
                      'link',
                      'insertTable',
                      'blockQuote',
                      '|',
                      'bulletedList',
                      'numberedList',
                      '|',
                      'outdent',
                      'indent',
                      'alignment',
                    ],
                  }}
                />
              </Box>
            </Grid>

            <Grid item xs={4}>
              <FormControl fullWidth size='small' variant='outlined'>
                <InputLabel id='select-feedback-type-label'>Feedback Type</InputLabel>
                <Select
                  labelId='select-feedback-type-label'
                  value={feedbackType}
                  onChange={(e) => setFeedbackType(e.target.value)}
                  label='Feedback Type'
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '20px',
                    },
                    '& .MuiSelect-select': {
                      display: 'flex',
                      alignItems: 'center',
                      padding: '10px 12px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderRadius: '20px',
                    },
                    '& .MuiInputLabel-root': {
                      top: '-6px',
                    },
                  }}
                >
                  <MenuItem value='Interview'>interview</MenuItem>
                  <MenuItem value='Drive'>Drive</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={4}>
              <FormControl fullWidth size='small' variant='outlined'>
                <InputLabel id='select-round-label'>Round</InputLabel>
                <Select
                  labelId='select-round-label'
                  value={round}
                  onChange={(e) => setRound(e.target.value)}
                  label='Round'
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '20px',
                    },
                    '& .MuiSelect-select': {
                      display: 'flex',
                      alignItems: 'center',
                      padding: '10px 12px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderRadius: '20px',
                    },
                    '& .MuiInputLabel-root': {
                      top: '-6px',
                    },
                  }}
                >
                  {roundfeedbackDetailsOptions[0]?.map((option: any) => (
                    <MenuItem key={option.id} value={option.round_name}>
                      {option.round_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={4}>
              <FormControl fullWidth size='small' variant='outlined'>
                <InputLabel id='select-position-label'>Position</InputLabel>
                <Select
                  labelId='select-position-label'
                  value={position}
                  onChange={(e) => setPosition(e.target.value)}
                  label='Position'
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '20px',
                    },
                    '& .MuiSelect-select': {
                      display: 'flex',
                      alignItems: 'center',
                      padding: '10px 12px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderRadius: '20px',
                    },
                    '& .MuiInputLabel-root': {
                      top: '-6px',
                    },
                  }}
                >
                  {PositionOptions &&
                    PositionOptions[1]?.map((option) => (
                      <MenuItem key={option.id} value={option.name}>
                        {option.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            {!currentEditData && (
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox checked={autoFill} onChange={handleAutoFill} color='primary' />
                  }
                  label='Use Default Values'
                  sx={{
                    marginBottom: -4,
                  }}
                />
              </Grid>
            )}
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{}}>
        <Button
          onClick={handleClose}
          variant='text'
          color='primary'
          sx={{
            textTransform: 'none',
            fontFamily: 'Montserrat-SemiBold',
            fontWeight: '700',
            borderRadius: '20px',
            fontSize: '13px',
            color: 'black',
            backgroundColor: 'rgb(226, 226, 226)',
            padding: '6px 8px',
            boxShadow: 'none',
            marginTop: '10px',
            marginBottom: '10px',
            width: '100px',
            fontStretch: '100%',
          }}
        >
          CANCEL
        </Button>

        <Button
          onClick={handleSubmit}
          variant='text'
          color='primary'
          disabled={!isFormValid}
          sx={{
            textTransform: 'none',
            fontFamily: 'Montserrat, sans-serif',
            fontWeight: '400',
            borderRadius: '20px',
            fontSize: '13px',
            color: isFormValid ? 'white' : '#a0a0a0',
            backgroundColor: isFormValid ? '#193c6d' : '#e2e2e2',
            marginTop: '10px',
            marginBottom: '10px',
            fontStretch: '100%',
            width: '100px',
            '&.Mui-disabled': {
              color: '#b0b0b0',
              pointerEvents: 'none',
            },
            '&:hover': {
              backgroundColor: isFormValid ? 'rgb(25,60,109)' : 'transparent',
              color: isFormValid ? 'white' : '#b0b0b0',
            },
          }}
        >
          {currentEditData?'UPDATE':'CREATE'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  roundfeedbackDetailsOptions: recruitmentEntity.getRecruitment(state).roundfeedbackDetails || {},
  PositionOptions: recruitmentEntity.getRecruitment(state).getPositionData || {},
  subeditFeedback: recruitmentEntity.getRecruitment(state).SubEditFeedbackDetails || {},
})

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    roundfeedbackDetails: (data: { round_type: number }) =>
      dispatch(roundfeedbackDetails.request({ data })),
    fetchPosition: () => dispatch(fetchPositions.request()),
    addfeedbackDetails: (data: {
      feedback_type: string
      round: string
      position: string
      feedback_data: string
    }) => dispatch(AddFeedback.request({ data })),
    subeditFeedback: (data: { data: {} }) => dispatch(SubEditFeedback.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddFeedbackModal)
