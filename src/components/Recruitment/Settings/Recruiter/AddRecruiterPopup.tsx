import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  Box,
} from '@mui/material'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity } from '../../../../reducers'
import { Dispatch } from 'redux'
import { fetchAddUserDetails, fetchEditUserDetails } from '../../../../actions'
import { connect } from 'react-redux'
import Name from './Name'
import { toast } from 'react-toastify'

interface Recruiter {
  id?: number
  name?: string
  email: string
  phone_no: string
  role: string
  status: string
  first_name?: string
  last_name?: string
  password: string
}

interface AddRecruiterPopupProps {
  open: boolean
  onClose: () => void
  fetchAddUserDetails: (data: Recruiter) => void
  fetchEditUserDetails: (data: any) => void
  initialData?: Recruiter
  refreshRecruiters: () => void
  getEditUserDetails: any
  AddUserOptions: any
}

const AddRecruiterPopup: React.FC<AddRecruiterPopupProps> = ({
  open,
  onClose,
  fetchAddUserDetails,
  fetchEditUserDetails,
  initialData,
  refreshRecruiters,
  getEditUserDetails,
  AddUserOptions,
}) => {
  const [first_name, setFirstName] = useState('')
  const [last_name, setLastName] = useState('')
  const [email, setEmail] = useState(initialData?.email || '')
  const [phone_no, setContactNumber] = useState(initialData?.phone_no || '')
  const [role, setRole] = useState(initialData?.role || '')
  const [status, setStatus] = useState(initialData?.status || '')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [name, setName] = useState(initialData?.name)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  useEffect(() => {
    if (initialData) {
      setEmail(initialData.email || '')
      setContactNumber(initialData.phone_no || '')
      setRole(initialData.role || '')
      setStatus(initialData.status || '')
      setName(initialData.name || '')
      setPassword('')
      setConfirmPassword('')
      setFirstName(initialData.name?.split(' ')[0] || '')
      setLastName(initialData.name?.split(' ')[1] || '')
    } else {
      setEmail('')
      setContactNumber('')
      setRole('')
      setStatus('')
      setName('')
      setPassword('')
      setConfirmPassword('')
      setFirstName('')
      setLastName('')
    }
    setErrors({})
  }, [open, initialData])

  useEffect(() => {
    if (AddUserOptions?.code < 0) {
      toast.error(AddUserOptions?.message)
    } else {
      toast.success(AddUserOptions?.message)
    }
  }, [AddUserOptions])

  useEffect(() => {
    if (getEditUserDetails?.code < 0) {
      toast.error(getEditUserDetails?.message)
    } else {
      toast.success(getEditUserDetails?.message)
    }
  }, [getEditUserDetails])

  const validateField = (name: string, value: string) => {
    let error = ''

    if (name === 'email') {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!value) {
        error = 'Email is required'
      } else if (!emailPattern.test(value)) {
        error = 'Invalid email format'
      }
    } else if (name === 'phone_no') {
      if (!/^\d+$/.test(value)) {
        error = 'Only numbers allowed'
      }
    } else if (name === 'first_name' || name === 'last_name') {
      if (/\s/.test(value)) {
        error = 'No spaces allowed'
      }
    } else if (name === 'password' || name === 'confirmPassword') {
      if (name === 'confirmPassword' && value !== password) {
        error = 'Passwords do not match'
      }
    } else if (!value) {
      error = 'This field is required'
    }

    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: error,
    }))
  }

  const isFormValid =
    email &&
    phone_no &&
    status &&
    (initialData
      ? name && (!password || password === confirmPassword) && password && confirmPassword
      : first_name && last_name && role && password && password === confirmPassword)

  const handleSubmit = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) newErrors.email = 'Email is required'
    if (!phone_no) newErrors.phone_no = 'Phone number is required'
    if (!status) newErrors.status = 'Status is required'
    if (!initialData) {
      if (!first_name) newErrors.first_name = 'First name is required'
      if (!last_name) newErrors.last_name = 'Last name is required'
      if (!role) newErrors.role = 'Role is required'
      if (!password) newErrors.password = 'Password is required'
      if (password !== confirmPassword) newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)

    if (Object.values(newErrors).some((err) => err)) {
      toast.error('Please correct the errors before submitting.')
      return
    }

    const payload: Recruiter = {
      first_name,
      last_name,
      email,
      phone_no,
      role,
      status,
      password,
    }

    const editPayload = {
      name: name,
      email,
      phone_no,
      status,
      password,
    }

    if (initialData) {
      fetchEditUserDetails({ ...editPayload, id: initialData.id })
    } else {
      fetchAddUserDetails(payload)
      setFirstName('')
      setLastName('')
      setEmail('')
      setContactNumber('')
      setRole('')
      setStatus('')
      setPassword('')
      setConfirmPassword('')
      setName('')
      setErrors({})
    }

    setTimeout(() => {
      refreshRecruiters()
    }, 300)

    onClose()
  }

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
      <DialogTitle
        sx={{
          backgroundColor: '#1a2a50',
          color: 'white',
          fontSize: { xs: '16px', sm: '20px' },
          padding: { xs: '12px', sm: '15px 16px' },
          textAlign: 'center',
          fontFamily: 'Montserrat-Medium',
        }}
      >
        {initialData ? 'Edit Recruiter' : 'Add Recruiter'}
      </DialogTitle>

      <DialogContent
        sx={{
          justifyContent: 'center',
          padding: '20px',
          margin: '20px',
          marginBottom: '0px',
          width: '85%',
          fontFamily: 'Montserrat-Medium',
        }}
      >
        <Box sx={{ display: 'flex', gap: '10px' }}>
          <Name
            fName={first_name}
            lName={last_name}
            isEdit={initialData?.name ? 'Edit' : 'Add'}
            setFirstName={setFirstName}
            setLastName={setLastName}
            setName={setName}
            name={name || ''}
          />
        </Box>

        <Box sx={{ display: 'flex', gap: '10px' }}>
          <TextField
            fullWidth
            label='Email'
            value={email}
            onChange={(e) => {
              setEmail(e.target.value)
              validateField('email', e.target.value)
            }}
            error={!!errors.email}
            helperText={errors.email}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
          <TextField
            fullWidth
            label='Contact Number'
            value={phone_no}
            onChange={(e) => {
              const cleaned = e.target.value.replace(/\D/g, '')
              if (cleaned.length <= 10) {
                setContactNumber(cleaned)
                validateField('phone_no', cleaned)
              }
            }}
            error={!!errors.phone_no}
            helperText={errors.phone_no}
            margin='dense'
            inputProps={{ maxLength: 10 }}
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
        </Box>

        <Box sx={{ display: 'flex', gap: '10px' }}>
          {!initialData && (
            <TextField
              select
              fullWidth
              label='Role'
              value={role}
              onChange={(e) => setRole(e.target.value)}
              error={!!errors.role}
              helperText={errors.role}
              margin='dense'
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            >
              <MenuItem value='2'>HR</MenuItem>
            </TextField>
          )}

          <TextField
            select
            fullWidth
            label='Status'
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            error={!!errors.status}
            helperText={errors.status}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          >
            <MenuItem value='Active'>Active</MenuItem>
            <MenuItem value='Inactive'>Inactive</MenuItem>
          </TextField>
        </Box>

        <Box sx={{ display: 'flex', gap: '10px' }}>
          <TextField
            fullWidth
            label='Password'
            type='password'
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            error={!!errors.password}
            helperText={errors.password}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
          <TextField
            fullWidth
            label='Confirm Password'
            type='password'
            value={confirmPassword}
            onChange={(e) => {
              setConfirmPassword(e.target.value)
              validateField('confirmPassword', e.target.value)
            }}
            error={!!errors.confirmPassword}
            helperText={errors.confirmPassword}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
        </Box>
      </DialogContent>

      <DialogActions
        sx={{ paddingBottom: '30px', justifyContent: 'flex-end', paddingRight: '50px' }}
      >
        <Button
          onClick={onClose}
          variant='contained'
          sx={{
            backgroundColor: '#e2e2e2',
            color: '#000',
            borderRadius: '40px',
            padding: '7px 16px',
            fontWeight: 'bold',

            '&:hover': {
              backgroundColor: 'rgb(226, 226, 226)',
              color: 'black',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color='primary'
          variant='contained'
          sx={{ borderRadius: '40px', padding: '7px 16px', fontWeight: 'bold' }}
          disabled={!isFormValid}
        >
          {initialData ? 'Update' : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  AddUserOptions: recruitmentEntity.getRecruitment(state).getAddUserDetails,
  getEditUserDetails: recruitmentEntity.getRecruitment(state).getEditUserDetails,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchAddUserDetails: (data: {}) => dispatch(fetchAddUserDetails.request({ data })),
  fetchEditUserDetails: (data: Recruiter) => dispatch(fetchEditUserDetails.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(AddRecruiterPopup)
