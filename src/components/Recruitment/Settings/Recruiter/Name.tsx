import { TextField } from '@mui/material'

type IsEdit = 'Edit' | 'Add'

interface Props {
  fName: string
  lName: string
  isEdit: IsEdit
  setFirstName: (fName: string) => void
  setLastName: (lName: string) => void
  setName: (name: string) => void
  name: string
}

function Name({ fName, lName, isEdit, setFirstName, setLastName, setName, name }: Props) {
  return (
    <>
      {isEdit === 'Add' ? (
        <>
          <TextField
            fullWidth
            label='First Name'
            value={fName}
            onChange={(e) => setFirstName(e.target.value)}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
          <TextField
            fullWidth
            label='Last Name'
            value={lName}
            onChange={(e) => setLastName(e.target.value)}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
        </>
      ) : (
        <>
          <TextField
            fullWidth
            label='Name'
            value={name}
            onChange={(e) => setName(e.target.value)}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
          />
        </>
      )}
    </>
  )
}
export default Name
