import React, { useState } from 'react'
import { TableRow, TableCell, IconButton, Tooltip } from '@mui/material'
import { Edit, Delete } from '@mui/icons-material'
import { Round } from './RoundsTypes'
import EditRoundDialog from './EditRoundDialog'
import { handleEditSave } from './RoundsUtils'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { DeleteRounds, EditRounds } from 'actions'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { toast } from 'react-toastify'

interface RoundsTableRowProps {
  row: Round
  onEdit: (round: Round) => void
  deleteRounds: ({}) => void
  roundTypeOptions: any
  fetchEditRounds: any
}

const RoundsTableRow: React.FC<RoundsTableRowProps> = ({
  row,
  onEdit,
  deleteRounds,
  roundTypeOptions,
  fetchEditRounds,
}) => {
  const [openDialog, setOpenDialog] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedRound, setSelectedRound] = useState<Round | null>(null)
  const [roundToDelete, setRoundToDelete] = useState<number | null>(null)

  const handleDeleteClick = (id: number) => {
    setRoundToDelete(id)
    setOpenDialog(true)
  }

  const handleDelete = () => {
    deleteRounds({ id: roundToDelete })
    toast.success('Round Delete Successfully!')
  }

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ textAlign: 'center' }}>{row.round_name}</TableCell>
        <TableCell sx={{ textAlign: 'center' }}>{row.type_name}</TableCell>
        <TableCell sx={{ textAlign: 'center' }}>{row.description}</TableCell>
        <TableCell sx={{ textAlign: 'center' }}>
          <Tooltip title='Edit Round'>
            <IconButton
              color='primary'
              onClick={() => {
                setSelectedRound(row)
                setEditDialogOpen(true)
              }}
              sx={{ borderRadius: '50%' }}
            >
              <Edit />
            </IconButton>
          </Tooltip>
          <Tooltip title='Delete Round'>
            <IconButton
              color='error'
              onClick={() => handleDeleteClick(row.id)}
              sx={{ borderRadius: '50%' }}
            >
              <Delete />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <DeleteConfirmationDialog
        open={openDialog}
        handleClose={() => setOpenDialog(false)}
        handleConfirm={handleDelete}
      />

      <EditRoundDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        round={selectedRound}
        setRound={setSelectedRound}
        roundTypeOptions={roundTypeOptions}
        onSave={() => {
          if (selectedRound) {
            fetchEditRounds({
              id: selectedRound.id,
              round_name: selectedRound.round_name,
              round_type: selectedRound.type_id,
              schedule_required: '',
              template_required: 1,
              description: selectedRound.description,
            })
          }
        }}
      />
    </>
  )
}

const mapStateToProps = (state: RootState) => ({})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  deleteRounds: (id: {}) => dispatch(DeleteRounds.request({ id })),
  fetchEditRounds: (data: {}) => dispatch(EditRounds.request(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(RoundsTableRow)
