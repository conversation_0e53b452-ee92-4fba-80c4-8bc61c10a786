import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Select,
  Typography,
  SelectChangeEvent,
} from '@mui/material'
import { Round } from './RoundsTypes'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { EditRounds, fetchAddRound, fetchRounds } from 'actions'
import { toast } from 'react-toastify'
 
interface EditRoundDialogProps {
  open: boolean
  onClose: () => void
  round: Round | null
  setRound: (round: Round | null) => void
  onSave: () => void
  editRoundOptions: any
  fetchEditRounds: ({}) => void
  roundTypeOptions: any
}
 
const EditRoundDialog: React.FC<EditRoundDialogProps> = ({
  open,
  onClose,
  round,
  setRound,
  onSave,
  roundTypeOptions,
  editRoundOptions,
  fetchEditRounds,
}) => {
  const [errors, setErrors] = useState<{ [key in keyof Round]?: string }>({})
 
  if (!round) return null
 
  const handleInputChange = (field: keyof Round, value: string | number) => {
    setRound(round ? { ...round, [field]: value } : null)
  }
 
  const handleRoundTypeChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value
    const numericValue = Number(value)
    handleInputChange('type_id', numericValue)
  }

  const handleSave = () => {
    const newErrors: typeof errors = {}
    if (!round.round_name?.trim())    newErrors.round_name = 'Name is required'
    if (round.type_id === undefined)  newErrors.type_id   = 'Please select a type'
    if (!round.description?.trim())   newErrors.description = 'Description is required'

    if (Object.keys(newErrors).length) {
      setErrors(newErrors)
      return
    }

    // no errors → proceed
    onSave()
    toast.success('Round edited successfully!')
  }
 
  return (
    <Dialog open={open} onClose={onClose} sx={{ '& .MuiPaper-root': { borderRadius: '20px' } }}>
      <DialogTitle sx={{ textAlign: 'center', bgcolor: '#193C6D', color: 'white', mb: 4 }}>
        Edit Round
      </DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Name"
          value={round.round_name}
          onChange={e => handleInputChange('round_name', e.target.value)}
          error={!!errors.round_name}
          helperText={errors.round_name}
          margin="dense"
          sx={{ '& .MuiOutlinedInput-root': { borderRadius: '25px' } }}
        />

        <Select
          fullWidth
          displayEmpty
          value={round.type_id != null ? String(round.type_id) : ''}
          onChange={handleRoundTypeChange}
          error={!!errors.type_id}
          sx={{ mb: 2, '& fieldset': { borderRadius: '50px' } }}
        >
          <MenuItem disabled value="">
            Select Round Type
          </MenuItem>
          {roundTypeOptions[0]?.map((opt: { name: string; type_id: string }) => (
            <MenuItem key={opt.type_id} value={opt.type_id}>
              {opt.name}
            </MenuItem>
          ))}
        </Select>
        {errors.type_id && (
          <Typography color="error" variant="body2" sx={{ mb: 2 }}>
            {errors.type_id}
          </Typography>
        )}

        <TextField
          fullWidth
          label="Description"
          value={round.description}
          onChange={e => handleInputChange('description', e.target.value)}
          error={!!errors.description}
          helperText={errors.description}
          margin="dense"
          multiline
          rows={3}
          sx={{ '& .MuiOutlinedInput-root': { borderRadius: '25px' } }}
        />
      </DialogContent>
      <DialogActions>
        <ActionButton onClick={onClose} sx={{ borderRadius: '20px', bgcolor: '#193C6D', color: '#fff', mr: 2, mb: 2 }}>
          Cancel
        </ActionButton>
        <ActionButton
          onClick={handleSave}      // ← use our validator
          variant="contained"
          sx={{ borderRadius: '20px', px: 3, bgcolor: '#193C6D', mb: 2 }}
        >
          Save
        </ActionButton>
      </DialogActions>
    </Dialog>
  )
}
const mapStateToProps = (state: RootState) => ({
  editRoundOptions: recruitmentEntity.getRecruitment(state).editRounds,
  addRoundOptions: recruitmentEntity.getRecruitment(state).getAddRound,
})
 
const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchEditRounds: (data: {}) => dispatch(EditRounds.request(data)),
  fetchAddRound: (data: any) => dispatch(fetchAddRound.request({ data })),
})
 
export default connect(mapStateToProps, mapDispatchToProps)(EditRoundDialog)