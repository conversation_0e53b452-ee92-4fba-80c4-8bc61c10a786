import React from 'react'
import { TextField, Select, MenuItem, FormControlLabel, Checkbox, Typography } from '@mui/material'
import roundTypeOptions from './RoundsTable'
 
interface RoundFormProps {
  data: { name: string; roundType: string; description: string }
  errors: { name?: string; roundType?: string; description?: string }
  onChange: (field: string, value: string) => void
  onBlur: (field: string, value: string) => void
  roundTypeOptions: any
}
 
const AddRoundForm: React.FC<RoundFormProps> = ({
  data,
  errors,
  onChange,
  onBlur,
  roundTypeOptions,
}) => {
  return (
    <>
      <TextField
        fullWidth
        label='Name'
        variant='outlined'
        value={data.name}
        onChange={(e) => onChange('name', e.target.value)}
        onBlur={() => onBlur('name', data.name)}
        error={!!errors.name}
        helperText={errors.name}
        sx={{ mb: 2, '& fieldset': { borderRadius: '50px' } }}
      />
      <Select
        fullWidth
        displayEmpty
        value={data.roundType}
        onChange={(e) => onChange('roundType', e.target.value)}
        onBlur={() => onBlur('roundType', data.roundType)}
        sx={{ mb: 2, '& fieldset': { borderRadius: '50px' } }}
      >
        <MenuItem disabled value=''>
          Select Round Type
        </MenuItem>
        {roundTypeOptions.map((pos: { name: string; type_id: number }) => (
          <MenuItem key={pos.name} value={pos.type_id}>
            {pos.name}
          </MenuItem>
        ))}
      </Select>
      {errors.roundType && (
        <Typography color='error' variant='body2' sx={{ mb: 2 }}>
          {errors.roundType}
        </Typography>
      )}
      <TextField
        fullWidth
        label='Description'
        multiline
        rows={3}
        variant='outlined'
        value={data.description}
        onChange={(e) => onChange('description', e.target.value)}
        onBlur={() => onBlur('description', data.description)}
        error={!!errors.description}
        helperText={errors.description}
        sx={{ mb: 2, '& fieldset': { borderRadius: '30px' } }}
      />
      {/* <Typography sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>Options:</Typography>
      <FormControlLabel control={<Checkbox />} label='Auto Assign' />
      <FormControlLabel control={<Checkbox />} label='Template Required' />
      <FormControlLabel control={<Checkbox />} label='Schedule Required' /> */}
    </>
  )
}
 
export default AddRoundForm
 
 