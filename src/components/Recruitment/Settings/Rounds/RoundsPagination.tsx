import React from 'react'
import { Box, Pagination } from '@mui/material'

interface RoundsPaginationProps {
  totalPages: number
  page: number
  setPage: (value: number) => void
}

const RoundsPagination: React.FC<RoundsPaginationProps> = ({ totalPages, page, setPage }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'flex-end',
        padding: 2,
      }}
    >
      <Pagination
        count={totalPages}
        page={page}
        onChange={(_event, value) => setPage(value)}
        color='primary'
        shape='circular'
        sx={{
          '& .MuiPaginationItem-root': {
            fontSize: '16px',
          },
          '& .MuiPaginationItem-page.Mui-selected': {
            backgroundColor: 'rgb(25,60,109)',
            color: '#fff',
          },
        }}
      />
    </Box>
  )
}

export default RoundsPagination
