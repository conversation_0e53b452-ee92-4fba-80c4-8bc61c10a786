import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogTitle, DialogContent, <PERSON>alogActions, Button } from '@mui/material'
import AddRoundForm from './AddRoundForm'
import { connect } from 'react-redux'
import { fetchAddRound } from '../../../../actions'
import { Dispatch } from 'redux'
import { recruitmentEntity } from '../../../../reducers'
import { RootState } from '../../../../configureStore'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'
 
interface AddRoundDialogProps {
  open: boolean
  onClose: () => void
  roundTypeOptions: any
  fetchAddRound: (data: any) => void
}
 
const AddRoundDialog: React.FC<AddRoundDialogProps> = ({
  open,
  onClose,
  roundTypeOptions,
  fetchAddRound,
}) => {
  const [roundData, setRoundData] = useState({ name: '', roundType: '', description: '' })
  const [errors, setErrors] = useState<{ name?: string; roundType?: string; description?: string }>(
    {},
  )
 
  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setRoundData({ name: '', roundType: '', description: '' })
      setErrors({})
    }
  }, [open])
 
  const handleChange = (field: string, value: string) => {
    setRoundData((prev) => ({ ...prev, [field]: value }))
 
    // Clear error while typing
    if (errors[field as keyof typeof errors]) {
      setErrors((prevErrors) => ({ ...prevErrors, [field]: '' }))
    }
  }
 
  const validateField = (field: string, value: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: value ? '' : `${field.charAt(0).toUpperCase() + field.slice(1)}  is required`,
    }))
  }
 
  const handleSubmit = () => {
    validateField('name', roundData.name)
    validateField('roundType', roundData.roundType)
    validateField('description', roundData.description)
 
    if (!roundData.name.trim() || !roundData.roundType || !roundData.description.trim()) return
    fetchAddRound({
      round_name: roundData.name,
      round_type: roundData.roundType,
      description: roundData.description,
      schedule_required: true,
      template_required: true,
    })
    toast.success('Round Added Successfully')
    onClose()
  }
 
  return (
    <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth>
      <DialogTitle
        sx={{
          bgcolor: '#0d3c6e',
          color: 'white',
          textAlign: 'center',
          fontSize: '1.2rem',
        }}
      >
        Add Round
      </DialogTitle>
      <DialogContent dividers sx={{ padding: '16px 24px' }}>
        <AddRoundForm
          data={roundData}
          errors={errors}
          onChange={handleChange}
          onBlur={validateField}
          roundTypeOptions={roundTypeOptions}
        />
      </DialogContent>
      <DialogActions sx={{ padding: '12px 24px' }}>
        <ActionButton
          onClick={onClose}
          sx={{
            color: '#333',
            backgroundColor: '#f0f0f0',
            borderRadius: '50px',
            marginRight: '17px',
            marginBottom: '9px',
            marginTop: '8px',
            '&:hover': {
              backgroundColor: '#f0f0f0 !important',
              color: '#333 !important',
            },
          }}
        >
          Cancel
        </ActionButton>
        <ActionButton
          variant='contained'
          onClick={handleSubmit}
          disabled={!roundData.name.trim() || !roundData.roundType || !roundData.description.trim()}
          sx={{
            backgroundColor: '#0d3b66',
            color: 'white',
            borderRadius: '50px',
            marginBottom: '9px',
            marginTop: '8px',
          }}
        >
          Submit
        </ActionButton>
      </DialogActions>
    </Dialog>
  )
}
 
const mapStateToProps = (state: RootState) => ({
  addRoundOptions: recruitmentEntity.getRecruitment(state).getAddRound,
})
 
const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchAddRound: (data: any) => dispatch(fetchAddRound.request({ data })),
})
 
export default connect(mapStateToProps, mapDispatchToProps)(AddRoundDialog)
 