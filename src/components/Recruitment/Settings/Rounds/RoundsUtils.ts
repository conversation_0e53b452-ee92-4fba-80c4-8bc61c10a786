import { Round, Rounds } from './RoundsTypes'

export const handleDelete = (id: number, rounds: Rounds, setRounds: (rounds: Rounds) => void) => {
  setRounds(rounds.filter((round) => round.id !== id))
}

export const handleEditClick = (
  round: Round,
  setSelectedRound: (round: Round) => void,
  setEditDialogOpen: (open: boolean) => void,
) => {
  setSelectedRound(round)
  setEditDialogOpen(true)
}

export const handleEditSave = (
  rounds: Round[],
  selectedRound: Round | null,
  setRounds: (rounds: Round[]) => void,
  setEditDialogOpen: (open: boolean) => void,
) => {
  if (!selectedRound) return
  setRounds(rounds.map((round) => (round.id === selectedRound.id ? selectedRound : round)))
  setEditDialogOpen(false)
}

export const getFilteredRounds = (rounds: Round[], searchTerm: string) => {
  return rounds.filter((round) => round.round_name.toLowerCase().includes(searchTerm.toLowerCase()))
}

export const getPaginatedRounds = (filteredRounds: Round[], page: number, rowsPerPage: number) => {
  return (
    Array.isArray(filteredRounds) &&
    filteredRounds.slice((page - 1) * rowsPerPage, page * rowsPerPage)
  )
}
