import React, { useState } from 'react'
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip,
  Card,
  useMediaQuery,
  useTheme,
  TableSortLabel,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import Loader from 'components/Common/Loader'

interface BlockedDomain {
  id: number
  name: string
}

interface Props {
  paginatedBlockedDomains: BlockedDomain[]
  handleEdit: (id: number) => void
  handleDelete: (id: number) => void
  isBlockDomains: boolean
}

const BlockedDomainList: React.FC<Props> = ({
  paginatedBlockedDomains,
  handleEdit,
  handleDelete,
  isBlockDomains,
}) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const [order, setOrder] = useState<'asc' | 'desc'>('asc')

  const sortedBlockedDomains = [...paginatedBlockedDomains].sort((a, b) =>
    order === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name),
  )

  return (
    <>
      <Loader state={!isBlockDomains} />
      <Card sx={{ borderRadius: '4px', overflowX: 'auto' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#193C6D' }}>
            <TableRow>
              <TableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  fontWeight: 600,
                  width: '50%',
                  color: 'white',
                  textAlign: 'center',
                }}
              >
                <TableSortLabel
                  active={true}
                  direction={order}
                  onClick={() => setOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))}
                  sx={{
                    color: 'white',

                    '& .MuiTableSortLabel-icon': {
                      color: 'white !important',
                    },
                  }}
                >
                  <span style={{ color: 'white', fontWeight: 600 }}>Domain Name </span>
                </TableSortLabel>
              </TableCell>

              <TableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  fontWeight: 600,
                  color: 'white',
                  textAlign: 'center',
                }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedBlockedDomains.length > 0 ? (
              sortedBlockedDomains.map((blockDom, index) => (
                <TableRow key={blockDom.id} sx={{}}>
                  <TableCell
                    sx={{
                      textAlign: 'center',
                      minWidth: 150,
                      maxWidth: 250,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    {blockDom.name}
                  </TableCell>
                  <TableCell
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    <Tooltip title='Edit'>
                      <IconButton sx={{ color: '#193C6D' }} onClick={() => handleEdit(blockDom.id)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title='Delete'>
                      <IconButton
                        sx={{ color: '#db3700' }}
                        onClick={() => handleDelete(blockDom.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  align='center'
                  sx={{
                    fontFamily: 'Montserrat-Medium',
                    color: '#888',
                    py: 4,
                  }}
                >
                  No records found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
    </>
  )
}

export default BlockedDomainList
