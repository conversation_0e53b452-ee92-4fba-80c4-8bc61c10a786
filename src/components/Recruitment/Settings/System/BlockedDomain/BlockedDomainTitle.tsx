import { Box, Typography } from '@mui/material'
import BackButton from 'components/Recruitment/Common/GeneralizedBackButton'
import { useState } from 'react'

const BlockedSubjectTitle: React.FC = () => {
  const [isClicked, setIsClicked] = useState(false)

  const handleClick = () => {
    setIsClicked(true)
    setTimeout(() => setIsClicked(false), 200)
  }

  return (
    <Box display='flex' alignItems='center' justifyContent='space-between'>
    <Typography
      variant='h6'
      sx={{
        fontFamily: 'Montserrat-Semibold',
        fontWeight: 600,
        position: 'relative',
        display: 'inline-block',
        padding: '8px 16px',
        color: 'primary.main',
        borderRadius: '4px',
        cursor: 'pointer',
        transition: 'background-color 0.3s ease-in-out, color 0.3s ease-in-out',
        backgroundColor: isClicked ? 'rgba(25, 60, 109, 0.3)' : 'transparent',
        '&::after': {
          content: '""',
          display: 'block',
          width: '100%',
          height: '2px',
          backgroundColor: '#193C6D',
          position: 'absolute',
          bottom: '0px',
          left: 0,
        },
      }}
      onClick={handleClick}
    >
      Blocked Domain
    </Typography>
      <BackButton tooltip='Go Back' />
    </Box>
  )
}

export default BlockedSubjectTitle