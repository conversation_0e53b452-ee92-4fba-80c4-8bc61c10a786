import { useState, useEffect } from 'react'
import { Box, Card, useMediaQuery, useTheme } from '@mui/material'
import BlockedDomainHeader from './BlockedDomainHeader'
import BlockedDomainList from './BlockedDomainList'
import BlockedDomainDialog from './BlockedDomainDialog'
import DeleteConfirmationDialog from './DeleteConfirmationDialog'
import CustomPagination from './CustomPagination'
import BlockedDomainTitle from './BlockedDomainTitle'
import {
  addBlockDomains,
  deleteBlockDomains,
  editBlockDomains,
  fetchBlockDomainsData,
} from '../../../../../actions'
import { Dispatch } from 'redux'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'
interface BlockedDomain {
  id: number
  name: string
}

function BlockedDomain(props: any) {
  const {
    fetchBlockDomainsData,
    blockDomainsOptions,
    addBlockDomainsData,
    editBlockDomainsData,
    isBlockDomains,
  } = props
  const [blockedDomains, setBlockedDomains] = useState<BlockedDomain[]>([])
  const [open, setOpen] = useState<boolean>(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)
  const [newBlockedDomain, setNewBlockedDomain] = useState<string>('')
  const [page, setPage] = useState(1)

  const itemsPerPage = 10
  const filteredBlockedDomains = blockedDomains.filter((blockDom) =>
    blockDom.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )
  const totalPages = Math.ceil(filteredBlockedDomains.length / itemsPerPage)
  const paginatedBlockedDomains = filteredBlockedDomains.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  useEffect(() => {
    fetchBlockDomainsData()
  }, [fetchBlockDomainsData])

  useEffect(() => {
    if (blockDomainsOptions && Array.isArray(blockDomainsOptions[0])) {
      setBlockedDomains(blockDomainsOptions[0].map(({ id, name }) => ({ id, name })))
    }
  }, [blockDomainsOptions])

  useEffect(() => {
    setPage(1)
  }, [searchQuery])

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleOpen = () => {
    setEditIndex(null)
    setNewBlockedDomain('')
    setOpen(true)
  }

  const handleClose = () => setOpen(false)

  const handleEdit = (id: number) => {
    const domainToEdit = blockedDomains.find((domain) => domain.id === id)
    if (domainToEdit) {
      setEditIndex(domainToEdit.id)
      setNewBlockedDomain(domainToEdit.name)
      setOpen(true)
    }
  }

  const handleSaveBlockedDomain = (newBlockedDomain: string) => {
    if (!newBlockedDomain.trim()) return

    if (editIndex !== null) {
      editBlockDomainsData({
        id: editIndex,
        name: newBlockedDomain,
      })
      toast.success('Domain Updated successfully!', { position: 'top-right' })
    } else {
      const newEntry = { id: Date.now(), name: newBlockedDomain }
      setBlockedDomains([...blockedDomains, newEntry])
      addBlockDomainsData({ name: newBlockedDomain })
      toast.success('Domain Added successfully!', { position: 'top-right' })
    }
    setTimeout(() => {
      fetchBlockDomainsData()
    }, 500)

    setDeleteIndex(null)

    handleClose()
  }

  const handleDelete = (id: number) => {
    setDeleteIndex(id)
  }

  const handleConfirm = () => {
    if (deleteIndex !== null) {
      props.deleteBlockDomainsData(deleteIndex)
      setTimeout(() => {
        props.fetchBlockDomainsData()
      }, 500)
      toast.success('Domain Deleted successfully!', { position: 'top-right' })
      setDeleteIndex(null)
    }
  }

  return (
    <Box
      sx={{
        width: '98%',
        fontFamily: 'Montserrat-Medium',
        display: 'flex',
        justifyContent: 'center',
        padding: isMobile ? '10px' : '20px',
      }}
    >
      <Card
        sx={{
          padding: isMobile ? '10px' : '20px',
          width: isMobile ? '100%' : '97%',
          margin: '0px auto',
          marginLeft: '5px',
        }}
      >
        <BlockedDomainTitle />
        <BlockedDomainHeader
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          handleOpen={handleOpen}
        />
        <BlockedDomainList
          paginatedBlockedDomains={paginatedBlockedDomains}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          isBlockDomains={isBlockDomains}
        />
        {totalPages >= 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
            <CustomPagination count={totalPages} page={page} onChange={handlePageChange} />
          </Box>
        )}
      </Card>
      <BlockedDomainDialog
        open={open}
        handleClose={handleClose}
        newBlockedDomain={newBlockedDomain}
        setNewBlockedDomain={setNewBlockedDomain}
        handleSaveBlockedDomain={handleSaveBlockedDomain}
        editIndex={editIndex}
      />
      <DeleteConfirmationDialog
        open={deleteIndex !== null}
        handleClose={() => setDeleteIndex(null)}
        handleConfirm={handleConfirm}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  blockDomainsOptions: recruitmentEntity.getRecruitment(state).getBlockDomainsData,
  isBlockDomains: recruitmentStateUI.getRecruitment(state).isBlockDomains,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchBlockDomainsData: () => dispatch(fetchBlockDomainsData.request()),
  addBlockDomainsData: (data: { name: string }) => dispatch(addBlockDomains.request({ data })),
  deleteBlockDomainsData: (id: number) => dispatch(deleteBlockDomains.request({ id })),
  editBlockDomainsData: (data: any) => dispatch(editBlockDomains.request(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(BlockedDomain)
