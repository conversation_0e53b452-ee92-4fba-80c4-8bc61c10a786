import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, DialogTitle, DialogContent, DialogActions, Button, TextField } from '@mui/material'

interface Props {
  open: boolean
  handleClose: () => void
  newBlockedDomain: string
  setNewBlockedDomain: (value: string) => void
  handleSaveBlockedDomain: (value: string) => void
  editIndex: number | null
}

const BlockedDomainDialog: React.FC<Props> = ({
  open,
  handleClose,
  newBlockedDomain,
  setNewBlockedDomain,
  handleSaveBlockedDomain,
  editIndex,
}) => {
  const [originalValue, setOriginalValue] = useState('')
  const [error, setError] = useState('')

  useEffect(() => {
    if (open && editIndex !== null) {
      setOriginalValue(newBlockedDomain.trim())
    } else if (open && editIndex === null) {
      setNewBlockedDomain('')
      setOriginalValue('')
    }
  }, [open, editIndex])

  // Validation function for domain or email
  const isValidDomainOrEmail = (value: string) => {
    // Email regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    // Domain regex (simple, allows subdomains, no protocol, no path)
    const domainRegex = /^(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.(?:[A-Za-z]{2,})$/
    // Also allow subdomains like sub.example.com
    const fullDomainRegex = /^(?!-)([A-Za-z0-9-]{1,63}\.)+[A-Za-z]{2,}$/
    return emailRegex.test(value) || domainRegex.test(value) || fullDomainRegex.test(value)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewBlockedDomain(e.target.value)
    if (e.target.value.trim() === '') {
      setError('')
    } else if (!isValidDomainOrEmail(e.target.value.trim())) {
      setError('Please enter a valid domain or email address')
    } else {
      setError('')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const trimmedValue = newBlockedDomain.trim()
    if (trimmedValue && trimmedValue !== originalValue && !error) {
      handleSaveBlockedDomain(trimmedValue)
      handleClose()
    }
  }

  const trimmedInput = newBlockedDomain.trim()
  const isButtonDisabled =
    !trimmedInput || (editIndex !== null && trimmedInput === originalValue) || !!error

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm'>
      <form onSubmit={handleSubmit}>
        <DialogTitle
          sx={{
            bgcolor: '#1f3a5f',
            color: 'white',
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
            fontWeight: 600,
          }}
        >
          {editIndex !== null ? 'Edit Domain' : 'Add Domain'}
        </DialogTitle>
        <DialogContent sx={{ padding: '20px', borderRadius: '24px' }}>
          <TextField
            fullWidth
            label='Blocked Domain'
            variant='outlined'
            value={newBlockedDomain}
            onChange={handleChange}
            autoFocus
            error={!!error}
            helperText={error || 'Enter a domain (example.com) or email (<EMAIL>)'}
            sx={{
              fontFamily: 'Montserrat, sans-serif',
              '& .MuiOutlinedInput-root': {
                borderRadius: '28px',
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                  borderWidth: '2px',
                },
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ padding: '10px 20px', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            variant='contained'
            onClick={handleClose}
            sx={{
              backgroundColor: '#E2E2E2',
              color: '#000000',
              height: '40px',
              fontSize: '15px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '120px',
              '&:hover': { backgroundColor: '#E2E2E2', color: '#000000' },
            }}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            variant='contained'
            disabled={isButtonDisabled}
            sx={{
              backgroundColor: '#193C6D',
              color: '#FFFFFF',
              fontSize: '15px',
              height: '40px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '120px',
            }}
          >
            {editIndex !== null ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default BlockedDomainDialog
