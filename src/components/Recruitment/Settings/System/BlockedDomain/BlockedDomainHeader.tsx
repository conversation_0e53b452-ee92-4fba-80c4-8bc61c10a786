import { Box, Button, TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

interface Props {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  handleOpen: () => void;
}

const BlockedSubjectHeader: React.FC<Props> = ({ searchQuery, setSearchQuery, handleOpen }) => {
  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center', 
      mb: 0,
      gap: 2
    }}>
      <TextField
        variant="outlined"
        size="small"
        placeholder="Search Blocked Domain"
        value={searchQuery}

        onChange={(e) => setSearchQuery(e.target.value)}
        sx={{
          '& .MuiOutlinedInput-root': {
            height: '50px', 
            width:'23%',
            borderRadius:"23px"
          }
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon fontSize="medium" />
            </InputAdornment>
          ),
        }}
      />
      
      <Button
        variant='contained'
        color='secondary'
        onClick={handleOpen}
        sx={{
          fontFamily: 'Montserrat-Semibold',
          borderRadius: '26px',
          fontSize: '16px',
          pl: '28px',
          pr: '22px',
          height: "50px",
          width:"300px"
        }}
      >
        Add Blocked Domain
      </Button>
    </Box>
  );
};

export default BlockedSubjectHeader;