import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from '@mui/material'

interface DeleteConfirmationDialogProps {
  open: boolean
  handleClose: () => void
  handleConfirm: () => void
  message?: string
}

const DeleteConfirmationDialog = ({
  open,
  handleClose,
  handleConfirm,
  message = ' Are you sure you want to delete this item? This action cannot be undone.',
}: DeleteConfirmationDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth='xs'
      fullWidth
      PaperProps={{
        sx: { borderRadius: '8px', width: '40%', fontFamily: 'Montserrat, sans-serif' },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: 'rgb(25, 60, 109)',
          color: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '1rem',
          marginBottom: '1rem',
          fontFamily: 'Montserrat, sans-serif',
          fontWeight: 'bold',
        }}
      >
        Confirm Deletion
      </DialogTitle>

      <DialogContent
        sx={{ padding: '20px', fontFamily: 'Montserrat, sans-serif', paddingBottom: '0rem' }}
      >
        <Typography sx={{ fontFamily: 'Montserrat, sans-serif' }}>
          {message}
        </Typography>
      </DialogContent>

      <DialogActions sx={{ padding: '20px', fontFamily: 'Montserrat, sans-serif' }}>
        <Button
          onClick={handleClose}
          variant='outlined'
          sx={{
            backgroundColor: 'rgb(25, 60, 109)',
            color: '#fff',
            fontWeight: 'bold',
            textTransform: 'none',
            fontSize: '13px',
            height: '35px',
            fontFamily: 'Montserrat, sans-serif',
            marginLeft: 'auto',
            border: '1px solid rgba(25, 60, 109)',
            borderRadius: '20px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: 'rgb(25, 60, 109)' },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          variant='contained'
          sx={{
            backgroundColor: '#db3700',
            color: '#fff',
            fontWeight: 'bold',
            textTransform: 'none',
            fontSize: '13px',
            height: '35px',
            fontFamily: 'Montserrat, sans-serif',
            marginLeft: 'auto',
            border: '1px solid #db3700',
            borderRadius: '20px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: '#db3700' },
          }}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DeleteConfirmationDialog
