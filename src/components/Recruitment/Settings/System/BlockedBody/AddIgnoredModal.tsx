import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Box,
  Tooltip,
  Typography,
  Modal,
  TextField,
  Pagination,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { ActionButton } from "../../../../HolidayList/HolidaysStyles";
import { toast } from "react-toastify";
import { addRejectedBody, deleteRejectedBody, editRejectedBody, getBlockedBody } from "actions";
import { RootState } from "configureStore";
import { recruitmentEntity, recruitmentStateUI } from "reducers";
import { connect } from "react-redux";
import { Dispatch } from "redux";
import Loader from "components/Common/Loader";
import DeleteConfirmationDialog from "components/Recruitment/Common/DeleteConfirmationDialog";
import BackButton from "components/Recruitment/Common/GeneralizedBackButton";

interface IgnoredBody {
  id: number;
  content: string;
}

interface AddIgnoredBodyPageProps {
  fetchIgnoredBodysData: () => void;
  IgnoredBodyOptions: any;
  fetchAddRejectedBody: (payload: { body: string }) => void;
  addRejectedBodyOptions: any;
  isRejectedBodyAdded:boolean;
  deleteblockedBodyData:(id:number)=>void;
  editrejectedBodyData: (payload: { id: number; body: string }) => void;
  isBlockedBody: any;

}

const AddIgnoredBodyPage: React.FC<AddIgnoredBodyPageProps> = ({
  fetchIgnoredBodysData,
  IgnoredBodyOptions,
  fetchAddRejectedBody,
  addRejectedBodyOptions,
  isRejectedBodyAdded,
  deleteblockedBodyData,
  editrejectedBodyData,
  isBlockedBody,
}) => {
  const [ignoredBodies, setIgnoredBodies] = useState<IgnoredBody[]>([]);
  const [text, setText] = useState("");
  const [open, setOpen] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [isClicked, setIsClicked] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [page, setPage] = useState(1);
const [rowsPerPage, setRowsPerPage] = useState(5);


  useEffect(() => {
    if (addRejectedBodyOptions && addRejectedBodyOptions.success) {
      toast.success("Added Body Successfully");
      fetchIgnoredBodysData();
      setOpen(false);
      setText("");
    }
  }, [addRejectedBodyOptions,isRejectedBodyAdded]);

  useEffect(() => {
    fetchIgnoredBodysData();
  }, [fetchIgnoredBodysData,isRejectedBodyAdded]);

  useEffect(() => {
    if (Array.isArray(IgnoredBodyOptions) && Array.isArray(IgnoredBodyOptions[0])) {
      const flatData = IgnoredBodyOptions[0].map((item: any) => ({
        id: item.id,
        content: item.body?.trim() || "",
      }));
      setIgnoredBodies(flatData);
    }
  }, [IgnoredBodyOptions]);

  const handleAddOrUpdate = () => {
    if (!text.trim()) return;
  
    if (editIndex !== null) {
      const updated = [...ignoredBodies];
      const idToEdit = ignoredBodies[editIndex].id;
      updated[editIndex].content = text.trim();
      setIgnoredBodies(updated);
      setEditIndex(null);
  
      editrejectedBodyData({ id: idToEdit, body: text.trim() });
  
      toast.success("Edited Body Successfully");
      setOpen(false);
      setText("");
  
      setTimeout(() => {
        fetchIgnoredBodysData();
      }, 500);
    } else {
      fetchAddRejectedBody({ body: text.trim() });
    
      setTimeout(() => {
        fetchIgnoredBodysData(); 
      }, 500); 
    
      toast.success("Added Body Successfully");
      setOpen(false);
      setText("");
    }
  };
  
  

  const handleEdit = (index: number) => {
    setText(ignoredBodies[index].content);
    setEditIndex(index);
    setOpen(true);
  };

  const handleDelete = (index: number) => {
    setDeleteIndex(index);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (deleteIndex !== null) {
      const idToDelete = ignoredBodies[deleteIndex].id; 
      deleteblockedBodyData(idToDelete);                
  
      const updated = ignoredBodies.filter((_, i) => i !== deleteIndex);
      setIgnoredBodies(updated);
      setText("");
      setEditIndex(null);
  
      setTimeout(() => {
        fetchIgnoredBodysData();
      }, 500);
  
      toast.success("Deleted Body Successfully");
      setDeleteIndex(null);
    }
    setDeleteDialogOpen(false);
  };
  

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setDeleteIndex(null);
  };

  return (
    <>
    <Loader state={!isBlockedBody} />
    <Box p={3}>
      <Card
        sx={{
          width: "95%",
          mx: "auto",
          borderRadius: "8px",
          padding: "24px",
          backgroundColor: "white",
          boxShadow: 2,
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" >
            <Typography
              variant="h6"
              sx={{
                fontFamily: "Montserrat-Semibold",
                fontWeight: 600,
                position: "relative",
                display: "inline-block",
                padding: "8px 16px",
                color: "primary.main",
                borderRadius: "4px",
                cursor: "pointer",
                transition: "background-color 0.3s ease-in-out, color 0.3s ease-in-out",
                backgroundColor: isClicked ? "rgba(25, 60, 109, 0.3)" : "transparent",
                "&::after": {
                  content: '""',
                  display: "block",
                  width: "100%",
                  height: "2px",
                  backgroundColor: "#193C6D",
                  position: "absolute",
                  bottom: "0px",
                  left: 0,
                },
              }}
            >
              Blocked Body
            </Typography>
            <Box display='flex' alignItems='center' justifyContent='space-between'>
              <BackButton tooltip='Go Back' />
            </Box>
          </Box>
          <Box marginBottom={'10px'} display='flex' alignItems='flex-start' justifyContent='flex-end'>
            <ActionButton
              sx={{ fontSize: '15px' }}
              variant="contained"
              onClick={() => {
                setText("");
                setEditIndex(null);
                setOpen(true);
              }}
            >
              Add Blocked Body
            </ActionButton>
          </Box>
        <Box>
          <Card sx={{ borderRadius: "4px" }}>
            <Table>
              <TableHead sx={{ bgcolor: "#193C6D" }}>
                <TableRow>
                  <TableCell
                    sx={{
                      fontFamily: "Montserrat-Medium",
                      fontWeight: 600,
                      color: "white",
                      pl: "250px",
                      fontSize:'17px'
                    
                    }}
                  >
                    Blocked Body
                  </TableCell>
                  <TableCell
                    sx={{
                      fontFamily: "Montserrat-Medium",
                      fontWeight: 600,
                      color: "white",
                      pr: "250px",
                      textAlign: "right",
                      fontSize:'17px'

                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>

              <TableBody>
              {ignoredBodies.length > 0 ? (
  ignoredBodies
    .slice((page-1) * rowsPerPage, page * rowsPerPage)
    .map((item, index) => (
      <TableRow key={item.id} sx={{ borderBottom: "1px solid #ddd" }}>

<TableCell
  sx={{
    pl: "250px",
    fontFamily: "Montserrat-Medium",
    fontSize: "15px",
    fontWeight: 500,
  }}
>
  {item.content}
</TableCell>

{/* <TableCell sx={{ pl: "250px", fontFamily: "Montserrat-Medium" }}>

  {item.content}
</TableCell> */}
        <TableCell sx={{ display: "flex", justifyContent: "flex-end", pr: "250px" }}>
        <Tooltip title="Edit">
  <IconButton
    sx={{ color: "#193C6D" }}
    onClick={() => handleEdit((page - 1) * rowsPerPage + index)} 
  >
    <EditIcon />
  </IconButton>
</Tooltip>
<Tooltip title="Delete">
  <IconButton
    sx={{ color: "#db3700" }}
    onClick={() => handleDelete((page - 1) * rowsPerPage + index)} 
  >
    <DeleteIcon />
  </IconButton>
</Tooltip>

        </TableCell>
      </TableRow>
    ))
) : (
  <TableRow>
    <TableCell colSpan={2} align="center" sx={{ fontFamily: "Montserrat-Medium", fontWeight: 600, py: 3, color: "gray" }}>
      No records found
    </TableCell>
  </TableRow>
)}

              </TableBody>
            </Table>
          </Card>
        </Box>
        <Box display="flex" justifyContent="flex-end" mt={2}>
  <Pagination
    count={Math.ceil(ignoredBodies.length / rowsPerPage)}
    page={page}
    onChange={(_, value) => setPage(value)}
    color="primary"
  />
</Box>

      </Card>

      <Modal open={open} onClose={() => setOpen(false)}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: 700,
            bgcolor: "white",
            boxShadow: 24,
            borderRadius: "8px",
            fontFamily: "Montserrat, sans-serif",
          }}
        >
          <Box
            sx={{
              backgroundColor: "#193C6D",
              color: "white",
              padding: "12px 16px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              borderTopLeftRadius: "8px",
              borderTopRightRadius: "8px",
            }}
          >
            <Typography variant="h5" sx={{ fontWeight: "bold" }}>
              {editIndex !== null ? "Edit" : "Add"} Blocked Body
            </Typography>
          </Box>

          <Box sx={{ padding: "16px" }}>
            <TextField
              fullWidth
              multiline
              minRows={4}
              placeholder="Blocked Body..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              variant="outlined"
              sx={{
                backgroundColor: "#f4f4f4",
                borderRadius: "4px",
                fontFamily: "Montserrat, sans-serif",
              }}
            />
            <Box display="flex" justifyContent="flex-end" mt={2} gap={2}>
              <ActionButton
                sx={{
                  backgroundColor: "#E2E2E2",
                  color: "#000000",
                  height: "40px",
                  fontSize: "15px",
                  borderRadius: "50px",
                  fontWeight: "bold",
                  fontFamily: "Montserrat-Semibold",
                  minWidth: "120px",
                  "&:hover": {
                    backgroundColor: "#E2E2E2",
                    color: "#000000",
                  },
                }}
                variant="outlined"
                onClick={() => setOpen(false)}
              >
                Cancel
              </ActionButton>
              <ActionButton
                sx={{
                  backgroundColor: "#193C6D",
                  color: "#FFFFFF",
                  fontSize: "15px",
                  height: "40px",
                  borderRadius: "50px",
                  fontWeight: "bold",
                  fontFamily: "Montserrat-Semibold",
                  minWidth: "120px",
                }}
                variant="contained"
                onClick={handleAddOrUpdate}
                disabled={!text.trim()}
              >
                {editIndex !== null ? "Update" : "Add"}
              </ActionButton>
            </Box>
          </Box>
        </Box>
      </Modal>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={cancelDelete}
        handleConfirm={confirmDelete}
      />
    </Box>
    </>
  );
};

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchIgnoredBodysData: () => dispatch(getBlockedBody.request()),
    fetchAddRejectedBody: (data:{}) => dispatch(addRejectedBody.request({data})),
    deleteblockedBodyData: (id: number) => dispatch(deleteRejectedBody.request({ id })),
    editrejectedBodyData: (payload: { id: number; body: string }) =>
      dispatch(editRejectedBody.request(payload)),
  };
  }

const mapStateToProps = (state: RootState) => {
  return {
    isBlockedBody:recruitmentStateUI.getRecruitment(state).isBlockedBody,
    IgnoredBodyOptions: recruitmentEntity.getRecruitment(state).getBlockedBody,
    addRejectedBodyOptions: recruitmentEntity.getRecruitment(state).addRejectedBody,
    isRejectedBodyAdded:recruitmentStateUI.getRecruitment(state).isRejectedBodyAdded
    
    
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(AddIgnoredBodyPage);
