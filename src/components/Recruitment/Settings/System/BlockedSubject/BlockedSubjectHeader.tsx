import { Box, Button, TextField, InputAdornment } from '@mui/material'
import SearchIcon from '@mui/icons-material/Search'
import { BlockedSubjectHeaderProps } from './BlockedSubjectType'

const BlockedSubjectHeader: React.FC<BlockedSubjectHeaderProps> = ({
  handleOpen,
  searchTerm,
  onSearchChange,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        gap: 2,
        width: '100%',
        '@media (max-width: 400px)': {
          flexDirection: 'column',
          alignItems: 'stretch',
        },
      }}
    >
      <TextField
        placeholder='Search Subjects'
        variant='outlined'
        size='small'
        value={searchTerm}
        onChange={onSearchChange}
        InputProps={{
          startAdornment: (
            <InputAdornment position='start'>
              <SearchIcon />
            </InputAdornment>
          ),
          sx: {
            '& input::placeholder': {
              opacity: 1,
              color: 'rgba(0, 0, 0, 0.6)',
            },
          },
        }}
        sx={{
          minWidth: '200px',
          '& .MuiOutlinedInput-root': {
            borderRadius: '24px',
            fontSize: '13px',
            width: '180px',
            fontFamily: 'Montserrat-Medium',
            '@media (max-width: 400px)': {
              width: '100%',
            },
            '&.Mui-focused': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#1976d2',
                borderWidth: '1px',
              },
            },
            '&:hover:not(.Mui-focused)': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(0, 0, 0, 0.23)',
              },
            },
          },
          '& .MuiOutlinedInput-input': {
            padding: '8.5px 14px 8.5px 0',
          },
        }}
      />

      <Button
        variant='contained'
        color='secondary'
        onClick={handleOpen}
        sx={{
          fontFamily: 'Montserrat-Medium',
          borderRadius: '24px',
          fontSize: '13px',
          flexShrink: 0,
          width: '180px',
          '@media (max-width: 400px)': {
            width: '100%',
          },
        }}
      >
        Add Blocked Subject
      </Button>
    </Box>
  )
}

export default BlockedSubjectHeader
