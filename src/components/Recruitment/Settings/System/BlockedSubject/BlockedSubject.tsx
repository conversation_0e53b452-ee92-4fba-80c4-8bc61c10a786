import { useEffect, useState } from 'react'
import { Box, Card, TextField, Button, useMediaQuery } from '@mui/material'
import BlockedSubjectHeader from './BlockedSubjectHeader'
import BlockedSubjectList from './BlockedSubjectList'
import BlockedSubjectDialog from './BlockedSubjectDialog'
import HandlePagination from '../../HandlePagination'
import BlockedSubjectTitle from './BlockedSubjectTitle'
import {
  addRejectedSubjects,
  deleteRejectedSubjects,
  editRejectedSubjects,
  fetchBlockedSubjects,
} from '../../../../../actions'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import DeleteConfirmationDialog from '../BlockedDomain/DeleteConfirmationDialog'
import { BlockSubjects } from './BlockedSubjectType'
import { BlockedSubjectProps } from './BlockedSubjectType'

const BlockedSubject: React.FC<BlockedSubjectProps> = ({
  blockedSubjectOptions,
  fetchblockedSubjectsData,
  addrejectedSubjectsData,
  editrejectedSubjectsData,
  deleteblockedSubjectsData,
  isBlockedSubjectData,
}) => {
  const [blockedSubjects, setBlockedSubjects] = useState<BlockSubjects[]>([])
  const [open, setOpen] = useState<boolean>(false)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)
  const [newBlockedSubject, setNewBlockedSubject] = useState<string>('')
  const [page, setPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [subjectExists, setSubjectExists] = useState<boolean>(false)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  const isMobile = useMediaQuery('(max-width:600px)')
  const isTablet = useMediaQuery('(max-width:900px)')

  const itemsPerPage = 10

  const numericAwareSort = (a: BlockSubjects, b: BlockSubjects, order: 'asc' | 'desc') => {
    const aSubject = a.subject.toLowerCase()
    const bSubject = b.subject.toLowerCase()
    
    const aStartsWithNumber = /^\d/.test(aSubject)
    const bStartsWithNumber = /^\d/.test(bSubject)
    
    if (aStartsWithNumber && bStartsWithNumber) {
      const aNum = parseInt(aSubject.match(/^\d+/)?.[0] || '0', 10)
      const bNum = parseInt(bSubject.match(/^\d+/)?.[0] || '0', 10)
      
      if (aNum !== bNum) {
        return order === 'asc' ? aNum - bNum : bNum - aNum
      }
    } else if (aStartsWithNumber && !bStartsWithNumber) {
      return order === 'asc' ? -1 : 1
    } else if (!aStartsWithNumber && bStartsWithNumber) {
      return order === 'asc' ? 1 : -1
    }
    
    return order === 'asc' 
      ? aSubject.localeCompare(bSubject)
      : bSubject.localeCompare(aSubject)
  }

  const sortedBlockedSubjects = [...blockedSubjects].sort((a, b) => numericAwareSort(a, b, sortOrder))
  
  const filteredBlockedSubjects = sortedBlockedSubjects.filter((subject) =>
    subject.subject.toLowerCase().includes(searchTerm.toLowerCase()),
  )
  
  const totalPages = Math.ceil(filteredBlockedSubjects.length / itemsPerPage)
  const paginatedBlockedSubjects = filteredBlockedSubjects.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleSort = () => {
    setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'))
  }

  const handleOpen = () => {
    setEditIndex(null)
    setNewBlockedSubject('')
    setOpen(true)
  }

  const handleClose = () => setOpen(false)

  const handleEdit = (id: number) => {
    const subject = blockedSubjects.find(sub => sub.id === id)
    if (subject) {
      setEditIndex(id)
      setNewBlockedSubject(subject.subject)
      setOpen(true)
    }
  }

  const handleDelete = (id: number) => {
    setDeleteIndex(id)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }
  
  const checkSubjectExists = (subject: string) => {
    const exists = blockedSubjects.some(
      (item) => item.subject.toLowerCase() === subject.toLowerCase(),
    )
    setSubjectExists(exists)
    return exists
  }
  
  const handleSaveBlockedSubject = (newBlockedSubject: string) => {
    if (checkSubjectExists(newBlockedSubject)) {
      return
    }

    if (editIndex !== null) {
      editrejectedSubjectsData({ id: editIndex, subject: newBlockedSubject })
      setBlockedSubjects((prev) =>
        prev.map((sub) =>
          sub.id === editIndex ? { ...sub, subject: newBlockedSubject } : sub,
        )
      )
    } else {
      addrejectedSubjectsData({ subject: newBlockedSubject })
      setBlockedSubjects((prev) =>
        [...prev, { id: Date.now(), subject: newBlockedSubject }]
      )
    }
    setTimeout(() => {
      fetchblockedSubjectsData()
    }, 500)
    handleClose()
  }

  const handleConfirmDelete = () => {
    if (deleteIndex !== null) {
      deleteblockedSubjectsData(deleteIndex)
      setTimeout(() => {
        fetchblockedSubjectsData()
      }, 500)
      setDeleteIndex(null)
    }
  }
  
  const handleSubjectChange = (value: string) => {
    setNewBlockedSubject(value)
    if (subjectExists) {
      setSubjectExists(false)
    }
  }
  
  useEffect(() => {
    fetchblockedSubjectsData()
  }, [])

  useEffect(() => {
    if (blockedSubjectOptions) {
      setBlockedSubjects([...blockedSubjectOptions])
    }
  }, [blockedSubjectOptions])

  return (
    <Box
      sx={{
        fontFamily: 'Montserrat-Medium',
        maxWidth: '100%',
        margin: isMobile ? '5px' : '10px',
      }}
    >
      <Card
        sx={{
          padding: isMobile ? '10px' : '20px',
          width: isMobile ? '100%' : isTablet ? '90%' : '95%',
          margin: isMobile ? '10px auto' : '20px auto',
        }}
      >
        <BlockedSubjectTitle />
        <BlockedSubjectHeader
          handleOpen={handleOpen}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
        />
        <BlockedSubjectList
          paginatedBlockedSubjects={paginatedBlockedSubjects}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          isBlockedSubjectData={isBlockedSubjectData}
          sortOrder={sortOrder}
          handleSort={handleSort}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: isMobile ? 'center' : 'flex-end',
            mt: 2,
            pr: isMobile ? 0 : 2,
          }}
        >
          <HandlePagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>
      <BlockedSubjectDialog
        open={open}
        handleClose={handleClose}
        newBlockedSubject={newBlockedSubject}
        setNewBlockedSubject={handleSubjectChange}
        handleSaveBlockedSubject={handleSaveBlockedSubject}
        editIndex={editIndex}
        subjectExists={subjectExists}
      />
      <DeleteConfirmationDialog
        open={deleteIndex !== null}
        handleClose={() => setDeleteIndex(null)}
        handleConfirm={handleConfirmDelete}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    blockedSubjectOptions: recruitmentEntity.getRecruitment(state).getBlockedSubjectData,
    isBlockedSubjectData: recruitmentStateUI.getRecruitment(state).isBlockedSubjectData,
  }
}
const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchblockedSubjectsData: () => dispatch(fetchBlockedSubjects.request()),
    addrejectedSubjectsData: (data: { subject: string }) =>
      dispatch(addRejectedSubjects.request({ data })),
    editrejectedSubjectsData: (payload: { id: number; subject: string }) =>
      dispatch(editRejectedSubjects.request(payload)),
    deleteblockedSubjectsData: (id: number) => dispatch(deleteRejectedSubjects.request({ id })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(BlockedSubject)
