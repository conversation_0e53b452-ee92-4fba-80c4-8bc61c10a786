import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, DialogTitle, DialogContent, <PERSON>alogA<PERSON>, Button, TextField } from '@mui/material'
import { BlockedSubjectDialogProps } from './BlockedSubjectType'

const BlockedSubjectDialog: React.FC<BlockedSubjectDialogProps> = ({
  open,
  handleClose,
  newBlockedSubject,
  setNewBlockedSubject,
  handleSaveBlockedSubject,
  editIndex,
  subjectExists,
}) => {
  const [originalValue, setOriginalValue] = useState('')
  const [localSubjectExists, setLocalSubjectExists] = useState(false)

  useEffect(() => {
    if (open && editIndex !== null) {
      setOriginalValue(newBlockedSubject.trim())
    } else if (open && editIndex === null) {
      setNewBlockedSubject('')
      setOriginalValue('')
    }
    setLocalSubjectExists(false)
  }, [open, editIndex])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewBlockedSubject(e.target.value)
    if (localSubjectExists) {
      setLocalSubjectExists(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const trimmedValue = newBlockedSubject.trim()

    if (!trimmedValue || (editIndex !== null && trimmedValue === originalValue)) {
      return
    }

    if (subjectExists) {
      setLocalSubjectExists(true)
      return
    }

    handleSaveBlockedSubject(trimmedValue)
  }

  const trimmedInput = newBlockedSubject.trim()
  const isButtonDisabled =
    !trimmedInput || (editIndex !== null && trimmedInput === originalValue) || localSubjectExists

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth='xs'
      PaperProps={{
        sx: { borderRadius: '8px', width: '40%', fontFamily: 'Montserrat, sans-serif' },
      }}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle
          sx={{
            bgcolor: '#193C6D',
            color: '#fff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '1rem',
            fontFamily: 'Montserrat-Medium, sans-serif',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          {editIndex !== null ? 'Edit Blocked Subject' : 'Add Blocked Subject'}
        </DialogTitle>
        <DialogContent sx={{ padding: '10px 20px', borderRadius: '24px' }}>
          <TextField
            fullWidth
            label='Blocked Subject'
            variant='outlined'
            value={newBlockedSubject}
            onChange={handleChange}
            error={localSubjectExists}
            helperText={localSubjectExists ? 'This subject is already blocked' : ''}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '28px',
                '& .MuiInputBase-input': {
                  fontFamily: 'Montserrat-Medium',
                  fontSize: '14px',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                  borderWidth: '2px',
                },
              },
              '& .MuiInputLabel-root': {
                fontFamily: 'Montserrat-Medium',
              },
              '& .MuiFormHelperText-root': {
                fontFamily: 'Montserrat-Medium',
                marginLeft: '5px',
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ padding: '0 20px 15px 20px', marginTop: '-5px' }}>
          <Button
            variant='contained'
            onClick={handleClose}
            sx={{
              backgroundColor: '#E2E2E2',
              color: '#000000',
              height: '35px',
              fontSize: '15px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              '&:hover': { backgroundColor: '#E2E2E2', color: '#000000' },
            }}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            variant='contained'
            disabled={isButtonDisabled}
            sx={{
              backgroundColor: isButtonDisabled ? '#cccccc' : '#193C6D',
              color: '#FFFFFF',
              fontSize: '15px',
              height: '35px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '90px',
              '&:hover': {
                backgroundColor: isButtonDisabled ? '#cccccc' : '#193C6D',
              },
            }}
          >
            {editIndex !== null ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default BlockedSubjectDialog
