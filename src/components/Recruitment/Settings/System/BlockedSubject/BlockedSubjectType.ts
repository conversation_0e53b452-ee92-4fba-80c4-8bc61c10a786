export interface BlockSubjects {
  id: number
  subject: string
}

export interface BlockedSubjectListProps {
  paginatedBlockedSubjects: BlockSubjects[]
  handleEdit: (id: number) => void
  handleDelete: (id: number) => void
  isBlockedSubjectData: boolean
  sortOrder: 'asc' | 'desc'
  handleSort: () => void
}

export interface BlockedSubjectHeaderProps {
  handleOpen: () => void
  searchTerm: string
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export interface BlockedSubjectDialogProps {
  open: boolean
  handleClose: () => void
  newBlockedSubject: string
  setNewBlockedSubject: (value: string) => void
  handleSaveBlockedSubject: (value: string) => void
  editIndex: number | null
  subjectExists?: boolean
}

export interface BlockedSubjectProps {
  blockedSubjectOptions: any
  fetchblockedSubjectsData: () => void
  addrejectedSubjectsData: (data: { subject: string }) => void
  editrejectedSubjectsData: (payload: { id: number; subject: string }) => void
  deleteblockedSubjectsData: (id: number) => void
  isBlockedSubjectData: boolean
}

export interface EditRejectedSubjectsPayload {
  id: number
  subject: string
}

export interface DeleteRejectedSubjectsPayload {
  id: number
}
