import React from 'react'
import { Box, Tabs, Tab, IconButton } from '@mui/material'
import { ArrowBack } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const TopHeading: React.FC = () => {
  const navigate = useNavigate()

  const handleBackClick = () => {
    navigate('/home/<USER>/settings')
  }

  return (
    <Box paddingRight="10px"
      sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}
    >
      <Tabs
        value={0}
        aria-label='Tabs for different tables'
        sx={{ display: 'flex', flexDirection: 'row' }}
      >
        <Tab
          label='Static Data'
          sx={{
            backgroundColor: 'rgba(0, 0, 0, 0)',
            fontWeight: '800',
            fontSize: 'medium',
            fontFamily: 'Montserrat-Medium',
            paddingBottom: 1,
            color: 'rgb(25, 60, 109)',
            '&.Mui-selected': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '&:hover': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '& .MuiTouchRipple-root': {
              display: 'none',
            },
          }}
        />
      </Tabs>

      <IconButton
        onClick={handleBackClick}
        sx={{
          color: 'rgb(25, 60, 109)',
          '&:hover': {
            backgroundColor: 'rgba(25, 60, 109, 0)',
          },
        }}
        aria-label='Go back to settings'
      >
        <ArrowBack />
      </IconButton>
    </Box>
  )
}

export default TopHeading
