import { useState, useEffect } from 'react'
import { Box, Paper, Table, TableBody, TableContainer, TableRow, TableCell } from '@mui/material'
import EditStaticData from './editStaticData'
import AddStaticData from './addStaticData'
import StaticHeader from './staticHeader'
import StaticPagination from './staticPagination'
import StaticTableHeader from './staticTableHeader'
import StaticTableRow from './staticTableRow'
import DeleteConfirmationDialog from '../../../Common/DeleteConfirmationDialog'
import { StaticData } from './initialStaticData'
import {
  fetchStaticData,
  addStaticData,
  editStaticData,
  deleteStaticData,
} from '../../../../../../src/actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
import { useNavigate } from 'react-router-dom'
import Loader from '../../../../Common/Loader'

function StaticTable(props: any) {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const rowsPerPage = 10
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')

  const [staticData, setStaticData] = useState<StaticData[]>([])
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedRound, setSelectedRound] = useState<StaticData | null>(null)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const { fetchStaticData, statictableData, addStaticData, editStaticData, deleteStaticData } =
    props

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        await setTimeout(()=> {fetchStaticData()
        },1000)      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [])
  useEffect(() => {
    if (statictableData) {
      setStaticData(statictableData)
    }
  }, [statictableData])

  const handleEditClick = (staticData: StaticData) => {
    setSelectedRound(staticData)
    setEditDialogOpen(true)
  }

  const handleEditSave = async () => {
    if (selectedRound) {
      setLoading(true)
      try {
        await editStaticData({ id: selectedRound.id, value: selectedRound.value })
        await setTimeout(()=> {fetchStaticData()
        },1000)      } finally {
        setLoading(false)
        setEditDialogOpen(false)
      }
    }
  }

  const handleAddSave = async (newStaticData: { key: string; value: string }) => {
    setLoading(true)
    try {
      await addStaticData(newStaticData)
      await setTimeout(()=> {fetchStaticData()
      },1000)    } finally {
      setLoading(false)
      setAddDialogOpen(false)
    }
  }

  const handleDeleteClick = (id: number) => {
    setDeleteId(id)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (deleteId !== null) {
      setLoading(true)
      try {
        await deleteStaticData({ id: deleteId })
        await setTimeout(()=> {fetchStaticData()
        },1000)      } finally {
        setLoading(false)
        setDeleteDialogOpen(false)
      }
    }
  }
  const handleSort = () => {
    setOrder(order === 'asc' ? 'desc' : 'asc')
  }
  const Data =
    Array.isArray(statictableData) && Array.isArray(statictableData[0]) ? statictableData[0] : []

  const filteredRounds = Data.filter((user) =>
    user.key.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const sortedUsers = [...filteredRounds].sort((a, b) => {
    return order === 'asc' ? a.key.localeCompare(b.key) : b.key.localeCompare(a.key)
  })
  const totalPages = Math.ceil(sortedUsers.length / rowsPerPage)
  const paginatedRounds = sortedUsers.slice((page - 1) * rowsPerPage, page * rowsPerPage)
  if(paginatedRounds?.length == 0 && page >1 && totalPages>0){
    setPage((prev)=>prev-1)
 }
  return (
    <Box sx={{ width: '100%', padding: 0 }}>
      {loading && <Loader state={loading} />}

      <StaticHeader
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        onAddClick={() => setAddDialogOpen(true)}
      />

      <Paper
        sx={{
          width: '100%',
          overflow: 'hidden',
          borderRadius: '0px',
          boxShadow: 3,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <TableContainer sx={{ position: 'relative', backgroundColor: '', maxWidth: '100%' }}>
          {loading && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)', // Light overlay effect
                zIndex: 10,
              }}
            >
              <Loader state={loading} />
            </Box>
          )}

          <Table stickyHeader>
            <StaticTableHeader sortOrder={order} handleSort={handleSort}/>
            <TableBody>
              {!loading && paginatedRounds.length > 0 ? (
                paginatedRounds.map((row) => (
                  <StaticTableRow
                    key={row.key}
                    row={row}
                    onEdit={handleEditClick}
                    onDelete={() => handleDeleteClick(row.id)}
                  />
                ))
              ) : !loading && paginatedRounds.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    sx={{ textAlign: 'center', fontFamily: 'Montserrat-Medium', color: 'gray' }}
                  >
                    No data found
                  </TableCell>
                </TableRow>
              ) : null}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', padding: 2 }}>
          <StaticPagination totalPages={totalPages} page={page} setPage={setPage} />
        </Box>
      </Paper>

      <EditStaticData
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        round={selectedRound}
        setRound={setSelectedRound}
        onSave={handleEditSave}
      />

      <AddStaticData
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        onSave={handleAddSave}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={() => setDeleteDialogOpen(false)}
        handleConfirm={handleDeleteConfirm}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    statictableData: recruitmentEntity.getRecruitment(state).getStaticData,
    responseSubmit: recruitmentEntity.getRecruitment(state).postStaticData,
    responseEdit: recruitmentEntity.getRecruitment(state).postEditStaticData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchStaticData: () => dispatch(fetchStaticData.request()),
    addStaticData: (data: any) => dispatch(addStaticData.request(data)),
    editStaticData: (data: any) => dispatch(editStaticData.request(data)),
    deleteStaticData: (data: { id: number }) => dispatch(deleteStaticData.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(StaticTable)
