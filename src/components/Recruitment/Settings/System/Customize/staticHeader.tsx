import React from 'react'
import { Box, Typography, Button } from '@mui/material'
import SearchInput from './SearchInput'

interface StaticHeaderProps {
  searchTerm: string
  setSearchTerm: (value: string) => void
  onAddClick: () => void
}

const StaticHeader: React.FC<StaticHeaderProps> = ({ searchTerm, setSearchTerm, onAddClick }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0px 10px',

      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '270px'  }}>
        <SearchInput label='Search by Key' value={searchTerm} onChange={setSearchTerm} />
      </Box>

      <Typography
        variant='h5'
        sx={{
          flexGrow: 1,
          color: 'rgb(0, 0, 0)',
          marginLeft:"24%",
          marginRight:"24%"
        }}
      >
      </Typography>

      <Button
        sx={{ borderRadius: '35px',fontFamily: 'Montserrat-Medium',fontSize:"medium"
        }}
        variant='contained'
        color='secondary'
        onClick={onAddClick}
      >
        Static Data
      </Button>
    </Box>
  )
}

export default StaticHeader
