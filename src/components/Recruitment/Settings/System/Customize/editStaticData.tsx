import React from 'react'
import {
  Dialog,
  DialogActions,
  Box,
  DialogContent,
  DialogTitle,
  Button,
  TextField,
} from '@mui/material'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'

interface EditStaticDataProps {
  open: boolean
  onClose: () => void
  round: { key: string; value: string } | null
  setRound: (round: any) => void
  onSave: () => void
}

const EditStaticData: React.FC<EditStaticDataProps> = ({
  open,
  onClose,
  round,
  setRound,
  onSave,
}) => {
  if (!round) return null

  return (
    <Dialog open={open} onClose={onClose} sx={{ '& .MuiPaper-root': { borderRadius: '16px' } }}>
      <DialogTitle margin='auto'>Edit Static Data</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label='Key'
          value={round.key}
          disabled
          margin='dense'
          sx={{
            borderRadius: '16px',
            '& .MuiOutlinedInput-root': { borderRadius: '16px' },
          }}
        />

        <Box
          sx={{
            width: '500px',
            backgroundColor: 'white',
            maxHeight: '250px',
            overflow: 'auto',
          }}
        >
          <CKEditor
            editor={ClassicEditor}
            config={{}}
            data={round.value}
            onReady={() => {}}
            onChange={(event, editor) => {
              const data = editor.getData()
              setRound({ ...round, value: data })
            }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Box display='flex' justifyContent='center' gap={1} mt={0}>
          <Button
            variant='contained'
            sx={{
              backgroundColor: 'rgb(226, 226, 226)',
              color: 'black',
              padding: '8px 16px',
              borderRadius: '25px',
              fontFamily: 'Montserrat, sans-serif',
              fontSize: '14px',
              minWidth: '100px',
              '&:hover': {
                backgroundColor: 'rgb(226, 226, 226)', 
                color:'black'
              },
            }}
            onClick={onClose}
          >
            CANCEL
          </Button>

          <Button
            type='submit'
            variant='contained'
            sx={{
              color: 'white',
              padding: '8px 16px',
              borderRadius: '25px',
              fontFamily: 'Montserrat, sans-serif',
              fontSize: '14px',
              minWidth: '100px',
            }}
            onClick={onSave}
          >
            UPDATE
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  )
}

export default EditStaticData
