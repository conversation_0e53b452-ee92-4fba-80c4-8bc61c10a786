import React from 'react'
import StaticTable from './staticTable'
import { Box, Paper, Typography } from '@mui/material'
import TopHeading from './topHeading'

const CustomizedScreen: React.FC = () => {
  return ( 
    <Box display="flex" justifyContent="center" paddingTop="15px" paddingLeft="20px" paddingRight="20px" alignItems="baseline" minHeight="100vh">
      <Paper elevation={3} sx={{ width: '98%', padding: '25px', backgroundColor: 'white', borderRadius: '1px' }}>
      <TopHeading/>
        <StaticTable />
      </Paper>
    </Box>
  )
}

export default CustomizedScreen
