import React, { useState } from 'react'
import { TableRow, TableCell, IconButton } from '@mui/material'
import { Edit, Delete, ExpandMore, ExpandLess } from '@mui/icons-material'
import { StaticData } from './initialStaticData'

interface StaticTableRowProps {
  row: StaticData
  onEdit: (staticData: StaticData) => void
  onDelete: (key: number) => void
}

// 🔧 Utility function to strip HTML tags
const stripHtmlTags = (html: string): string => {
  return html.replace(/<[^>]*>/g, '').trim()
}

const StaticTableRow: React.FC<StaticTableRowProps> = ({ row, onEdit, onDelete }) => {
  const [expanded, setExpanded] = useState(false)

  // Strip HTML tags from the value
  const cleanValue = stripHtmlTags(row.value)

  return (
    <TableRow hover sx={{ padding: '1px' }}>
      {/* Key Column */}
      <TableCell
        sx={{
          textAlign: 'center',
          padding: '4px',
          fontFamily: 'Montserrat-Medium',
          verticalAlign: 'baseline',
        }}
      >
        {row.key}
      </TableCell>

      {/* Value Column with Expand/Collapse */}
      <TableCell
        sx={{
          textAlign: 'center',
          padding: '4px',
          fontFamily: 'Montserrat-Medium',
          maxWidth: '150px',
          whiteSpace: expanded ? 'normal' : 'nowrap',
          overflow: expanded ? 'visible' : 'hidden',
          textOverflow: expanded ? 'clip' : 'ellipsis',
          wordBreak: 'break-word',
        }}
      >
        {expanded
          ? cleanValue
          : cleanValue.length > 100
          ? `${cleanValue.substring(0, 100)}...`
          : cleanValue}
      </TableCell>

      {/* Expand/Collapse Button */}
      <TableCell
        sx={{
          textAlign: 'center',
          padding: '4px',
          fontFamily: 'Montserrat-Medium',
          verticalAlign: 'baseline',
        }}
      >
        <IconButton onClick={() => setExpanded(!expanded)} size='small'>
          {expanded ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </TableCell>

      {/* Edit/Delete Buttons */}
      <TableCell
        sx={{
          textAlign: 'center',
          padding: '4px',
          fontFamily: 'Montserrat-Medium',
          verticalAlign: 'baseline',
        }}
      >
        <IconButton color='primary' onClick={() => onEdit(row)}>
          <Edit />
        </IconButton>
        <IconButton color='error' onClick={() => onDelete(row.id)}>
          <Delete />
        </IconButton>
      </TableCell>
    </TableRow>
  )
}

export default StaticTableRow
