import React from 'react'
import { TableHead, TableRow, TableCell, TableSortLabel } from '@mui/material'

interface StaticTableHeaderProps {
  sortOrder: 'asc' | 'desc'
  handleSort: () => void
}

const StaticTableHeader: React.FC<StaticTableHeaderProps> = ({ sortOrder, handleSort }) => {
  return (
    <TableHead>
      <TableRow sx={{ backgroundColor: '#1f2d3d', width: '100%' }}>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            fontFamily: 'Montserrat-Medium',
            backgroundColor: 'rgb(25,60,109)',
            cursor: 'pointer',
            width: '20%',
          }}
        >
          <TableSortLabel
            sx={{
              color: 'white',
              '&.Mui-active': {
                color: 'white',
              },
              '& .MuiTableSortLabel-icon': {
                color: 'white !important',
              },
            }}
            active={true}
            direction={sortOrder}
            onClick={handleSort}
          >
            Key
          </TableSortLabel>
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            fontFamily: 'Montserrat-Medium',
            backgroundColor: 'rgb(25,60,109)',
            width: '60%',
          }}
        >
          Value
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            fontFamily: 'Montserrat-Medium',
            backgroundColor: 'rgb(25,60,109)',
            padding: '20px',
          }}
        ></TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            fontFamily: 'Montserrat-Medium',
            backgroundColor: 'rgb(25,60,109)',
            width: '10%',
            padding: '20px',
          }}
        >
          Actions
        </TableCell>
      </TableRow>
    </TableHead>
  )
}

export default StaticTableHeader
