import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  useMediaQuery,
  useTheme,
  Box,
} from '@mui/material'
import { LocalizationProvider, DatePicker, TimePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { Dayjs } from 'dayjs'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { fetchAddDateTimeDetails, fetchEditDateTimeDetails } from '../../../../actions'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

interface ScheduleData {
  id?: number
  dateTime?: string
  id_round?: number
  round_name?: string
  date_time?: string
}

interface AddScheduleProps {
  open: boolean
  onClose: () => void
  roundOptions: any
  interviewerOption: any
  editData?: ScheduleData
  fetchAddDateTimeDetails: (data: ScheduleData) => void
  fetchEditDateTimeDetails: (data: ScheduleData) => void
  onSuccess?: () => void
}

const AddSchedule: React.FC<AddScheduleProps> = ({
  open,
  onClose,
  roundOptions,
  editData,
  fetchAddDateTimeDetails,
  fetchEditDateTimeDetails,
  onSuccess,
}) => {
  const [round, setRound] = useState('')
  const [date, setDate] = useState<Dayjs | null>(null)
  const [time, setTime] = useState<Dayjs | null>(null)

  const theme = useTheme()
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'))
  const isFormValid = round && date && time

  useEffect(() => {
    if (editData) {
      setRound(editData.round_name || '')
      if (editData.date_time) {
        const [editDate, editTime] = editData.date_time.split(' at ')
        setDate(editDate ? dayjs(editDate, 'MM-DD-YYYY') : null)
        setTime(editTime ? dayjs(editTime, 'hh:mm A') : null)
      }
    } else {
      setRound('')
      setDate(null)
      setTime(null)
    }
  }, [editData])

  const handleSubmit = () => {
    if (isFormValid) {
      const payload = {
        id: editData?.id,
        dateTime: `${date?.format('MM-DD-YYYY')} at ${time?.format('HH:mm A')}`,
        id_round: roundOptions.find((r: any) => r.round_name === round)?.id || 0,
      }

      if (editData) {
        fetchEditDateTimeDetails(payload)
        toast.success('Schedule updated successfully!')
      } else {
        fetchAddDateTimeDetails(payload)
        toast.success('Schedule added successfully!')

        setRound('')
        setDate(null)
        setTime(null)
      }
      setTimeout(() => {
        onSuccess?.()
        onClose()
      }, 300)
    }
  }

  const textFieldStyle = { '& .MuiOutlinedInput-root': { borderRadius: '30px' } }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth fullScreen={fullScreen}>
        <DialogTitle
          sx={{
            backgroundColor: '#1a2a50',
            color: 'white',
            fontWeight: 'bold',
            fontSize: { xs: '16px', sm: '18px' },
            padding: { xs: '12px', sm: '16px' },
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
          }}
        >
          {editData ? 'Edit Schedule' : 'Add Schedule'}
        </DialogTitle>
        <DialogContent sx={{ padding: { xs: '10px', sm: '15px' }, marginTop: '20px' }}>
          <Box display='flex' flexDirection='column' gap={0.25}>
            <TextField
              select
              fullWidth
              label='Round'
              margin='dense'
              variant='outlined'
              value={round}
              onChange={(e) => setRound(e.target.value)}
              sx={textFieldStyle}
              SelectProps={{ MenuProps: { PaperProps: { sx: { maxHeight: 200, width: 250 } } } }}
            >
              {roundOptions.map((round: any) => (
                <MenuItem key={round.id} value={round.round_name}>
                  {round.round_name}
                </MenuItem>
              ))}
            </TextField>
            <DatePicker
              label='Date'
              value={date}
              onChange={setDate}
              slotProps={{ textField: { fullWidth: true, margin: 'dense', variant: 'outlined' } }}
              sx={textFieldStyle}
            />
            <TimePicker
              label='Time'
              value={time}
              onChange={setTime}
              slotProps={{ textField: { fullWidth: true, margin: 'dense', variant: 'outlined' } }}
              sx={textFieldStyle}
            />
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'flex-end',
            paddingTop: '0px',
            paddingBottom: '30px',
            paddingRight: '20px',
          }}
        >
          <Button
            onClick={onClose}
            variant='contained'
            sx={{
              backgroundColor: '#e2e2e2',
              color: '#000',
              borderRadius: '40px',
              padding: '7px 16px',
              fontWeight: 'bold',
              '&:hover': { backgroundColor: 'rgb(226, 226, 226)', color: 'black' },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            color='primary'
            variant='contained'
            sx={{ borderRadius: '40px', padding: '7px 16px', fontWeight: 'bold' }}
            disabled={!isFormValid}
          >
            {editData ? 'Update' : 'Submit'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  )
}
const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchAddDateTimeDetails: (data: ScheduleData) =>
    dispatch(fetchAddDateTimeDetails.request({ data })),
  fetchEditDateTimeDetails: (data: ScheduleData) =>
    dispatch(fetchEditDateTimeDetails.request({ data })),
})

export default connect(null, mapDispatchToProps)(AddSchedule)
