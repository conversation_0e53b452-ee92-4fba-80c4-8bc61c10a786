import React, { useEffect, useState } from 'react'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  IconButton,
  Pagination,
} from '@mui/material'
import { Edit, Delete } from '@mui/icons-material'
import { connect } from 'react-redux'
import {
  fetchInterviewer,
  fetchRounds,
  fetchDateandTime,
  fetchDeleteDateTimeDetails,
} from '../../../../actions'
import { Dispatch } from 'redux'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { RootState } from '../../../../configureStore'
import HeaderSection from './HeaderSection'
import AddSchedule from './AddSchedule'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import Loader from 'components/Common/Loader'
import { toast } from 'react-toastify'
import dayjs from 'dayjs'

interface ScheduleData {
  id?: number
  dateTime: string
  id_round: number
  round_name?: string
}

interface ScheduleTableProps {
  roundOptions: any
  fetchRounds: () => void
  interviewerOptions: any
  fetchInterviewer: () => void
  scheduleOptions: any
  fetchDateandTime: () => void
  deleteDateTime: (id: number) => void
  getDateTimeData: any
}

const ScheduleTable: React.FC<ScheduleTableProps> = ({
  roundOptions,
  fetchRounds,
  interviewerOptions,
  fetchInterviewer,
  scheduleOptions,
  fetchDateandTime,
  deleteDateTime,
  getDateTimeData,
}) => {
  const [open, setOpen] = useState(false)
  const [page, setPage] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const [editData, setEditData] = useState<ScheduleData | undefined>(undefined)

  useEffect(() => {
    fetchRounds()
    fetchInterviewer()
    fetchDateandTime()
  }, [fetchDateandTime, fetchInterviewer, fetchRounds])

  const schedules = Array.isArray(scheduleOptions[0]) ? scheduleOptions[0] : []
  const roundData = Array.isArray(roundOptions[0]) ? roundOptions[0] : []

  const handleChangePage = (_event: unknown, newPage: number) => setPage(newPage)

  const handleOpenDialog = (id: number) => {
    setSelectedId(id)
    setOpenDialog(true)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
    setSelectedId(null)
  }

  const handleConfirmDelete = () => {
    if (selectedId !== null) deleteDateTime(selectedId)
    setTimeout(() => {
      fetchDateandTime()
      handleCloseDialog()
      toast.success('Schedule Deleted Success')
    }, 300)
  }

  const handleEdit = (schedule: ScheduleData) => {
    setEditData(schedule)
    setOpen(true)
  }
  const rowsPerPage = 10

  return (
    <>
      <Loader state={!getDateTimeData} />
      <Paper sx={{ px: '2rem', m: '1.5rem' }}>
        <HeaderSection setOpen={setOpen} />
        <Box>
          <TableContainer component={Paper} elevation={3}>
            <Table>
              <TableHead>
                <TableRow>
                  {['Date', 'Round Name', 'Actions'].map((header, index) => (
                    <TableCell
                      key={index}
                      align='center'
                      sx={{
                        fontWeight: 'bold',
                        backgroundColor: 'rgb(25, 60, 109)',
                        color: 'white',
                        fontFamily: 'Montserrat-Medium',
                        width: '30%',
                      }}
                    >
                      {header}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {schedules.length > 0 ? (
                  schedules.slice((page - 1) * rowsPerPage, page * rowsPerPage).map((schedule) => (
                    <TableRow key={schedule.id}>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {(() => {
                          const [datePart, timePartWithMeridiem] = schedule.date_time.split(' at ')
                          const cleanedTime = timePartWithMeridiem.replace(/\s?(AM|PM)$/i, '')
                          const formattedTime = dayjs(cleanedTime, 'HH:mm').format('hh:mm A')
                          return `${datePart} at ${formattedTime}`
                        })()}
                      </TableCell>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {schedule.round_name}
                      </TableCell>
                      <TableCell align='center'>
                        <IconButton color='primary' onClick={() => handleEdit(schedule)}>
                          <Edit />
                        </IconButton>
                        <IconButton color='error' onClick={() => handleOpenDialog(schedule.id)}>
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                      No matching records found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Box>
            <Pagination
              count={Math.ceil(schedules.length / rowsPerPage)}
              page={page}
              onChange={handleChangePage}
              color='primary'
              sx={{ display: 'flex', justifyContent: 'flex-end', my: 2, padding: '0px' }}
            />
          </Box>
          <DeleteConfirmationDialog
            open={openDialog}
            handleClose={handleCloseDialog}
            handleConfirm={handleConfirmDelete}
          />
        </Box>
        <AddSchedule
          open={open}
          onClose={() => {
            setOpen(false)
            setEditData(undefined)
          }}
          roundOptions={roundData}
          interviewerOption={interviewerOptions}
          editData={editData}
          onSuccess={fetchDateandTime}
        />
      </Paper>
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  roundOptions: recruitmentEntity.getRecruitment(state).getRoundData,
  interviewerOptions: recruitmentEntity.getRecruitment(state).getInterviewerData,
  scheduleOptions: recruitmentEntity.getRecruitment(state).getDateandTimeData,
  getDateTimeData: recruitmentStateUI.getRecruitment(state).getDateandTimeData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchRounds: () => dispatch(fetchRounds.request()),
  fetchInterviewer: () => dispatch(fetchInterviewer.request({})),
  fetchDateandTime: () => dispatch(fetchDateandTime.request()),
  deleteDateTime: (id: number) => dispatch(fetchDeleteDateTimeDetails.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(ScheduleTable)
