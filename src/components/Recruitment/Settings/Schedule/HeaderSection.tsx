import { Button, Box, Tabs, Tab } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import BackButton from 'components/Recruitment/Common/GeneralizedBackButton'

interface HeaderProps {
  setOpen: (value: boolean) => void
}

const HeaderSection: React.FC<HeaderProps> = ({ setOpen }) => {
  return (
    <>
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        mb={2}
        marginTop={'10px'}
        marginBottom={'0px'}
      >
        <Box paddingRight='16px' display='flex' alignItems='center' justifyContent='space-between' className='templates-page-tabs'>
          <Tabs value={0} aria-label='Tabs for different tables'>
            <Tab label='Schedules' />
          </Tabs>
          
        </Box>

        <BackButton tooltip='Go Back' />

      </Box>
      <Box marginBottom={'10px'} display='flex' alignItems='flex-start' justifyContent='flex-end'>
        <ActionButton onClick={() => setOpen(true)}>Add Schedule</ActionButton>
      </Box >
    </>
  )
}

export default HeaderSection
