import { InputAdornment, TextField, IconButton } from '@mui/material'
import SearchIcon from '@mui/icons-material/Search'
import ClearIcon from '@mui/icons-material/Clear'
import { useState } from 'react'

interface SearchInputProp {
  label: string
  value: string
  onChange: (value: string) => void
}

function SearchInput({ label, value, onChange }: SearchInputProp) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <TextField
      size='small'
      variant='outlined'
      label={isFocused ? label : ''}
      placeholder={isFocused ? '' : label}
      sx={{
        '& .MuiOutlinedInput-root': { borderRadius: '20px' },
        width: '100%',
      }}
      InputProps={{
        startAdornment: (
          <InputAdornment position='start'>
            <SearchIcon />
          </InputAdornment>
        ),
        endAdornment: (
          <InputAdornment position='end'>
            <IconButton
              size='small'
              onClick={() => onChange('')}
              sx={{ visibility: value ? 'visible' : 'hidden' }}
            >
              <ClearIcon />
            </IconButton>
          </InputAdornment>
        ),
      }}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
    />
  )
}

export default SearchInput
