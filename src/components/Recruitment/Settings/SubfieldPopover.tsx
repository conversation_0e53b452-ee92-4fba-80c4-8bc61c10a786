import React from 'react'
import { Popover, Paper, Typography, Box } from '@mui/material'
import { SvgIconComponent } from '@mui/icons-material'

interface SubfieldPopoverProps {
  anchorEl: null | HTMLElement
  onClose: () => void
  subfields: { icon: SvgIconComponent; label: string; route: string; isModal?: boolean }[]
  onSubfieldClick: (route: string, isModal?: boolean) => void
  
}

const SubfieldPopover: React.FC<SubfieldPopoverProps> = ({
  anchorEl,
  onClose,
  subfields,
  onSubfieldClick,
}) => {
  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      transformOrigin={{ vertical: 'top', horizontal: 'center' }}
      disableRestoreFocus 
      sx={{ mt: 1 }}
    >
      <Paper
        sx={{
          padding: '12px',
          backgroundColor: '#fff',
          color: '#193C6D',
          borderRadius: '8px',
          minWidth: '220px',
        }}
      >
        {subfields.length > 0 ? (
          subfields.map((subfield, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '8px 12px',
                marginY: '5px',
                cursor: 'pointer',
                borderRadius: '4px',
                '&:hover': { backgroundColor: 'rgba(25,60,109,0.1)' },
              }}
              onClick={() => {
                onSubfieldClick(subfield.route, subfield.isModal);
                onClose();
              }}
              
            >
              <subfield.icon sx={{ color: '#193C6D', fontSize: '1.2rem' }} />
              <Typography sx={{ fontSize: '1rem', color: '#193C6D', fontWeight: '500' }}>
                {subfield.label}
              </Typography>
            </Box>
          ))
        ) : (
          <Typography sx={{ textAlign: 'center', padding: '10px 0', color: '#193C6D' }}>
            No Subfields
          </Typography>
        )}
      </Paper>
    </Popover>
  )
}

export default SubfieldPopover