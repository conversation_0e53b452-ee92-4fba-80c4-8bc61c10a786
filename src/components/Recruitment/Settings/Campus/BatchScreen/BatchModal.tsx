import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from "@mui/material";

interface BatchModalProps {
  open: boolean;
  onClose: () => void;
  onAdd: (batch: string) => void;
  initialData?: string;
}
const BatchModal: React.FC<BatchModalProps> = ({ open, onClose, onAdd, initialData }) => {
  const [batch, setBatch] = useState("");

  useEffect(() => {
    if (initialData !== undefined) {
      setBatch(initialData);
    } else {
      setBatch("");
    }
  }, [initialData]);

  const handleAdd = () => {
    if (batch.trim()) {
      onAdd(batch);
      setBatch("");
      onClose();
    }
  };
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          bgcolor: "#1f3a5f",
          color: "white",
          textAlign: "center",
          fontFamily: "Montserrat-Medium",
          fontWeight: 600,
        }}
      >
        {initialData ? "Edit Batch" : "Add Batch"}
      </DialogTitle>
      <DialogContent sx={{ padding: "20px", borderRadius: "24px" }}>
        <TextField
          fullWidth
          label="Batch"
          variant="outlined"
          value={batch}
          onChange={(e) => {
            const value = e.target.value;
            if (/^[\d-]*$/.test(value) && value.length <= 10) {
              setBatch(value);
            }
          }}
          onKeyDown={(e) => {
            const isUpdateDisabled =
              !batch.trim() || (initialData !== undefined && batch === initialData);
            if (e.key === "Enter" && !isUpdateDisabled) {
              e.preventDefault();
              handleAdd();
            } else if (e.key === "Enter" && isUpdateDisabled) {
              e.preventDefault();
            }
          }}
          helperText={`${batch.length}/10`}
          sx={{
            fontFamily: "Montserrat, sans-serif",
            "& .MuiOutlinedInput-root": {
              borderRadius: "28px",
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#193C6D",
              },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderColor: "#193C6D",
                borderWidth: "2px",
              },
            },
          }}
        />
      </DialogContent>
      <DialogActions sx={{ padding: "20px" }}>
        <Button
          variant="contained"
          onClick={onClose}
          sx={{
            backgroundColor: "#E2E2E2",
            color: "#000000",
            height: "40px",
            fontSize: "15px",
            borderRadius: "50px",
            fontWeight: "bold",
            minWidth: "120px",
            fontFamily: "Montserrat-Semibold",
            "&:hover": { backgroundColor: "#E2E2E2", color: "#000000" },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleAdd}
          variant="contained"
          disabled={!batch.trim() || (initialData !== undefined && batch === initialData)}
          sx={{
            backgroundColor: "#193C6D",
            color: "#FFFFFF",
            fontSize: "15px",
            height: "40px",
            borderRadius: "50px",
            fontWeight: "bold",
            minWidth: "120px",
            fontFamily: "Montserrat-Semibold",
          }}
        >
          {initialData ? "Update" : "Add"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BatchModal;