import React from "react";
import { Pagination, Box, PaginationItem } from "@mui/material";
import { NavigateBefore, NavigateNext } from "@mui/icons-material";
import { ArrowBack, ArrowForward } from '@mui/icons-material'

interface PaginationProps {
  count: number
  page: number
  onChange: (event: React.ChangeEvent<unknown>, value: number) => void
}

const CustomPagination: React.FC<PaginationProps> = ({ count, page, onChange }) => {
  return (
    <Box display='flex' justifyContent='flex-end' mt={2}>
      <Pagination
        count={count}
        page={page}
        onChange={onChange}
        color='primary'
        shape='circular'
        size='medium'
        siblingCount={1}
        boundaryCount={1}
        renderItem={(item) => (
          <PaginationItem
            {...item}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'rgb(25, 60, 109)',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgb(20, 50, 90)',
                },
              },
              '&.MuiPaginationItem-previousNext': {
                backgroundColor: 'transparent',
                color: 'rgb(25, 60, 109)',
                '&.Mui-disabled': {
                  color: 'rgba(0, 0, 0, 0.26)',
                },
              },
            }}
            slots={{
              previous: NavigateBefore,
              next: NavigateNext,
            }}
          />
        )}
      />
    </Box>
  )
}

export default CustomPagination
