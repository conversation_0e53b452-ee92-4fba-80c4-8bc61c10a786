import React from 'react'
import { Card, CardContent, Typography } from '@mui/material'
import { SvgIconComponent } from '@mui/icons-material'

interface MenuCardProps {
  icon: SvgIconComponent
  label: string
  onClick: (event: React.MouseEvent<HTMLDivElement>) => void
  subfieldColor?: string 
}

const MenuCard: React.FC<MenuCardProps> = ({
  icon: Icon,
  label,
  onClick,
  subfieldColor = '#193C6D',
}) => {
  return (
    <Card
      sx={{
        backgroundColor: 'white',
        boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
        borderRadius: '8px',
        border: '2px solid #193C6D',
        transition: 'transform 0.2s ease-in-out, border-color 0.2s',
        cursor: 'pointer',
        '&:hover': { transform: 'scale(1.05)', backgroundColor: '#f0f0f0', borderColor: '#1E4B8C' },
      }}
      onClick={onClick}
    >
      <CardContent sx={{ textAlign: 'center', padding: '20px' }}>
        <Icon sx={{ color: subfieldColor }} />
        <Typography
          sx={{ marginTop: 1, fontSize: '1rem', fontWeight: 'bold', color: subfieldColor }}
        >
          {label}
        </Typography>
      </CardContent>
    </Card>
  )
}

export default MenuCard