import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from "@mui/material";

interface QualificationModalProps {
  open: boolean;
  onClose: () => void;
  onAdd: (qualification: string) => void;
  initialData?: string;
}

const QualificationModal: React.FC<QualificationModalProps> = ({ open, onClose, onAdd, initialData }) => {
  const [qualification, setQualification] = useState("");

  useEffect(() => {
    if (initialData !== undefined) {
      setQualification(initialData);
    } else {
      setQualification("");
    }
  }, [initialData]);

  const handleAdd = () => {
    if (qualification.trim()) {
      onAdd(qualification);
      setQualification("");
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          bgcolor: "#1f3a5f",
          color: "white",
          textAlign: "center",
          fontFamily: "Montserrat-Medium",
          fontWeight: 600,
        }}
      >
        {initialData ? "Edit Qualification" : "Add Qualification"}
      </DialogTitle>
      <DialogContent sx={{ padding: "20px", borderRadius: "24px" }}>
        <TextField
          fullWidth
          label="Qualification"
          variant="outlined"
          value={qualification}
          onChange={(e) => setQualification(e.target.value)}
          onKeyDown={(e) => {
            const isUpdateDisabled =
              !qualification.trim() || (initialData !== undefined && qualification === initialData);
            if (e.key === "Enter" && !isUpdateDisabled) {
              e.preventDefault();
              handleAdd();
            }
          }}
          inputProps={{ maxLength: 100 }}
          helperText={`${qualification.length}/100`}
          sx={{
            fontFamily: "Montserrat, sans-serif",
            "& .MuiOutlinedInput-root": {
              borderRadius: "28px",
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#193C6D",
              },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderColor: "#193C6D",
                borderWidth: "2px",
              },
            },
          }}
        />
      </DialogContent>
      <DialogActions sx={{ padding: "20px" }}>
        <Button
          variant="contained"
          onClick={onClose}
          sx={{
            backgroundColor: "#E2E2E2",
            color: "#000000",
            height: "40px",
            fontSize: "15px",
            borderRadius: "50px",
            fontWeight: "bold",
            fontFamily: "Montserrat-Semibold",
            minWidth: "120px",
            "&:hover": { backgroundColor: "#E2E2E2", color: "#000000" },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleAdd}
          variant="contained"
          disabled={!qualification.trim() || (initialData !== undefined && qualification === initialData)}
          sx={{
            backgroundColor: "#193C6D",
            color: "#FFFFFF",
            fontSize: "15px",
            height: "40px",
            borderRadius: "50px",
            fontWeight: "bold",
            minWidth: "120px",
            fontFamily: "Montserrat-Semibold",
          }}
        >
          {initialData ? "Update" : "Add"}
        </Button>
      </DialogActions>

    </Dialog>
  );
};

export default QualificationModal;