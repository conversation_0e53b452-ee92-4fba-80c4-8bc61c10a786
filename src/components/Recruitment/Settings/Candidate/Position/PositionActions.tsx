import React from 'react'
import { IconButton } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'

interface Props {
  onEdit: () => void
  onDelete: () => void
}

const PositionActions: React.FC<Props> = ({ onEdit, onDelete }) => {
  return (
    <>
      <IconButton onClick={onEdit} color='primary'>
        <EditIcon />
      </IconButton>
      <IconButton onClick={onDelete} color='error'>
        <DeleteIcon />
      </IconButton>
    </>
  )
}

export default PositionActions
