import React, { useEffect, useState } from 'react'
import { Box } from '@mui/material'
import PositionHeader from './PositionHeader'
import PositionTable from './PositionTable'
import { addJobPosition, fetchCandidatePosition } from '../../../../../actions'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from 'reducers'

interface Position {
  id: number
  name: string
  job_description: string
}

interface Props {
  fetchCandidatePositionData: () => void
  candidatepositionOptions: Position[]
  deletePositionDetailsOptions: { message: string }
}

const CandidatePositionPage: React.FC<Props> = ({
  fetchCandidatePositionData,
  candidatepositionOptions,
  deletePositionDetailsOptions,
}) => {
  const [searchText, setSearchText] = useState('')
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    // Fetch data when component mounts or when refresh<PERSON><PERSON> changes
    fetchCandidatePositionData()
  }, [fetchCandidatePositionData, deletePositionDetailsOptions, refreshKey])

  // Add a focus listener to refresh data when returning to this page
  useEffect(() => {
    const handleFocus = () => {
      setRefreshKey(prev => prev + 1)
    }
    
    window.addEventListener('focus', handleFocus)
    
    return () => {
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const filteredPositions = candidatepositionOptions.filter(
    (position) =>
      position.name.toLowerCase().includes(searchText.toLowerCase()) ||
      position.job_description.toLowerCase().includes(searchText.toLowerCase()),
  )

  return (
    <Box
      sx={{
        backgroundColor: '#f4f6f8',
        minHeight: '80vh',
        padding: 1,
        marginTop: '10px',
      }}
    >
      <PositionHeader searchText={searchText} setSearchText={setSearchText} />

      <PositionTable positions={filteredPositions} />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    candidatepositionOptions:
      state?.entities?.recruitment?.getCandidatePositionData?.map((position: any) => ({
        id: position?.id || position?.job_id,
        name: position?.name || position?.title,
        job_description: position?.job_description || position?.description,
      })) || [],
    deletePositionDetailsOptions: recruitmentEntity.getRecruitment(state).deletepositionDetails,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchCandidatePositionData: () => dispatch(fetchCandidatePosition.request()),
    addJobPosition: (data: { data: { name: string; job_description: string } }) =>
      dispatch(addJobPosition.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CandidatePositionPage)
