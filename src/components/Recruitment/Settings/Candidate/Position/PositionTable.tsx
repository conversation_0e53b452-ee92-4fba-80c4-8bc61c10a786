import React, { useEffect, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Pagination,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { useNavigate } from 'react-router-dom'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { deletepositionDetails } from 'actions'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { toast } from 'react-toastify'
 
interface Position {
  id: number
  name: string
  job_description: string
}
 
interface Props {
  positions: Position[]
  deletepositionDetails: (data: { id: number }) => void
  deletePositionDetailsOptions: { message: string }
  resetPositionDetails : ()=>void
}
 
 
const PositionTable: React.FC<Props> = ({
  positions,
  deletepositionDetails,
  deletePositionDetailsOptions,
  resetPositionDetails,
}) => {
  const [page, setPage] = useState(1)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedPositionId, setSelectedPositionId] = useState<number | null>(null)
  const rowsPerPage = 10
  const navigate = useNavigate()
 
  useEffect(() => {
    if (deletePositionDetailsOptions?.message) {
      toast.success(deletePositionDetailsOptions.message)
      resetPositionDetails();
    }
  }, [deletePositionDetailsOptions])
 
  // Reset page to 1 when positions change (e.g., when search is applied)
  useEffect(() => {
    setPage(1)
  }, [positions])
 
  const handleChangePage = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
  }
 
  const handleEdit = (position: Position) => {
    navigate('/home/<USER>/settings/candidate/position/add-position', {
      state: { positionData: position },
    })
  }
 
  const handleDeleteClick = (id: number) => {
    setSelectedPositionId(id)
    setDeleteDialogOpen(true)
  }
 
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false)
    setSelectedPositionId(null)
  }
 
  const handleDeleteConfirm = () => {
    if (selectedPositionId !== null) {
      deletepositionDetails({ id: selectedPositionId })
    }
    setDeleteDialogOpen(false)
  }
 
  // Sort positions alphabetically by name (case-insensitive)
  const sortedPositions = [...positions].sort((a, b) => 
    a.name.toLowerCase().localeCompare(b.name.toLowerCase())
  )
 
  const paginatedPositions = sortedPositions.slice((page - 1) * rowsPerPage, page * rowsPerPage)
 
  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold' }}>
                Job Name
              </TableCell>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold' }}>
                Job Description
              </TableCell>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold', textAlign: 'center' }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedPositions.length > 0 ? (
              paginatedPositions.map((position) => (
                <TableRow key={position.id}>
                  <TableCell>{position.name}</TableCell>
                  <TableCell>{position.job_description}</TableCell>
                  <TableCell align='center'>
                    <IconButton color='primary' onClick={() => handleEdit(position)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton color='error' onClick={() => handleDeleteClick(position.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} align='center'>
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
 
      {sortedPositions.length > rowsPerPage && (
        <Pagination
          count={Math.ceil(sortedPositions.length / rowsPerPage)}
          page={page}
          onChange={handleChangePage}
          color='primary'
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: 2,
          }}
        />
      )}
 
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleDeleteDialogClose}
        handleConfirm={handleDeleteConfirm}
        message='Are you sure you want to Delete this Entry?'
      />
    </>
  )
}
 
const mapStateToProps = (state: RootState) => ({
  deletePositionDetailsOptions: recruitmentEntity.getRecruitment(state).deletepositionDetails,
})
 
const mapDispatchToProps = (dispatch: Dispatch) => ({
  deletepositionDetails: (data: any) => dispatch(deletepositionDetails.request({ data })),
  resetPositionDetails: () => dispatch(deletepositionDetails.reset()),
})
 
export default connect(mapStateToProps, mapDispatchToProps)(PositionTable)
 