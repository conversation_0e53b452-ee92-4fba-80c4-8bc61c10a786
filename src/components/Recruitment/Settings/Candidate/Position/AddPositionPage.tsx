import React, { useState, useEffect } from 'react'
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON><PERSON>, Button } from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { addJobPosition, submitpositionDetails } from 'actions'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'

interface Position {
  id?: number
  name: string
  job_description: string
}

interface Props {
  addJobPosition: (data: { data: Position }) => void
  submitpositionDetails: (data: { data: Position }) => void
}
const MAX_TITLE_LENGTH = 50

const AddPositionPage: React.FC<Props> = ({ addJobPosition, submitpositionDetails }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const editingPosition = location.state?.positionData as Position | undefined

  const [jobTitle, setJobTitle] = useState('')
  const [jobDescription, setJobDescription] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [initialValues, setInitialValues] = useState({ name: '', job_description: '' })
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    if (editingPosition) {
      setJobTitle(editingPosition.name)
      setJobDescription(editingPosition.job_description)
      setIsEditing(true)
      setInitialValues({
        name: editingPosition.name,
        job_description: editingPosition.job_description
      })
    }
  }, [editingPosition])

  useEffect(() => {
    if (isEditing) {
      const changed = 
        jobTitle !== initialValues.name || 
        jobDescription !== initialValues.job_description;
      setHasChanges(changed);
    }
  }, [jobTitle, jobDescription, initialValues, isEditing])

  const isFormValid = jobTitle.trim() !== '' && jobDescription.trim() !== '' && (!isEditing || hasChanges)

  const handleSubmit = () => {
    const positionData = {
      name: jobTitle,
      job_description: jobDescription,
      ...(isEditing && editingPosition?.id && { id: editingPosition.id }),
    }

    if (isEditing) {
      submitpositionDetails({ data: positionData })
      toast.success('Position Edited Successfully!', {
        autoClose: 2000 // Reduced toaster time to 2 seconds
      })
    } else {
      addJobPosition({ data: positionData })
      toast.success('Position Added Successfully!', {
        autoClose: 2000 // Reduced toaster time to 2 seconds
      })
    }

    // Navigate after a short delay to ensure API call completes
    setTimeout(() => {
      navigate('/home/<USER>/settings/candidate/position')
    }, 300)
  }

  const handleCancel = () => {
    navigate('/home/<USER>/settings/candidate/position')
  }

  return (
    <Box
      sx={{
        backgroundColor: '#ffffff',
        padding: 6,
        maxWidth: '700px',
        width: '100%',
        margin: '50px auto',
        boxShadow: '0px 4px 12px rgba(0,0,0,0.1)',
        borderRadius: '8px',
        boxSizing: 'border-box',
      }}
    >
      <Typography
        variant='h6'
        fontWeight='600'
        sx={{
          textAlign: 'center',
          color: 'rgb(25, 60, 109)',
          marginBottom: 3,
        }}
      >
        {isEditing ? 'Edit Position' : 'Add Position'}
      </Typography>

      <TextField
        label='Job Title'
        value={jobTitle}
        onChange={(e) => setJobTitle(e.target.value)}
        fullWidth
        variant='outlined'
        sx={{ marginBottom: 2 }}
        inputProps={{
          maxLength: MAX_TITLE_LENGTH,
        }}
      />
      <Typography
        variant='caption'
        sx={{
          display: 'block',
          textAlign: 'right',
          color: jobTitle.length === MAX_TITLE_LENGTH ? 'error.main' : 'text.secondary',
          mb: 2,
        }}
      >
        {jobTitle.length}/{MAX_TITLE_LENGTH}
      </Typography>

      <TextField
        label='Job Description'
        value={jobDescription}
        onChange={(e) => setJobDescription(e.target.value)}
        fullWidth
        multiline
        rows={4}
        variant='outlined'
        sx={{ marginBottom: 2 }}
      />

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 1,
          marginTop: 3,
        }}
      >
        <Button
          onClick={handleCancel}
          variant='text'
          color='primary'
          sx={{
            textTransform: 'none',
            fontFamily: 'Montserrat-SemiBold',
            fontWeight: '700',
            borderRadius: '20px',
            fontSize: '13px',
            color: 'black',
            backgroundColor: 'rgb(226, 226, 226)',
            padding: '6px 8px',
            boxShadow: 'none',
            marginTop: '10px',
            marginBottom: '-4px',
            width: '100px',
            fontStretch: '100%',
            '&:hover': {
              backgroundColor: 'rgb(210, 210, 210)',
            },
          }}
        >
          Cancel
        </Button>

        <Button
          onClick={handleSubmit}
          variant='text'
          color='primary'
          disabled={!isFormValid}
          sx={{
            textTransform: 'none',
            fontFamily: 'Montserrat, sans-serif',
            fontWeight: '400',
            borderRadius: '20px',
            fontSize: '13px',
            color: isFormValid ? 'white' : '#a0a0a0',
            backgroundColor: isFormValid ? '#193c6d' : '#e2e2e2',
            marginTop: '10px',
            marginBottom: '-4px',
            fontStretch: '100%',
            width: '100px',
            '&.Mui-disabled': {
              color: '#b0b0b0',
              backgroundColor: '#f5f5f5',
              pointerEvents: 'none',
            },
            '&:hover': {
              backgroundColor: isFormValid ? 'rgb(25,60,109)' : 'transparent',
              color: isFormValid ? 'white' : '#b0b0b0',
            },
          }}
        >
          {isEditing ? 'Update' : 'Submit'}
        </Button>
      </Box>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    candidatepositionOptions:
      state?.entities?.recruitment?.getCandidatePositionData?.map((position: any) => ({
        id: position?.id || position?.job_id,
        name: position?.name || position?.title,
        job_description: position?.job_description || position?.description,
      })) || [],
    submissionState: state?.entities?.recruitment?.submitpositionDetails || {},
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    addJobPosition: (data: { data: Position }) => dispatch(addJobPosition.request(data)),
    submitpositionDetails: (data: { data: Position }) =>
      dispatch(submitpositionDetails.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddPositionPage)
