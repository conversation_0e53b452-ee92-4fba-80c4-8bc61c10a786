import { useEffect, useState } from 'react'
import { Box, Card } from '@mui/material'
import ExperienceHeader from './ExperienceHeader'
import ExperienceList from './ExperienceList'
import ExperienceDialog from './ExperienceDialog'
import HandlePagination from '../../HandlePagination'
import ExperienceTitle from './ExperienceTitle'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import {
  addJobExperiences,
  deleteJobExperiences,
  editJobExperiences,
  fetchExperiences,
} from '../../../../../actions'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import DeleteConfirmationDialog from '../../../../../components/Recruitment/Settings/System/BlockedDomain/DeleteConfirmationDialog'
import { Experience } from './ExperienceTableType'
import { ExperienceTableProps } from './ExperienceTableType'

const ExperienceTable: React.FC<ExperienceTableProps> = ({
  experienceOptions,
  fetchExperiencesData,
  addExperiencesData,
  editExperiencesData,
  deleteExperiencesData,
  isExperienceData,
}) => {
  const [experiences, setExperiences] = useState<Experience[]>([])

  const [open, setOpen] = useState<boolean>(false)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [newExperience, setNewExperience] = useState<string>('')
  const [page, setPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState<string>('')
  
  const [experienceExists, setExperienceExists] = useState<boolean>(false)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  const itemsPerPage = 10

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  const numericAwareSort = (a: Experience, b: Experience, order: 'asc' | 'desc') => {
    const getNumber = (str: string) => {
      const numMatch = str.match(/\d+\.?\d*/)
      return numMatch ? parseFloat(numMatch[0]) : 0
    }

    const hasAdditionalChars = (str: string) => {
      const numMatch = str.match(/\d+\.?\d*/)
      if (!numMatch) return false
      return str.length > numMatch[0].length
    }

    const numA = getNumber(a.experience)
    const numB = getNumber(b.experience)
    const aHasExtra = hasAdditionalChars(a.experience)
    const bHasExtra = hasAdditionalChars(b.experience)

    if (numA !== numB) {
      return order === 'asc' ? numA - numB : numB - numA
    } else if (aHasExtra !== bHasExtra) {
      return order === 'asc' ? (aHasExtra ? 1 : -1) : aHasExtra ? -1 : 1
    } else {
      return order === 'asc'
        ? a.experience.localeCompare(b.experience)
        : b.experience.localeCompare(a.experience)
    }
  }

  const sortedExperiences = [...experiences].sort((a, b) => numericAwareSort(a, b, sortOrder))
  
  const filteredExperiences = sortedExperiences.filter((exp) =>
    exp.experience.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const totalPages = Math.ceil(filteredExperiences.length / itemsPerPage)
  const paginatedExperiences = filteredExperiences.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleSort = () => {
    setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'))
  }

  const handleOpen = () => {
    setEditIndex(null)
    setNewExperience('')
    setOpen(true)
  }

  const handleClose = () => setOpen(false)

  const handleEdit = (id: number) => {
    const experience = experiences.find(exp => exp.id === id)
    if (experience) {
      setEditIndex(id)
      setNewExperience(experience.experience)
      setOpen(true)
    }
  }

  const isWithinRange = (value: number, range: string): boolean => {
    const [start, end] = range.match(/[\d.]+/g)?.map(parseFloat) || []
    return start !== undefined && end !== undefined && value >= start && value <= end
  }
  const parseExperienceValue = (value: string): number => {
    const numbers = value.match(/[\d.]+/g)
    if (!numbers) return 0
    if (value.includes('-')) {
      return parseFloat(numbers[0])
    }
    return parseFloat(numbers[0])
  }
  const checkExperienceExists = (experience: string): boolean => {
    const newValue = parseExperienceValue(experience)

    return experiences.some((exp) => {
      const existing = exp.experience.toLowerCase()
      const existingValue = parseExperienceValue(existing)

      if (existing.includes('-')) {
        return isWithinRange(newValue, existing)
      } else {
        return newValue === existingValue
      }
    })
  }

  const handleSaveExperience = (newExperience: string) => {
    if (checkExperienceExists(newExperience)) {
      return
    }

    if (editIndex !== null) {
      editExperiencesData({
        id: editIndex,
        experience: newExperience,
      })

      setExperiences((prev) =>
        prev.map((exp) =>
          exp.id === editIndex ? { ...exp, experience: newExperience } : exp,
        )
      )
    } else {
      addExperiencesData({ experience: newExperience })

      setExperiences((prev) =>
        [...prev, { id: Date.now(), experience: newExperience }]
      )
    }

    setTimeout(() => {
      fetchExperiencesData()
    }, 500)

    handleClose()
  }

  const handleDelete = (id: number) => {
    setDeleteId(id)
  }

  const handleConfirmDelete = () => {
    if (deleteId !== null) {
      deleteExperiencesData(deleteId)
      setTimeout(() => {
        fetchExperiencesData()
      }, 500)
      setDeleteId(null)
    }
  }

  useEffect(() => {
    fetchExperiencesData()
  }, [fetchExperiencesData])

  useEffect(() => {
    if (experienceOptions) {
      setExperiences([...experienceOptions])
    }
  }, [experienceOptions])

  return (
    <Box sx={{ fontFamily: 'Montserrat-Medium', maxWidth: '100%', margin: '10px' }}>
      <Card sx={{ padding: '20px', width: '95%', margin: '20px auto' }}>
        <ExperienceTitle />
        <ExperienceHeader
          handleOpen={handleOpen}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
        />
        <ExperienceList
          experiences={paginatedExperiences}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          isExperienceData={isExperienceData}
          sortOrder={sortOrder}
          handleSort={handleSort}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
          <HandlePagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>

      <ExperienceDialog
        open={open}
        handleClose={handleClose}
        newExperience={newExperience}
        setNewExperience={setNewExperience}
        handleSaveExperience={handleSaveExperience}
        editIndex={editIndex}
        experienceExists={experienceExists}
        checkExperienceExists={checkExperienceExists}
      />

      <DeleteConfirmationDialog
        open={deleteId !== null}
        handleClose={() => setDeleteId(null)}
        handleConfirm={handleConfirmDelete}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  experienceOptions: recruitmentEntity.getRecruitment(state).getExperienceData,
  isExperienceData: recruitmentStateUI.getRecruitment(state).isExperienceData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchExperiencesData: () => dispatch(fetchExperiences.request()),
  addExperiencesData: (data: { experience: string }) =>
    dispatch(addJobExperiences.request({ data })),
  editExperiencesData: (payload: { id: number; experience: string }) =>
    dispatch(editJobExperiences.request(payload)),
  deleteExperiencesData: (id: number) => dispatch(deleteJobExperiences.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(ExperienceTable)
