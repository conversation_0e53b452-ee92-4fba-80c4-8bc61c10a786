import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  <PERSON><PERSON><PERSON>,
  Card,
  TableSortLabel,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { ExperienceListProps } from './ExperienceTableType'
import Loader from 'components/Common/Loader'
import { StyledTableCell } from 'components/Common/CommonStyles'

const ExperienceList: React.FC<ExperienceListProps> = ({
  experiences,
  handleEdit,
  handleDelete,
  isExperienceData,
  sortOrder,
  handleSort,
}) => {
  return (
    <>
      <Loader state={!isExperienceData} />
      <Card sx={{ borderRadius: '4px', overflow: 'hidden' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#193C6D' }}>
            <TableRow sx={{ height: '50px !important' }}>
              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Semibold',
                  color: 'white',
                  fontSize: '13px !important',
                  width: '50% !important',
                }}
              >
                Experience
                <TableSortLabel
                  active
                  direction={sortOrder}
                  onClick={handleSort}
                  sx={{
                    color: 'white',
                    '& .MuiTableSortLabel-icon': { color: 'white !important' },
                  }}
                ></TableSortLabel>
              </StyledTableCell>

              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  color: 'white',
                  width: '50% !important',
                  fontSize: '13px !important',
                }}
              >
                Actions
              </StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {experiences.length > 0 ? (
              experiences.map((exp) => (
                <TableRow
                  key={exp.id}
                  sx={{ borderBottom: '1px solid #ddd', height: '50px !important' }}
                >
                  <StyledTableCell
                    sx={{ width: '50% !important', fontFamily: 'Montserrat-Medium' }}
                  >
                    {exp.experience}
                  </StyledTableCell>
                  <StyledTableCell
                    sx={{
                      textAlign: 'right',
                      width: '50% !important',
                      fontFamily: 'Montserrat-Medium',
                      fontSize: '12px !important',
                    }}
                  >
                    <Tooltip title='Edit'>
                      <IconButton sx={{ color: '#193C6D' }} onClick={() => handleEdit(exp.id)}>
                        <EditIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title='Delete'>
                      <IconButton sx={{ color: '#db3700' }} onClick={() => handleDelete(exp.id)}>
                        <DeleteIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </StyledTableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  align='center'
                  sx={{ fontFamily: 'Montserrat-Medium', color: '#888' }}
                >
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
    </>
  )
}

export default ExperienceList
