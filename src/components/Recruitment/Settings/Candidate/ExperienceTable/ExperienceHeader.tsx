import { Box, Button, TextField, InputAdornment } from '@mui/material'
import SearchIcon from '@mui/icons-material/Search'
import { ExperienceHeaderProps } from './ExperienceTableType'

const ExperienceHeader: React.FC<ExperienceHeaderProps> = ({
  handleOpen,
  searchTerm,
  onSearchChange,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        gap: 2,
        width: '100%',
        '@media (max-width: 400px)': {
          flexDirection: 'column',
          alignItems: 'stretch',
        },
      }}
    >
      <TextField
        placeholder='Search Experiences'
        variant='outlined'
        size='small'
        value={searchTerm}
        onChange={onSearchChange}
        InputProps={{
          startAdornment: (
            <InputAdornment position='start'>
              <SearchIcon />
            </InputAdornment>
          ),
          sx: {
            '& input::placeholder': {
              opacity: 1,
              color: 'rgba(0, 0, 0, 0.6)',
            },
          },
        }}
        sx={{
          minWidth: '200px',
          '& .MuiOutlinedInput-root': {
            width: '200px',
            borderRadius: '24px',
            fontSize: '13px',
            fontFamily: 'Montserrat-Medium',
            '@media (max-width: 400px)': {
              width: '100%',
            },
            '&.Mui-focused': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
                borderWidth: '1px',
              },
            },
            '&:hover:not(.Mui-focused)': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(0, 0, 0, 0.23)',
              },
            },
          },
          '& .MuiOutlinedInput-input': {
            padding: '8.5px 14px 8.5px 0',
          },
        }}
      />

      <Button
        variant='contained'
        color='secondary'
        onClick={handleOpen}
        sx={{
          fontFamily: 'Montserrat-Medium',
          borderRadius: '24px',
          fontSize: '13px',
          flexShrink: 0,
          width: '145px',
          '@media (max-width: 400px)': {
            width: '100%',
          },
        }}
      >
        Add Experience
      </Button>
    </Box>
  )
}

export default ExperienceHeader
