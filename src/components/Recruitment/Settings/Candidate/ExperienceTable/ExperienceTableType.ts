export interface Experience {
  id: number
  experience: string
}

export interface ExperienceDialogProps {
  open: boolean
  handleClose: () => void
  newExperience: string
  setNewExperience: (value: string) => void
  handleSaveExperience: (value: string) => void
  editIndex: number | null
  experienceExists?: boolean
  checkExperienceExists: (experience: string) => boolean
}

export interface ExperienceHeaderProps {
  handleOpen: () => void
  searchTerm: string
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export interface ExperienceListProps {
  experiences: Experience[]
  handleEdit: (id: number) => void
  handleDelete: (id: number) => void
  isExperienceData: boolean
  sortOrder: 'asc' | 'desc'
  handleSort: () => void
}

export interface ExperienceTableProps {
  experienceOptions: any
  fetchExperiencesData: () => void
  addExperiencesData: (data: { experience: string }) => void
  editExperiencesData: (payload: { id: number; experience: string }) => void
  deleteExperiencesData: (id: number) => void
  isExperienceData: boolean
}

export interface EditJobExperiencesPayload {
  id: number
  experience: string
}

export interface DeleteJobExperiencesPayload {
  id: number
}
