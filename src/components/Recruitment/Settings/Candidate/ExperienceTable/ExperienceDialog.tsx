import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogTitle, DialogContent, DialogActions, Button, TextField } from '@mui/material'
import { ExperienceDialogProps } from './ExperienceTableType'

const ExperienceDialog: React.FC<ExperienceDialogProps> = ({
  open,
  handleClose,
  newExperience,
  setNewExperience,
  handleSaveExperience,
  editIndex,
  experienceExists,
  checkExperienceExists,
}) => {
  const [error, setError] = useState<string | null>(null)
  const [originalExperience, setOriginalExperience] = useState('')
  const trimmedInput = newExperience.trim()

  useEffect(() => {
    if (open) {
      if (editIndex !== null) {
        setOriginalExperience(newExperience.trim())
      } else {
        setOriginalExperience('')
        setNewExperience('')
      }
      setError(null)
    }
  }, [open, editIndex])

  const handleExperienceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const trimmedValue = value.trim()
    const endsWithYears = trimmedValue.toLowerCase().endsWith('years')
    const beforeYears = trimmedValue.replace(/years$/i, '').trim()

    if (value === '') {
      setNewExperience('')
      setError(null)
      return
    }

    let isValidBeforeYears = /^[0-9\s+.-]*$/.test(beforeYears)
    let numericPartValid = false
    let total = 0

    const numbers = beforeYears.match(/[\d.]+/g)
    if (numbers) {
      total = numbers.reduce((sum, num) => sum + parseFloat(num), 0)
      numericPartValid = total > 0 && total < 100
    }

    setNewExperience(value)

    if (!endsWithYears) {
      setError('Experience must end with "Years"')
    } else if (!isValidBeforeYears) {
      setError(
        'Only numbers, spaces, hyphens (-), plus (+), and dots (.) are allowed before "Years"',
      )
    } else if (!numericPartValid) {
      setError('Experience should be more than 0 and less than 100')
    } else if (
      (editIndex === null || trimmedValue !== originalExperience) &&
      checkExperienceExists(value)
    ) {
      setError('This experience already exists')
    } else {
      setError(null)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    let finalValue = trimmedInput
    if (!finalValue.toLowerCase().endsWith('years')) {
      finalValue += ' Years'
    }

    const beforeYears = finalValue.replace(/years$/i, '').trim()
    const isValidBeforeYears = /^[0-9\s+.-]*$/.test(beforeYears)
    const numbers = beforeYears.match(/[\d.]+/g)
    const total = numbers ? numbers.reduce((sum, num) => sum + parseFloat(num), 0) : 0
    const numericPartValid = total > 0 && total < 100

    if (!isValidBeforeYears || !numericPartValid || checkExperienceExists(finalValue)) {
      return
    }

    handleSaveExperience(finalValue)
  }

  const isButtonDisabled =
    !trimmedInput ||
    !!error ||
    (editIndex !== null && trimmedInput === originalExperience) ||
    (editIndex === null && experienceExists) ||
    !trimmedInput.toLowerCase().endsWith('years')

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth='xs'
      PaperProps={{
        sx: { borderRadius: '8px', width: '40%', fontFamily: 'Montserrat, sans-serif' },
      }}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle
          sx={{
            bgcolor: '#193C6D',
            color: '#fff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '1rem',
            fontFamily: 'Montserrat-Medium, sans-serif',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          {editIndex !== null ? 'Edit Experience' : 'Add Experience'}
        </DialogTitle>
        <DialogContent sx={{ padding: '10px 20px' }}>
          <TextField
            fullWidth
            label='Experience'
            variant='outlined'
            value={newExperience}
            onChange={handleExperienceChange}
            error={!!error}
            helperText={error}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '28px',
                '& .MuiInputBase-input': {
                  fontFamily: 'Montserrat-Medium',
                  fontSize: '14px',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#193C6D',
                  borderWidth: '2px',
                },
              },
              '& .MuiInputLabel-root': {
                fontFamily: 'Montserrat-Medium',
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ padding: '0 20px 15px 20px', marginTop: '-5px' }}>
          <Button
            variant='contained'
            onClick={handleClose}
            sx={{
              backgroundColor: '#E2E2E2',
              color: '#000000',
              height: '35px',
              fontSize: '15px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              '&:hover': { backgroundColor: '#E2E2E2', color: '#000000' },
            }}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            variant='contained'
            disabled={isButtonDisabled}
            sx={{
              backgroundColor: isButtonDisabled ? '#E2E2E2' : '#193C6D',
              color: isButtonDisabled ? '#000000' : '#FFFFFF',
              fontSize: '15px',
              height: '35px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '90px',
              '&:hover': {
                backgroundColor: isButtonDisabled ? '#E2E2E2' : '#193C6D',
              },
            }}
          >
            {editIndex !== null ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default ExperienceDialog
