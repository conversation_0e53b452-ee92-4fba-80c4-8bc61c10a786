import React from 'react'
import { <PERSON>, <PERSON><PERSON>ield, But<PERSON> } from '@mui/material'
import { SearchBoxCustom, SearchIconStyle } from 'components/Common/CommonStyles'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import {Close as CloseIcon, Search as SearchIcon} from "@mui/icons-material/"

interface SearchBarProps {
  searchTerm: string
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>
  setAddTagOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const SearchBar: React.FC<SearchBarProps> = ({ searchTerm, setSearchTerm, setAddTagOpen }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '0.1rem',
        marginTop: '0.5rem',
        gap: '1rem',
      }}
    >
      <SearchBoxCustom
        id='outlined-basic'
        placeholder='Search Tags'
        variant='outlined'
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        InputProps={{
          startAdornment: <SearchIconStyle />
        }}
      > 
        <CloseIcon />
      </SearchBoxCustom>

      <ActionButton
        onClick={() => setAddTagOpen(true)}
        sx={{
          width: { xs: 'auto' },
          minWidth: '110px',
          whiteSpace: 'nowrap',
          padding: '8px 24px',
          fontSize: '15px',
        }}
      >
        Add Tag
      </ActionButton>
    </Box>
  )
}

export default SearchBar