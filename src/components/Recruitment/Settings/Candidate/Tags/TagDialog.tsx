import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  FormControlLabel,
  Checkbox,
} from '@mui/material'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Tag } from './TagsTable'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface TagDialogProps {
  deleteIndex: number | null
  setDeleteIndex: React.Dispatch<React.SetStateAction<number | null>>
  editIndex: number | null
  setEditIndex: React.Dispatch<React.SetStateAction<number | null>>
  editTag: Tag
  setEditTag: React.Dispatch<React.SetStateAction<Tag>>
  addTagOpen: boolean
  setAddTagOpen: React.Dispatch<React.SetStateAction<boolean>>
  newTag: Tag
  setNewTag: React.Dispatch<React.SetStateAction<Tag>>
  fetchAddNewTag: (data: {}) => void
  fetchEditTag: (data: {}) => void
  fetchDeleteTag: (data: { id: number | null }) => void
}

const validationSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required('Tag Name is required')
    .min(3, 'Tag Name must be at least 3 characters')
    .max(50, 'Tag Name cannot exceed 50 characters'),
  is_shortcut_tag: Yup.boolean(),
})

const TagDialog: React.FC<TagDialogProps> = ({
  deleteIndex,
  setDeleteIndex,
  editIndex,
  setEditIndex,
  editTag,
  setEditTag,
  addTagOpen,
  setAddTagOpen,
  newTag,
  setNewTag,
  fetchAddNewTag,
  fetchEditTag,
  fetchDeleteTag,
}) => {
  const handleSave = (values: { name: string; is_shortcut_tag: boolean }) => {
    fetchAddNewTag({
      tag: values.name,
      is_shortcut_tag: values.is_shortcut_tag,
    })
    setAddTagOpen(false)
  }

  const handleEditTag = (values: { name: string; is_shortcut_tag: boolean }) => {
    fetchEditTag({
      id: editIndex,
      name: values.name,
      is_shortcut_tag: values.is_shortcut_tag,
    })
    setEditIndex(null)
  }

  return (
    <>
      <Dialog open={editIndex !== null} onClose={() => setEditIndex(null)}>
        <Box sx={{ backgroundColor: '#193C6D', color: 'white', padding: '10px' }}>
          <DialogTitle
            sx={{
              display: 'flex',
              justifyContent: 'center',
              fontSize: '30px',
              fontFamily: 'Montserrat-SemiBold',
            }}
          >
            Edit Tag
          </DialogTitle>
        </Box>
        <Formik
          initialValues={{
            name: editTag.name,
            is_shortcut_tag: editTag.is_shortcut_tag,
          }}
          validationSchema={validationSchema}
          onSubmit={handleEditTag}
          enableReinitialize
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue, isValid }) => (
            <Form>
              <DialogContent>
                <Field
                  as={TextField}
                  fullWidth
                  label='Tag Name'
                  name='name'
                  value={values.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  margin='dense'
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: '45px' } }}
                  error={touched.name && Boolean(errors.name)}
                  helperText={touched.name && errors.name}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      name='is_shortcut_tag'
                      checked={values.is_shortcut_tag}
                      onChange={(e) => setFieldValue('is_shortcut_tag', e.target.checked)}
                    />
                  }
                  label='Shortcut Tag'
                />
              </DialogContent>
              <DialogActions>
                <ActionButton
                  onClick={() => setEditIndex(null)}
                  sx={{ 
                    backgroundColor: '#1A3A6F', 
                    color: 'white',
                    borderRadius: '25px', 
                    padding: '6px 20px',
                    '&:hover': {
                      backgroundColor: '#1A3A6F',
                      color: 'white',
                    }
                  }}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  type='submit'
                  disabled={!isValid}
                  sx={{
                    backgroundColor: isValid ? '#1A3A6F' : '#E2E2E2',
                    color: isValid ? 'white' : '#000000',
                    borderRadius: '25px',
                    padding: '6px 20px',
                    '&:hover': {
                      backgroundColor: isValid ? '#1A3A6F' : '#E2E2E2',
                      color: isValid ? 'white' : '#000000',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: '#E2E2E2',
                      color: '#000000',
                    },
                  }}
                >
                  Save
                </ActionButton>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
      <Dialog open={addTagOpen} onClose={() => setAddTagOpen(false)}>
        <Box sx={{ backgroundColor: '#193C6D', color: 'white' }}>
          <DialogTitle
            sx={{
              display: 'flex',
              justifyContent: 'center',
              fontSize: '30px',
              fontFamily: 'Montserrat-SemiBold',
            }}
          >
            Add Tag
          </DialogTitle>
        </Box>
        <Formik
          initialValues={{
            name: '',
            is_shortcut_tag: false,
          }}
          validationSchema={validationSchema}
          onSubmit={handleSave}
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue, isValid }) => (
            <Form>
              <DialogContent>
                <Field
                  as={TextField}
                  fullWidth
                  label='Tag Name'
                  name='name'
                  value={values.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  margin='dense'
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: '45px' } }}
                  error={touched.name && Boolean(errors.name)}
                  helperText={touched.name && errors.name}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      name='is_shortcut_tag'
                      checked={values.is_shortcut_tag}
                      onChange={(e) => setFieldValue('is_shortcut_tag', e.target.checked)}
                    />
                  }
                  label='Shortcut Tag'
                />
              </DialogContent>
              <DialogActions>
                <ActionButton
                  onClick={() => setAddTagOpen(false)}
                  sx={{ 
                    backgroundColor: '#1A3A6F', 
                    color: 'white',
                    borderRadius: '25px', 
                    padding: '6px 20px',
                    '&:hover': {
                      backgroundColor: '#1A3A6F',
                      color: 'white',
                    }
                  }}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  type='submit'
                  disabled={!isValid}
                  sx={{
                    backgroundColor: isValid ? '#1A3A6F' : '#E2E2E2',
                    color: isValid ? 'white' : '#000000',
                    borderRadius: '25px',
                    padding: '6px 20px',
                    '&:hover': {
                      backgroundColor: isValid ? '#1A3A6F' : '#E2E2E2',
                      color: isValid ? 'white' : '#000000',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: '#E2E2E2',
                      color: '#000000',
                    },
                  }}
                >
                  Save
                </ActionButton>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
    </>
  )
}

export default TagDialog