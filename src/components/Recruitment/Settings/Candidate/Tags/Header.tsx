import React from 'react'
import { Box, Typography } from '@mui/material'
import BackButton from 'components/Recruitment/Common/GeneralizedBackButton'

interface HeaderProps {
  isClicked: boolean
  handleClick: () => void
}

const Header: React.FC<HeaderProps> = ({ isClicked, handleClick }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        color: 'primary.main',
        p: 2,
        borderRadius: 1,
      }}
    >
      <Typography
        variant='h6'
        sx={{
          fontFamily: 'Montserrat-Semibold',
          fontWeight: 700,
          position: 'relative',
          display: 'inline-block',
          padding: '8px 16px',
          color: 'primary.main',
          marginTop: '0px',
          borderRadius: '4px',
          cursor: 'pointer',
          transition: 'background-color 0.3s ease-in-out, color 0.3s ease-in-out',
          backgroundColor: isClicked ? 'rgba(25, 60, 109, 0.3)' : 'transparent',
          '&::after': {
            content: '""',
            display: 'block',
            width: '100%',
            height: '2px',
            backgroundColor: '#193C6D',
            position: 'absolute',
            bottom: '0px',
            left: 0,
          },
        }}
        onClick={handleClick}
      >
        Tags
      </Typography>
      <BackButton tooltip='Go Back' />
    </Box>
  )
}

export default Header