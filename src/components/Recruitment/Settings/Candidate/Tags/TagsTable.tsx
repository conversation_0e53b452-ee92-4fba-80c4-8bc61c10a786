import React, { useEffect, useState } from 'react'
import { Box, Paper } from '@mui/material'
import TagDialog from './TagDialog'
import SearchBar from './SearchBar'
import PaginationControl from './PaginationControlled'
import Header from './Header'
import TableContainer from './TableContainer'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { recruitmentEntity, recruitmentStateUI } from 'reducers'
import { fetchTagCandidates, getNewTag, editTag, getDeleteTag } from 'actions'

export interface Tag {
  id: number | null
  name: string
  candidates: number
  is_shortcut_tag: boolean
}

function TagsTable(props: any) {
  const [tags, setTags] = useState<Tag[]>([])
  const [page, setPage] = useState<number>(1)
  const rowsPerPage: number = 10
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [editTag, setEditTag] = useState<Tag>({
    id: null,
    name: '',
    candidates: 0,
    is_shortcut_tag: false,
  })
  const [addTagOpen, setAddTagOpen] = useState(false)
  const [newTag, setNewTag] = useState<Tag>({
    id: null,
    name: '',
    candidates: 0,
    is_shortcut_tag: false,
  })
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isClicked, setIsClicked] = useState(false)
  const { fetchTagCandidateData, tagCandidateOptions, isAddTag, isEditTag, isDeleteTag } = props

  // Initial data fetch on component mount only
  useEffect(() => {
    fetchTagCandidateData()
  }, []) // Empty dependency array - runs only once on mount

  useEffect(() => {
    if (Array.isArray(tagCandidateOptions) && tagCandidateOptions.length > 0) {
      setTags(tagCandidateOptions[0])
    }
  }, [tagCandidateOptions])

  // Handle tag operation success states - refresh when operations complete
  useEffect(() => {
    if (isAddTag === false) {
      fetchTagCandidateData()
    }
  }, [isAddTag])

  useEffect(() => {
    if (isEditTag === false) {
      fetchTagCandidateData()
    }
  }, [isEditTag])

  useEffect(() => {
    if (isDeleteTag === false) {
      fetchTagCandidateData()
    }
  }, [isDeleteTag])

  const handleClick = () => {
    setIsClicked(true)
    setTimeout(() => setIsClicked(false), 200)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleDeleteConfirm = () => {
    if (deleteIndex !== null) {
      // deleteIndex now contains the tag ID, not the array index
      const tagId = deleteIndex
      
      // Call the Redux action to delete the tag from backend
      props.fetchDeleteTag({ id: tagId })
      
      // Close the dialog - the toaster and data refresh will be handled by Redux state
      setDeleteIndex(null)
    }
  }

  const filteredTags = tags.filter((tag) =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <Box sx={{ p: 3, fontFamily: 'Montserrat-Medium' }}>
      <Paper elevation={5} sx={{ padding: '20px', backgroundColor: 'white' }}>
        <Header isClicked={isClicked} handleClick={handleClick} />
        <SearchBar
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          setAddTagOpen={setAddTagOpen}
        />

        <Box mt={3}>
          <TableContainer
            tags={filteredTags}
            page={page}
            rowsPerPage={rowsPerPage}
            setEditIndex={setEditIndex}
            setEditTag={setEditTag}
            setDeleteIndex={setDeleteIndex}
            deleteIndex={deleteIndex}
            handleDeleteConfirm={handleDeleteConfirm}
          />
        </Box>
        <PaginationControl
          page={page}
          handlePageChange={handlePageChange}
          filteredTags={filteredTags}
          rowsPerPage={rowsPerPage}
        />
        <TagDialog
          deleteIndex={deleteIndex}
          setDeleteIndex={setDeleteIndex}
          editIndex={editIndex}
          setEditIndex={setEditIndex}
          editTag={editTag}
          setEditTag={setEditTag}
          addTagOpen={addTagOpen}
          setAddTagOpen={setAddTagOpen}
          newTag={newTag}
          setNewTag={setNewTag}
          fetchAddNewTag={props.fetchAddNewTag}
          fetchEditTag={props.fetchEditTag}
          fetchDeleteTag={props.fetchDeleteTag}
        />
      </Paper>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    tagCandidateOptions: recruitmentEntity.getRecruitment(state).getTagCandidatesData,
    isAddTag: recruitmentStateUI.getRecruitment(state).addNewTag,
    isEditTag: recruitmentStateUI.getRecruitment(state).editNewTag,
    isDeleteTag: recruitmentStateUI.getRecruitment(state).deleteTag,
    addNewTagOptions: recruitmentEntity.getRecruitment(state).addNewTag,
  }
}
const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchTagCandidateData: () => dispatch(fetchTagCandidates.request()),
    fetchAddNewTag: (data: {}) => dispatch(getNewTag.request({ data })),
    fetchEditTag: (data: {}) => dispatch(editTag.request({ data })),
    fetchDeleteTag: (data: {}) => dispatch(getDeleteTag.request({ data })),
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(TagsTable)