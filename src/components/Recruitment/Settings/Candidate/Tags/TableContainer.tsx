import React from 'react'
import {
  TableContainer as MuiTableContainer,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
} from '@mui/material'
import { Tag } from './TagsTable'
import TagRow from './TagRow'
import { RootState } from 'configureStore'
import { Dispatch } from 'redux'
import { getDeleteTag } from 'actions'
import { connect } from 'react-redux'
import { StyledTableCellForMyTeam } from 'components/Common/CommonStyles'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'

interface TableContainerProps {
  tags: Tag[]
  page: number
  rowsPerPage: number
  setEditIndex: React.Dispatch<React.SetStateAction<number | null>>
  setEditTag: React.Dispatch<React.SetStateAction<Tag>>
  setDeleteIndex: React.Dispatch<React.SetStateAction<number | null>>
  deleteIndex: number | null
  handleDeleteConfirm: () => void
}

const TableContainer: React.FC<TableContainerProps> = ({
  tags,
  page,
  rowsPerPage,
  setEditIndex,
  setEditTag,
  setDeleteIndex,
  deleteIndex,
  handleDeleteConfirm,
}) => {
  return (
    <>
      <MuiTableContainer
        component={Paper}
        sx={{ backgroundColor: 'white', maxHeight: '100%', overflow: 'auto' }}
      >
        <Table sx={{ width: '100%', tableLayout: 'auto' }} aria-label='customized table'>
          <TableHead sx={{ backgroundColor: 'rgb(25,60,109)', color: 'fff' }}>
            <TableRow>
              <StyledTableCellForMyTeam style={{ color: 'white', width: '20%', textAlign: 'left' }}>
                Tag Name
              </StyledTableCellForMyTeam>
              <StyledTableCellForMyTeam style={{ color: 'white', width: '20%', textAlign: 'left' }}>
                Candidates
              </StyledTableCellForMyTeam>
              <StyledTableCellForMyTeam style={{ color: 'white', width: '20%', textAlign: 'left' }}>
                Shortcut Tag
              </StyledTableCellForMyTeam>
              <StyledTableCellForMyTeam style={{ color: 'white', width: '5%', textAlign: 'left' }}>
                Actions
              </StyledTableCellForMyTeam>
            </TableRow>
          </TableHead>
          <TableBody>
            {tags.slice((page - 1) * rowsPerPage, page * rowsPerPage).map((tag, index) => (
              <TagRow
                key={index}
                tag={{
                  ...tag,
                  is_shortcut_tag: tag.is_shortcut_tag,
                }}
                index={index}
                setEditIndex={setEditIndex}
                setEditTag={setEditTag}
                setDeleteIndex={setDeleteIndex}
              />
            ))}
          </TableBody>
        </Table>
      </MuiTableContainer>
      <DeleteConfirmationDialog
        open={deleteIndex !== null}
        handleClose={() => setDeleteIndex(null)}
        handleConfirm={() => {
          handleDeleteConfirm()
          setDeleteIndex(null)
        }}
        message={'Are you sure you want to delete this tag? This action cannot be undone.'}
      />
    </>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchDeleteTag: (data: {}) => dispatch(getDeleteTag.request({ data })),
  }
}

export default connect(null, mapDispatchToProps)(TableContainer)