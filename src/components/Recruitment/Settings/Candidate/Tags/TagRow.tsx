import React, { useEffect } from 'react'
import { TableRow, TableCell, IconButton, Tooltip } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { Tag } from './TagsTable'
import { StyledTableCellForMyTeam } from 'components/Common/CommonStyles'


interface TagRowProps {
  tag: Tag
  index: number
  setEditIndex: React.Dispatch<React.SetStateAction<number | null>>
  setEditTag: React.Dispatch<React.SetStateAction<Tag>>
  setDeleteIndex: React.Dispatch<React.SetStateAction<number | null>>

}

const TagRow: React.FC<TagRowProps> = ({
  tag,
  index,
  setEditIndex,
  setEditTag,
  setDeleteIndex,
}) => {
  return (
    <TableRow key={index}>
      <StyledTableCellForMyTeam>{tag.name}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>{tag.candidates}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>{tag.is_shortcut_tag ? 'Yes' : 'No'}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>
        <Tooltip title='Edit'>
          <IconButton
            onClick={() => {
              setEditIndex(tag.id)
              setEditTag(tag)
            }}
            sx={{ color: 'rgb(25,60,109)', width: '36px', height: '36px' }}
          >
            <EditIcon sx={{ width: '20px', height: '20px' }} />
          </IconButton>
        </Tooltip>
        <Tooltip title='Delete'>
          <IconButton
            onClick={() => {
              setDeleteIndex(tag.id)
            }}
            sx={{ color: '#db3700', width: '36px', height: '36px' }}
          >
            <DeleteIcon sx={{ width: '20px', height: '20px' }} />
          </IconButton>
        </Tooltip>
      </StyledTableCellForMyTeam>
    </TableRow>
  )
}



export default TagRow