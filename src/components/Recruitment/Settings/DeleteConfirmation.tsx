import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from '@mui/material'
import { useEffect } from 'react'

interface DeleteConfirmationProps {
  open: boolean
  handleClose: () => void
  handleConfirm: () => void
  message?: string
}

const DeleteConfirmation = ({
  open,
  handleClose,
  handleConfirm,
  message = 'Are you sure you want to delete this?',
}: DeleteConfirmationProps) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return

      if (e.key === 'Enter') {
        e.preventDefault()
        handleConfirm()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        handleClose()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [open, handleConfirm, handleClose])

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth='xs'
      fullWidth
      PaperProps={{
        sx: { borderRadius: '8px', width: '40%', fontFamily: 'Montserrat-Medium' },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: '#193C6D',
          color: '#fff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '1rem',
          marginBottom: '1rem',
          fontFamily: 'Montserrat-Medium',
          fontWeight: 'bold',
          fontSize: '16px',
        }}
      >
        Confirm Deletion
      </DialogTitle>

      <DialogContent
        sx={{ padding: '20px', fontFamily: 'Montserrat-Medium', paddingBottom: '0rem' }}
      >
        <Typography sx={{ fontFamily: 'Montserrat-Medium', fontSize: '14px' }}>
          {message}
        </Typography>
      </DialogContent>

      <DialogActions sx={{ padding: '20px', fontFamily: 'Montserrat-Medium' }}>
        <Button
          onClick={handleClose}
          variant='contained'
          sx={{
            backgroundColor: '#E2E2E2',
            color: '#000000',
            height: '35px',
            fontSize: '15px',
            borderRadius: '50px',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Semibold',
            '&:hover': { backgroundColor: '#E2E2E2', color: '#000000' },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          variant='contained'
          sx={{
            backgroundColor: '#db3700',
            color: '#FFF',
            fontSize: '15px',
            height: '35px',
            borderRadius: '50px',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Semibold',
            '&:hover': { backgroundColor: '#db3700' },
          }}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DeleteConfirmation
