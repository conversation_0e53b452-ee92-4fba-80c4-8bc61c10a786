import PersonAddIcon from '@mui/icons-material/PersonAdd'
import AccountCircleIcon from '@mui/icons-material/AccountCircle'
import PeopleIcon from '@mui/icons-material/People'
import BusinessIcon from '@mui/icons-material/Business'
import SettingsIcon from '@mui/icons-material/Settings'
import AddIcon from '@mui/icons-material/Add'
import ScheduleIcon from '@mui/icons-material/Schedule'
import EmailIcon from '@mui/icons-material/Email'
import FeedbackIcon from '@mui/icons-material/Feedback'
import UploadIcon from '@mui/icons-material/Upload'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import BlockIcon from '@mui/icons-material/Block'
import StorageIcon from '@mui/icons-material/Storage'
import WorkIcon from '@mui/icons-material/Work'
import StarIcon from '@mui/icons-material/Star'
import LabelIcon from '@mui/icons-material/Label'
import SchoolIcon from '@mui/icons-material/School'
import GroupIcon from '@mui/icons-material/Group'
import ContactsIcon from '@mui/icons-material/Contacts'
import HowToRegIcon from '@mui/icons-material/HowToReg'
import { SvgIconComponent } from '@mui/icons-material'
import { MenuItem } from '@mui/material'

interface Subfield {
  icon: SvgIconComponent
  label: string
  route: string
  isModal?: boolean
}

interface Subfield {
  icon: SvgIconComponent
  label: string
  route: string
  isModal?: boolean
}

interface MenuItem {
  icon: SvgIconComponent
  label: string
  subfields: Subfield[]
}

export const menuItems: MenuItem[] = [
  {
    icon: PersonAddIcon,
    label: 'Recruitment',
    subfields: [
      { icon: AddIcon, label: 'Rounds', route: '/home/<USER>/settings/rounds' },
      { icon: ScheduleIcon, label: 'Schedule', route: '/home/<USER>/settings/schedule' },
      { icon: EmailIcon, label: 'Email Templates', route: '/home/<USER>/templates' },
      {
        icon: FeedbackIcon,
        label: 'Feedback Templates',
        route: '/home/<USER>/settings/recruitment/feedback-template',
      },
      {
        icon: UploadIcon,
        label: 'Upload Candidates',
        route: '/home/<USER>/settings/recruitment/upload-candidate',
      },
      { icon: CalendarTodayIcon, label: 'Cooling Off Period', route: 'cooling-off', isModal: true },
      { icon: CalendarTodayIcon, label: 'Send Reminders', route: 'send-reminder' },
    ],
  },
  {
    icon: AccountCircleIcon,
    label: 'Candidate',
    subfields: [
      {
        icon: WorkIcon,
        label: 'Positions',
        route: '/home/<USER>/settings/candidate/position',
      },
      {
        icon: StarIcon,
        label: 'Experiences',
        route: '/home/<USER>/settings/candidate/experience-table',
      },
      { icon: LabelIcon, label: 'Tags', route: '/home/<USER>/settings/candidate/tags' },
      {
        icon: SchoolIcon,
        label: 'Qualifications',
        route: '/home/<USER>/settings/candidate/qualification',
      },
    ],
  },
  {
    icon: PeopleIcon,
    label: 'Users',
    subfields: [
      {
        icon: GroupIcon,
        label: 'Interviewers',
        route: '/home/<USER>/settings/users/interviewer',
      },
      {
        icon: AccountCircleIcon,
        label: 'Recruiters',
        route: '/home/<USER>/settings/recruiter',
      },
      { icon: ContactsIcon, label: 'Contacts', route: 'users/contacts' },
      // { icon: ContactsIcon, label: 'Contacts', route: '/home/<USER>/settings/users/index' },
      { icon: HowToRegIcon, label: 'Joiners', route: '/home/<USER>/settings/users/joiners' },
    ],
  },
  {
    icon: BusinessIcon,
    label: 'Campus',
    subfields: [
      { icon: PeopleIcon, label: 'Batches', route: '/home/<USER>/settings/campus/batch' },
      {
        icon: BusinessIcon,
        label: 'Organizations',
        route: '/home/<USER>/settings/organization',
      },
    ],
  },
  {
    icon: SettingsIcon,
    label: 'System',
    subfields: [
      {
        icon: BlockIcon,
        label: 'Blocked Domains',
        route: '/home/<USER>/settings/system/blocked-domain-table',
      },
      {
        icon: BlockIcon,
        label: 'Blocked Subject',
        route: '/home/<USER>/settings/system/blocked-subject',
      },
      {
        icon: BlockIcon,
        label: 'Blocked Body',
        route: '/home/<USER>/Settings/System/BlockedBody',
      },
      {
        icon: StorageIcon,
        label: 'Customize',
        route: '/home/<USER>/settings/system/customize',
      },
    ],
  },
]



