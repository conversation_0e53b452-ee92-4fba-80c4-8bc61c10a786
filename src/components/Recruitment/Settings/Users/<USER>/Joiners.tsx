import { useState } from 'react'
import JoinerDetails from './JoinerDetails'
import Box from '@mui/material/Box'
import JoinerHeader from './JoinerHeader'
import { JoinerProvider } from './JoinerContext'

export default function Joiners() {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <JoinerProvider>
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 3 }}>
      <JoinerHeader searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
    </Box>
    </JoinerProvider>
  )
}
