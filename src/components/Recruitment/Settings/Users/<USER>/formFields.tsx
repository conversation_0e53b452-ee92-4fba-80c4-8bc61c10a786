import React from 'react'
import { TextField, Box, MenuItem } from '@mui/material'
import { styled } from '@mui/system'

const InputField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    minHeight: '36px',
    padding: '0px',
    fontFamily: 'Montserrat-Medium',
    '& fieldset': {
      borderColor: '#ccc',
    },
    '&:hover fieldset': {
      borderColor: '#888',
    },
  },
  '& .MuiOutlinedInput-input': {
    padding: '10px 12px',
    fontSize: '14px',
    height: '20px',
    lineHeight: '1.2',
  },
  '& .MuiInputLabel-root': {
    fontSize: '14px',
    transform: 'translate(14px, 10px) scale(1)',
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, -4px) scale(0.75)',
  },
  '& input:-webkit-autofill': {
    backgroundColor: 'white !important',
    WebkitBoxShadow: '0 0 0 100px white inset !important',
  },
}))

interface FormFieldsProps {
  formData: {
    first_name:string
    last_name:string
    email: string
    phone_no: string
    role: string
    status: string
    password: string
    confirmPassword: string
    round: string
    position: string
    experience: string
  }
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  roundArray: string[]
  jobOption: string[]
  experienceOption: string[]
  errors?: Partial<Record<keyof FormFieldsProps['formData'], string>>
}

const roles = [{name:'Interviewer',value:4}]
const statuses = ['Active', 'Inactive']

const FormFields2: React.FC<FormFieldsProps> = ({
  formData,
  handleChange,
  roundArray,
  jobOption,
  experienceOption,
  errors = {},
}) => {
  
  return (
    <>
      <Box display='flex' gap={2} flexWrap='wrap' width='100%' justifyContent='center'>
        <InputField
          label='First Name'
          name='first_name'
          value={formData.first_name}
          onChange={handleChange}
          required
          error={!!errors.first_name}
          helperText={errors.first_name}
          sx={{ minWidth: '220px', flex: 1,         
 }}

          
        />
        <InputField
          label='Last Name'
          name='last_name'
          value={formData.last_name}
          onChange={handleChange}
          required
          sx={{ minWidth: '220px', flex: 1 , }}
        />
        <InputField
          label='Email'
          name='email'
          type='email'
          value={formData.email}  
          onChange={handleChange}
          required
          error={!!errors.email}
          helperText={errors.email}
          sx={{ minWidth: '260px', flex: 1 , }}
        />
        
      </Box>

      <Box display='flex' gap={2} flexWrap='wrap' width='100%' justifyContent='center' mt={2}>
      <InputField
          label='Contact Number'
          name='phone_no'
          type='tel'
          value={formData.phone_no}
          onChange={handleChange}
          required
          error={!!errors.phone_no}
          helperText={errors.phone_no}
          sx={{ minWidth: '220px', flex: 1 , }}
        />
        <InputField
          select
          label='Role'
          name='role'
          value={formData.role || '' }
          onChange={handleChange}
          required
          error={!!errors.role}
          helperText={errors.role}
          sx={{ minWidth: '220px', flex: 1 , }}
        >
          {roles.map((role) => (
            <MenuItem key={role.name} value={role.value}>
              {role.name}
            </MenuItem>
          ))}
        </InputField>

        <InputField
          select
          label='Status'
          name='status'
          value={formData.status || '' }
          onChange={handleChange}
          required
          error={!!errors.status}
          helperText={errors.status}
          sx={{ minWidth: '220px', flex: 1 ,}}
        >
          {statuses.map((status) => (
            <MenuItem key={status} value={status}>
              {status}
            </MenuItem>
          ))}
        </InputField>
      </Box>

      <Box display='flex' gap={2} flexWrap='wrap' width='100%' justifyContent='center' mt={2}>
        <InputField
          select
          label='Round'
          name='round'
          value={formData.round || ''}
          onChange={handleChange}
          required
          error={!!errors.round}
          helperText={errors.round}
          sx={{ minWidth: '220px', flex: 1 ,}}
        >
          {roundArray.map((tag) => (
            <MenuItem key={tag} value={tag}>
              {tag}
            </MenuItem>
          ))}
        </InputField>

        <InputField
          select
          label='Position'
          name='position'
          value={formData.position || ''}
          onChange={handleChange}
          required
          error={!!errors.position}
          helperText={errors.position}
          sx={{ minWidth: '220px', flex: 1 , }}
        >
          {jobOption.map((position) => (
            <MenuItem key={position} value={position}>
              {position}
            </MenuItem>
          ))}
        </InputField>

        <InputField
          select
          label='Experience'
          name='experience'
          value={formData.experience ||''}
          onChange={handleChange}
          required
          error={!!errors.experience}
          helperText={errors.experience}
          sx={{ minWidth: '220px', flex: 1 ,}}
        >
          {experienceOption.map((exp) => (
            <MenuItem key={exp} value={exp}>
              {exp}
            </MenuItem>
          ))}
        </InputField>
      </Box>

      <Box display='flex' gap={2} flexWrap='wrap' width='100%' justifyContent='center' mt={2}>
        <InputField
          label='Password'
          name='password'
          type='password'
          value={formData.password}
          onChange={handleChange}
          required
          error={!!errors.password}
          helperText={errors.password}
          sx={{ minWidth: '260px', flex: 1 ,}}
        />
        <InputField
          label='Confirm Password'
          name='confirmPassword'
          type='password'
          value={formData.confirmPassword ||''}
          onChange={handleChange}
          required
          error={!!errors.confirmPassword}
          helperText={errors.confirmPassword}
          sx={{ minWidth: '260px', flex: 1 , }}
        />
      </Box>
    </>
  )
}

export default FormFields2
