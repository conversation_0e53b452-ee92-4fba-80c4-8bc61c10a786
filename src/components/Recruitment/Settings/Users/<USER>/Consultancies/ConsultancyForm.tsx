import React, { useState, useEffect } from 'react'
import { TextField } from '@mui/material'
import { Consultancy } from './ConsultancyData'

interface ConsultancyFormProps {
  consultancy: Consultancy | null
  setConsultancy: (consultancy: Consultancy) => void
}

const ConsultancyForm: React.FC<ConsultancyFormProps> = ({ consultancy, setConsultancy }) => {
  const [localConsultancy, setLocalConsultancy] = useState<Consultancy | null>(consultancy)

  useEffect(() => {
    setLocalConsultancy(consultancy)
  }, [consultancy])

  useEffect(() => {
    if (localConsultancy) setConsultancy(localConsultancy)
  }, [localConsultancy, setConsultancy])

  if (!localConsultancy) {
    return <p>Loading...</p> 
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalConsultancy({ ...localConsultancy, [e.target.name]: e.target.value })
  }

  return (
    <>
      <TextField
        label='Consultancy Name'
        name='company'
        value={localConsultancy.company}
        onChange={handleChange}
        fullWidth
        margin='dense'
        sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
      />
      <TextField
        label='Type'
        name='type'
        value={localConsultancy.type}
        onChange={handleChange}
        fullWidth
        margin='dense'
        sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
      />
      <TextField
        label='Contact Person'
        name='contactPerson'
        value={localConsultancy.contactPerson}
        onChange={handleChange}
        fullWidth
        margin='dense'
        sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
      />
      <TextField
        label='Email'
        name='email'
        value={localConsultancy.email}
        onChange={handleChange}
        fullWidth
        margin='dense'
        sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
      />
      <TextField
        label='Website'
        name='website'
        value={localConsultancy.website}
        onChange={handleChange}
        fullWidth
        margin='dense'
        sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
      />
    </>
  )
}

export default ConsultancyForm
