import * as React from 'react'
import Grid from '@mui/material/Grid'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import MultiSelectDropdown from './MultiSelectDropdown'
import { useEffect, useState } from 'react'
import { Button, Collapse } from '@mui/material'
import { fetchRoundsByType } from 'actions'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { RootState } from 'configureStore'

interface FilterButtonProps {
  open: boolean
  fetchRoundsByType: (data: {}) => void
  RoundOptions: any[]
}

function FilterButton({ open, fetchRoundsByType, RoundOptions }: FilterButtonProps) {
  const [values, setValues] = React.useState({
    box1: '',
    box2: '',
    box3: '',
  })

  useEffect(() => {
    fetchRoundsByType({ round_type: 3 })
  }, [fetchRoundsByType])

  const handleChange = (name: string) => (event: SelectChangeEvent) => {
    setValues((prev) => ({
      ...prev,
      [name]: event.target.value as string,
    }))
  }
  return (
    <Collapse in={open}>
      <Grid container spacing={8} alignItems='center' sx={{ p: 2, borderRadius: 2 }}>
        <Grid item xs={12} sm={6} md={2.4} lg={2.4}>
          <FormControl fullWidth>
            <InputLabel>Select Rounds</InputLabel>
            <Select
              value={values.box1}
              onChange={handleChange('box1')}
              sx={{ borderRadius: '40px' }}
            >
              {RoundOptions && RoundOptions.length > 0 ? (
                RoundOptions.map((round: { round_name: string }) => (
                  <MenuItem key={round.round_name} value={round.round_name}>
                    {round.round_name}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled>No Data Available</MenuItem>
              )}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4} lg={2.4}>
          <FormControl fullWidth>
            <InputLabel>Select Template</InputLabel>
            <Select
              value={values.box2}
              onChange={handleChange('box2')}
              sx={{ borderRadius: '40px' }}
            >
              {['Template 1', 'Template 2', 'Template 3'].map((option, i) => (
                <MenuItem key={option} value={(i + 1) * 10}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4} lg={2.4}>
          <FormControl fullWidth>
            <InputLabel>Select Date & Time</InputLabel>
            <Select
              value={values.box3}
              onChange={handleChange('box3')}
              sx={{ borderRadius: '40px' }}
            >
              {['Undergraduate', 'Graduate', 'Masters'].map((option, i) => (
                <MenuItem key={option} value={(i + 1) * 10}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Collapse>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    RoundOptions: recruitmentEntity.getRecruitment(state).getRoundsByTypeData || [],
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchRoundsByType: (data: {}) => dispatch(fetchRoundsByType.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(FilterButton)
