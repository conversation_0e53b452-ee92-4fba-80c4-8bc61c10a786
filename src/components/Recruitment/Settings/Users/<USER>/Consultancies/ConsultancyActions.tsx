import React from 'react'
import { IconButton } from '@mui/material'
import { Edit, Delete } from '@mui/icons-material'

interface ConsultancyActionsProps {
  onEdit: () => void
  onDelete: () => void
}

const TPOActions: React.FC<ConsultancyActionsProps> = ({ onEdit, onDelete }) => {
  return (
    <>
      <IconButton size='small' sx={{ color: '#0d3c6e' }} onClick={onEdit}>
        <Edit />
      </IconButton>
      <IconButton size='small' color='error' onClick={onDelete}>
        <Delete />
      </IconButton>
    </>
  )
}

export default ConsultancyActionsProps
