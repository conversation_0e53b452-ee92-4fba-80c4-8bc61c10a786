import React from 'react'
import { Dialog, DialogTitle, DialogActions, Button } from '@mui/material'

interface DeleteConsultancyDialogProps {
  open: boolean
  onClose: () => void
  consultancy: any
}

const DeleteConsultancyDialog: React.FC<DeleteConsultancyDialogProps> = ({
  open,
  onClose,
  consultancy,
}) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Are you sure you want to delete this consultancy?</DialogTitle>
      <DialogActions>
        <Button onClick={onClose} sx={{ marginRight: '14px', borderRadius: '20px' }}>
          Cancel
        </Button>
        <Button
          onClick={consultancy}
          sx={{
            bgcolor: 'error.main',
            borderRadius: '20px',
            color: 'white',
            '&:hover': { bgcolor: 'error.dark' },
          }}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DeleteConsultancyDialog
