import React from 'react'
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material'
import ConsultancyForm from './ConsultancyForm'
import { Consultancy } from './ConsultancyData'

interface EditConsultancyDialogProps {
  open: boolean
  onClose: () => void
  consultancy: Consultancy | null
  setConsultancy: (consultancy: Consultancy) => void
  onSave: () => void
}

const EditConsultancyDialog: React.FC<EditConsultancyDialogProps> = ({
  open,
  onClose,
  consultancy,
  setConsultancy,
  onSave,
}) => {
  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <DialogTitle>Edit Consultancy</DialogTitle>
      <DialogContent>
        <ConsultancyForm consultancy={consultancy} setConsultancy={setConsultancy} />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ borderRadius: '20px', marginRight: '13px', padding: '10px' }}>
          Cancel
        </Button>
        <Button onClick={onSave} color='primary' sx={{ borderRadius: '20px', marginRight: '5px', padding: '10px' }}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default EditConsultancyDialog
