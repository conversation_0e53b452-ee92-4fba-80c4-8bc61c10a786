import { useState, MouseEvent } from 'react'
import { Menu, MenuItem, Checkbox, ListItemText, TextField, Button, SxProps } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'

interface MultiSelectDropdownProps {
  label: string
  options: string[]
  selectedOptions: string[]
  setSelectedOptions: (selected: string[]) => void
  sx?: SxProps;
}

export default function MultiSelectDropdown({
  label,
  options,
  selectedOptions,
  setSelectedOptions,
}: MultiSelectDropdownProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [searchText, setSearchText] = useState<string>('')

  const open = Boolean(anchorEl)

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleToggleOption = (option: string) => {
    setSelectedOptions(
      selectedOptions.includes(option)
        ? selectedOptions.filter((item) => item !== option)
        : [...selectedOptions, option],
    )
  }

  const handleSelectAll = () => {
    setSelectedOptions(selectedOptions.length === options.length ? [] : options)
  }

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchText.toLowerCase()),
  )

  return (
    <div>
      <Button
        variant='outlined'
        onClick={handleClick}
        sx={{ justifyContent: 'space-between', borderRadius: '20px' }}
        fullWidth
      >
        {label} {selectedOptions.length > 0 ? `+${selectedOptions.length}` : ''}
        <ArrowDropDownIcon />
      </Button>

      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem onClick={handleSelectAll}>
          <Checkbox checked={selectedOptions.length === options.length} />
          {selectedOptions.length < options.length ? 'Select All' : 'Un-Select All'}
        </MenuItem>

        <MenuItem onKeyDown={(e) => e.stopPropagation()}>
          <TextField
            fullWidth
            variant='standard'
            placeholder='Search'
            onChange={(e) => setSearchText(e.target.value)}
          />
        </MenuItem>

        {filteredOptions.map((option) => (
          <MenuItem key={option} onClick={() => handleToggleOption(option)}>
            <Checkbox checked={selectedOptions.includes(option)} />
            <ListItemText primary={option} />
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}
