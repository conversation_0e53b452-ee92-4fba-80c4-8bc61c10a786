import React from 'react'
import { TableCell, TableRow, IconButton } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'

interface RowProps {
  data: {
    instituteName: string
    type: string
    contactPerson: string
    email: string
    website: string
    phoneNumber: string
    lastTouch: string
    fresherCharges: string
    experienceCharges: string
    status: string
  }
  onEdit: () => void
  onDelete: () => void
}

const TableRowComponent: React.FC<RowProps> = ({ data, onEdit, onDelete }) => {
  return (
    <TableRow>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.instituteName}
      </TableCell>
      <TableCell align='center' style={{ textAlign: 'left' }}>
        {data.type}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.contactPerson}
      </TableCell>
      <TableCell align='center' style={{ textAlign: 'left' }}>
        {data.email}
      </TableCell>
      <TableCell align='center' style={{ textAlign: 'left' }}>
        {data.website}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.phoneNumber}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.lastTouch}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.fresherCharges}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.experienceCharges}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        {data.status}
      </TableCell>
      <TableCell align='center' style={{  textAlign: 'left' }}>
        <IconButton onClick={onEdit} sx={{ color: 'rgb(25,60,109)' }}><EditIcon /></IconButton>
        <IconButton onClick={onDelete} color='error'><DeleteIcon /></IconButton>
      </TableCell>
    </TableRow>
  )
}

export default TableRowComponent
