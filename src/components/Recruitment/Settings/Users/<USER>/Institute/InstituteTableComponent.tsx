import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
} from '@mui/material'
import TableRowComponent from './TableRowComponent'

interface Props {
  data: Array<{
    id: number
    instituteName: string
    type: string
    contactPerson: string
    email: string
    website: string
    phoneNumber: string
    lastTouch: string
    fresherCharges: string
    experienceCharges: string
    status: string
  }>
  onEdit: (index: number) => void
  onDelete: (index: number) => void
}


const InstituteTable: React.FC<Props> = ({ data, onEdit, onDelete }) => {
  return (
    <Box
      sx={{
        width: '100%',
        overflowX: 'auto',
        boxShadow: '0px 4px 10px rgba(0,0,0,0.15)',
        borderRadius: '8px',
      }}
    >
      <TableContainer component={Paper} sx={{ width: '100%', minWidth: 1500 }}>
        <Table
          sx={{
            width: '100%',
            borderCollapse: 'separate',
          }}
        >
          <TableHead sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
            <TableRow>
              {[
                'Institute Name',
                'Type',
                'Contact Person',
                'Email',
                'Website',
                'Phone Number',
                'Last Touch',
                'Fresher Charges',
                'Experience Charges',
                'Status',
                'Action',
              ].map((heading) => (
                <TableCell
                  key={heading}
                  sx={{
                    color: 'white',
                    fontWeight: 'normal',
                    fontSize: '14px',
                    padding: '12px 10px',
                    textAlign: 'center',
                  }}
                >
                  {heading}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          <TableBody>
            {data.length > 0 ? (
              data.map((row, index) => (
                <TableRowComponent
                  key={index}
                  data={row}
                  onEdit={() => onEdit(row.id)}
                  onDelete={() => onDelete(row.id)}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} align='center'>
                  No matching records found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  )
}

export default InstituteTable



   