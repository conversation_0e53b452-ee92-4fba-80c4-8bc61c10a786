import React, { useState } from 'react'
import { Box, FormControlLabel, Checkbox } from '@mui/material'

interface FilterProps {
  onToggle: (value: string | null) => void
}

const FilterComponent: React.FC<FilterProps> = ({ onToggle }) => {
  const [selected, setSelected] = useState<string | null>(null)

  const handleChange = (key: string) => () => {
    const newSelected = selected === key ? null : key
    setSelected(newSelected)

    onToggle(newSelected)
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        gap: 2,
        mt: 2,
      }}
    >
      <FormControlLabel
        control={
          <Checkbox checked={selected === 'tpos'} onChange={handleChange('tpos')} color='primary' />
        }
        label='TPOs'
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={selected === 'institutes'}
            onChange={handleChange('institutes')}
            color='primary'
          />
        }
        label='Institutes'
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={selected === 'consultancies'}
            onChange={handleChange('consultancies')}
            color='primary'
          />
        }
        label='Consultancies'
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={selected === 'college'}
            onChange={handleChange('college')}
            color='primary'
          />
        }
        label='College'
      />
    </Box>
  )
}

export default FilterComponent
