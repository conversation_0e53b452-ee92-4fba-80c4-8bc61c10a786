import React, { useEffect, useState } from 'react'
import { Box, Typography, TextField, Button, MenuItem } from '@mui/material'
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { Dayjs } from 'dayjs'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { RootState } from 'configureStore'
import { Dispatch } from 'redux'
import { addInstDetails, editInstDetails, subInstDetails } from 'actions'
import { connect } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { recruitmentEntity } from 'reducers'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

const validateEmail = (email: string) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return re.test(email)
}

const validatePhone = (phone: string) => {
  const re = /^\d{10}$/
  return re.test(phone)
}

const validateWebsite = (website: string) => {
  if (!website) return true
  const re = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
  return re.test(website)
}

const AddInstitutePage: React.FC<any> = ({
  addInstDetails,
  editInstDetails,
  editInstituteDetailsOptions,
  subInstDetails,
}: any) => {
  const { id } = useParams()
  const isEditMode = Boolean(id)
  const navigate = useNavigate()
  const [latestTouch, setLatestTouch] = useState<Dayjs | null>(null)
  const [feedbackData, setFeedbackData] = useState<string>('')
  const [contactPerson, setContactPerson] = useState<string>('')
  const [email, setEmail] = useState<string>('')
  const [contactNumbers, setContactNumbers] = useState<string>('')
  const [fresherCharges, setFresherCharges] = useState<string>('')
  const [experienceCharges, setExperienceCharges] = useState<string>('')
  const [status, setStatus] = useState<string>('Active')
  const [instituteName, setInstituteName] = useState<string>('')
  const [website, setWebsite] = useState<string>('')
  const [errors, setErrors] = useState({
    contactPerson: '',
    email: '',
    contactNumbers: '',
    fresherCharges: '',
    experienceCharges: '',
    instituteName: '',
    website: '',
  })

  useEffect(() => {
    if (isEditMode) {
      editInstDetails({ id: id })
    }
  }, [])
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '')
    setContactNumbers(value)
  }
  useEffect(() => {
    if (isEditMode && editInstituteDetailsOptions[0]) {
      setContactPerson(editInstituteDetailsOptions[0].name || '')
      setEmail(editInstituteDetailsOptions[0].email || '')
      setContactNumbers(editInstituteDetailsOptions[0].phone_no || '')
      setFresherCharges(editInstituteDetailsOptions[0].fresher_charges || '')
      setExperienceCharges(editInstituteDetailsOptions[0].experience_charges || '')
      setStatus(editInstituteDetailsOptions[0].status || 'Active')
      setInstituteName(editInstituteDetailsOptions[0].college_name || '')
      setWebsite(editInstituteDetailsOptions[0].website || '')
      setLatestTouch(
        editInstituteDetailsOptions[0].last_touch
          ? dayjs(editInstituteDetailsOptions[0].last_touch, 'DD-MM-YYYY')
          : null,
      )
      setFeedbackData(editInstituteDetailsOptions[0].notes || '')
    }
  }, [isEditMode, editInstituteDetailsOptions])

  const validateForm = () => {
    let valid = true
    const newErrors = {
      contactPerson: '',
      email: '',
      contactNumbers: '',
      fresherCharges: '',
      experienceCharges: '',
      instituteName: '',
      website: '',
    }
    if (!contactPerson.trim()) {
      newErrors.contactPerson = 'Contact person is required'
      valid = false
    }
    if (!email.trim()) {
      newErrors.email = 'Email is required'
      valid = false
    } else if (!validateEmail(email)) {
      newErrors.email = 'Please enter a valid email'
      valid = false
    }
    if (!contactNumbers.trim()) {
      newErrors.contactNumbers = 'Contact number is required'
      valid = false
    } else if (!validatePhone(contactNumbers)) {
      newErrors.contactNumbers = 'Please enter a 10-digit phone number'
      valid = false
    }
    if (!fresherCharges.trim()) {
      newErrors.fresherCharges = 'Fresher charges are required'
      valid = false
    }
    if (!experienceCharges.trim()) {
      newErrors.experienceCharges = 'Experience charges are required'
      valid = false
    }
    if (!instituteName.trim()) {
      newErrors.instituteName = 'Institute name is required'
      valid = false
    }
    if (!website.trim()) {
      newErrors.website = 'Website is required'
      valid = false
    } else if (!validateWebsite(website)) {
      newErrors.website = 'Please enter a valid website URL'
      valid = false
    }

    setErrors(newErrors)
    return valid
  }

  const handleSubmit = () => {
    if (!validateForm()) return

    const formData = {
      id: id,
      name: contactPerson,
      email: email,
      phone_no: contactNumbers,
      fresher_charges: fresherCharges,
      experience_charges: experienceCharges,
      status: status,
      stage: 23,
      college_name: instituteName,
      website: website,
      last_touch: latestTouch ? latestTouch.format('DD-MM-YYYY') : null,
      notes: feedbackData,
      representative_type: 'Institute',
    }
    toast.dismiss()
    localStorage.removeItem('showDeleteToast')
    
    if (isEditMode) {
      subInstDetails(formData)
      toast.success('Institute Details Edited Successfully', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })
    } else {
      addInstDetails(formData)
      toast.success('Institute Details Added Successfully', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })
    }
    localStorage.setItem('fromInstituteForm', 'true')
    localStorage.setItem('activeTab', '1')
    navigate('/home/<USER>/settings/users/index')
  }

  const handleCancel = () => {
    localStorage.setItem('fromInstituteForm', 'true')
    localStorage.setItem('activeTab', '1')
    navigate('/home/<USER>/settings/users/index')
  }

  const isFormValid = () => {
    return (
      contactPerson.trim() &&
      email.trim() &&
      validateEmail(email) &&
      contactNumbers.trim() &&
      validatePhone(contactNumbers) &&
      fresherCharges.trim() &&
      experienceCharges.trim() &&
      instituteName.trim() &&
      website.trim() &&
      validateWebsite(website)
    )
  }

  return (
    <Box
      sx={{
        backgroundColor: 'rgb(231, 235, 240)',
        minHeight: 'calc(100vh - 80px)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 5,
      }}
    >
      <Box
        sx={{
          width: '90%',
          backgroundColor: '#fff',
          padding: '24px',
          borderRadius: '20px',
          boxShadow: 3,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: '16px',
          }}
        >
          <Typography
            variant='body1'
            sx={{
              fontWeight: '800',
              fontSize: '32px',
              fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
              color: 'rgb(25, 60, 109)',
              paddingBottom: '8px',
              width: 'fit-content',
            }}
          >
            {isEditMode ? 'Edit Institute' : 'Add Institute'}
          </Typography>
        </Box>

        <Box
          component='form'
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          <Box sx={{ display: 'flex', gap: '16px' }}>
            <TextField
              label='Contact Person'
              value={contactPerson}
              onChange={(e) => setContactPerson(e.target.value)}
              variant='outlined'
              fullWidth
              sx={{ width: '50%' }}
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.contactPerson}
              helperText={errors.contactPerson}
              required
            />
            <TextField
              label='Email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              variant='outlined'
              fullWidth
              sx={{ width: '50%' }}
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.email}
              helperText={errors.email || 'e.g., <EMAIL>'}
              required
            />
          </Box>

          <Box sx={{ display: 'flex', gap: '16px' }}>
            <TextField
              label='Contact Numbers'
              value={contactNumbers}
              onChange={handlePhoneChange}
              //onChange={(e) => setContactNumbers(e.target.value)}
              variant='outlined'
              fullWidth
              InputProps={{
                sx: { borderRadius: '20px' },
                inputProps: {
                  maxLength: 10, // Limit to 10 digits
                },
              }}
              error={!!errors.contactNumbers}
              helperText={errors.contactNumbers || '10 digits only'}
              required
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                label='Latest Touch'
                value={latestTouch}
                onChange={(newValue) => setLatestTouch(newValue)}
                format='DD-MM-YYYY'
                maxDate={dayjs()}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    fullWidth: true,
                    InputProps: {
                      sx: { borderRadius: '20px' },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </Box>

          <Box sx={{ display: 'flex', gap: '16px' }}>
            <TextField
              label='Fresher Charges'
              value={fresherCharges}
              onChange={(e) => setFresherCharges(e.target.value)}
              variant='outlined'
              fullWidth
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.fresherCharges}
              helperText={errors.fresherCharges}
              required
            />
            <TextField
              label='Experience Charges'
              variant='outlined'
              value={experienceCharges}
              onChange={(e) => setExperienceCharges(e.target.value)}
              fullWidth
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.experienceCharges}
              helperText={errors.experienceCharges}
              required
            />
          </Box>

          <Box sx={{ display: 'flex', gap: '16px' }}>
            <TextField
              select
              label='Status'
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              variant='outlined'
              fullWidth
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
            >
              <MenuItem value='Active'>Active</MenuItem>
              <MenuItem value='Pending'>Pending</MenuItem>
              <MenuItem value='Inactive'>Inactive</MenuItem>
            </TextField>
            <TextField
              label='Institute Name'
              value={instituteName}
              onChange={(e) => setInstituteName(e.target.value)}
              variant='outlined'
              fullWidth
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.instituteName}
              helperText={errors.instituteName}
              required
            />
          </Box>

          <Box sx={{ display: 'flex', gap: '16px' }}>
            <TextField
              label='Website'
              value={website}
              onChange={(e) => setWebsite(e.target.value)}
              variant='outlined'
              sx={{ flex: 1, maxWidth: '50%' }}
              InputProps={{
                sx: { borderRadius: '20px' },
              }}
              error={!!errors.website}
              helperText={errors.website || 'e.g., https://example.com'}
              required
            />
            <Box sx={{ flex: 1 }} />
          </Box>

          <Box>
            <Typography variant='body1' sx={{ fontWeight: 'bold', marginBottom: 1 }}></Typography>
            <Box
              sx={{
                height: '150px',
                overflowY: 'auto',
                border: '1px solid #ccc',
                borderRadius: 1,
                padding: 1,
                marginBottom: 2,
              }}
            >
              <CKEditor
                editor={ClassicEditor}
                data={feedbackData}
                onChange={(_event, editor) => {
                  const data = editor.getData()
                  setFeedbackData(data)
                }}
                config={{
                  toolbar: [
                    'undo',
                    'redo',
                    '|',
                    'paragraph',
                    '|',
                    'bold',
                    'italic',
                    '|',
                    'link',
                    'insertTable',
                    'blockQuote',
                    '|',
                    'bulletedList',
                    'numberedList',
                    '|',
                    'outdent',
                    'indent',
                    'alignment',
                  ],
                }}
              />
            </Box>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: '24px', marginTop: '16px' }}>
            <Button
              onClick={handleCancel}
              variant='text'
              color='primary'
              sx={{
                borderRadius: '20px',
                opacity: '0.75',
                color: 'black',
                backgroundColor: 'rgb(226, 226, 226)',
                boxShadow: 'none',
                marginTop: '16px',
                width: '100px',
              }}
            >
              Cancel
            </Button>

            <Button
              type='submit'
              variant='contained'
              sx={{
                backgroundColor: 'rgb(25, 60, 109)',
                borderRadius: '20px',
                '&:hover': { backgroundColor: 'rgb(20, 50, 90)' },
                marginTop: '16px',
                alignSelf: 'flex-end',
              }}
              onClick={handleSubmit}
              disabled={!isFormValid()}
            >
              {isEditMode ? 'Submit Changes' : 'Submit'}
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  editInstituteDetailsOptions: recruitmentEntity.getRecruitment(state).EditInstDetails || {},
  institutes: recruitmentEntity.getRecruitment(state).AddInstDetails || {},
  subInstituteDetailsOptions: recruitmentEntity.getRecruitment(state).SubInstDetails || {},
})

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    addInstDetails: (data: { data: {} }) => dispatch(addInstDetails.request({ data })),
    editInstDetails: (data: { data: {} }) => dispatch(editInstDetails.request({ data })),
    subInstDetails: (data: { data: {} }) => dispatch(subInstDetails.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddInstitutePage)
