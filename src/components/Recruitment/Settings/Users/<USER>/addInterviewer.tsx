import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'

import { Container, Typography, Box, Paper, Button } from '@mui/material'
import FormFields2 from './formFields'
import {
  fetchRounds,
  fetchJobExperience,
  addInterviewerData,
  editInterviewerData,
} from '../../../../../actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
import { toast } from 'react-toastify'

interface Job {
  job_name: string
  name: string
}

interface Experience {
  experience_years: string
  experience: string
}

function AddCandidate(props: any) {
  const location = useLocation()
  const [errors, setErrors] = useState<Record<string, string>>({})

  const candidate = location.state?.candidate || null
  const navigate = useNavigate()

  const getFirstAndLastName = (fullName: string) => {
    if (!fullName) return { first_name: '', last_name: '' }
    const nameParts = fullName.trim().split(' ')
    return {
      first_name: nameParts[0] || '',
      last_name: nameParts.slice(1).join(' ') || '',
    }
  }

  const { first_name, last_name } = candidate
    ? getFirstAndLastName(candidate.name)
    : { first_name: '', last_name: '' }

  const [formData, setFormData] = useState({
    id: candidate ? candidate.id : -1,
    first_name,
    last_name,
    email: candidate ? candidate.email : '',
    phone_no: candidate ? candidate.phone_no : '',
    role: candidate ? candidate.role : '',
    password: candidate ? candidate.password : '',
    confirmPassword: candidate ? candidate.confirmPassword : '',
    round: candidate ? candidate.round : '',
    position: candidate ? candidate.position : '',
    status: candidate ? candidate.status : '',
    experience: candidate ? candidate.experience : '',
  })
  const {
    fetchRounds,
    roundData,
    fetchJobExperience,
    jobExperienceData,
    addInterviewerData,
    responseSubmit,
    editInterviewerData,
    responseUpdate,
  } = props
  const rounds = Array.isArray(roundData) && Array.isArray(roundData[0]) ? roundData[0] : []
  const roundArray = rounds.map((item) => item.round_name)
  const job = jobExperienceData[1] as Job[]
  const experience = jobExperienceData[0] as Experience[]

  const jobOption = Array.isArray(job) ? job.map((item: Job) => item.name) : []

  const experienceOption = Array.isArray(experience)
    ? experience.map((item: Experience) => item.experience)
    : []

  useEffect(() => {
    fetchRounds()
  }, [])
  useEffect(() => {
    fetchJobExperience()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    if (name === 'phone_no') {
      const numericValue = value.replace(/\D/g, '')
      setFormData((prevData) => ({
        ...prevData,
        [name]: numericValue,
      }))
    } else if (name === 'email') {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }))
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: emailPattern.test(value) ? '' : 'Invalid email format',
      }))
    } else if (name === 'first_name' || name === 'last_name') {
      const trimmedValue = value.replace(/\s/g, '')
      setFormData((prevData) => ({
        ...prevData,
        [name]: trimmedValue,
      }))
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: name === 'role' ? Number(value) : value,
      }))
    }

    if (name === 'password' || name === 'confirmPassword') {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }))
      setErrors((prevErrors) => ({
        ...prevErrors,
        confirmPassword:
          name === 'confirmPassword' && value !== formData.password ? 'Passwords do not match' : '',
      }))
    }
  }

  const isFormValid = () => {
    return (
      Object.values(formData).every(
        (field) => (typeof field === 'string' && field.trim() !== '') || typeof field === 'number',
      ) && formData.password == formData.confirmPassword
    )
  }

  const handleSubmit = async (e?: React.FormEvent<HTMLFormElement>) => { 
    if (e) e.preventDefault()
  
    const {
      id,
      first_name,
      last_name,
      email,
      phone_no,
      role,
      password,
      round,
      position,
      status,
      experience,
    } = formData
  
    const name = first_name + ' ' + last_name
  
    if (candidate) {
      try {
        const response = await editInterviewerData({
          id,
          email,
          name,
          password,
          phone_no,
          status,
        })
  
          toast.success('Data Updated successfully!')
      } catch (error) {
        toast.error('Something went wrong')
      }
    } else {
      try {
        const response = await addInterviewerData({ 
          id, first_name, last_name, email, phone_no, role, password, round, position, status, experience 
        }) 
          toast.success('Data added successfully!')
        }
       catch (error) {
        toast.error('Something went wrong')
      }
    }
  
    setTimeout(() => {
      navigate(-1)
    }, 100)
  }
    useEffect(() => {
    if (responseUpdate?.success) {
      navigate(-1)
    }
  }, [responseUpdate, navigate])
  useEffect(() => {
    if (responseSubmit?.success) {
      navigate(-1)
    }
  }, [responseSubmit, navigate])

  return (
    <Container
      sx={{
        padding: '10px',
        borderRadius: '15px',
        backgroundColor: '',
        width: '70%',
        fontFamily: 'Montserrat',
      }}
    >
      <Paper
        sx={{
          padding: '10px',
          borderRadius: '15px',
          width: '100%',
          backgroundColor: '',
          boxShadow: '1px 4px 10px rgba(0, 0, 0, 0.1)',
        }}
        elevation={3}
      >
        <Box display='flex' fontFamily={'Montserrat'} justifyContent='center' sx={{ mb: 3 }}>
          <Typography variant='h5' sx={{ color: 'rgba(0, 0, 0, 0.87)' }}>
            {candidate ? 'Edit Interviewer' : 'Add Interviewer'}
          </Typography>
        </Box>
        <form onSubmit={handleSubmit}>
          <FormFields2
            formData={formData}
            handleChange={handleChange}
            roundArray={roundArray}
            jobOption={jobOption}
            experienceOption={experienceOption}
            errors={errors}
          />

          <Box display='flex' justifyContent='end' gap={2} mt={2}>
            <Button
              variant='contained'
              sx={{
                backgroundColor: 'rgb(226, 226, 226)',
                color: '#000000',
                padding: '8px 16px',
                borderRadius: '25px',
                fontFamily: 'Montserrat, sans-serif',
                fontSize: '14px',
                minWidth: '100px',
                '&:hover': {
                  backgroundColor: 'rgb(226, 226, 226)',
                  color: 'black',
                },
              }}
              onClick={() => {
                setFormData({
                  id: -1,
                  first_name: '',
                  last_name: '',
                  email: '',
                  phone_no: '',
                  role: '',
                  status: '',
                  password: '',
                  confirmPassword: '',
                  round: '',
                  position: '',
                  experience: '',
                })
                navigate(-1)
              }}
            >
              CANCEL
            </Button>
            <Button
              type='submit'
              variant='contained'
              sx={{
                color: 'white',
                padding: '8px 16px',
                borderRadius: '25px',
                fontFamily: 'Montserrat, sans-serif',
                fontSize: '14px',
                minWidth: '100px',
              }}
              disabled={!isFormValid()}
            >
              {candidate ? 'UPDATE' : 'CREATE'}
            </Button>
          </Box>
        </form>
      </Paper>
    </Container>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    roundData: recruitmentEntity.getRecruitment(state).getRoundData,
    jobExperienceData: recruitmentEntity.getRecruitment(state).getJobExperience,
    responseSubmit: recruitmentEntity.getRecruitment(state).postInterviewerData,
    responseUpdate: recruitmentEntity.getRecruitment(state).postEditInterviewerData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchRounds: () => dispatch(fetchRounds.request()),
    fetchJobExperience: () => dispatch(fetchJobExperience.request()),
    addInterviewerData: (data: any) => dispatch(addInterviewerData.request(data)),
    editInterviewerData: (data: any) => dispatch(editInterviewerData.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddCandidate)
