export interface JoinerData {
  email: string
  id: number
  phone_no: string
  qualification: string
  stage: string
  status: string
  name: string
  college_name: string
}

export type JoinersData = JoinerData[]
export interface Interviewer {
    email:string
    id:number
    phone_no:string
    qualification:string
    stage:string
    status:string
    name:string
    college_name: string;
}

export type Interviewers = Interviewer[]
