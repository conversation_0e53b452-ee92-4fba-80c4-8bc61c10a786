import { useEffect, useState } from 'react'
import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Paper from '@mui/material/Paper'
import IconButton from '@mui/material/IconButton'
import DeleteIcon from '@mui/icons-material/Delete'
import DescriptionIcon from '@mui/icons-material/Description'
import EditIcon from '@mui/icons-material/Edit'
import Tooltip from '@mui/material/Tooltip'
import FormControlLabel from '@mui/material/FormControlLabel'
import Checkbox from '@mui/material/Checkbox'
import TableSortLabel from '@mui/material/TableSortLabel'
import { Box, Pagination, TableCell, tableCellClasses, Typography } from '@mui/material'
import style from '../../../../../utils/styles.json'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { connect } from 'react-redux'
import {
  editExpectedJoiners,
  fetchDeleteExpectedJoiners,
  fetchExpectedJoiners,
} from '../../../../../actions'

import { Dispatch } from 'redux'
import { JoinersData } from '../Types'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { toast } from 'react-toastify'
import Loader from 'components/Common/Loader'
import { useJoinerContext } from './JoinerContext'

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  zIndex: 1,
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
}))

const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    padding: '6px 16px',
    '& .MuiTableSortLabel-root': {
      color: 'white',
    },
    '& .MuiTableSortLabel-icon': {
      fill: 'white !important',
    },
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    padding: '5px',
  },
}))

interface Props {
  searchQuery: string
  expectedJoinerData: JoinersData
  fetchExpectedJoinersData: ({}) => void
  fetchDeleteExpectedJoiners: (id: number) => void
  onEdit: (data: any) => void
  getAddExpectedJoinerData: any
 getEditExpectedJoinerData: any
  getDeleteExpectedJoinerData: any
  isExpectedJoinerData: any
}

function JoinerDetails(props: Props) {
  const {
    fetchExpectedJoinersData,
    expectedJoinerData,
    searchQuery,
    fetchDeleteExpectedJoiners,
    onEdit,
    getAddExpectedJoinerData,
    getEditExpectedJoinerData,
    getDeleteExpectedJoinerData,
    isExpectedJoinerData,
  } = props

  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({})
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const [orderBy, setOrderBy] = useState<keyof typeof expectedJoinerData[0]>('name')
  const [page, setPage] = useState(1)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const {state} = useJoinerContext()

  const rowsPerPage = 10

  useEffect(() => {
    fetchExpectedJoinersData({
      "pageNumber": page,
      "limit": 20,
      "search_input": "",
      "batchId": state.batchOption
  })
  }, [getAddExpectedJoinerData, getEditExpectedJoinerData, getDeleteExpectedJoinerData,state.batchOption,page])

  const handleSort = (property: keyof typeof expectedJoinerData[0]) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }

  const handleDeleteClick = (id: number) => {
    setSelectedId(id)
    setOpenDialog(true)
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
    setSelectedId(null)
  }

  const handleConfirmDelete = () => {
    if (selectedId !== null) {
      fetchDeleteExpectedJoiners(selectedId)
      handleCloseDialog()
      toast.success('Deleted Expected Joiner Successfully')
    }
  }

  const handleCheckboxChange = (rowId: string) => {
    setSelectedRows((prev) => ({
      ...prev,
      [rowId]: !prev[rowId],
    }))
  }

  const handleSelectAllChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked
    const newSelection = isChecked
      ? Object.fromEntries(expectedJoinerData.map((row) => [row.name, true]))
      : {}
    setSelectedRows(newSelection)
  }

  const filteredRows = expectedJoinerData
    .filter((joiner) => {
      const name = joiner?.name?.toLowerCase() || ''
      const email = joiner?.email?.toLowerCase() || ''
      const phoneNo = joiner?.phone_no || ''
      const qualification = joiner?.qualification?.toLowerCase() || ''
      const college = joiner?.college_name?.toLowerCase() || ''
      const status = joiner?.status?.toLowerCase() || ''
      const query = searchQuery?.toLowerCase() || ''
      return (
        name.includes(query) ||
        email.includes(query) ||
        phoneNo.includes(query) ||
        qualification.includes(query) ||
        college.includes(query) ||
        status.includes(query)
      )
    })
    .sort((a, b) => {
      const aVal = a[orderBy] ?? ''
      const bVal = b[orderBy] ?? ''
      return order === 'asc'
        ? String(aVal).localeCompare(String(bVal))
        : String(bVal).localeCompare(String(aVal))
    })

  const paginatedRows = filteredRows.slice((page - 1) * rowsPerPage, page * rowsPerPage)
  const currentRowKeys = paginatedRows.map((row) => row.name)
  const selectedCount = currentRowKeys.filter((key) => selectedRows[key]).length

  const isAllSelected = selectedCount === currentRowKeys.length && currentRowKeys.length > 0
  const isIndeterminate = selectedCount > 0 && selectedCount < currentRowKeys.length


  
  return (
    <>
    {!paginatedRows && <Loader state={!isExpectedJoinerData} />}
    <Paper sx={{ p: 3, overflow: 'hidden', boxShadow: 3 }}>
      <TableContainer component={Paper} sx={{ backgroundColor: 'white', maxHeight: '100%' }}>
        <Table sx={{ width: '100%', tableLayout: 'fixed' }} aria-label='customized table'>
          <StyledTableHead>
            <TableRow>
              <StyledTableCell>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isAllSelected}
                      indeterminate={isIndeterminate}
                      onChange={handleSelectAllChange}
                      sx={{
                        color: 'white',
                        '&.Mui-checked': { color: 'white' },
                        '&.MuiCheckbox-indeterminate': { color: 'white' },
                      }}
                    />
                  }
                  label=''
                  sx={{ marginLeft: '12px' }}
                />
              </StyledTableCell>
              <StyledTableCell>
                <TableSortLabel
                  active={orderBy === 'name'}
                  direction={orderBy === 'name' ? order : 'asc'}
                  onClick={() => handleSort('name')}
                >
                  Name
                </TableSortLabel>
              </StyledTableCell>
              <StyledTableCell>Email</StyledTableCell>
              <StyledTableCell>Qualification</StyledTableCell>
              <StyledTableCell>Organization Name</StyledTableCell>
              <StyledTableCell>Phone Number</StyledTableCell>
              <StyledTableCell>Application Status</StyledTableCell>
              <StyledTableCell>Actions</StyledTableCell>
            </TableRow>
          </StyledTableHead>

          <TableBody>
            {paginatedRows.length>0?( paginatedRows.map((row) => (
              <TableRow key={row.name}>
                <StyledTableCell>
                  <Checkbox
                    checked={!!selectedRows[row.name]}
                    onChange={() => handleCheckboxChange(row.name)}
                  />
                </StyledTableCell>
                <StyledTableCell>{row.name}</StyledTableCell>
                <StyledTableCell>{row.email}</StyledTableCell>
                <StyledTableCell>{row.qualification}</StyledTableCell>
                <StyledTableCell>{row.college_name}</StyledTableCell>
                <StyledTableCell>{row.phone_no}</StyledTableCell>
                <StyledTableCell>{row.status}</StyledTableCell>
                <StyledTableCell>
                  <Tooltip title='View Resume'>
                    <IconButton color='primary' onClick={() => toast.error('Resume Not Found')}>
                      <DescriptionIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title='Edit'>
                    <IconButton color='primary' onClick={() => onEdit(row)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title='Delete'>
                    <IconButton
                      sx={{ color: '#db3700' }}
                      onClick={() => handleDeleteClick(row.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </StyledTableCell>
              </TableRow>
            ))):(
              <TableRow>
                <TableCell colSpan={8} align="center">
                  No Data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
          <Pagination
            count={Math.ceil(filteredRows.length / rowsPerPage)}
            color='primary'
            page={page}
            onChange={(event, newPage) => setPage(newPage)}
            sx={{ marginBottom: '5px' }}
          />
        </Box>
      </TableContainer>

      <DeleteConfirmationDialog
        open={openDialog}
        handleClose={handleCloseDialog}
        handleConfirm={handleConfirmDelete}
        message='Are you sure you want to delete this joiner?'
      />
    </Paper>
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  expectedJoinerData: recruitmentEntity.getRecruitment(state).getExpectedJoinerData as JoinersData,
  getAddExpectedJoinerData: recruitmentEntity.getRecruitment(state).getAddExpectedJoinerData,
  getEditExpectedJoinerData: recruitmentEntity.getRecruitment(state).editExpectedJoiners,
  getDeleteExpectedJoinerData: recruitmentEntity.getRecruitment(state).fetchDeleteExpectedJoiners,
  isExpectedJoinerData: recruitmentStateUI.getRecruitment(state).isExpectedJoinerData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchExpectedJoinersData: (data:{}) => dispatch(fetchExpectedJoiners.request({data})),
  fetchDeleteExpectedJoiners: (id: number) => dispatch(fetchDeleteExpectedJoiners.request({ id })),
 editExpectedJoiners: (id: number) => dispatch(editExpectedJoiners.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(JoinerDetails)
