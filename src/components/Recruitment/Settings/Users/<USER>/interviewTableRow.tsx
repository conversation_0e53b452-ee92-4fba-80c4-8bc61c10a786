import React, { useState } from 'react';
import { TableRow, TableCell, IconButton, Tooltip } from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';
import { InterviewUserData } from './initialInterviewUserData';
import DeleteConfirmationDialog from '../../../Common/DeleteConfirmationDialog';

interface InterviewTableRowProps {
  user: InterviewUserData;
  onEdit: (user: InterviewUserData) => void;
  onDelete: (id: number) => void;
}

const InterviewTableRow: React.FC<InterviewTableRowProps> = ({ user, onEdit, onDelete }) => {
  const [isDialogOpen, setDialogOpen] = useState(false);
  

  const handleDeleteClick = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    onDelete(user.id);
    setDialogOpen(false);
  };

  return (
    <>
      <TableRow hover sx={{ padding: '4px' }}>
        <TableCell sx={{ textAlign: 'center', padding: '4px', fontFamily: 'Montserrat-Medium' }}>
          {user.name}
        </TableCell>
        <TableCell sx={{ textAlign: 'center', padding: '4px', fontFamily: 'Montserrat-Medium' }}>
          {user.email}
        </TableCell>
        <TableCell sx={{ textAlign: 'center', padding: '4px', fontFamily: 'Montserrat-Medium' }}>
          {user.phone_no}
        </TableCell>
        <TableCell sx={{ textAlign: 'center', padding: '4px', fontFamily: 'Montserrat-Medium' }}>
          {user.status}
        </TableCell>
        <TableCell sx={{ textAlign: 'center', padding: '4px', fontFamily: 'Montserrat-Medium' }}>
          <Tooltip title='Edit User'>
            <IconButton color='primary' onClick={() => onEdit(user)}>
              <Edit />
            </IconButton>
          </Tooltip>
          <Tooltip title='Delete User'>
            <IconButton color='error' onClick={handleDeleteClick}>
              <Delete />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={isDialogOpen}
        handleClose={handleCloseDialog}
        handleConfirm={handleConfirmDelete}
      />
    </>
  );
};

export default InterviewTableRow;
