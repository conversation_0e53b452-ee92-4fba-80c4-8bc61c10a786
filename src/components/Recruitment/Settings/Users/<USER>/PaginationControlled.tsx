import * as React from 'react'
import Typography from '@mui/material/Typography'
import Pagination from '@mui/material/Pagination'
import Stack from '@mui/material/Stack'
import Box from '@mui/material/Box'

export default function PaginationControlled() {
  const [page, setPage] = React.useState(1)
  const handleChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  return (
    <Box sx={{ display: 'flex', justifyContent: 'flex-end', width: '100%', p: 2 }}>
      <Stack spacing={2} alignItems='flex-end'>
        <Typography>Page: {page}</Typography>
        <Pagination
          count={10}
          page={page}
          onChange={handleChange}
          sx={{
            '& .MuiPaginationItem-root': {
              fontSize: '16px',
            },
            '& .MuiPaginationItem-page.Mui-selected': {
              backgroundColor: 'rgb(25,60,109)',
              color: '#fff',
            },
          }}
        />
      </Stack>
    </Box>
  )
}
