import React, { <PERSON>actNode, useEffect, useState } from 'react'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  IconButton,
  Pagination,
  TableSortLabel,
  Typography,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import SearchBar from './SearchBar'
import AddTpoModal from './AddTpoModal'
import FilterButton from './FilterButton'
import EditTPODialog from './EditTPODialog'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../../reducers'
import { RootState } from '../../../../../../configureStore'
import { DeleteTpo, fetchTpoDetails } from '../../../../../../actions'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { StyledTableCellForMyTeam } from 'components/Common/CommonStyles'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

function TPOTable(props: any) {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [page, setPage] = useState<number>(1)
  const itemsPerPage: number = 6
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedTPO, setSelectedTPO] = useState<any>(null)
  const {
    fetchTPOData,
    TPOOptions,
    deleteTpo,
    isConsultancyLoaded,
    isEditorLoaded,
    deleteTpoResponse,
  } = props
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedTPOId, setSelectedTPOId] = useState<number | null>(null)

  useEffect(() => {
    fetchTPOData({ representative_type: 'TPO' })
  }, [isConsultancyLoaded, isEditorLoaded, deleteTpoResponse])

  const handleEditClick = (tpo: any) => {
    setSelectedTPO(tpo)
    setEditDialogOpen(true)
  }

  const handleConfirm = () => {
    if (selectedTPOId !== null) {
      deleteTpo(selectedTPOId)
      setDeleteDialogOpen(false)
      setSelectedTPOId(null)
      toast.success('TPO Delete Successfully!')
    }
  }
  const handleDeleteClick = (id: number) => {
    setSelectedTPOId(id)
    setDeleteDialogOpen(true)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleEmail = () => {
    toast.error('Email not found')
  }
  const handleSort = () => {
    setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))
  }

  const sortedData = [...(TPOOptions[0] || [])].sort((a, b) => {
    if (sortOrder === 'asc') {
      return a.name.localeCompare(b.name)
    } else {
      return b.name.localeCompare(a.name)
    }
  })

  const filteredData = sortedData.filter((row) =>
    row.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '10px',
        }}
      >
        <Box sx={{ maxWidth: '220px', flex: 1 }}>
          <SearchBar label='Search Person' value={searchTerm} onChange={setSearchTerm} />
        </Box>
        <Box sx={{ display: 'flex', gap: '10px', font: 'Montserrat-Medium' }}>
          <ActionButton
            variant='contained'
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '20px',
              padding: '8px 20px',
            }}
            onClick={() => setIsModalOpen(true)}
          >
            Add TPO
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleEmail}
            disabled
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '20px',
              padding: '8px 20px',
            }}
          >
            Send Test Email
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleEmail}
            disabled
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '20px',
              padding: '8px 20px',
            }}
          >
            Send Email
          </ActionButton>
        </Box>
      </Box>

      <FilterButton open={isFilterOpen} />

      <TableContainer component={Paper} sx={{ overflow: 'hidden', flex: 1 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#0d3c6e' }}>
              <StyledTableCellForMyTeam
                sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center', mWidth: 120 }}
              >
                <TableSortLabel
                  active={true}
                  direction={sortOrder}
                  onClick={handleSort}
                  sx={{
                    color: 'white',
                    '& .MuiTableSortLabel-icon': { color: 'white !important' },
                  }}
                ></TableSortLabel>
                Contact Person
              </StyledTableCellForMyTeam>
              {[
                'Type',
                'Email',
                'College Name',
                'Last Touch',
                'Phone Number',
                'Status',
                'Actions',
              ].map((heading) => (
                <StyledTableCellForMyTeam
                  key={heading}
                  sx={{
                    color: 'white',
                    fontWeight: 'bold',
                  }}
                >
                  {heading}
                </StyledTableCellForMyTeam>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredData.length > 0 ? (
              filteredData.slice((page - 1) * itemsPerPage, page * itemsPerPage).map((row) => (
                <TableRow key={row.id}>
                  <StyledTableCellForMyTeam>{row.name}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.representative_type}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.email}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.college_name}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.last_touch}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.phone_no}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>{row.status}</StyledTableCellForMyTeam>
                  <StyledTableCellForMyTeam>
                    <IconButton onClick={() => handleEditClick(row)}>
                      <EditIcon color='primary' />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(row.id)}>
                      <DeleteIcon color='error' />
                    </IconButton>
                  </StyledTableCellForMyTeam>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} align='center'>
                  <Typography variant='h6' color='textSecondary'>
                    No data found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
        <Pagination
          count={Math.ceil(TPOOptions[0]?.length / itemsPerPage)}
          color='primary'
          page={page}
          onChange={handlePageChange}
        />
      </Box>

      <AddTpoModal open={isModalOpen} onClose={() => setIsModalOpen(false)} />
      <EditTPODialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        tpo={selectedTPO}
      />
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={() => setDeleteDialogOpen(false)}
        handleConfirm={handleConfirm}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  TPOOptions: recruitmentEntity.getRecruitment(state).getTpoDetails,
  isConsultancyLoaded: recruitmentStateUI.getRecruitment(state).AddTpo,
  deleteTpoResponse: recruitmentEntity.getRecruitment(state).deleteTpo,
  isEditorLoaded: recruitmentStateUI.getRecruitment(state).editTpo,
})
const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchTPOData: (data: {}) => dispatch(fetchTpoDetails.request({ data })),
  deleteTpo: (id: number) => dispatch(DeleteTpo.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(TPOTable)
