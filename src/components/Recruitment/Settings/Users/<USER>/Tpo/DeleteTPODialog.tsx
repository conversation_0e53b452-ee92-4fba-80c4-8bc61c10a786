import React from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from '@mui/material'

interface DeleteTPODialogProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}

const DeleteTPODialog: React.FC<DeleteTPODialogProps> = ({ open, onClose, onConfirm }) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth='xs' fullWidth>
      <DialogTitle>Delete Confirmation</DialogTitle>
      <DialogContent>
        <Typography>Are you sure you want to delete this TPO?</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color='secondary'>
          Cancel
        </Button>
        <Button onClick={onConfirm} variant='contained' color='error'>
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DeleteTPODialog
