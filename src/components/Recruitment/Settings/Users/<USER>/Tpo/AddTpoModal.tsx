import React, { useState } from 'react'
import { Dialog, DialogTitle, DialogContent, TextField, Box, MenuItem } from '@mui/material'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { AddTpo } from 'actions'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

interface AddTpoModalProps {
  open: boolean
  onClose: () => void
  fetchAddTPO: (data: any) => void
}

const AddTpoModal: React.FC<AddTpoModalProps> = ({ open, onClose, fetchAddTPO }) => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    college_name: '',
    phone_no: '',
    representative_type: 'TPO',
    last_touch: '',
    status: '',
    notes: '',
  })
  const [body, setBody] = useState('')
  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    college_name: '',
    phone_no: '',
    representative_type: '',
    last_touch: '',
    status: '',
    notes: '',
  })

  const statusOptions = ['Pending', 'In Progress', 'Completed']
  const collegeOptions = ['College A', 'College B', 'College C']

  const validateField = (name: string, value: string) => {
    let error = ''
    switch (name) {
      case 'name':
      case 'contactPerson':
        if (!value.trim()) error = 'This field is required'
        else if (!/^[A-Za-z\s]+$/.test(value)) error = 'Only letters and spaces are allowed'
        break
      case 'email':
        if (!value.trim()) error = 'Email is required'
        else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) error = 'Invalid email format'
        break
      case 'college':
        if (!value.trim()) error = 'College is required'
        break
      case 'phone_no':
        if (!value.trim()) error = 'Phone number is required'
        else if (!/^\d{10}$/.test(value)) error = 'Phone number must be exactly 10 digits'
        break
      case 'lastTouch':
        if (!value) error = 'Last touch date is required'
        break
      case 'status':
        if (!value) error = 'Status is required'
        break
      case 'body':
        if (!value.trim()) error = 'Message is required'
        break
      default:
        break
    }
    return error
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })
    setFormErrors({ ...formErrors, [name]: validateField(name, value) })
  }

  const handleDateChange = (date: any) => {
    setForm((prev) => ({ ...prev, last_touch: date || null }))
    setFormErrors((prev) => ({
      ...prev,
      last_touch: validateField('last_touch', date ? date.toString() : ''),
    }))
  }

  const handleEditorChange = (event: any, editor: any) => {
    const data = editor.getData().trim()

    setBody(data)
    form.notes = data
    setFormErrors({ ...formErrors, notes: validateField('notes', data) })
  }

  const isFormValid = () => {
    return (
      Object.values(formErrors).every((error) => error === '') &&
      Object.values(form).every((value) => value !== null && value.toString().trim() !== '') &&
      body.trim() !== ''
    )
  }

  const handleSubmit = () => {
    if (!isFormValid()) return

    const payload = { ...form, message: body }
    fetchAddTPO(payload)
    onClose()
    toast.success('TPO Added Successfully!')
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' sx={{ width: '950px', margin: 'auto' }}>
      <DialogTitle sx={{ background: '#0d3c6e', color: 'white', textAlign: 'center' }}>
        Add TPO
      </DialogTitle>
      <DialogContent sx={{ padding: 3, display: 'flex', flexDirection: 'column' }}>
        <TextField
          name='name'
          label='Name'
          value={form.name}
          onChange={handleChange}
          fullWidth
          error={!!formErrors.name}
          helperText={formErrors.name}
          InputProps={{
            sx: { borderRadius: '24px' },
          }}
        />

        <TextField
          name='email'
          label='Email'
          value={form.email}
          onChange={handleChange}
          fullWidth
          error={!!formErrors.email}
          helperText={formErrors.email}
          InputProps={{
            sx: { borderRadius: '24px' },
          }}
        />

        <TextField
          name='college_name'
          label='College'
          value={form.college_name}
          onChange={handleChange}
          fullWidth
          error={!!formErrors.college_name}
          helperText={formErrors.college_name}
          InputProps={{
            sx: { borderRadius: '24px' },
          }}
        />

        <TextField
          name='phone_no'
          label='Phone Number'
          value={form.phone_no}
          onChange={handleChange}
          fullWidth
          error={!!formErrors.phone_no}
          helperText={formErrors.phone_no}
          InputProps={{
            sx: { borderRadius: '24px' },
          }}
        />

        <Box sx={{ display: 'flex', gap: 1 }}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label='Latest Touch'
              value={form.last_touch}
              onChange={(date) => handleDateChange(date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!formErrors.last_touch,
                  helperText: formErrors.last_touch,
                  sx: {
                    borderRadius: '24px',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '24px',
                    },
                  },
                },
              }}
            />
          </LocalizationProvider>

          <TextField
            select
            name='status'
            label='Status'
            value={form.status}
            onChange={handleChange}
            fullWidth
            error={!!formErrors.status}
            helperText={formErrors.status}
            InputProps={{
              sx: { borderRadius: '24px' },
            }}
          >
            {statusOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </TextField>
        </Box>
        <CKEditor editor={ClassicEditor} data={body} onChange={handleEditorChange} />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <ActionButton
            onClick={onClose}
            sx={{
              borderRadius: '24px',
              marginRight: '19px',
              marginBottom: '4px',
              marginTop: '17px',
            }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleSubmit}
            disabled={!isFormValid()}
            sx={{
              borderRadius: '24px',
              marginBottom: '4px',
              marginTop: '17px',
              marginRight: '5px',
            }}
          >
            Submit
          </ActionButton>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchAddTPO: (data: any) => dispatch(AddTpo.request({ data })),
})
export default connect(null, mapDispatchToProps)(AddTpoModal)
