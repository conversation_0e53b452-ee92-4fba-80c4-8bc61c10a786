import React from 'react'
import { Edit, Delete } from '@mui/icons-material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface TPOActionsProps {
  onEdit: () => void
  onDelete: () => void
}

const TPOActions: React.FC<TPOActionsProps> = ({ onEdit, onDelete }) => {
  return (
    <>
      <ActionButton size='small' sx={{ color: '#0d3c6e' }} onClick={onEdit}>
        <Edit />
      </ActionButton>
      <ActionButton size='small' color='error' onClick={onDelete}>
        <Delete />
      </ActionButton>
    </>
  )
}

export default TPOActions
