import React, { useState, useEffect } from 'react'
import { Box, Paper, Table, TableBody, TableContainer, TableCell, TableRow, TableSortLabel } from '@mui/material'
import InterviewerHeader from './interviewerHeader'
import InterviewerPagination from './interviewerPagination'
import InterviewTableHeader from './interviewerTableHeader'
import InterviewTableRow from './interviewTableRow'
import { InterviewUserData } from './initialInterviewUserData'
import { fetchInterviewer, deleteInterviewerData } from '../../../../../actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
import { Interviewers } from '../Types'
import { useNavigate } from 'react-router-dom'

function InterviewerTable(props: any) {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const rowsPerPage = 10
  const { fetchInterviewer, interviewerData, deleteInterviewerData } = props
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')

  useEffect(() => {
    fetchInterviewer({})
  }, [])

  const handleDelete = (id: number) => {
    deleteInterviewerData({ id })
    fetchInterviewer({})
  }

  const handleEditClick = (user: InterviewUserData) => {
    const updatedUser = {
      ...user,
      password: null,
      confirmPassword: null,
    };
  
    navigate('/home/<USER>/settings/users/interviewer/addinterviewer', {
      state: { candidate: updatedUser },
    });
  };
  

  const handleSort = () => {
    setOrder(order === 'asc' ? 'desc' : 'asc')
  }

  let Data = Array.isArray(interviewerData) && Array.isArray(interviewerData[0]) ? interviewerData[0] : []
  const filteredUsers = Data.filter((user) => user.name.toLowerCase().includes(searchTerm.toLowerCase()))

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    return order === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
  })

  const totalPages = Math.ceil(sortedUsers.length / rowsPerPage)
  const paginatedUsers = sortedUsers.slice((page - 1) * rowsPerPage, page * rowsPerPage)

  return (
    <Box sx={{ padding: 0 }}>
      <InterviewerHeader searchTerm={searchTerm} setSearchTerm={setSearchTerm} />

      <Paper sx={{ height: 'calc(100% - 100px)', overflow: 'hidden', boxShadow: 3, display: 'flex', flexDirection: 'column' }}>
        <TableContainer sx={{ flexGrow: 1, overflow: 'auto' }}>
          <Table stickyHeader>
          <InterviewTableHeader sortOrder={order} handleSort={handleSort}/>
       

            <TableBody>
              {paginatedUsers.length > 0 ? (
                paginatedUsers.map((user) => (
                  <InterviewTableRow key={user.id} user={user} onEdit={handleEditClick} onDelete={handleDelete} />
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} sx={{ textAlign: 'center', color: 'gray' }}>
                    No data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', padding: 2 }}>
          <InterviewerPagination totalPages={totalPages} page={page} setPage={setPage} />
        </Box>
      </Paper>
    </Box>
  )
}

const mapStateToProps = (state:RootState) => {
  return {
    interviewerData: recruitmentEntity.getRecruitment(state).getInterviewer as Interviewers,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchInterviewer: () => dispatch(fetchInterviewer.request({})),
    deleteInterviewerData: (id: number) => dispatch(deleteInterviewerData.request({ id })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(InterviewerTable)
