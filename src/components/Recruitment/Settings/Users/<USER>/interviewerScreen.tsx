import React from 'react'
import InterviewerTable from './interviewerTable'
import { Box, Paper, Typography } from '@mui/material'
import TopHeading from './TopHeading'

const InterviewerScreen: React.FC = () => {
  return ( 
    <Box display="flex" justifyContent="center" paddingTop="15px" paddingLeft="20px" paddingRight="20px" alignItems="baseline" minHeight="100vh">
      <Paper elevation={3} sx={{ width: '98%', padding: '25px', backgroundColor: 'white', borderRadius: '1px' }}>
      <TopHeading/>
        <InterviewerTable />
      </Paper>
    </Box>
  )
}

export default InterviewerScreen
