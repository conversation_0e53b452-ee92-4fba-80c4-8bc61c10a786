import * as React from 'react'
import Grid from '@mui/material/Grid'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import { useState } from 'react'
import { Collapse, Box } from '@mui/material'
import Buttons from './JoinerButtons'
import { fetchDateandTime, fetchDateTimeByRound, FetchTemplateByRound } from 'actions'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { connect } from 'react-redux'
import { formatForMultiSelect } from 'components/Recruitment/Candidates/Dashboard/utils'
import { useJoinerContext } from './JoinerContext'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import RestartAltIcon from '@mui/icons-material/RestartAlt'

function JoinersDrop({
  open,
  batchDropDown,
  RoundOptions,
  fetchTemplateByRoundData,
  fetchDateTimeByRoundData,
  templatesOptions,
  dateandtimeOptions
}: {
  open: boolean
  batchDropDown: any[]
  RoundOptions: any[]
  fetchTemplateByRoundData: any
  fetchDateTimeByRoundData: any
  templatesOptions: any
  dateandtimeOptions: any
}) {
  const [values, setValues] = useState({
    box1: '',
    box2: '',
    box3: '',
    box4: '',
  })

  const { state, dispatch } = useJoinerContext()

  const handleChange = (name: string) => (event: SelectChangeEvent) => {
    setValues((prev) => ({
      ...prev,
      [name]: event.target.value as string,
    }))
    if (name === 'box2') {
      dispatch({ type: 'SET_ROUND_FILTER', payload: event.target.value })
      fetchTemplateByRoundData({ id_round: event.target.value })
      fetchDateTimeByRoundData({ id_round: event.target.value })
    }

    if (name === 'box1') {
      dispatch({ type: 'SET_BATCH_OPTION', payload: event.target.value })
    }

    if (name === 'box3') {
      dispatch({ type: 'SET_TEMPLATE_FILTER', payload: event.target.value })
    }

    if (name === 'box4') {
      dispatch({ type: 'SET_SCHEDULE_FILTER', payload: event.target.value })
    }
  }

  const handleReset = () => {
    setValues({
      box1: '',
      box2: '',
      box3: '',
      box4: '',
    })

    dispatch({ type: 'SET_BATCH_OPTION', payload: '' })
    dispatch({ type: 'SET_ROUND_FILTER', payload: '' })
    dispatch({ type: 'SET_TEMPLATE_FILTER', payload: '' })
    dispatch({ type: 'SET_SCHEDULE_FILTER', payload: '' })
  }

  return (
    <Collapse in={open}>
      <Grid container spacing={2} alignItems='center' sx={{ p: 2, borderRadius: 2 }}>
        <Grid item xs={12} sm={6} md={3} lg={3}>
          <FormControl fullWidth>
            <InputLabel>Batch</InputLabel>
            <Select
              value={values.box1}
              label='Batch'
              onChange={handleChange('box1')}
              sx={{ borderRadius: '40px' }}
            >
              {batchDropDown.map((batch, i) => (
                <MenuItem key={i} value={batch.id}>
                  {batch.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3} lg={3}>
          <FormControl fullWidth>
            <InputLabel>Select Rounds</InputLabel>
            <Select
              value={values.box2}
              label='Select Rounds'
              onChange={handleChange('box2')}
              sx={{ borderRadius: '40px' }}
            >
              {RoundOptions.map((round, i) => (
                <MenuItem key={i} value={round.id}>
                  {round.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3} lg={3}>
          <FormControl fullWidth>
            <InputLabel>Select Template</InputLabel>
            <Select
              value={values.box3}
              label='Select Template'
              onChange={handleChange('box3')}
              sx={{ borderRadius: '40px' }}
            >
              {Array.isArray(templatesOptions[0]) && templatesOptions[0].length > 0 ? (
                formatForMultiSelect(templatesOptions, 'name', 'id', 0).map((item) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled value=''>
                  Not Found For Selected Rounds
                </MenuItem>
              )}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3} lg={3}>
          <Box display="flex" alignItems="center" gap={1}>
            <FormControl fullWidth sx={{ flex: 1 }}>
              <InputLabel>Select Date | Time</InputLabel>
              <Select
                value={values.box4}
                label='Select Date | Time'
                onChange={handleChange('box4')}
                sx={{ borderRadius: '40px' }}
              >
                {Array.isArray(dateandtimeOptions[0]) && dateandtimeOptions[0].length > 0 ? (
                  formatForMultiSelect(dateandtimeOptions, 'date_time', 'date_time', 0).map(
                    (item) => (
                      <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem>
                    )
                  )
                ) : (
                  <MenuItem disabled value=''>
                    Not Found For Selected Rounds
                  </MenuItem>
                )}
              </Select>
            </FormControl>

            <ActionButton
  variant='outlined'
  onClick={handleReset}
  sx={{
    width: 56,
    height: 56,
    minWidth: 56,
    minHeight: 56,
    px: 0,
    borderRadius: '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    mt: '3px', // 👈 this is the secret sauce
  }}
>
  <RestartAltIcon sx={{ fontSize: 28 }} />
</ActionButton>

          </Box>
        </Grid>
      </Grid>

      <Buttons />
    </Collapse>
  )
}

const mapStateToProps = (state: RootState) => ({
  templatesOptions: recruitmentEntity.getRecruitment(state).getTemplateByRound,
  dateandtimeOptions: recruitmentEntity.getRecruitment(state).getDateTimeByRound,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchTemplateByRoundData: (data: {}) => dispatch(FetchTemplateByRound.request({ data })),
  fetchDateTimeByRoundData: (data: {}) => dispatch(fetchDateTimeByRound.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(JoinersDrop)
