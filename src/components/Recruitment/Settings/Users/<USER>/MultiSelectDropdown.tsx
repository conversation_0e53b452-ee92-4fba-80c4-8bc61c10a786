import { useState, MouseEvent } from 'react'
import { <PERSON>u, <PERSON>uItem, Checkbox, ListItemText, TextField, Button } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'

interface MultiSelectDropdownProps {
  label: string
  options: string[]
  selectedOptions: string[]
  setSelectedOptions: (selected: string[]) => void
  sx?: object
}

export default function MultiSelectDropdown({
  label,
  options,
  selectedOptions,
  setSelectedOptions,
  sx,
}: MultiSelectDropdownProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [searchText, setSearchText] = useState<string>('')

  const open = Boolean(anchorEl)

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleToggleOption = (option: string) => {
    setSelectedOptions(
      selectedOptions.includes(option)
        ? selectedOptions.filter((item) => item !== option)
        : [...selectedOptions, option],
    )
  }

  const handleSelectAll = () => {
    setSelectedOptions(selectedOptions.length === options.length ? [] : options)
  }

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchText.toLowerCase()),
  )

  return (
    <div>
      <Button
        variant='outlined'
        onClick={handleClick}
        sx={{
          justifyContent: 'space-between',
          borderRadius: '20px',
          width: '320px',
          height: '55px',
          fontSize: '14px',
          fontWeight: 'normal',
          fontFamily: "'Poppins', sans-serif",
          p: 1,
          ...sx,
          backgroundColor: 'white',
          color: 'black',
          '&:hover': {
            backgroundColor: 'transparent',
            color: 'black',
          },
        }}
        fullWidth
      >
        {label} {selectedOptions.length > 0 ? `+${selectedOptions.length}` : ''}
        <ArrowDropDownIcon />
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          '& .MuiPaper-root': {
            width: '250px',
            maxHeight: '300px',
            overflowY: 'auto',
            borderRadius: '12px',
          },
        }}
      >
        <MenuItem onClick={handleSelectAll}>
          <Checkbox checked={selectedOptions.length === options.length} />
          {selectedOptions.length < options.length ? 'Select All' : 'Un-Select All'}
        </MenuItem>

        <MenuItem onKeyDown={(e) => e.stopPropagation()}>
          <TextField
            fullWidth
            variant='standard'
            placeholder='Search'
            onChange={(e) => setSearchText(e.target.value)}
            sx={{ fontSize: '14px', p: 1 }}
          />
        </MenuItem>

        {filteredOptions.map((option) => (
          <MenuItem key={option} onClick={() => handleToggleOption(option)}>
            <Checkbox checked={selectedOptions.includes(option)} />
            <ListItemText primary={option} />
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}
