import React, { useState, useEffect } from 'react'
import { <PERSON>po<PERSON>, TextField, MenuItem, Grid, Box } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { Dispatch } from 'redux'
import {
  addExpectedJoiners,
  editExpectedJoiners,
  fetchAddQualification,
  fetchOrganisationDetails,
  fetchQualification,
  fetchRounds,
} from 'actions'
import { connect } from 'react-redux'
import { recruitmentEntity } from 'reducers'
import { RootState } from 'configureStore'
import { toast } from 'react-toastify'

interface AddJoinerPageProps {
  onCancel: () => void
  addExpectedJoiners: (data: any) => void
  editData?: any
  batchDropDown: any
  fetchOrganisationDetails: () => void
  orgData: any
  fetchQualification: () => void
  qualificationData: any
  roundsData: any
  getRounds: () => void
  editExpectedJoiners: (data: any) => void
}

const AddJoinerPage: React.FC<AddJoinerPageProps> = ({
  onCancel,
  addExpectedJoiners,
  editData,
  batchDropDown,
  fetchOrganisationDetails,
  orgData,
  fetchQualification,
  qualificationData,
  roundsData,
  getRounds,
  editExpectedJoiners,
}) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_no: '',
    batchId: '',
    qualification: '',
    college_name: '',
    status: '',
    stage: '',
  })

  const [errors, setErrors] = useState<any>({})

  useEffect(() => {
    if (editData) {
      const nameParts = editData.name?.split(' ') || []

      const qualificationObj = qualificationData[0]?.find(
        (q: any) => q.qualification === editData.qualification
      )
      const qualificationId = qualificationObj?.id || ''

      const roundObj = roundsData[0]?.find((r: any) => r.round_name === editData.stage)
      const roundId = roundObj?.id || ''

      const batchObj = batchDropDown?.find((b: any) => b.batch === editData.batch)
      const batchId = batchObj?.id || ''

      setFormData({
        first_name: nameParts[0] || '',
        last_name: nameParts.slice(1).join(' ') || '',
        email: editData.email || '',
        phone_no: editData.phone_no || '',
        batchId,
        qualification: qualificationId,
        college_name: editData.college_name || '',
        status: editData.status || '',
        stage: roundId,
      })
    }
  }, [editData, batchDropDown, qualificationData, roundsData])

  useEffect(() => {
    fetchOrganisationDetails()
    fetchQualification()
    getRounds()
  }, [])

  const validateForm = () => {
    const newErrors: any = {}

    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required'
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required'
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailPattern.test(formData.email)) {
        newErrors.email = 'Invalid email format'
      }
    }
    if (!formData.phone_no.trim()) {
      newErrors.phone_no = 'Phone number is required'
    } else if (formData.phone_no.length !== 10) {
      newErrors.phone_no = 'Phone number must be 10 digits'
    }
    if (!formData.batchId) newErrors.batchId = 'Batch is required'
    if (!formData.qualification) newErrors.qualification = 'Qualification is required'
    if (!formData.college_name) newErrors.college_name = 'College name is required'
    if (!formData.status) newErrors.status = 'Status is required'
    if (editData && !formData.stage) newErrors.stage = 'Stage is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setErrors((prev: any) => ({ ...prev, [name]: '' }))

    if (name === 'phone_no') {
      const numericValue = value.replace(/\D/g, '')
      setFormData((prevData) => ({
        ...prevData,
        [name]: numericValue,
      }))
    } else if (name === 'first_name' || name === 'last_name') {
      const trimmedValue = value.replace(/\s/g, '')
      setFormData((prevData) => ({
        ...prevData,
        [name]: trimmedValue,
      }))
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }))
    }
  }

  const handleSubmit = () => {
    if (!validateForm()) return
    addExpectedJoiners(formData)
    onCancel()
    toast.success("Added Joiner Successfully")
  }

  const handleEdit = () => {
    if (!validateForm()) return

    const finalPayload = {
      id: editData.id,
      name: `${formData.first_name} ${formData.last_name}`.trim(),
      email: formData.email,
      phone_no: formData.phone_no,
      college_name: formData.college_name,
      status: formData.status,
      qualification: formData.qualification,
      batchId: formData.batchId,
      stage: formData.stage,
    }

    editExpectedJoiners(finalPayload)
    toast.success("Edited Joiner Successfully")
    onCancel()
  }

  const textFieldStyle = {
    fontFamily: 'Montserrat, sans-serif',
    '& .MuiOutlinedInput-root': {
      borderRadius: '28px',
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: '#193C6D',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: '#193C6D',
        borderWidth: '2px',
      },
    },
  }

  return (
    <Box
      sx={{
        padding: 0,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      <Typography
        variant='h6'
        sx={{
          backgroundColor: '#1f3a5f',
          color: '#fff',
          padding: '12px',
          borderRadius: 0,
          textAlign: 'center',
          margin: 0,
          fontFamily: 'Montserrat-Medium',
          fontWeight: 600,
        }}
      >
        {editData ? 'Edit Expected Joiner' : 'Add Expected Joiner'}
      </Typography>
  
      <Box sx={{ padding: '16px', flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <TextField
              label='First Name'
              name='first_name'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.first_name}
              onChange={handleChange}
              error={!!errors.first_name}
              helperText={errors.first_name}
              sx={textFieldStyle}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              label='Last Name'
              name='last_name'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.last_name}
              onChange={handleChange}
              error={!!errors.last_name}
              helperText={errors.last_name}
              sx={textFieldStyle}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              label='Email'
              name='email'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.email}
              onChange={handleChange}
              error={!!errors.email}
              helperText={errors.email}
              sx={textFieldStyle}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              label='Phone Number'
              name='phone_no'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.phone_no}
              onChange={handleChange}
              error={!!errors.phone_no}
              helperText={errors.phone_no}
              sx={textFieldStyle}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              select
              label='Batch'
              name='batchId'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.batchId}
              onChange={handleChange}
              error={!!errors.batchId}
              helperText={errors.batchId}
              sx={textFieldStyle}
            >
              {batchDropDown.map((batch: any) => (
                <MenuItem key={batch.id} value={batch.id}>
                  {batch.batch}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={6}>
            <TextField
              select
              label='Qualification'
              name='qualification'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.qualification}
              onChange={handleChange}
              error={!!errors.qualification}
              helperText={errors.qualification}
              sx={textFieldStyle}
            >
              {qualificationData.length > 0 &&
                qualificationData[0].map((q: any) => (
                  <MenuItem key={q.id} value={q.id}>
                    {q.qualification}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
          {editData && (
            <Grid item xs={6}>
              <TextField
                select
                label='Stage'
                name='stage'
                variant='outlined'
                fullWidth
                size='small'
                value={formData.stage}
                onChange={handleChange}
                error={!!errors.stage}
                helperText={errors.stage}
                sx={textFieldStyle}
              >
                {roundsData.length > 0 &&
                  roundsData[0].map((round: any) => (
                    <MenuItem key={round.id} value={round.id}>
                      {round.round_name}
                    </MenuItem>
                  ))}
              </TextField>
            </Grid>
          )}
          <Grid item xs={6}>
            <TextField
              select
              label='Status'
              name='status'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.status}
              onChange={handleChange}
              error={!!errors.status}
              helperText={errors.status}
              sx={textFieldStyle}
            >
              <MenuItem value='Active'>Active</MenuItem>
              <MenuItem value='Inactive'>Inactive</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={6}>
            <TextField
              select
              label='Organization Name'
              name='college_name'
              variant='outlined'
              fullWidth
              size='small'
              value={formData.college_name}
              onChange={handleChange}
              error={!!errors.college_name}
              helperText={errors.college_name}
              sx={textFieldStyle}
            >
              {orgData.length > 0 &&
                orgData[0].map((org: any) => (
                  <MenuItem key={org.id} value={`${org.organisation}, ${org.city}`}>
                    {`${org.organisation}, ${org.city}`}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
        </Grid>
  
        <Box display='flex' justifyContent='flex-end' alignItems='center' gap={2} mt={2}>
          <ActionButton
            variant='contained'
            onClick={onCancel}
            sx={{
              backgroundColor: '#E2E2E2',
              color: '#000000',
              height: '40px',
              fontSize: '15px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '120px',
              '&:hover': {
                backgroundColor: '#E2E2E2',
                color: '#000000',
              },
            }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={editData ? handleEdit : handleSubmit}
            sx={{
              backgroundColor: '#193C6D',
              color: '#FFFFFF',
              fontSize: '15px',
              height: '40px',
              borderRadius: '50px',
              fontWeight: 'bold',
              fontFamily: 'Montserrat-Semibold',
              minWidth: '120px',
            }}
          >
            {editData ? 'Update' : 'Submit'}
          </ActionButton>
        </Box>
      </Box>
    </Box>
  )
  
}

const mapStateToProps = (state: RootState) => ({
  batchDropDown: recruitmentEntity.getRecruitment(state).getBatchesData,
  orgData: recruitmentEntity.getRecruitment(state).getOrganisationDetailsData,
  qualificationData: recruitmentEntity.getRecruitment(state).getAddQualificationData,
  roundsData: recruitmentEntity.getRecruitment(state).getRoundData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  addExpectedJoiners: (data: any) => dispatch(addExpectedJoiners.request({ data })),
  editExpectedJoiners: (data: any) => dispatch(editExpectedJoiners.request({ data })),
  fetchOrganisationDetails: () => dispatch(fetchOrganisationDetails.request()),
  fetchQualification: () => dispatch(fetchAddQualification.request()),
  getRounds: () => dispatch(fetchRounds.request()),
})

export default connect(mapStateToProps, mapDispatchToProps)(AddJoinerPage)
