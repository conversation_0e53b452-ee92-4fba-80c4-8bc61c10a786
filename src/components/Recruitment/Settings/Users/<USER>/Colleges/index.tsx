import { connect } from 'react-redux'
import { RootState } from '../../../../../../configureStore'
import { Dispatch } from 'redux'
import { addDriveDetails, deleteCollege, EditOrgDetails, getColleges } from '../../../../../../actions'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../../reducers'
import Colleges from './CollegeList'
import { CollegeDriveData, CollegesPayload, OrgData } from '../../../../../Types'

const mapDispatchToProps = (dispatch: Dispatch) => ({
  getColleges: (data: CollegesPayload) => dispatch(getColleges.request(data)),
  deleteCollege: (data: { id: number }) => dispatch(deleteCollege.request(data)),
  addDriveDetails: (data: CollegeDriveData) => dispatch(addDriveDetails.request(data)),
  EditOrgDetails: (data: OrgData) => dispatch(EditOrgDetails.request(data)),
  resetCollegeDeleted: () => dispatch(deleteCollege.reset()),
  resetDriveAdded: () => dispatch(addDriveDetails.reset()),
  resetOrgEdited: () => dispatch(EditOrgDetails.reset()),
})

const mapStateToProps = (state: RootState) => {
  return {
    collegeData: recruitmentEntity.getRecruitment(state).getAllCollegesDetails,
    isGetCollegeData: recruitmentStateUI.getRecruitment(state).isGetAllCollegesList,
    isCollegeDeleted: recruitmentStateUI.getRecruitment(state).isCollegeDeleted,
    isCollegeDeleting: recruitmentStateUI.getRecruitment(state).isCollegeDeleting,
    isDriveAdded: recruitmentStateUI.getRecruitment(state).isDriveAdded,
    isOrgEdited: recruitmentStateUI.getRecruitment(state).isOrgEdited,
    isDriveAdding: recruitmentStateUI.getRecruitment(state).isDriveAdding,
    isOrgEditing: recruitmentStateUI.getRecruitment(state).isOrgEditing,
  };
};

const CollegeListTableDataMapped = connect(mapStateToProps, mapDispatchToProps)(Colleges)

const CollegeList = () => (
  <>
    <CollegeListTableDataMapped />
  </>
)

export default CollegeList
