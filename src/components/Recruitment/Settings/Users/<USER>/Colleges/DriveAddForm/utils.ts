import * as Yup from 'yup';

export interface DriveAddFormProps {
  open: boolean;
  rowData: {
    id: number;
    organisation: string;
    city: string;
    last_drive_date?: string
  };
  onClose: () => void;
  onSubmit: (payload: { college_id: number; drive_date: string; notes: string }) => void;
}

export const validationSchema = Yup.object({
  driveDate: Yup.string().required('Last Drive Date is required'),
  notes: Yup.string().trim()
    .max(1500, 'Notes cannot exceed 1500 characters'),
})