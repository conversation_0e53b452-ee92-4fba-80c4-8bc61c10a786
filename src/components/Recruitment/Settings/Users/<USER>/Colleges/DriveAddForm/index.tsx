import React from 'react';
import {
  Dialog,
  DialogContent,
  Button,
  Box,
  Typography,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import DateRangePicker from '../Common/DatePicker';
import Editor from '../Common/Editor';
import { Formik, Form } from 'formik';
import dayjs, { Dayjs } from 'dayjs';
import { DriveAddFormProps, validationSchema } from './utils';

const DriveAddForm: React.FC<DriveAddFormProps> = ({ open, rowData, onClose, onSubmit }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} fullWidth maxWidth="md" >
        <Formik
          initialValues={{
            driveDate: rowData?.last_drive_date ? dayjs(rowData.last_drive_date, "MM-DD-YYYY").isValid() ? dayjs(rowData.last_drive_date, "MM-DD-YYYY").format("MM-DD-YYYY") : '' : '',
            notes: '',
          }}
          validationSchema={validationSchema}
          onSubmit={(values, { resetForm }) => {
            const formattedDate = values.driveDate
              ? dayjs(values.driveDate, "MM-DD-YYYY").format("DD-MM-YYYY")
              : '';
            onSubmit({ college_id: rowData.id, drive_date: formattedDate, notes: values.notes });
            resetForm();
            onClose();
          }}
        >
          {({ values, setFieldValue, errors, touched, handleBlur, setFieldTouched }) => (
            <Form>
              <DialogContent dividers>
                <Typography variant="h5" sx={{ p: 2, textAlign: 'center', fontFamily: 'Montserrat-Medium', marginBottom: "1rem" }}>
                  Add Recruitment Drive
                </Typography>

                {rowData && (
                  <Typography variant="subtitle1" sx={{ marginBottom: "1rem" }}>
                    Selected College: {rowData.organisation} - {rowData.city}
                  </Typography>
                )}
                <Box sx={{ height: "80px", width: "840px", margin: "auto" }}>
                  <DateRangePicker
                    label="Last Drive Date"
                    startDate={values.driveDate ? dayjs(values.driveDate, "MM-DD-YYYY") : null}
                    setStartDate={(newValue) => {
                      const date = newValue as Dayjs | null;
                      setFieldValue('driveDate', date && date.isValid() ? date.format("MM-DD-YYYY") : '');
                    }}
                    error={Boolean(errors.driveDate)}
                    touched={Boolean(touched.driveDate)}
                    onBlur={handleBlur('driveDate')}
                  />

                  {touched.driveDate && errors.driveDate && (
                    <Typography color="error" variant="caption" sx={{ marginLeft: "1rem" }}>
                      {errors.driveDate}
                    </Typography>
                  )}
                </Box>
                <Box
                  sx={{
                    width: '53rem',
                    overflow: 'hidden',
                    margin: "auto",
                  }}
                >
                  <Editor
                    key="drive-add-editor"
                    body={values.notes}
                    setBody={(val: string) => {
                      setFieldValue('notes', val);
                      setFieldTouched('notes', true, false);
                    }}
                  />
                  {touched.notes && errors.notes && (
                    <Typography color="error" variant="caption" sx={{ marginLeft: "1rem" }}>
                      {errors.notes}
                    </Typography>
                  )}
                </Box>
                <Box display="flex" justifyContent="flex-end" gap={2} sx={{ p: 2, justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    onClick={() => {
                      setFieldValue('driveDate', '');
                      setFieldValue('notes', '');
                      onClose();
                    }}
                    sx={{
                      backgroundColor: 'rgb(226, 226, 226)',
                      color: 'black',
                      borderRadius: '25px',
                      fontFamily: 'Montserrat, sans-serif',
                      fontSize: '14px',
                      minWidth: '100px',
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    type="submit"

                    disabled={Boolean(errors.driveDate) || Boolean(errors.notes) || !values.driveDate}
                    sx={{
                      backgroundColor: 'rgb(25, 60, 109)',
                      color: 'white',
                      borderRadius: '25px',
                      fontFamily: 'Montserrat, sans-serif',
                      fontSize: '14px',
                      minWidth: '100px',
                    }}
                  >
                    Add Drive
                  </Button>
                </Box>
              </DialogContent>
            </Form>
          )}
        </Formik>
      </Dialog>
    </LocalizationProvider>
  );
};

export default DriveAddForm;
