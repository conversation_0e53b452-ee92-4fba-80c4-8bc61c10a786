import React from 'react';
import { Grid } from '@mui/material';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';

interface EditorProps {
  body: string;
  setBody: (body: string) => void;
}

const EditorComponent: React.FC<EditorProps> = ({ body, setBody }) => {
  return (
    <Grid
      item
      xs={12}
      sm={12}
      sx={{
        width: '100%',
        backgroundColor: 'white',
        p: 1,
        margin: 'auto'
      }}
    >
      <CKEditor
        editor={ClassicEditor}
        config={{}}
        data={body || ''}
        onReady={(editor) => {
          const styleElement = document.createElement('style');
          styleElement.innerHTML = `.ck-powered-by { display: none !important; }`;
          document.head.appendChild(styleElement);

          const editableElement = editor.ui.view.editable.element;

          if (editableElement) {
            editableElement.style.maxHeight = "150px";
            editableElement.style.overflowY = "auto";
          }
        }}
        onChange={(event, editor) => {
          const data = editor.getData();
          setBody(data);
        }}
      />
    </Grid>
  );
};

const Editor = React.memo(EditorComponent);

export default Editor;
