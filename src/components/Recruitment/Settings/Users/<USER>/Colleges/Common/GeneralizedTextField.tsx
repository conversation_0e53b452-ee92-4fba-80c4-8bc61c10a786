import React, { useState } from 'react';
import { TextField } from '@mui/material';
import { Box, styled } from '@mui/system';
import styles from '../../../../../../../utils/styles.json'

const AutofillTextField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
    lineHeight: "1.4375em"
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiInputLabel-root': {
    transform: 'translate(14px, 50%) scale(1)',
    transformOrigin: 'left top',
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, -10px) scale(0.75)',
    transformOrigin: 'left top',
  }
}))

export interface GeneralizedTextFieldProps {
  label: string;
  name: string;
  value: string;
  onChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  required?: boolean;
  type?: string;
  sx?: object;
  error?: boolean;
  helperText?: string | boolean;
}

const GeneralizedTextField: React.FC<GeneralizedTextFieldProps> = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  required = false,
  type = 'text',
  sx,
  error,
  helperText
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <Box sx={{ height: "90px", width: "840px", margin: "auto" }}>
      <AutofillTextField
        label={label}
        variant="outlined"
        name={name}
        value={value}
        onChange={onChange}
        onFocus={() => setIsFocused(true)}
        onBlur={(e) => {
          setIsFocused(false);
          if (onBlur) onBlur(e);
        }}
        required={required}
        type={type}
        sx={sx}
        error={!isFocused && error ? true : false}
        helperText={!isFocused && error ? helperText : ''}
      />
    </Box>
  );
};

export default GeneralizedTextField;
