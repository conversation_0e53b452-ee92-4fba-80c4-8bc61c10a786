import { Box, styled } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import useOpenable from 'hooks/useOpenable';
import styles from '../../../../../../../utils/styles.json'

const StyledDatePicker = styled(DatePicker)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
    lineHeight: "1.4375em"
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiInputLabel-root': {
    transform: 'translate(14px, 50%) scale(1)', 
    transformOrigin: 'left top', 
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, -10px) scale(0.75)', 
    transformOrigin: 'left top',
  }
}))


type PropType = {
  startDate: Dayjs | null;
  setStartDate: (date: Dayjs | null) => void;
  label: string;
  error?: boolean;
  touched?: boolean;
  onBlur?: React.FocusEventHandler<HTMLInputElement>;
};

const DateRangePicker = (props: PropType) => {
  const {
    setStartDate,
    startDate,
    label,
    error = false,
    touched = false,
    onBlur
  } = props;

  const { isOpen, onOpen, onClose } = useOpenable();
  const maxDate = dayjs().add(4, 'year');
  const minDate = dayjs('2010-01-01');

  const isInvalid =
    (error && touched) ||
    (startDate && (!startDate.isValid() || startDate.toString() === 'Invalid date'));

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box display="flex" alignItems="center" justifyContent="center" m={0} p={0} sx={{
        '& .MuiFormControl-root': {
          marginBottom: '4px !important',
        },
      }}>
        <StyledDatePicker
          label={label}
          format="MM-DD-YYYY"
          value={startDate}
          onAccept={(newValue) => setStartDate(newValue as Dayjs)}
          open={isOpen}
          onClose={onClose}
          maxDate={maxDate}
          minDate={minDate}
          slotProps={{
            textField: {
              onClick: () => onOpen(),
              onBlur: onBlur,
              placeholder: isInvalid ? "MM-DD-YYYY" : undefined,
              error: isInvalid ? true : undefined,
              inputProps: {
                spellCheck: false,
              }
            },
          }}
        />
      </Box>
    </LocalizationProvider>
  );
};

export default DateRangePicker;