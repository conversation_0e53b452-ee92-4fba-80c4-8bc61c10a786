import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { College, CollegeTableProps, columnMappings } from './utils'
import DeleteConfirmationDialog from '../../../../Common/DeleteConfirmationDialog'
import { useCollegeTableLogic } from './utils'
import GeneralizedTable from '../../../../Common/GeneralizedTable'
import CollegeEditModal, { RecruitmentDrive } from './CollegeEditForm'
import { OrgData } from '../../../../../Types'
import useOpenable from '../../../../../../hooks/useOpenable'

interface ExtendedProps extends CollegeTableProps {
  onRowSelect: (row: College) => void
  selectedRowId?: number
  EditOrgDetails: (data: OrgData) => void
}

const CollegeTable: React.FC<ExtendedProps> = ({
  data,
  deleteCollege,
  getColleges,
  isCollegeDeleted,
  onRowSelect,
  selectedRowId,
  EditOrgDetails,
}) => {
  const {
    displayColumns,
    order,
    orderBy,
    handleDeleteClick,
    handleConfirmDelete,
    handleSort,
    sortedData,
    handleEditClick,
    selectedEditRow,
    setSelectedEditRow,
  } = useCollegeTableLogic(data, deleteCollege, isCollegeDeleted)

  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useOpenable()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useOpenable()
  const renderActions = (row: College) => (
    <>
      <Tooltip title='Edit'>
        <IconButton
          onClick={() => {
            handleEditClick(row)
            onEditOpen()
          }}
          sx={{ color: 'rgb(25,60,109)', width: '36px', height: '36px' }}
        >
          <EditIcon sx={{ width: '20px', height: '20px' }} />
        </IconButton>
      </Tooltip>
      <Tooltip title='Delete'>
        <IconButton
          onClick={() => {
            handleDeleteClick(row)
            onDeleteOpen()
          }}
          sx={{ color: '#db3700', width: '36px', height: '36px' }}
        >
          <DeleteIcon sx={{ width: '20px', height: '20px' }} />
        </IconButton>
      </Tooltip>
    </>
  )
  return (
    <>
      <GeneralizedTable
        columns={displayColumns}
        columnMappings={columnMappings}
        data={sortedData}
        order={order}
        orderBy={orderBy}
        onSort={handleSort}
        renderActions={renderActions}
        onRowClick={onRowSelect}
        selectedRowId={selectedRowId}
      />
      <DeleteConfirmationDialog
        open={isDeleteOpen}
        handleClose={onDeleteClose}
        handleConfirm={() => {
          handleConfirmDelete()
          onDeleteClose()
        }}
      />
      {selectedEditRow && (
        <CollegeEditModal
          open={isEditOpen}
          rowData={selectedEditRow}
          onClose={() => {
            setSelectedEditRow(null)
            onEditClose()
          }}
          onSubmit={(orgData: RecruitmentDrive) => {
            const payload: OrgData = {
              id: orgData.id ? orgData.id : -1,
              organisation: orgData.organisation,
              city: orgData.city,
              representative_type: 'College',
              last_invited: orgData.last_invited,
              notes: orgData.notes,
            };
            EditOrgDetails(payload);
            setSelectedEditRow(null);
            onEditClose();
          }}
        />
      )}

    </>
  )
}

export default CollegeTable
