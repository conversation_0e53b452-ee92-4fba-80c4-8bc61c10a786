import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Dayjs } from 'dayjs';
import Editor from '../Common/Editor';
import GeneralizedTextField from '../Common/GeneralizedTextField';
import DateRangePicker from '../Common/DatePicker';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import { validationSchema } from './utils';

export interface RecruitmentDrive {
  id?: number;
  organisation: string;
  city: string;
  last_invited: string;
  notes: string;
}

export interface RecruitmentDriveModalProps {
  open: boolean;
  rowData: RecruitmentDrive;
  onClose: () => void;
  onSubmit: (payload: RecruitmentDrive) => void;
}

const CollegeEditModal: React.FC<RecruitmentDriveModalProps> = ({
  open,
  rowData,
  onClose,
  onSubmit,
}) => {
  const initialValues = {
    id: rowData.id,
    organisation: rowData.organisation || '',
    city: rowData.city || '',
    last_invited: rowData.last_invited || '',
    notes: rowData.notes || '',
  };
  
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={(values, { resetForm }) => {
            onSubmit(values);
            resetForm();
            onClose();
          }}
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue, setFieldTouched }) => {
            const dateValue: Dayjs | null = values.last_invited
              ? dayjs(values.last_invited, 'MM-DD-YYYY')
              : null;
            return (
              <Form>
                <DialogContent dividers>
                  <Typography variant="h5" sx={{ p: 2, textAlign: 'center', fontFamily: 'Montserrat-Medium', marginBottom: "1rem" }}>
                    Edit Recruitment Drive
                  </Typography>
                  <GeneralizedTextField
                    label="Organization"
                    name="organisation"
                    value={values.organisation}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    required
                    error={touched.organisation && Boolean(errors.organisation)}
                    helperText={touched.organisation && errors.organisation}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
                  />
                  <GeneralizedTextField
                    label="City"
                    name="city"
                    value={values.city}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    required
                    error={touched.city && Boolean(errors.city)}
                    helperText={touched.city && errors.city}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '20px' } }}
                  />
                  <Box sx={{ height: "90px", width: "840px", margin: "auto" }}>
                    <DateRangePicker
                      startDate={dateValue}
                      setStartDate={(newValue) => {
                        const date = newValue as Dayjs | null;
                        setFieldValue(
                          'last_invited',
                          date && date.isValid() ? date.format('MM-DD-YYYY') : ''
                        );
                      }}
                      label="Last Invited Date"
                      error={Boolean(errors.last_invited)}
                      touched={Boolean(touched.last_invited)}
                      onBlur={handleBlur('last_invited')}
                    />
                    {touched.last_invited && errors.last_invited && (
                      <Typography color="error" variant="caption" sx={{ marginLeft: "1rem" }}>
                        {errors.last_invited}
                      </Typography>
                    )}
                  </Box>
                  <Box sx={{ width: '53rem', overflow: 'hidden', margin: "auto" }}>
                    <Editor
                      body={values.notes}
                      setBody={(newNotes: string) => {
                        setFieldValue('notes', newNotes);
                        setFieldTouched('notes', true, false);
                      }}
                    />
                    {touched.notes && errors.notes && (
                      <Typography color="error" variant="caption" sx={{ marginLeft: "1rem" }}>
                        {errors.notes}
                      </Typography>
                    )}
                  </Box>

                  <Box display="flex" justifyContent="flex-end" gap={2} sx={{ marginTop: "1rem" }}>
                    <Button
                      variant="contained"
                      onClick={() => {
                        onClose();
                      }}
                      sx={{
                        backgroundColor: 'rgb(226, 226, 226)',
                        color: 'black',
                        borderRadius: '25px',
                        fontFamily: 'Montserrat, sans-serif',
                        fontSize: '14px',
                        minWidth: '100px',
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      disabled={!!Object.keys(errors).length}
                      sx={{
                        backgroundColor: 'rgb(25, 60, 109)',
                        color: 'white',
                        borderRadius: '25px',
                        fontFamily: 'Montserrat, sans-serif',
                        fontSize: '14px',
                        minWidth: '100px',
                      }}
                    >
                      Update
                    </Button>
                  </Box>
                </DialogContent>
              </Form>
            );
          }}
        </Formik>
      </Dialog>
    </LocalizationProvider>
  );
};

export default CollegeEditModal;
