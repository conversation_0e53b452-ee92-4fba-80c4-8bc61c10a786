import dayjs, { Dayjs } from 'dayjs';
import { useState, useEffect } from 'react';
import { RecruitmentDrive } from '.';
import * as Yup from 'yup';
const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1);

export const validationSchema = Yup.object({
  organisation: Yup.string().trim()
    .required('Organization is required')
    .min(3, 'Organization name must be at least 3 characters')
    .max(50, 'Organization name cannot exceed 50 characters'),

  city: Yup.string().trim()
    .required('City is required')
    .min(2, 'City name must be at least 2 characters')
    .max(30, 'City name cannot exceed 30 characters'),

  last_invited: Yup.string().required('Last Invited Date is required'),

  notes: Yup.string().trim()
    .max(1500, 'Notes cannot exceed 1500 characters'),
});

export const useFormState = (rowData: RecruitmentDrive) => {
  const [formData, setFormData] = useState<RecruitmentDrive>({
    organisation: rowData?.organisation || '',
    city: rowData?.city || '',
    last_invited: rowData?.last_invited
      ? formatDayjs(getDayjsFromString(rowData.last_invited))
      : '',
    notes: rowData?.notes || '',
    id: rowData.id,
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (rowData) {
      setFormData({
        organisation: rowData.organisation || '',
        city: rowData.city || '',
        last_invited: rowData.last_invited
          ? formatDayjs(getDayjsFromString(rowData.last_invited))
          : '',
        notes: rowData.notes || '',
        id: rowData.id,
      });
    }
  }, [rowData]);

  return { formData, setFormData, errors, setErrors };
};

export const getDayjsFromString = (dateStr: string | null): Dayjs | null => {
  if (!dateStr) return null;
  const parsed = dayjs(dateStr, "MM-DD-YYYY", true);
  return parsed.isValid() ? parsed : null;
};

export const formatDayjs = (dateObj: Dayjs | null): string => {
  return dateObj ? dateObj.format("MM-DD-YYYY") : '';
};

export const handleChange = (
  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  setFormData: React.Dispatch<React.SetStateAction<RecruitmentDrive>>,
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>
) => {
  const { name, value } = e.target;
  setFormData(prev => ({ ...prev, [name]: value || '' }));
  if (value.trim() === '') {
    setErrors(prev => ({ ...prev, [name]: `${capitalize(name)} is required` }));
  } else {
    setErrors(prev => ({ ...prev, [name]: '' }));
  }
};

export const handleDateChange = (
  newValue: Dayjs | null | ((prevState: Dayjs | null) => Dayjs | null),
  setFormData: React.Dispatch<React.SetStateAction<RecruitmentDrive>>
) => {
  const resolvedDate = typeof newValue === 'function' ? newValue(null) : newValue;
  setFormData(prev => ({
    ...prev,
    last_invited:
      resolvedDate && resolvedDate.isValid()
        ? resolvedDate.format("MM-DD-YYYY")
        : '',
  }));
};

export const handleEditorChange = (
  newNotes: string | null,
  setFormData: React.Dispatch<React.SetStateAction<RecruitmentDrive>>
) => {
  setFormData(prev => ({ ...prev, notes: newNotes || '' }));
};

export const isFormValid = (formData: RecruitmentDrive) => {
  return (
    formData.organisation.trim() !== '' &&
    formData.city.trim() !== '' &&
    formData.last_invited.trim() !== ''
  );
};

export const validateForm = (
  formData: RecruitmentDrive
): { [key: string]: string } => {
  const errors: { [key: string]: string } = {};
  if (formData.organisation.trim() === '')
    errors.organisation = `${capitalize('organisation')} is required`;
  if (formData.city.trim() === '')
    errors.city = `${capitalize('city')} is required`;
  return errors;
};
