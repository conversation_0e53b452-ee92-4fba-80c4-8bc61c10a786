import React, { useRef } from 'react';
import { Box, Pagination, Button, Tooltip } from '@mui/material';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import CollegeTable from './CollegeTable';
import SearchComponent from '../../../../Templates/SearchComponent';
import { useCollegesLogic, useDriveLogic, useCollegeSideEffects, CollegesProps } from './utils';
import Loader from '../../../../../Common/Loader';
import DriveAddForm from './DriveAddForm';
import { toast } from 'react-toastify';
import useOpenable from '../../../../../../hooks/useOpenable';

const Colleges: React.FC<CollegesProps> = (props) => {
    const { page, searchQuery, rowsPerPage, handleSearchChange, clearSearch, handlePageChange, currentData, processedData } = useCollegesLogic(props);
    const { isOrgEditing, isDriveAdding, isCollegeDeleting, isDriveAdded, isOrgEdited, getColleges, addDriveDetails, EditOrgDetails, deleteCollege, isGetCollegeData, isCollegeDeleted, resetCollegeDeleted, resetDriveAdded, resetOrgEdited } = props;
    const { selectedDriveCollege, setSelectedDriveCollege, handleDriveSubmit } = useDriveLogic({ addDriveDetails });
    const addDriveButtonRef = useRef<HTMLButtonElement>(null);
    const { isOpen, onOpen, onClose } = useOpenable();

    const { handleRowClick, handleAddDriveClick, handleClickAway } = useCollegeSideEffects({
        getColleges,
        page,
        rowsPerPage,
        searchQuery,
        isCollegeDeleted,
        isDriveAdded,
        isOrgEdited,
        resetDriveAdded,
        resetOrgEdited,
        resetCollegeDeleted,
        toast,
        setSelectedDriveCollege,
        selectedDriveCollege,
        onOpen,
        isOpen,
        addDriveButtonRef
    });

    const isLoading = isGetCollegeData || isCollegeDeleting || isOrgEditing || isDriveAdding;
    return (
        <>
            {isLoading && <Loader state={true} />}
            <Box
                sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    marginBottom: '0.1rem',
                    marginTop: '0.5rem',
                    gap: '1rem'
                }}
            >
                <SearchComponent
                    clearSearch={clearSearch}
                    searchQuery={searchQuery}
                    handleSearchChange={handleSearchChange}
                    placeholder="Search by college name and city name"
                />
                <Tooltip
                    title="Select college"
                    disableHoverListener={!!selectedDriveCollege}
                    disableFocusListener={!!selectedDriveCollege}
                    disableTouchListener={!!selectedDriveCollege}
                >
                    <span style={{ marginLeft: 'auto' }}>
                        <Button
                            ref={addDriveButtonRef}
                            disabled={!selectedDriveCollege}
                            onClick={handleAddDriveClick}
                            sx={{
                                backgroundColor: 'rgb(25, 60, 109)',
                                color: '#fff',
                                fontWeight: 'bold',
                                textTransform: 'none',
                                fontSize: '13px',
                                height: '35px',
                                fontFamily: 'Montserrat, sans-serif',
                                border: '1px solid rgba(25, 60, 109, 0.5)',
                                borderRadius: '20px',
                                padding: '5px 20px',
                                '&:hover': { backgroundColor: 'rgb(25, 60, 109)' },
                                '&.Mui-disabled': {
                                    color: 'rgba(0, 0, 0, 0.26)',
                                    boxShadow: 'none',
                                    backgroundColor: 'rgba(0, 0, 0, 0.12)',
                                    border: '1px solid rgba(0, 0, 0, 0.12)'
                                }
                            }}
                        >
                            Add Drive
                        </Button>
                    </span>
                </Tooltip>
            </Box>
            <ClickAwayListener onClickAway={handleClickAway}>
                <Box>
                    <CollegeTable
                        data={currentData}
                        deleteCollege={deleteCollege}
                        getColleges={getColleges}
                        isCollegeDeleted={isCollegeDeleted}
                        onRowSelect={handleRowClick}
                        selectedRowId={selectedDriveCollege ? selectedDriveCollege.id : undefined}
                        EditOrgDetails={EditOrgDetails}
                    />
                </Box>
            </ClickAwayListener>
            {processedData.length > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
                    <Pagination
                        count={Math.ceil(processedData.length / rowsPerPage)}
                        page={page}
                        onChange={handlePageChange}
                        color="primary"
                        sx={{
                            '& .MuiPaginationItem-root.Mui-selected': {
                                backgroundColor: 'rgb(25, 60, 109)',
                                border: '1px solid rgb(25, 60, 109)',
                                color: '#fff'
                            }
                        }}
                    />
                </Box>
            )}
            {isOpen && selectedDriveCollege && (
                <DriveAddForm
                    open={isOpen}
                    rowData={selectedDriveCollege}
                    onClose={onClose}
                    onSubmit={(payload: { college_id: number; drive_date: string; notes: string }) => {
                        handleDriveSubmit(payload);
                        onClose();
                    }}
                />
            )}
        </>
    );
};

export default Colleges;
