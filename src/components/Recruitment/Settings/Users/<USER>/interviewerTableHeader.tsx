import React from 'react'
import { TableHead, TableRow, TableCell, TableSortLabel } from '@mui/material'

interface InterviewTableHeaderProps {
  sortOrder: 'asc' | 'desc'
  handleSort: () => void
}

const InterviewTableHeader: React.FC<InterviewTableHeaderProps> = ({ sortOrder, handleSort }) => {
  return (
    <TableHead>
      <TableRow sx={{ backgroundColor: '#1f2d3d' }}>
        <TableCell
          sx={{
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
            color: 'white',
            backgroundColor: 'rgb(25,60,109)',
            cursor: 'pointer',
            width: '25%',
          }}
        >
          <TableSortLabel
            sx={{
              color: 'white',
              '&.Mui-active': {
                color: 'white',
              },
              '& .MuiTableSortLabel-icon': {
                color: 'white !important',
              },
            }}
            active={true}
            direction={sortOrder}
            onClick={handleSort}
          >
            Name
          </TableSortLabel>
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width: '20%',
          }}
        >
          Email
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width:'20%'
          }}
        >
          Phone No.
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width: '20%',
          }}
        >
          Status
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width:"20%"
          }}
        >
          Actions
        </TableCell>
      </TableRow>
    </TableHead>
  )
}

export default InterviewTableHeader
