import React from 'react'
import { TableHead, TableRow, TableCell } from '@mui/material'

const InterviewTableHeader: React.FC = () => {
  return (
    <TableHead>
      <TableRow sx={{ backgroundColor: '#1f2d3d', }}>
        <TableCell
          sx={{
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',

          }}
        >
          Name
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',

          }}
        >
          Email
        </TableCell>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width: '20%',

          }}
        >
          Phone No.
        </TableCell>
        
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
            width: '20%',

          }}
        >
          Status
        </TableCell>

        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Montserrat-Medium',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',

          }}
        >
          Actions
        </TableCell>
      </TableRow>
    </TableHead>
  )
}

export default InterviewTableHeader