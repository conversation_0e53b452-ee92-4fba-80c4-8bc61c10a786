import { Box, Modal, Paper } from '@mui/material'
import AddJoinerPage from './AddJoinerPage'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { RootState } from 'configureStore'

interface Props {
  isOpen: boolean
  onClose: () => void
  editData: any
}

const AddEditJoiner = ({ isOpen, onClose, editData }: Props) => {
  return (
    <Modal open={isOpen} onClose={onClose}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          overflow: 'auto',
          padding: 2,
        }}
      >
        <Paper
          sx={{
            width: '100%',
            maxWidth: '900px',
            maxHeight: '90vh',
            overflowY: 'auto',
            boxShadow: 5,
            borderRadius: 3,
          }}
        >
          <AddJoinerPage onCancel={onClose} editData={editData} />
        </Paper>
      </Box>
    </Modal>
  )
}


const mapStateToProps = (state: RootState) => ({
})

const mapDispatchToProps = (dispatch: Dispatch) => ({

})

export default connect(mapStateToProps, mapDispatchToProps)(AddEditJoiner)
