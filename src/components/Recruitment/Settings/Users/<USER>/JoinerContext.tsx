import React, { createContext, useReducer, useContext } from 'react'
import { Dayjs } from 'dayjs'



interface State {
  searchQuery: string
  batchOption: string
  roundFilter: string
  templateFilter: string
  scheduleFilter: string
}

type Action =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_BATCH_OPTION'; payload: string }
  | { type: 'SET_ROUND_FILTER'; payload: string }
  | { type: 'SET_TEMPLATE_FILTER'; payload: string }
  | { type: 'SET_SCHEDULE_FILTER'; payload: string }

const initialState: State = {
  searchQuery: '',
  batchOption: '',
  roundFilter: '',
  templateFilter: '',
  scheduleFilter: '',
}

function joinerReducer(state: State, action: Action) {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
    case 'SET_BATCH_OPTION':
      return { ...state, batchOption: action.payload }
    case 'SET_ROUND_FILTER':
      return { ...state, roundFilter: action.payload }
    case 'SET_TEMPLATE_FILTER':
      return { ...state, templateFilter: action.payload }
    case 'SET_SCHEDULE_FILTER':
      return { ...state, scheduleFilter: action.payload }
    default:
      return state
  }
}

const JoinerContext = createContext<{
  state: State
  dispatch: React.Dispatch<Action>
}>({
  state: initialState,
  dispatch: () => {},
})

export const JoinerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(joinerReducer, initialState)
  return (
    <JoinerContext.Provider value={{ state, dispatch }}>{children}</JoinerContext.Provider>
  )
}

export const useJoinerContext = () => useContext(JoinerContext)
