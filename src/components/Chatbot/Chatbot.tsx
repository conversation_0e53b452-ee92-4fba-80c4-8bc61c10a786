import React, { useState, useRef, useEffect } from 'react';
import { Box, TextField, Typography, Paper, IconButton, CircularProgress, Chip, Accordion, AccordionSummary, AccordionDetails, Button, styled, Tooltip } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import ChatIcon from '@mui/icons-material/Chat';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import DataObjectIcon from '@mui/icons-material/DataObject';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CodeIcon from '@mui/icons-material/Code';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import MemoryIcon from '@mui/icons-material/Memory';
import ClearIcon from '@mui/icons-material/Clear';
import HistoryIcon from '@mui/icons-material/History';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import styles from '../../utils/styles.json';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import ReactMarkdown from 'react-markdown';

// Get API URL from environment variables
const API_BASE_URL = process.env.REACT_APP_BASE_URL;
// const API_BASE_URL = 'http://localhost:3001';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  data?: any;
  sources?: string[];
  sql?: string;
  reasoning?: string;
  processingTime?: number;
  isError?: boolean;
  isVoiceMessage?: boolean;
}

interface ChatbotProps {
  authToken?: string; // Your JWT token for authentication
}

const CancelButton = styled(Button)(({ theme }) => ({
  background: 'transparent',
  color: '#f44336',
  fontSize: '12px',
  height: '32px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px', // More rounded for modern look
  border: '1px solid #f44336',
  '&:hover': {
    background: 'rgba(244, 67, 54, 0.04)',
    color: '#d32f2f',
    borderColor: '#d32f2f',
  },
  marginRight: '10px',
  textTransform: 'none',
  boxShadow: 'none',
}));

const AcceptButton = styled(Button)(({ theme }) => ({
  background: '#4caf50',
  color: '#ffffff',
  fontSize: '12px',
  height: '32px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px', // More rounded for modern look
  '&:hover': {
    background: '#388e3c',
    color: '#ffffff',
  },
  textTransform: 'none',
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.2)',
}));

const Chatbot: React.FC<ChatbotProps> = ({ authToken }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I’m your intelligent Kuber assistant powered by AI. I can help you with:\n- Leave balances and requests\n- Profile information \n\nWhat would you like to know?",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingServiceRequest, setIsCreatingServiceRequest] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerType, setDatePickerType] = useState<'start_date' | 'end_date' | null>(null);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [serviceRequestStartDate, setServiceRequestStartDate] = useState<dayjs.Dayjs | null>(null);
  
  // Voice-related state
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  
  // Text-to-Speech state
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSynthesis, setSpeechSynthesis] = useState<SpeechSynthesis | null>(null);
  const [currentUtterance, setCurrentUtterance] = useState<SpeechSynthesisUtterance | null>(null);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  
  const [hasConversationContext, setHasConversationContext] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [messages, isOpen]);



  // Initialize Text-to-Speech
  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setSpeechSynthesis(window.speechSynthesis);
      
      // Load available voices
      const loadVoices = () => {
        const voices = window.speechSynthesis.getVoices();
        setAvailableVoices(voices);
        
        // Select a good default voice (prefer English female voice)
        const preferredVoice = voices.find(voice => 
          voice.lang.startsWith('en') && voice.name.toLowerCase().includes('female')
        ) || voices.find(voice => 
          voice.lang.startsWith('en')
        ) || voices[0];
        
        setSelectedVoice(preferredVoice);
      };
      
      // Load voices immediately
      loadVoices();
      
      // Load voices when they become available (some browsers load them asynchronously)
      window.speechSynthesis.onvoiceschanged = loadVoices;
      
      return () => {
        window.speechSynthesis.onvoiceschanged = null;
      };
    }
  }, []);

  // Add this useEffect to clear service request state on page load/refresh
  useEffect(() => {
    // Clear any ongoing service request on component mount (page refresh)
    const clearServiceRequestState = async () => {
      try {
        const token = authToken || localStorage.getItem('token');
        
        if (token) {
          await fetch(`${API_BASE_URL}/api/chat/cancel-service-request`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });
        }
      } catch (error) {
        console.error('Error clearing service request state:', error);
      }
    };
    
    clearServiceRequestState();
    setIsCreatingServiceRequest(false);
  }, [authToken]);

  const handleDateSelection = (date: dayjs.Dayjs | null) => {
    if (!date) return;
    
    setSelectedDate(date);
    const formattedDate = date.format('YYYY-MM-DD');
    
    // Track start date for end date validation
    if (datePickerType === 'start_date') {
      setServiceRequestStartDate(date);
    }
    
    // Just set the date in input field, don't send automatically
    // User can change the date by clicking another date or send manually
    setInputMessage(formattedDate);
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() && !selectedDate && !isLoading) return;

    const messageText = selectedDate ? selectedDate.format('YYYY-MM-DD') : inputMessage.trim();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = performance.now();
    
    console.log(`[FRONTEND] Request ${requestId} started at ${new Date().toISOString()}`);
    console.log(`[FRONTEND] Message: "${messageText}"`);
    
    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setSelectedDate(null);
    setIsLoading(true);

    try {
      // Get token from props or localStorage
      const token = authToken || localStorage.getItem('token');
      
      if (!token) {
        throw new Error('Authentication token is missing');
      }

      const beforeApiCall = performance.now();
      console.log(`[FRONTEND] API call starting at ${new Date().toISOString()}`);
      console.log(`[FRONTEND] Pre-processing time: ${(beforeApiCall - startTime).toFixed(2)}ms`);

      const response = await fetch(`${API_BASE_URL}/api/chat/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Request-ID': requestId
        },
        body: JSON.stringify({ message: messageText })
      });

      const afterApiCall = performance.now();
      console.log(`[FRONTEND] API response received at ${new Date().toISOString()}`);
      console.log(`[FRONTEND] API call duration: ${(afterApiCall - beforeApiCall).toFixed(2)}ms`);

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const responseData = await response.json();
      
      const afterJsonParse = performance.now();
      console.log(`[FRONTEND] JSON parsed at ${new Date().toISOString()}`);
      console.log(`[FRONTEND] JSON parse time: ${(afterJsonParse - afterApiCall).toFixed(2)}ms`);
      
      // Check if conversation context was used
      if (responseData.data && responseData.data.conversation_context_used) {
        setHasConversationContext(true);
      }
      
      // Get bot response text (backend returns response field, not data.text)
      const botResponseText = responseData.response || responseData.data?.text || "";
      
      // Check if we're in a service request creation flow
      // The backend always includes the cancel hint when in service request mode
      if (responseData.data?.creatingServiceRequest || 
          responseData.data?.serviceRequestFlow ||
          botResponseText.includes('Type \'cancel\' anytime to stop')) {
        setIsCreatingServiceRequest(true);
      }
      
      // Check if the bot is asking for a date
      const isAskingForStartDate = botResponseText.toLowerCase().includes("start date") || 
                                   botResponseText.includes("What is the start date");
      const isAskingForEndDate = botResponseText.toLowerCase().includes("end date") || 
                                 botResponseText.includes("What is the end date");
      
      if (isAskingForStartDate) {
        setShowDatePicker(true);
        setDatePickerType('start_date');
      } else if (isAskingForEndDate) {
        setShowDatePicker(true);
        setDatePickerType('end_date');
      } else if (showDatePicker && !isAskingForStartDate && !isAskingForEndDate) {
        // Hide date picker only if we're not asking for dates
        setShowDatePicker(false);
        setDatePickerType(null);
      }
      
      // Check if service request creation was completed or cancelled
      if (responseData.data?.serviceRequestId || 
          responseData.data?.cancelled ||
          botResponseText.includes('Service request creation cancelled') ||
          botResponseText.includes('has been created successfully')) {
        setIsCreatingServiceRequest(false);
        setShowDatePicker(false);
        setDatePickerType(null);
        setServiceRequestStartDate(null); // Clear start date tracking
      }
      

      
      // Handle the response from the backend
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponseText,
        sender: 'bot',
        timestamp: new Date(),
        data: responseData.data || {},
        sources: responseData.sources || [],
        sql: responseData.data?.sqlQuery,
        reasoning: responseData.data?.reasoning,
        processingTime: responseData.data?.processing_time_ms,
        isError: responseData.data?.isError || responseData.isError || false
      };
      
      setMessages(prev => [...prev, botMessage]);
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      console.log(`[FRONTEND] Request ${requestId} completed at ${new Date().toISOString()}`);
      console.log(`[FRONTEND] Total frontend processing time: ${totalTime.toFixed(2)}ms`);
      console.log(`[FRONTEND] Backend reported processing time: ${responseData.data?.processing_time_ms || 'N/A'}ms`);
      
      if (responseData.data?.processing_time_ms) {
        const networkTime = totalTime - responseData.data.processing_time_ms;
        console.log(`[FRONTEND] Estimated network + overhead time: ${networkTime.toFixed(2)}ms`);
      }
      
      // Auto-speak bot response if voice is enabled
      if (voiceEnabled && !botMessage.isError) {
        setTimeout(() => {
          speakText(botMessage.text);
        }, 500); // Small delay to ensure message is rendered
      }
    } catch (error) {
      const errorTime = performance.now();
      console.error(`[FRONTEND] Request ${requestId} failed at ${new Date().toISOString()}`);
      console.error(`[FRONTEND] Time to error: ${(errorTime - startTime).toFixed(2)}ms`);
      console.error('Chat error:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, I'm having trouble connecting right now. Please try again later.",
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      
      // Auto-speak error message if voice is enabled
      if (voiceEnabled) {
        setTimeout(() => {
          speakText(errorMessage.text);
        }, 500);
      }
    } finally {
      setIsLoading(false);
      const finalTime = performance.now();
      console.log(`[FRONTEND] Request ${requestId} finalized at ${new Date().toISOString()}`);
      console.log(`[FRONTEND] Total time including cleanup: ${(finalTime - startTime).toFixed(2)}ms`);
    }
  };



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const toggleChat = () => {
    // Stop any ongoing speech when closing chat
    if (!isOpen) {
      stopSpeaking();
    }
    setIsOpen(!isOpen);
  };

  // Cleanup speech synthesis on unmount
  useEffect(() => {
    return () => {
      stopSpeaking();
    };
  }, []);

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Render the message content with additional details for bot messages
  const renderMessageContent = (message: Message) => {
    if (message.sender === 'user') {
      return (
        <Typography 
          variant="body2" 
          sx={{ 
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}
        >
          {message.text}
        </Typography>
      );
    }
    
    return (
      <>
        <Box
          sx={{ 
            '& p': { 
              margin: 0,
              marginBottom: '8px',
              '&:last-child': { marginBottom: 0 },
              fontSize: '0.875rem',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
              lineHeight: 1.43,
              letterSpacing: '0.01071em',
              fontWeight: 400
            },
            '& strong': {
              fontWeight: 'bold',
              color: 'inherit'
            },
            '& em': {
              fontStyle: 'italic'
            },
            '& ul, & ol': {
              marginLeft: '16px',
              marginBottom: '8px',
              '&:last-child': { marginBottom: 0 },
              fontSize: '0.875rem',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
            },
            '& li': {
              marginBottom: '2px',
              fontSize: '0.875rem',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
            },
            '& code': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              padding: '2px 4px',
              borderRadius: '3px',
              fontFamily: 'monospace',
              fontSize: '0.8rem'
            },
            '& pre': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              padding: '8px',
              borderRadius: '4px',
              overflow: 'auto',
              margin: '8px 0',
              fontFamily: 'monospace'
            },
            fontSize: '0.875rem',
            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
            lineHeight: 1.43,
            letterSpacing: '0.01071em',
            fontWeight: 400,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}
        >
          <ReactMarkdown>{message.text}</ReactMarkdown>
        </Box>
        
        {/* Display data sources if available and not an error */}
        {!message.isError && message.sources && message.sources.length > 0 && (
          <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {message.sources.map((source, index) => (
              <Chip 
                key={index} 
                label={source} 
                size="small" 
                variant="outlined"
                icon={source.includes('Context') ? <MemoryIcon fontSize="small" /> : <DataObjectIcon fontSize="small" />}
                sx={{ 
                  fontSize: '0.7rem',
                  backgroundColor: source.includes('Context') ? 'rgba(25, 118, 210, 0.1)' : 'transparent'
                }}
              />
            ))}
          </Box>
        )}
        
        {/* Show technical details in an accordion if available and not an error */}
        {!message.isError && (message.sql || message.reasoning || message.processingTime) && (
          <Accordion 
            sx={{ 
              mt: 1, 
              boxShadow: 'none', 
              '&:before': { display: 'none' },
              backgroundColor: 'rgba(0, 0, 0, 0.03)'
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon fontSize="small" />}
              sx={{ minHeight: '36px', p: 0 }}
            >
              <Typography variant="caption" color="text.secondary">
                Technical Details
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 1, pt: 0 }}>
              {message.sql && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="caption" display="flex" alignItems="center" sx={{ mb: 0.5 }}>
                    <CodeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} />
                    SQL Query:
                  </Typography>
                  <Paper 
                    variant="outlined" 
                    sx={{ 
                      p: 1, 
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      fontSize: '0.75rem',
                      fontFamily: 'monospace',
                      overflowX: 'auto'
                    }}
                  >
                    {message.sql}
                  </Paper>
                </Box>
              )}
              
              {message.reasoning && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="caption" sx={{ mb: 0.5 }}>
                    Reasoning:
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                    {message.reasoning}
                  </Typography>
                </Box>
              )}
              
              {message.processingTime && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
                  <Typography variant="caption">
                    Processing time: {message.processingTime}ms
                  </Typography>
                </Box>
              )}
            </AccordionDetails>
          </Accordion>
        )}
      </>
    );
  };

  const cancelServiceRequest = async () => {
    setIsCreatingServiceRequest(false);
    setShowDatePicker(false);
    setDatePickerType(null);
    setServiceRequestStartDate(null); // Reset start date tracking
    
    // Add a message to inform the user
    const cancelMessage: Message = {
      id: Date.now().toString(),
      text: "Service request creation cancelled. How else can I help you?",
      sender: 'bot',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, cancelMessage]);
    
    // Optionally, inform the backend that the flow was cancelled
    try {
      const token = authToken || localStorage.getItem('token');
      
      if (token) {
        await fetch(`${API_BASE_URL}/api/chat/cancel-service-request`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Error cancelling service request:', error);
    }
  };

  const clearConversationHistory = async () => {
    try {
      const token = authToken || localStorage.getItem('token');
      
      if (!token) {
        console.error('No authentication token available');
        return;
      }

      // Clear conversation history in backend
      const response = await fetch(`${API_BASE_URL}/conversation/clear/1`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        // Clear local messages except the initial greeting
        setMessages([
          {
            id: '1',
            text: "Hi! I’m your intelligent Kuber assistant powered by AI. I can help you with:\n- Leave balances and requests\n- Profile information \n\nWhat would you like to know?",
            sender: 'bot',
            timestamp: new Date()
          }
        ]);
        
        setHasConversationContext(false);
        
        // Add confirmation message
        const confirmMessage: Message = {
          id: Date.now().toString(),
          text: "Conversation history cleared! I've started fresh and won't remember our previous conversations.",
          sender: 'bot',
          timestamp: new Date()
        };
        
        setTimeout(() => {
          setMessages(prev => [...prev, confirmMessage]);
        }, 500);
      }
    } catch (error) {
      console.error('Error clearing conversation history:', error);
    }
  };

  // Voice recording methods
  const startRecording = async () => {
    try {
      // Check for microphone permissions first
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1
        } 
      });
      
      // Check if browser supports the required format
      const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
        ? 'audio/webm;codecs=opus' 
        : MediaRecorder.isTypeSupported('audio/webm') 
        ? 'audio/webm'
        : 'audio/wav';
      
      const recorder = new MediaRecorder(stream, { mimeType });
      
      const chunks: Blob[] = [];
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      recorder.onstop = async () => {
        const audioBlob = new Blob(chunks, { type: mimeType });
        
        // Check if audio blob has content
        if (audioBlob.size === 0) {
          alert('No audio was recorded. Please check your microphone and try again.');
          return;
        }
        
        await processVoiceInput(audioBlob);
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
      };
      
      recorder.start();
      setMediaRecorder(recorder);
      setAudioChunks(chunks);
      setIsRecording(true);
      
      console.log('Recording started with format:', mimeType);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      
      let errorMessage = 'Could not access microphone. ';
      if ((error as Error).name === 'NotAllowedError') {
        errorMessage += 'Please allow microphone permissions and try again.';
      } else if ((error as Error).name === 'NotFoundError') {
        errorMessage += 'No microphone found. Please connect a microphone and try again.';
      } else {
        errorMessage += 'Please check your microphone settings and try again.';
      }
      
      alert(errorMessage);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const processVoiceInput = async (audioBlob: Blob) => {
    const voiceRequestId = `voice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = performance.now();
    
    console.log(`[FRONTEND-VOICE] Request ${voiceRequestId} started at ${new Date().toISOString()}`);
    console.log(`[FRONTEND-VOICE] Audio blob size: ${audioBlob.size} bytes`);
    
    setIsProcessingVoice(true);
    
    try {
      const token = authToken || localStorage.getItem('token');
      
      if (!token) {
        throw new Error('Authentication token is missing');
      }

      // Create FormData to send audio file
      const formData = new FormData();
      formData.append('audio', audioBlob, 'voice_message.webm');
      
      const beforeApiCall = performance.now();
      console.log(`[FRONTEND-VOICE] API call starting at ${new Date().toISOString()}`);
      console.log(`[FRONTEND-VOICE] Pre-processing time: ${(beforeApiCall - startTime).toFixed(2)}ms`);
      
      // Use environment variable for API base URL
      const response = await fetch(`${API_BASE_URL}/api/chat/voice-to-text`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Request-ID': voiceRequestId
        },
        body: formData
      });

      const afterApiCall = performance.now();
      console.log(`[FRONTEND-VOICE] API response received at ${new Date().toISOString()}`);
      console.log(`[FRONTEND-VOICE] API call duration: ${(afterApiCall - beforeApiCall).toFixed(2)}ms`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server responded with status: ${response.status}. ${errorText}`);
      }

      const responseData = await response.json();
      
      const afterJsonParse = performance.now();
      console.log(`[FRONTEND-VOICE] JSON parsed at ${new Date().toISOString()}`);
      console.log(`[FRONTEND-VOICE] JSON parse time: ${(afterJsonParse - afterApiCall).toFixed(2)}ms`);
      
      if (responseData.data && responseData.data.text) {
        const transcribedText = responseData.data.text;
        
        // Show success message
        console.log(`[FRONTEND-VOICE] Voice transcribed successfully: "${transcribedText}"`);
        console.log(`[FRONTEND-VOICE] Confidence: ${responseData.data.confidence || 'N/A'}`);
        
        // Set the transcribed text in input field
          setInputMessage(transcribedText);
          
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        
        console.log(`[FRONTEND-VOICE] Request ${voiceRequestId} completed at ${new Date().toISOString()}`);
        console.log(`[FRONTEND-VOICE] Total processing time: ${totalTime.toFixed(2)}ms`);
      } else {
        throw new Error('No text received from voice processing');
      }
      
    } catch (error) {
      const errorTime = performance.now();
      console.error(`[FRONTEND-VOICE] Request ${voiceRequestId} failed at ${new Date().toISOString()}`);
      console.error(`[FRONTEND-VOICE] Time to error: ${(errorTime - startTime).toFixed(2)}ms`);
      console.error('Error processing voice input:', error);
      
      // More user-friendly error messages
      let errorMessage = 'Error processing voice input. ';
      if ((error as Error).message.includes('ECONNREFUSED') || (error as Error).message.includes('Network')) {
        errorMessage += 'Please check if the backend service is running.';
      } else if ((error as Error).message.includes('401') || (error as Error).message.includes('Authentication')) {
        errorMessage += 'Authentication failed. Please log in again.';
      } else if ((error as Error).message.includes('500')) {
        errorMessage += 'Server error. Please try again later.';
      } else {
        errorMessage += 'Please try again or check your microphone settings.';
      }
      
      alert(errorMessage);
    } finally {
      setIsProcessingVoice(false);
      const finalTime = performance.now();
      console.log(`[FRONTEND-VOICE] Request ${voiceRequestId} finalized at ${new Date().toISOString()}`);
      console.log(`[FRONTEND-VOICE] Total time including cleanup: ${(finalTime - startTime).toFixed(2)}ms`);
    }
  };

  const handleVoiceButtonClick = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  // Text-to-Speech functions
  const speakText = (text: string) => {
    if (!speechSynthesis || !voiceEnabled || !text.trim()) return;
    
    // Stop any current speech
    stopSpeaking();
    
    // Clean the text for better speech (remove markdown, emojis, etc.)
    const cleanText = text
      .replace(/[*_`~]/g, '') // Remove markdown
      .replace(/[\u2705\u274c\u26a0\ufe0f\u2022\ud83c\udfaf\ud83c\udf89\ud83d\udce7\ud83d\udcca\ud83d\udcc8\ud83d\udcc9\ud83d\udcb0\ud83d\udccb]/g, '') // Remove common emojis and bullet
      .replace(/\n+/g, '. ') // Replace line breaks with pauses
      .replace(/\s+/g, ' ') // Clean up whitespace
      .trim();
    
    if (!cleanText) return;
    
    const utterance = new SpeechSynthesisUtterance(cleanText);
    
    // Configure voice settings
    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }
    utterance.rate = 0.9; // Slightly slower for clarity
    utterance.pitch = 1.0;
    utterance.volume = 0.8;
    
    // Event handlers
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentUtterance(null);
    };
    
    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event);
      setIsSpeaking(false);
      setCurrentUtterance(null);
    };
    
    setCurrentUtterance(utterance);
    speechSynthesis.speak(utterance);
  };
  
  const stopSpeaking = () => {
    if (speechSynthesis && isSpeaking) {
      speechSynthesis.cancel();
      setIsSpeaking(false);
      setCurrentUtterance(null);
    }
  };
  
  const toggleVoiceOutput = () => {
    if (isSpeaking) {
      stopSpeaking();
    }
    setVoiceEnabled(!voiceEnabled);
  };
  
  const handleSpeakMessage = (text: string) => {
    if (isSpeaking) {
      stopSpeaking();
    } else {
      speakText(text);
    }
  };



  // Perfect input section with improved alignment
  const renderInputSection = () => (
          <Box 
            sx={{ 
              p: 2, 
              borderTop: '1px solid', 
              borderColor: 'divider',
        bgcolor: 'white',
        borderBottomLeftRadius: 16,
        borderBottomRightRadius: 16,
            }}
          >
            {isCreatingServiceRequest && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: 1, 
          mb: 2,
          animation: 'fadeIn 0.3s ease-in-out'
        }}>
                <CancelButton
                  onClick={cancelServiceRequest}
                  startIcon={<CloseIcon sx={{ fontSize: '16px' }} />}
                >
                Cancel  
                </CancelButton>
              </Box>
            )}
            
            {showDatePicker && (
        <Box sx={{ 
          mb: 2, 
          mt: 0.5, 
          display: 'flex', 
          justifyContent: 'center',
          animation: 'slideDown 0.3s ease-in-out'
        }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label={datePickerType === 'start_date' ? 'Select Start Date' : 'Select End Date'}
                    value={selectedDate}
                    onChange={handleDateSelection}
                    disablePast={datePickerType === 'start_date'}
                    minDate={datePickerType === 'end_date' && serviceRequestStartDate ? serviceRequestStartDate : undefined}
                    sx={{
                      width: '90%',
                      '& .MuiOutlinedInput-root': {
                  borderRadius: '25px',
                  height: '45px',
                  fontSize: '14px',
                        fontFamily: styles.FONT_MEDIUM,
                        '& .MuiInputAdornment-root': {
                    height: '100%',
                          maxHeight: '100%',
                          '& .MuiButtonBase-root': {
                      padding: '8px',
                          },
                          '& .MuiSvgIcon-root': {
                      fontSize: '1.2rem',
                          }
                        }
                      },
                      '& .MuiInputBase-input': {
                  padding: '12px 16px',
                      },
                      '& .MuiInputLabel-root': {
                  transform: 'translate(16px, 12px) scale(1)',
                  fontSize: '14px',
                      },
                      '& .MuiInputLabel-shrink': {
                  transform: 'translate(16px, -9px) scale(0.75)',
                      }
                    }}
                    slotProps={{
                      textField: {
                        size: 'small',
                        margin: 'dense',
                        placeholder: 'Select a date',
                      },
                      popper: {
                        sx: {
                          '& .MuiPaper-root': {
                      boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.12)',
                      borderRadius: '16px',
                            overflow: 'hidden',
                          }
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>
            )}
            
      {/* Main Input Container with Perfect Alignment */}
      <Box sx={{ 
        display: 'flex', 
        gap: 1, 
        alignItems: 'center',
        position: 'relative'
      }}>
        {/* Text Input Field */}
              <TextField
          placeholder={
            "Type or use voice input..."
          }
                variant="outlined"
                size="small"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
          disabled={isLoading || isRecording || isProcessingVoice}
                inputRef={inputRef}
                multiline
          maxRows={2}
          minRows={1}
          sx={{
            flexGrow: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: '25px',
              paddingRight: '8px',
              minHeight: '36px',
              backgroundColor: '#f8f9fa',
              border: '2px solid transparent',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                backgroundColor: '#f0f2f5',
                border: '2px solid rgba(25, 118, 210, 0.1)',
              },
              '&.Mui-focused': {
                backgroundColor: 'white',
                border: '2px solid #1976d2',
                boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
              },
              '&.Mui-disabled': {
                backgroundColor: '#e9ecef',
                opacity: 0.7,
              }
            },
            '& .MuiInputBase-input': {
              padding: '6px 16px',
              fontSize: '14px',
              fontFamily: styles.FONT_MEDIUM || 'inherit',
              lineHeight: '1.2',
              '&::placeholder': {
                color: '#6c757d',
                opacity: 1,
              }
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
            },
            margin: 0,
          }}
        />
        
        {/* Voice Input Button */}
        <Tooltip
          title={
            isProcessingVoice 
              ? "Processing voice input..." 
              : isRecording 
              ? "Recording... Click to stop" 
              : "Voice input: Click and speak"
          }
          placement="top"
        >
          <span>
            <IconButton 
              color={isRecording ? "error" : "primary"}
              onClick={handleVoiceButtonClick}
              disabled={isLoading || isProcessingVoice}
              sx={{
                width: 45,
                height: 45,
                flexShrink: 0,
                backgroundColor: isRecording 
                  ? 'error.main' 
                  : isProcessingVoice 
                  ? 'warning.main' 
                  : 'primary.main',
                color: 'white',
                border: '2px solid',
                borderColor: isRecording 
                  ? 'error.main' 
                  : isProcessingVoice 
                  ? 'warning.main' 
                  : 'primary.main',
                boxShadow: isRecording 
                  ? '0 0 0 4px rgba(244, 67, 54, 0.2)' 
                  : isProcessingVoice 
                  ? '0 0 0 4px rgba(255, 152, 0, 0.2)' 
                  : '0 2px 8px rgba(25, 118, 210, 0.3)',
                transition: 'all 0.2s ease-in-out',
                animation: isRecording ? 'pulse 1.5s infinite' : 'none',
                '&:hover': {
                  backgroundColor: isRecording 
                    ? 'error.dark' 
                    : isProcessingVoice 
                    ? 'warning.dark' 
                    : 'primary.dark',
                  transform: 'scale(1.05)',
                  boxShadow: isRecording 
                    ? '0 0 0 6px rgba(244, 67, 54, 0.3)' 
                    : isProcessingVoice 
                    ? '0 0 0 6px rgba(255, 152, 0, 0.3)' 
                    : '0 4px 12px rgba(25, 118, 210, 0.4)',
                },
                '&:disabled': {
                  backgroundColor: '#e0e0e0',
                  color: '#9e9e9e',
                  border: '2px solid #e0e0e0',
                  boxShadow: 'none',
                  transform: 'none',
                },
                '@keyframes pulse': {
                  '0%': {
                    boxShadow: '0 0 0 4px rgba(244, 67, 54, 0.2)',
                  },
                  '50%': {
                    boxShadow: '0 0 0 8px rgba(244, 67, 54, 0.4)',
                  },
                  '100%': {
                    boxShadow: '0 0 0 4px rgba(244, 67, 54, 0.2)',
                  },
                }
              }}
            >
              {isProcessingVoice ? (
                <CircularProgress size={22} sx={{ color: 'white' }} />
              ) : isRecording ? (
                <MicOffIcon sx={{ fontSize: '22px' }} />
              ) : (
                <MicIcon sx={{ fontSize: '22px' }} />
              )}
            </IconButton>
          </span>
        </Tooltip>
        
        {/* Send Button */}
        <Tooltip title="Send message" placement="top">
          <span>
              <IconButton 
                color="primary" 
                onClick={sendMessage} 
              disabled={(!inputMessage.trim() && !selectedDate) || isLoading || isRecording || isProcessingVoice}
              sx={{
                width: 45,
                height: 45,
                flexShrink: 0,
                backgroundColor: 'primary.main',
                color: 'white',
                border: '2px solid',
                borderColor: 'primary.main',
                boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                  borderColor: 'primary.dark',
                  transform: 'scale(1.05)',
                  boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
                },
                '&:disabled': {
                  backgroundColor: '#e0e0e0',
                  color: '#9e9e9e',
                  border: '2px solid #e0e0e0',
                  boxShadow: 'none',
                  transform: 'none',
                },
              }}
            >
              {isLoading ? (
                <CircularProgress size={22} sx={{ color: 'white' }} />
              ) : (
                <SendIcon sx={{ fontSize: '22px' }} />
              )}
              </IconButton>
          </span>
        </Tooltip>
            </Box>
      
      {/* Status Messages */}
      {isRecording && (
        <Box sx={{ 
          mt: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'error.light',
          color: 'error.contrastText',
          borderRadius: '20px',
          padding: '8px 16px',
          animation: 'fadeIn 0.3s ease-in-out'
        }}>
          <Box sx={{ 
            width: 8, 
            height: 8, 
            borderRadius: '50%', 
            backgroundColor: 'error.main',
            marginRight: 1,
            animation: 'blink 1s infinite'
          }} />
          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
            Recording... Click the microphone to stop
          </Typography>
          </Box>
      )}
      
      {isProcessingVoice && (
        <Box sx={{ 
          mt: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'warning.light',
          color: 'warning.contrastText',
          borderRadius: '20px',
          padding: '8px 16px',
          animation: 'fadeIn 0.3s ease-in-out'
        }}>
          <CircularProgress size={16} sx={{ marginRight: 1, color: 'warning.main' }} />
          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
            Processing voice input...
          </Typography>
        </Box>
      )}
      
      {isSpeaking && (
        <Box sx={{ 
          mt: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'success.light',
          color: 'success.contrastText',
          borderRadius: '20px',
          padding: '8px 16px',
          animation: 'fadeIn 0.3s ease-in-out'
        }}>
          <Box sx={{ 
            width: 8, 
            height: 8, 
            borderRadius: '50%', 
            backgroundColor: 'success.main',
            marginRight: 1,
            animation: 'pulse 1.5s infinite'
          }} />
          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
            Speaking... Click to stop
          </Typography>
        </Box>
      )}
      
      {/* Global Styles for Animations */}
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0.3; }
        }
      `}</style>
    </Box>
  );

  return (
    <Box sx={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000 }}>
      {isOpen ? (
        <Paper 
          elevation={8} 
          sx={{ 
            width: 380, // Slightly wider for better content display
            height: 550, // Slightly taller
            display: 'flex', 
            flexDirection: 'column',
            borderRadius: 4, // More rounded corners
            overflow: 'hidden',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            animation: 'slideUp 0.3s ease-out',
            '@keyframes slideUp': {
              from: {
                opacity: 0,
                transform: 'translateY(20px) scale(0.95)',
              },
              to: {
                opacity: 1,
                transform: 'translateY(0) scale(1)',
              },
            }
          }}
        >
          {/* Enhanced Header */}
          <Box 
            sx={{ 
              p: 2.5, 
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              color: 'white',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                component="img"
                src="/favicon.ico"
                alt="Kuber Logo"
                sx={{
                  width: '24px',
                  height: '24px',
                  borderRadius: '4px',
                  objectFit: 'contain',
                  backgroundColor: 'transparent'
                }}
              />
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                  Kuber Assistant
                </Typography>
                {hasConversationContext && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                    <MemoryIcon sx={{ fontSize: '14px', opacity: 0.8 }} />
                    <Typography variant="caption" sx={{ opacity: 0.8, fontSize: '0.7rem' }}>
                      Context aware
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>


              {/* Voice toggle button */}
              {speechSynthesis && (
                <Tooltip title={voiceEnabled ? "Disable voice output" : "Enable voice output"} placement="bottom">
                  <IconButton 
                    size="small" 
                    onClick={toggleVoiceOutput}
                    sx={{ 
                      color: 'white',
                      backgroundColor: voiceEnabled ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 255, 255, 0.1)',
                      '&:hover': {
                        backgroundColor: voiceEnabled ? 'rgba(76, 175, 80, 0.4)' : 'rgba(255, 255, 255, 0.2)',
                        transform: 'scale(1.1)',
                      }
                    }}
                  >
                    {voiceEnabled ? <VolumeUpIcon fontSize="small" /> : <VolumeOffIcon fontSize="small" />}
                  </IconButton>
                </Tooltip>
              )}
              
              {hasConversationContext && (
                <Tooltip title="Clear conversation history" placement="bottom">
                  <IconButton 
                    size="small" 
                    onClick={clearConversationHistory}
                    sx={{ 
                      color: 'white',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                        transform: 'scale(1.1)',
                      }
                    }}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              <IconButton 
                size="small" 
                onClick={toggleChat} 
                sx={{ 
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    transform: 'scale(1.1)',
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
          
          {/* Enhanced Messages Area */}
          <Box 
            sx={{ 
              flexGrow: 1, 
              p: 2, 
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: 1.5,
              background: 'linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%)',
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '3px',
                '&:hover': {
                  background: 'rgba(0, 0, 0, 0.3)',
                },
              },
            }}
          >
            {messages.map((message) => (
              <Box 
                key={message.id}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: message.sender === 'user' ? 'flex-end' : 'flex-start',
                  maxWidth: '100%',
                  animation: 'messageSlideIn 0.3s ease-out',
                  '@keyframes messageSlideIn': {
                    from: {
                      opacity: 0,
                      transform: message.sender === 'user' 
                        ? 'translateX(20px)' 
                        : 'translateX(-20px)',
                    },
                    to: {
                      opacity: 1,
                      transform: 'translateX(0)',
                    },
                  }
                }}
              >
                <Box 
                  sx={{ 
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 1,
                    maxWidth: '100%'
                  }}
                >
                  {message.sender === 'bot' && (
                    <Box sx={{
                      backgroundColor: 'transparent',
                      borderRadius: '50%',
                      p: 0.5,
                      mt: 0.5,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '2px solid rgba(25, 118, 210, 0.2)'
                    }}>
                      <Box
                        component="img"
                        src="/favicon.ico"
                        alt="Kuber Logo"
                        sx={{
                          width: '16px',
                          height: '16px',
                          borderRadius: '2px',
                          objectFit: 'contain'
                        }}
                      />
                    </Box>
                  )}
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', maxWidth: '85%' }}>
                    <Paper 
                      sx={{ 
                        p: 2, 
                        borderRadius: message.sender === 'user' ? '20px 20px 5px 20px' : '20px 20px 20px 5px',
                        bgcolor: message.sender === 'user' 
                          ? 'primary.main' 
                          : message.isError 
                          ? 'error.light' 
                          : 'white',
                        color: message.sender === 'user' 
                          ? 'white' 
                          : message.isError 
                          ? 'error.contrastText' 
                          : 'text.primary',
                        boxShadow: message.sender === 'user' 
                          ? '0 2px 8px rgba(25, 118, 210, 0.3)' 
                          : '0 2px 8px rgba(0, 0, 0, 0.1)',
                        border: message.sender === 'bot' ? '1px solid rgba(0, 0, 0, 0.05)' : 'none',
                      }}
                    >
                      {renderMessageContent(message)}
                    </Paper>
                    
                    {/* Voice controls for bot messages */}
                    {message.sender === 'bot' && speechSynthesis && (
                      <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5, justifyContent: 'flex-start' }}>
                        <Tooltip title={isSpeaking && currentUtterance ? "Stop speaking" : "Play message"} placement="top">
                          <IconButton
                            size="small"
                            onClick={() => handleSpeakMessage(message.text)}
                            disabled={!voiceEnabled}
                            sx={{
                              width: 24,
                              height: 24,
                              backgroundColor: isSpeaking && currentUtterance ? 'error.main' : 'primary.main',
                              color: 'white',
                              fontSize: '12px',
                              '&:hover': {
                                backgroundColor: isSpeaking && currentUtterance ? 'error.dark' : 'primary.dark',
                              },
                              '&:disabled': {
                                backgroundColor: 'grey.300',
                                color: 'grey.500'
                              },
                              opacity: voiceEnabled ? 1 : 0.5,
                              transition: 'all 0.2s ease-in-out'
                            }}
                          >
                            {isSpeaking && currentUtterance ? 
                              <StopIcon sx={{ fontSize: '12px' }} /> : 
                              <PlayArrowIcon sx={{ fontSize: '12px' }} />
                            }
                          </IconButton>
                        </Tooltip>
                      </Box>
                    )}
                  </Box>
                  
                  {message.sender === 'user' && (
                    <Box sx={{
                      backgroundColor: 'grey.300',
                      borderRadius: '50%',
                      p: 0.5,
                      mt: 0.5
                    }}>
                      <PersonIcon sx={{ color: 'grey.700', fontSize: '16px' }} />
                    </Box>
                  )}
                </Box>
                
                <Typography 
                  variant="caption" 
                  color="text.secondary"
                  sx={{ 
                    mt: 0.5, 
                    mx: 2,
                    alignSelf: message.sender === 'user' ? 'flex-end' : 'flex-start',
                    fontSize: '0.7rem',
                    opacity: 0.7
                  }}
                >
                  {formatTimestamp(message.timestamp)}
                </Typography>
              </Box>
            ))}
            
            {isLoading && (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1, 
                my: 1,
                animation: 'fadeIn 0.3s ease-in-out'
              }}>
                <Box sx={{
                  backgroundColor: 'transparent',
                  borderRadius: '50%',
                  p: 0.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid rgba(25, 118, 210, 0.2)'
                }}>
                  <Box
                    component="img"
                    src="/favicon.ico"
                    alt="Kuber Logo"
                    sx={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '2px',
                      objectFit: 'contain'
                    }}
                  />
                </Box>
                <Box sx={{
                  display: 'flex',
                  gap: 0.5,
                  alignItems: 'center',
                  backgroundColor: 'white',
                  borderRadius: '20px',
                  padding: '12px 16px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                }}>
                  <CircularProgress size={16} />
                  <Typography variant="body2" color="text.secondary">
                    Thinking...
                  </Typography>
                </Box>
              </Box>
            )}
            
            <div ref={messagesEndRef} />
          </Box>
          
          {/* Enhanced Input Section */}
          {renderInputSection()}
        </Paper>
      ) : (
        <IconButton
          color="primary"
          onClick={toggleChat}
          sx={{
            backgroundColor: 'primary.main',
            color: 'white',
            width: 56,
            height: 56,
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              backgroundColor: 'primary.dark',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',
            },
          }}
        >
          <ChatIcon sx={{ fontSize: '24px' }} />
        </IconButton>
      )}
    </Box>
  );
};

export default Chatbot;
