import React from 'react';
import { useSelector } from 'react-redux';
import Chatbot from './Chatbot';

const ChatbotWrapper: React.FC = () => {
  const userDetails = useSelector(
    (state: { entities: { dashboard: { getUserDetails: any } } }) =>
      state?.entities?.dashboard?.getUserDetails,
  );
  
  const homePageDetails = useSelector(
    (state: { entities: { dashboard: { getHomePageDetails: any } } }) =>
      state?.entities?.dashboard?.getHomePageDetails,
  );
  
  // Get auth token from localStorage
  const authToken = localStorage.getItem('token');
  
  // Allowed employee IDs for dev deployment
  const allowedEmployeeIds = ['1', '2', '32', '172', '103', '193', '266', '404', '434', '439', '453', '521', '538', '544', '677', '1816', '1813', '1588', '1782', '1791', '735', '194','1756'];
  
  // Get employee ID from home page details (which contains basicInfo.employeeId)
  const employeeId = homePageDetails?.basicInfo?.employeeId;
  
  // Debug logging
  console.log('ChatbotWrapper Debug:', {
    allowedEmployeeIds,
    employeeId,
    homePageDetails: homePageDetails?.basicInfo,
    userDetails,
    isAuthorized: employeeId && allowedEmployeeIds.includes(employeeId)
  });
  
  // Check if current user's employee_id is in the allowed list
  const isAuthorizedUser = employeeId && 
    allowedEmployeeIds.includes(employeeId);
  
  // Only render chatbot for authorized users
  if (!isAuthorizedUser) {
    return null;
  }
  
  return <Chatbot authToken={authToken || undefined} />;
};

export default ChatbotWrapper;
