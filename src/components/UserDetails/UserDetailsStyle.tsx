import { Button, MenuItem, TextField } from '@mui/material'
import { makeStyles } from '@mui/styles'
import { DatePicker } from '@mui/x-date-pickers'
import styled from 'styled-components'
import style from '../../utils/styles.json'
import { ButtonProps } from '@mui/material/Button'

interface StyledButtonProps extends ButtonProps {
  width?: string
  height?: string
  bgColor?: string
  hoverColor?: string
}

export const InputField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    fontFamily: style.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '& .<PERSON>i<PERSON>ormLabel-root.MuiInputLabel-root': {
    marginLeft: '-5px !important',
    fontSize: '15px',
    lineHeight: '1.8em',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

export const SelectField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-root': {
    marginTop: '2px',
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px !important',
  fontFamily: `${style.FONT_MEDIUM} !important`,
}))

export const useStyles = makeStyles((theme: { spacing: (arg0: number) => any }) => ({
  root: {
    width: '100%',
  },
  backButton: {
    marginRight: theme.spacing(1),
  },
  instructions: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
}))

export const CommonButton = styled(Button)(({ theme }) => ({
  fontSize: '16px !important',
  fontFamily: `${style.FONT_BOLD}!important`,
  width: '20%',
  borderRadius: '20px !important',
}))

export const StyledButton = styled(Button)<any>(
  ({ theme, width, height, bgColor, hoverColor, fontSize, color }) => {
    const defaultBgColor = theme?.palette?.primary?.main || '#193C6D'
    const defaultHoverColor = theme?.palette?.primary?.dark || '#193C6D'
    return {
      fontSize: fontSize || '16px !important',
      fontFamily: 'Roboto, sans-serif',
      width: width || '20%',
      height: height || '40px',
      borderRadius: '20px !important',
      backgroundColor: bgColor || defaultBgColor,
      color: color || '#fff',
      transition: 'all 0.3s ease-in-out',
      '&:hover': {
        backgroundColor: hoverColor || defaultHoverColor,
        transform: 'scale(1.05)',
        boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.2)',
      },
    }
  },
)

export const DateField = styled(DatePicker)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root, & .MuiInputLabel-root': {
    lineHeight: '1em',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))
