import { Checkbox, FormControl, InputLabel, MenuItem, OutlinedInput, Select, styled, Tooltip } from "@mui/material";
import React from "react";
import styless from '../../utils/styles.json'

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const style = {
  checkBoxItem: { fontSize: '14px', padding: '3px 12px' },
  selectStyle: {
    '& .MuiSelect-select': {
      height: '15px',
    },
  },
  gridStyle: {
    '& .MuiFormControl-root': {
      margin: '0 0 15px',
    },
  },
}

const CustomFormControll = styled(FormControl)(() => ({
  marginLeft: '15px',
  marginTop: '15px',
}))

const StyledSelectField = styled(Select)(({ theme }) => ({
  width: "92%",
  borderRadius: '20px',
  '& .MuiSelect-select': {
    padding: '9px 11px',
    fontSize: '13px',
    fontFamily: styless.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '&.MuiInputBase-root.MuiOutlinedInput-root.MuiSelect-root': {
    borderRadius: '20px',
    padding: '9px 11px',
    marginLeft: '-1px',
  },
}))
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
  // autoFocus: false,
  // disableAutoFocus: true,
  // disableAutoFocusItem: true,
  // // Keep the menu open
  // onClose: (event: any) => {
  //   event.stopPropagation();
  // },
  MenuListProps: {
    // 👇 This is the key part: stop the menu from closing on click
    onMouseDown: (event: React.MouseEvent) => {
      event.stopPropagation();
    },
  },
}
const DepartmentDropdown = React.memo(({
  userDepartment,
  handleUserDepartmentChange,
  fetchDepartmentDataFetch,
  hasPermission,
  userData
}: any) => (
  <>
    <CustomFormControll>

      <InputLabel
        id='demo-multiple-checkbox-label'
        sx={{
          marginTop: '-5px !important', // Apply directly here, not via '.MuiFormLabel-root'
          fontSize: '0.875rem', // Optional: match size with SelectField
        }}
      >
        Department Allocated
      </InputLabel>
      <Tooltip
        title={!hasPermission ? `${userData?.first_name ? userData?.first_name : ''} has administrator access.` : ""}
        placement="top"
        arrow
        disableHoverListener={hasPermission} // Disable tooltip when dropdown enabled
      >
        <div>
          <StyledSelectField
            labelId='demo-multiple-checkbox-label'
            id='demo-multiple-checkbox'
            variant='outlined'
            size='small'
            label='Department Allocated1'
            multiple
            input={<OutlinedInput label='Department Allocated12' />}
            value={userDepartment}
            onChange={handleUserDepartmentChange}
            renderValue={(selected: any) =>
              fetchDepartmentDataFetch?.department
                ?.filter((dept: any) => selected.includes(dept.id))
                .map((dept: any) => dept.dept_name)
                .join(', ')
            }
            MenuProps={MenuProps}
            disabled={!hasPermission}
          >
            {fetchDepartmentDataFetch.department?.map((departments: any) => {
              const deptId = departments?.id;
              return (
                <MenuItem key={deptId} value={deptId}>
                  <Checkbox
                    checked={userDepartment.includes(deptId)}
                    size='small'
                    sx={style.selectStyle}
                  />
                  {departments?.dept_name}
                </MenuItem>
              );
            })}
          </StyledSelectField>
        </div>
      </Tooltip>

    </CustomFormControll>
  </>
));

export default DepartmentDropdown;