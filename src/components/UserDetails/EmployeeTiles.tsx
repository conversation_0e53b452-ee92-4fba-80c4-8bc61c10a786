import { Paper, Box, Typography } from '@mui/material'
import Cards from '../Common/Card/Cards'
import BackgroundInfoSvg from '../../components/Svg/noun-personal-information-7242160 .svg'
import EmploymentInfoSvg from '../../components/Svg/employmentInfo.svg'
import BankInformation from '../../components/Svg/noun-bank-7329159.svg'
import SalarySlip from '../../components/Svg/noun-payroll-7160887.svg'
import Payroll from '../../components/Svg/payroll.svg'
import TaxReport from '../../components/Svg/noun-tax-6784489.svg'
import BasicInfo from '../../components/Svg/noun-account-info-4604294.svg'
import Compensation from '../../components/Svg/noun-funds-7223384.svg'
import LeaveInfo from '../../components/Svg/noun-absence-7388037.svg'
import WorkHistoryIcon from '../../components/Svg/WorkHistory.svg'
import { useLocation, useNavigate } from 'react-router-dom'

import styled from '@emotion/styled'
import style from '../../utils/styles.json'
import { ArrowBack, ConstructionOutlined } from '@mui/icons-material'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { fetchUserDetailsEntities } from '../../reducers'
import { fetchEmployee } from '../../actions'
import { useEffect } from 'react'
const employees = [
  { name: 'Background Info', avatar: BackgroundInfoSvg },
  { name: 'Bank Info', avatar: BankInformation },
  { name: 'Basic Info', avatar: BasicInfo },
  { name: 'Compensation', avatar: Compensation },
  { name: 'Employment Info', avatar: EmploymentInfoSvg },
  { name: 'Leave Details', avatar: LeaveInfo },
  { name: 'Payroll', avatar: Payroll },
  { name: 'Salary Slip', avatar: SalarySlip },
  { name: 'Tax Report', avatar: TaxReport },
  { name: 'Work Profile History', avatar: WorkHistoryIcon },
]

const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '28px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '20px',
}))

const EmployeeTiles = (props: any) => {
  const { fetchEmployee, rowdata, restFetchEmployee } = props
  const navigate = useNavigate()
  const location = useLocation()
  const rowData: any = location?.state

  const navigateByName = (name: string) => {
    switch (name) {
      case 'Background Info':
        navigate(`/home/<USER>/backgroundInfo?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData : {},
        })
        break
      case 'Employment Info':
        navigate(`/home/<USER>/employees-info?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData : {},
        })

        break
      case 'Payroll':
        navigate(`/home/<USER>/payroll?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData : {},
        })
        break
      case 'Bank Info':
        navigate(`/home/<USER>/bank-info?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData : {},
        })
        break
      case 'Salary Slip':
        navigate(`/home/<USER>/salarySlip?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData : {},
        })
        break
      case 'Leave Details':
        navigate(`/home/<USER>/LeaveDetails?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData?.userid : {},
        })
        break

      case 'Compensation':
        navigate(`/home/<USER>/compensation?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData?.userid : {},
        })
        break
      case 'Tax Report':
        navigate(`/home/<USER>/tax-report?userId=${rowData?.userid}`, {
          state: rowData?.userid ? rowData?.userid : {},
        })
        break
      case 'Work Profile History':
        navigate(`/home/<USER>/work-history?userId=${rowData?.userid}`, {
          state: rowData?.userid ? { id: rowData?.userid, empName: rowData?.first_name } : {},
        })
        break
      default:
        console.warn(`No route defined for name: ${name}`)
    }
  }

  const handleNavigateToCard = (name: string) => {
    if (name === 'Basic Info') fetchEmployee({ id: rowData?.userid })

    navigateByName(name)
  }
  useEffect(() => {
    if (rowdata?.userid) {
      navigate('/home/<USER>/new-employees', { state: rowData?.userid ? rowdata : {} })
      restFetchEmployee()
    }
  }, [rowdata])

  return (
    <Paper
      elevation={3}
      style={{
        width: '96%',
        padding: '10px 15px 0 15px',
        margin: '20px auto',
        background: '#FFFFFF',
        boxShadow: '0px 3px 6px #00000029',
        position: 'relative',
      }}
    >
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{ position: 'absolute', top: '23px', right: '15px', cursor: 'pointer' }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <HeaderHeading>{rowData?.first_name}'s Details</HeaderHeading>

      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 2,
          justifyContent: 'center',
        }}
      >
        {employees.map((employee, index) => (
          <Box
            key={index}
            sx={{
              width: { xs: '100%', sm: '48%', md: '23%' },
              marginBottom: 2,
            }}
          >
            <Cards
              name={employee.name}
              avatar={employee.avatar}
              handleNavigateToCard={handleNavigateToCard}
            />
          </Box>
        ))}
      </Box>
    </Paper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    rowdata: fetchUserDetailsEntities.fetchUserData(state).getEmployee,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchEmployee: (data: any) => dispatch(fetchEmployee.request(data)),
    restFetchEmployee: (data: any) => dispatch(fetchEmployee.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(EmployeeTiles)
