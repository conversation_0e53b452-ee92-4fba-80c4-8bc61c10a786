import { Dayjs } from 'dayjs'
import { Dispatch, SetStateAction } from 'react'

export interface TableDataType {
  project_location: string
  project_name: string
  project_source: string
  project_type: string
  project_status: number
  start_date: string
  end_date: string | null
  billable_dev: {
    fixed: number
    monthly: number
    hourly: number
  }
  billable_QA: {
    fixed: number
    monthly: number
    hourly: number
  }
  billable_lead: {
    fixed: number
    monthly: number
    hourly: number
  }
  billable_reviewer: {
    fixed: number
    monthly: number
    hourly: number
  }
  total_billable: {
    fixed: number
    monthly: number
    hourly: number
  }
}
export type CardDataType = {
  Total_project: number
  total_fixed: {
    dev_fixed: number
    qa_fixed: number
    lead_fixed: number
    reviewer_fixed: number
  }
  total_month: {
    dev: number
    qa: number
    lead: number
    reviewer: number
  }
  total_hour: {
    dev: number
    qa: number
    lead: number
    reviewer: number
  }
  total_review: {
    dev: number
    qa: number
    lead: number
    reviewer: number
  }
  resultList: Object[]
}
export type ProjectSheetPropType = {
  fetchTableData: (data: {
    startDate: string
    end_date: string
    page: string
    limit: string
    search: string
  }) => {}
  tableData: {
    Total_project: number
    total_fixed: {
      dev_fixed: number
      qa_fixed: number
      leadfixed: number
    }
    total_month: {
      dev_month: number
      qa_month: number
      lead_month: number
    }
    total_hour: {
      dev_hour: number
      qa_hour: number
      lead_hour: number
    }
    total_review: {
      dev_review: number
      qa_review: number
      lead_review: number
    }
    resultList: Object[]
    Count: number
  }
  isGettingTableData: boolean
}
export type GetDataType = {
  start_date: string
  end_date: string
  page: string
  limit: string
  search: string
}

export interface ActionBarPropsType {
  expand: boolean
  setExpand: Dispatch<SetStateAction<boolean>>
  searchQuery: string
  setSearchQuery: Dispatch<SetStateAction<string>>
  setRowsPerPage: Dispatch<SetStateAction<number>>
  setPage: Dispatch<SetStateAction<number>>
  startDate: Dayjs
  endDate: Dayjs
  setStartDate: Dispatch<SetStateAction<Dayjs>>
  setEndDate: Dispatch<SetStateAction<Dayjs>>
  cardData: { title: string; items: string[] }[]
  setShowResults: Dispatch<SetStateAction<boolean>>
  rowsPerPage: any
  selectedTab: any
  subTableData: any
  searchByProject: any
  setSearchByProject: any
  handleSearchFilterType: any
}
