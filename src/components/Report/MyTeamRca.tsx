import {
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import TableRowContainer from './TableRowContainer'
import { style } from './SheetStyles'
import { StyledEditableTableCell, StyledTableCell, StyledTableCellEmp, StyledTableRow } from '../Common/CommonStyles'
import { useState } from 'react'
import { log } from 'node:console'
import NoProjectRow from './NoProjectRow'

const RCATable = (props: any) => {
  const { rowsToDisplay, onRowClick } = props

  return (
    <>
      <Box width='100%' margin='20px  0px' sx={style.table}>
        <TableContainer component={Paper} id='export' sx={style.tableContainer}>
          <Table>
            <TableHead id='head'>
              <StyledTableRow sx={style.border}>
                <StyledTableCellEmp sx={style.tableHeadWidth}>Employee ID</StyledTableCellEmp>
                <StyledEditableTableCell width='200px' sx={style.tableHeadWidth}>Emp Name</StyledEditableTableCell>
                <StyledTableCell sx={style.tableHeadWidth}>Subject</StyledTableCell>
                <StyledTableCell sx={style.tableHeadWidth}>Project Name</StyledTableCell>
                <StyledTableCell sx={style.tableHeadWidth}>Manager Name</StyledTableCell>
              </StyledTableRow>
            </TableHead>
            <TableBody>
              {rowsToDisplay?.map((row: any) => (
                <StyledTableRow key={row.employee_id} onClick={() => onRowClick(row)}>
                  <StyledTableCellEmp>{row.employee_id}</StyledTableCellEmp>
                  <StyledTableCellEmp>{row.name}</StyledTableCellEmp>
                  <StyledTableCell>{row.subject}</StyledTableCell>
                  <StyledTableCell>{row.project_name}</StyledTableCell>
                  <StyledTableCell>{row.manager_name ? row.manager_name : 'NA'}</StyledTableCell>
                  </StyledTableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </>
  )
}

export default RCATable
