import { Autocomplete, Box, TextField } from '@mui/material'
import DateRangePicker from './DateRangePicker'
import { ActionBarPropsType } from './ProjectSheetTypes'
import { style } from './SheetStyles'
import ExportButton from './ExportButton'
import { useLocation } from 'react-router-dom'
import { useEffect, useState } from 'react'

const ActionBar = (props: any) => {
  const { state } = useLocation()
  const {
    searchQuery,
    selectedTab,
    setSearchQuery,
    startDate,
    endDate,
    setStartDate,
    setEndDate,
    setShowResults,
    subTableData,
    setWorkingEmployee,
    workingEmployee,
    employeeRecords,
  } = props
  const [value, setValue] = useState({})

  useEffect(() => {
    if (workingEmployee) setValue(workingEmployee)
  }, [workingEmployee])

  return (
    <Box component='div' id='actionBar'>
      <Box sx={style.actionBarConatiner} paddingBottom={'0'}>
        <Box sx={style.actionBarCurrent}>
          <Box padding='0' sx={style.datePickerContainerCurrent}>
            <DateRangePicker
              startDate={startDate}
              endDate={endDate}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              setShowResults={setShowResults}
              searchQuery={searchQuery}
              selectedTab={selectedTab}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default ActionBar
