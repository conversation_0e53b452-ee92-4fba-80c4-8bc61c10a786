import React, { useState, useEffect } from 'react';
import { 
  Badge, 
  IconButton, 
  Menu, 
  MenuItem, 
  Typography, 
  Box, 
  Avatar, 
  Divider 
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { getNotifications, markNotificationAsRead } from '../services';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';

// Define the Notification interface
interface Notification {
  id: number;
  title: string;
  message: string;
  user_picture?: string;
  is_read: boolean;
  created_at: string;
  service_request_id?: number;
}

const NotificationMenu = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    fetchNotifications();
    // Set up polling to check for new notifications every minute
    const interval = setInterval(fetchNotifications, 60000);
    return () => clearInterval(interval);
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await getNotifications();
      if (response && response.data) {
        setNotifications(response.data);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark notification as read
    try {
      await markNotificationAsRead(notification.id);
      // Update local state
      setNotifications(notifications.map(n => 
        n.id === notification.id ? { ...n, is_read: true } : n
      ));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }

    // Navigate to service request if applicable
    if (notification.service_request_id) {
      navigate(`/home/<USER>/service-request?id=${notification.service_request_id}`);
    }
    
    handleClose();
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <>
      <IconButton 
        color="inherit" 
        onClick={handleClick}
        aria-label={`${unreadCount} new notifications`}
      >
        <Badge badgeContent={unreadCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: 400,
            width: 360,
          },
        }}
      >
        <Typography variant="h6" sx={{ p: 2, pb: 0 }}>
          Notifications
        </Typography>
        <Divider sx={{ my: 1 }} />
        
        {notifications.length === 0 ? (
          <MenuItem>
            <Typography variant="body2">No notifications</Typography>
          </MenuItem>
        ) : (
          notifications.map((notification) => (
            <MenuItem 
              key={notification.id} 
              onClick={() => handleNotificationClick(notification)}
              sx={{ 
                py: 1.5, 
                px: 2,
                backgroundColor: notification.is_read ? 'inherit' : 'rgba(25, 118, 210, 0.08)',
              }}
            >
              <Box sx={{ display: 'flex', width: '100%' }}>
                {notification.user_picture ? (
                  <Avatar 
                    src={notification.user_picture} 
                    sx={{ width: 40, height: 40, mr: 2 }}
                  />
                ) : (
                  <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                    {notification.message.charAt(0)}
                  </Avatar>
                )}
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" color="text.primary" noWrap>
                    {notification.message}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                  </Typography>
                  {notification.service_request_id && (
                    <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                      TSR #{notification.service_request_id}
                    </Typography>
                  )}
                </Box>
              </Box>
            </MenuItem>
          ))
        )}
      </Menu>
    </>
  );
};

export default NotificationMenu;
