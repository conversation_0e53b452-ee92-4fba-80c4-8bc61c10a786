import { Paper, Box, Typography } from '@mui/material'
import Cards from '../Common/Card/Cards'
import BackgroundInfoSvg from '../../components/Svg/noun-personal-information-7242160 .svg'
import EmploymentInfoSvg from '../../components/Svg/employmentInfo.svg'
import AddEmp from '../../components/Svg/addEmployee.svg'
import BankInformation from '../../components/Svg/noun-bank-7329159.svg'
import SalarySlip from '../../components/Svg/noun-payroll-7160887.svg'
import Payroll from '../../components/Svg/payroll.svg'
import TaxReport from '../../components/Svg/noun-tax-6784489.svg'
import BasicInfo from '../../components/Svg/noun-account-info-4604294.svg'
import Compensation from '../../components/Svg/noun-funds-7223384.svg'
import LeaveInfo from '../../components/Svg/noun-absence-7388037.svg'
import LeaveType from '../../components/Svg/noun-calendar-5400775.svg'


import { useLocation, useNavigate } from 'react-router-dom'

import styled from '@emotion/styled'
import style from '../../utils/styles.json'
import { ArrowBack, ConstructionOutlined, Opacity } from '@mui/icons-material'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { fetchUserDetailsEntities } from '../../reducers'
import { fetchEmployee } from '../../actions'
import { useEffect } from 'react'
import { navigateByName } from './NavigationURL'

import Designaition from '../../components/Svg/noun-id-card-3030456.svg'
import HolidayLists from '../../components/Svg/holiday-lists.svg'
import AddLeaves from '../../components/Svg/add-holiday.svg'
import Timing from '../../components/Svg/noun-timing-7438790.svg'
import Qualification from '../../components/Svg/qualification.svg'

const employees = [
  { name: 'Background Info', avatar: BackgroundInfoSvg },
  { name: 'Bank Info', avatar: BankInformation },
  { name: 'Basic Info', avatar: BasicInfo },
  { name: 'Compensation', avatar: Compensation },
  { name: 'Employment Info', avatar: EmploymentInfoSvg },
  { name: 'Leave Details', avatar: LeaveInfo },
  { name: 'Payroll', avatar: Payroll },
  { name: 'Salary Slip', avatar: SalarySlip },
  { name: 'Tax Report', avatar: TaxReport },
]

const disableStylings = {
  cursor: 'not-allowed',
  opacity: '0.3',
  '&:hover': { boxShadow: 'unset', transform: 'unset' },
}

const hrControl = [
  { name: 'Designations', avatar: Designaition },
  { name: 'Holiday List', avatar: HolidayLists },
  { name: 'Add Leaves', avatar: AddLeaves },
  { name: 'Leave Type', avatar: LeaveType },
  { name: 'Shift Timings', avatar: Timing },
  { name: 'Qualification Skills', avatar: Qualification },
]

const   leaveReports = [
  { name: 'Leave Report', avatar: HolidayLists },
  {
    name: 'Attendance Report',
    avatar: Qualification,
    // sx: disableStylings,
    // comingSoon: true,
  },
  {
    name: 'Appraisal Reports',
    avatar: Designaition,
    sx: disableStylings,
    comingSoon: true,
  },
  { name: 'Library Reports', avatar: Timing, sx: disableStylings, comingSoon: true },
  { name: 'other', avatar: AddLeaves, sx: disableStylings, comingSoon: true },
]

const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '28px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '20px',
}))

const headings: Record<string, string> = {
  '/home/<USER>': 'HR Control',
  '/home/<USER>/reports': 'Reports',
  '/home/<USER>/reports/leaveReports': 'Leave Report',
}

const CommonTiles = (props: any) => {
  const { fetchEmployee, rowdata, restFetchEmployee } = props
  const navigate = useNavigate()
  const location = useLocation()

  const rowData: any = location?.state
  const pathName = location.pathname

  const heading = headings[pathName] || 'Default Heading'

  const tilesType = (
    pathName: string,
  ): { name: string; avatar: string; sx?: any; comingSoon?: boolean }[] => {
    switch (pathName) {
      case '/home/<USER>':
        return hrControl
      case '/home/<USER>/reports':
        return leaveReports
      case '/home/<USER>/leaveReports':
        return leaveReports

      default:
        return []
    }
  }

  const handleNavigateToCard = (name: string) => {
    navigateByName(name, navigate, location)
  }

  return (
    <Paper
      elevation={3}
      style={{
        width: '95%',
        height: '100%',
        padding: '10px 10px 0 10px',
        margin: '20px',
        background: '#FFFFFF',
        boxShadow: '0px 3px 6px #00000029',
        overflow: 'auto',
        minHeight: '450px',
      }}
    >
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{ float: 'right', pr: '20px', mt: '5px', cursor: 'pointer' }}
        >
          <ArrowBack />
        </Box>
      </Box>

      <HeaderHeading>{heading}</HeaderHeading>

      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 2,
          justifyContent: 'center',
        }}
      >
        {tilesType(pathName)?.map((employee, index) => (
          <Box
            key={index}
            sx={{
              width: { xs: '100%', sm: '48%', md: '23%' },
              marginBottom: 2,
            }}
          >
            <Cards
              name={employee.name}
              avatar={employee.avatar}
              handleNavigateToCard={handleNavigateToCard}
              sx={employee?.sx}
              comingSoon={employee?.comingSoon}
            />
          </Box>
        ))}
      </Box>
    </Paper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    rowdata: fetchUserDetailsEntities.fetchUserData(state).getEmployee,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchEmployee: (data: any) => dispatch(fetchEmployee.request(data)),
    restFetchEmployee: (data: any) => dispatch(fetchEmployee.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CommonTiles)
