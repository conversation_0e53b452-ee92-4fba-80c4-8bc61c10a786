export const navigateByName = (name: string, navigate: any, location: any) => {
  const rowData: any = location?.state
  const pathName = location.pathname

  switch (name) {
    case 'Designations':
      navigate(`${pathName}/designations`, {
        state: {},
      })
      break

    case 'Holiday List':
      navigate(`${pathName}/holidaysList`, {
        state: {},
      })
      break

    case 'Shift Timings':
      navigate(`${pathName}/officeArrivalTiming`, {
        state: {},
      })
      break

    case 'Qualification Skills':
      navigate(`${pathName}/qualificationSkillSets`, {
        state: {},
      })
      break

    case 'Add Leaves':
      navigate(`${pathName}/addLeaves`, {
        state: {},
      })
      break
    case 'Leave Report':
      navigate(`${pathName}/leaveReport`, {
        state: {},
      })
      break

    case 'Attendance Report':
      navigate(`${pathName}/attendanceReport`, {
        state: {},
      })
      break

    case 'Leave Type':
      navigate(`${pathName}/leaveType`, {
        state: {},
      })
      break

    default:
      console.warn(`No route defined for name: ${name}`)
  }
}
