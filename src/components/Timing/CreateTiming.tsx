import React, { useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Button,
  MenuItem,
  Typography,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  CircularProgress,
} from '@mui/material'
import { styled } from '@mui/material/styles'
import { TextField } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import dayjs from 'dayjs'
import styles from '../../utils/styles.json'
import CloseIcon from '@mui/icons-material/Close'
import { loaderProps } from '../Common/CommonStyles'
import { toast } from 'react-toastify'
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers'
import Loader from '../Common/Loader'

export const SelectField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '15px',
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-root': {
    marginTop: '-3px',
    fontSize: '15px',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))
export const InputField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    fontFamily: styles.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginLeft: '-5px !important',
    fontSize: '15px',
    lineHeight: '1.8em',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

const validationSchema = Yup.object({
  timing: Yup.string().required('Timing is required'),
})

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: styles.FONT_MEDIUM,
}))

function BootstrapDialogTitle(props: any) {
  const { children, onClose, ...other } = props

  return (
    <DialogTitle sx={{ m: 0, padding: 0, paddingTop: 1 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label='close'
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 15,
            top: 10,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  )
}

const CreateTiming = (props: any) => {
  const {
    open,
    setOpen,
    fetchTimingData,
    initialValues,
    onSubmit,
    payrollDropDown,
    createTiming,
    createTimingReset,
    isCreateTimingDone,
    isCreateTimingLoader,
    getCreateTiming,
  } = props
  const formik = useFormik({
    initialValues: initialValues || {
      timing: null,
      allowance: 0,
    },
    validationSchema,
    onSubmit: (values) => {
      onSubmit(values)
      onClose()
    },
  })

  const onClose = () => {
    setOpen({ openStatus: false, isEdit: 0, timing: {} })
    setTimeout(() => {
      formik.resetForm()
    }, 500)
  }
  const handleSubmit = async () => {
    try {
      await validationSchema.validate(formik.values, { abortEarly: false })
      const entity = {
        id: open.timing.id ? open.timing.id : 0,
        timing: formik.values.timing,
        idAllowance: formik.values.allowance,
      }
      createTiming(entity)
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const stepErrors = error.inner.reduce((acc, curr) => {
          if (curr.path) {
            acc[curr.path] = curr.message
          }
          return acc
        }, {} as { [K in keyof any]?: string })

        formik.setTouched(
          Object.keys(stepErrors).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {} as { [K in keyof any]: any }),
        )

        formik.setErrors(stepErrors)
      } else {
        console.error('Unexpected error:', error)
      }
    }
  }

  useEffect(() => {
    if (open.isEdit === 1) {
      const date = new Date(open.timing.timing);
      const hours = String(date.getUTCHours()).padStart(2, '0');
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      const time = `${hours}:${minutes}`;
      const formattedTiming = open.timing.timing ? time : ''
      formik.setFieldValue('timing', formattedTiming)
      formik.setFieldValue('allowance', open.timing.id_allowance)
    }
  }, [open.timing])

  useEffect(() => {
    if (isCreateTimingDone) {
      fetchTimingData()
      toast.success(getCreateTiming?.message)
      createTimingReset()
      onClose()
    }
  }, [isCreateTimingDone])

  return (
    <>
      {isCreateTimingLoader && (
          <Loader state={isCreateTimingLoader} />
      )}
      <Dialog
        open={open?.openStatus}
        onClose={onClose}
        PaperProps={{
          sx: { width: '30%' },
        }}
      >
        <BootstrapDialogTitle id='customized-dialog-title' onClose={onClose}>
          <Heading>
            {' '}
            {localStorage.getItem('timing') === 'edit' ? 'Edit Timing' : 'Add Timing'}
          </Heading>
        </BootstrapDialogTitle>
        <DialogContent>
          <form onSubmit={formik.handleSubmit}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                label='Office Timing'
                value={formik.values.timing ? dayjs(formik.values.timing, 'HH:mm') : null}
                onChange={(value) => {
                  const formattedTime = value ? value.format('HH:mm') : ''
                  formik.setFieldValue('timing', formattedTime)
                }}
                viewRenderers={{
                  hours: renderTimeViewClock,
                  minutes: renderTimeViewClock,
                  seconds: renderTimeViewClock,
                }}
                sx={{
                  '.MuiPopper-root .MuiPaper-root .MuiPickersLayout-root .MuiDialogActions-root .MuiButtonBase-root':
                    {
                      backgroundColor: 'red',
                    },
                  '& .MuiInputLabel-outlined': {
                    fontSize: '15px',
                    marginTop: '-3px',
                    fontFamily: styles.FONT_MEDIUM,
                  },
                  '& .MuiButtonBase-root': {
                    fontSize: '15px',
                    fontFamily: styles.FONT_MEDIUM,
                  },
                  '& .MuiButtonBase-root.MuiButton-root.MuiButton-text.MuiButton-textPrimary.MuiButton-sizeMedium.MuiButton-textSizeMedium.MuiButton-colorPrimary':
                    {
                      fontSize: '13px',
                      backgroundColor: '#f0f0f0', // Optional: background color
                      color: '#333', // Optional: text color
                      borderRadius: '8px', // Optional: rounded corners
                      padding: '8px 16px', // Optional: adjust padding
                    },
                }}
                slotProps={{
                  actionBar: {
                    actions: [],
                  },
                  textField: {
                    fullWidth: true,
                    error: formik.touched.timing && Boolean(formik.errors.timing),
                    InputProps: {
                      sx: {
                        borderRadius: '20px !important',
                        height: '45px',
                        fontSize: '13px',
                        fontFamily: styles.FONT_MEDIUM,
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>

            <SelectField
              select
              fullWidth
              label='Allowance'
              name='allowance'
              value={formik.values.allowance}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              sx={{ marginTop: '10px' }}
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    style: {
                      maxHeight: 200, // Maximum height for the dropdown
                      overflowY: 'auto', // Enable vertical scrolling
                    },
                  },
                },
              }}
              required={false}
            >
              <StyledMenuItem
                value={0}
                disabled
                sx={{ fontSize: '15px', fontFamily: styles.FONT_MEDIUM }}
              >
                Select Allowance
              </StyledMenuItem>
              {payrollDropDown?.allowance && payrollDropDown.allowance.length > 0 ? (
                payrollDropDown.allowance.map((data: any) => (
                  <StyledMenuItem
                    sx={{ textTransform: 'capitalize' }}
                    key={data.id}
                    value={data.id}
                  >
                    {data.name}
                  </StyledMenuItem>
                ))
              ) : (
                <StyledMenuItem disabled>No allowances available</StyledMenuItem>
              )}
            </SelectField>
          </form>
        </DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            margin: '-12px -10px 15px 15px',
            gap: '10px',
          }}
        >
          <CancelButton onClick={onClose} autoFocus>
            CANCEL
          </CancelButton>
          <ActionButton autoFocus type='submit' onClick={handleSubmit}>
            SAVE
          </ActionButton>
        </Box>
      </Dialog>
    </>
  )
}

export default CreateTiming
