import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Grid,
  IconButton,
  Pa<PERSON>ation,
  Tooltip,
  Typography,
} from '@mui/material'
import { ReactComponent as EditIcon } from '../../assets/images/editIcon.svg'
import { ReactComponent as DeleteIcon } from '../../assets/images/deleteIcon.svg'
import { useEffect, useState } from 'react'
import { CardsPropType, FormValues, deleteProjectCustomerType } from './ProjectCustomersTypes'
import { connect } from 'react-redux'
import { projectManagementUI } from '../../../reducers'
import { deleteMandateType, deleteProjectCustomer, editDesignations } from '../../actions'
import { Dispatch } from 'redux'
import ShowEntries from './ShowEntries'
import { NoCards } from './NoCards'
import { style } from './projectCustomersStyle'
import ConfirmDeleteDialog from './ConfirmDeleteDialog'
import moment from 'moment'
import Loader from '../Common/Loader'

const Cards = ({
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  timingData,
  isTimingDataLoader,
  setOpen,
  createTiming,
  fetchEditDesignations,
}: any) => {
  const [filteredRows, setFilteredRows] = useState<FormValues[]>()
  const [actionDialog, setActionDialog] = useState(false)
  const diaglogTitle: string = 'Delete Mandate'
  const [isDeleted, setIsDeleted] = useState(false)
  const [forRowDeleteId, setForRowDeleteId] = useState(0)
  const [overflowingTexts, setOverflowingTexts] = useState<Record<number, boolean>>({})

  useEffect(() => {
    setFilteredRows(timingData)
  })

  const deleteOpenConfirmationFunction = (rowId: number) => {
    createTiming({ id: rowId, is_deleted: 1 })
  }

  const handleChangePage = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  useEffect(() => {
    const updatedOverflowingTexts: Record<number, boolean> = {}
    filteredRows?.forEach((row) => {
      const element = document.getElementById(`customer-name-${row.id}`)
      if (element) {
        updatedOverflowingTexts[row.id] = element.scrollWidth > element.clientWidth
      }
    })
    setOverflowingTexts(updatedOverflowingTexts)
  }, [filteredRows])

  const UserRole = localStorage.getItem('roles')
  const isDisabled = !UserRole?.includes('Admin')

  const styles = {
    iconButtonStyles: {
      opacity: isDisabled ? 0.5 : 1,
    },
  }

  return (
    <>
      {isTimingDataLoader ? (
        <Loader state={isTimingDataLoader} />
      ) : (
        <Box sx={style.cardBox}>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 2 }}>
            {timingData?.length === 0 ? (
              <NoCards />
            ) : (
              timingData?.map((row: any) => (
                <Grid item xs={12} sm={6} md={4} key={row.id}>
                  <Card sx={style.cardStyle}>
                    <CardContent sx={style.cardContentStyle}>
                      <Box sx={style.cardContentBoxStyle}>
                        <Box width='70%'>
                          {overflowingTexts[row.id] ? (
                            <Tooltip
                              title={<Typography fontSize='1rem'> {row.mandate_name} </Typography>}
                            >
                              <Typography sx={style.cardContentTypoStyle}>
                              {row.timing ? moment.utc(row.timing).format('h:mm A') : 'NA'}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <Typography
                              id={`customer-name-${row.id}`}
                              noWrap
                              sx={style.cardContentTypoStyle}
                            >
                              {row.timing ? moment.utc(row.timing).format('h:mm A') : 'NA'}
                              </Typography>
                          )}
                        </Box>
                        <Box sx={style.iconBoxStyle}>
                          <Box sx={style.editIconsStyle}>
                            <IconButton
                              onClick={() => {
                                setOpen({ openStatus: true, isEdit: 1, timing: row })
                                localStorage.setItem('timing', 'edit')
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Box>
                          <Box sx={style.iconsStyle}>
                            <Tooltip title="Delete" arrow>
                            <IconButton
                              onClick={() => {
                                deleteOpenConfirmationFunction(row.id)
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>
      )}
      {/* {filteredRows && filteredRows?.length > 0 && (
        <Box sx={style.selectAndPaginationBox}>
          <Box>
            <ShowEntries rowsPerPage={rowsPerPage} setRowsPerPage={setRowsPerPage} />
          </Box>
          <Pagination
            count={Math.ceil(timingData?.count / rowsPerPage)}
            page={page}
            onChange={handleChangePage}
            color='primary'
          />
        </Box>
      )} */}
    </>
  )
}

const mapStateToProp = (state: any) => {
  return {}
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    deleteProjectCustomer: (data: deleteProjectCustomerType) =>
      dispatch(deleteMandateType.request(data)),
    fetchEditDesignations: (data: any) => dispatch(editDesignations.request({ data })),
  }
}
export default connect(mapStateToProp, mapDispatchToProp)(Cards)
