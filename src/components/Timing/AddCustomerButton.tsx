import { Button, styled } from '@mui/material'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import { style } from './projectCustomersStyle'
import styles from '../../utils/styles.json'
import { useEffect } from 'react'

const ActionButton = styled(Button)(() => ({
  fontSize: '15px',
  height: '42px',
  float: 'right',
  margin: '0',
  borderRadius: '20px',
  padding: '5px 15px',
  fontFamily: styles.FONT_MEDIUM,
}))

export const AddCustomerButton = ({ open, setOpen, fetchPayrollTypeDropDown }: any) => {
  const handleClickOpenDiaglogForm = () => {
    setOpen({ openStatus: true, isEdit: 0, timing: {} })
    localStorage.setItem('timing', 'create')
  }
  useEffect(() => {
    if (open) {
      fetchPayrollTypeDropDown()
    }
  }, [open])
  return (
    <>
      <ActionButton
        variant='outlined'
        startIcon={<AddTwoToneIcon sx={style.AddTwoToneIconStyle} />}
        onClick={handleClickOpenDiaglogForm}
      >
        ADD TIMING
      </ActionButton>
    </>
  )
}

export default AddCustomerButton
