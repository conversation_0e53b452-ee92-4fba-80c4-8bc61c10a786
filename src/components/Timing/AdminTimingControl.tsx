import { Box, CircularProgress, Dialog } from '@mui/material'
import { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import styles from '../../../utils/styles.json'
import DiaglogForm from './DiaglogForm'
import AddCustomerButton from './AddCustomerButton'
import SearchBox from './SearchBox'
import { Dispatch } from 'redux'
import { FormValues, DesignationPropType, getProjectCustomersType } from './ProjectCustomersTypes'
import Cards from './Cards'
import { ArrowBack } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import {
  createTiming,
  fetchEmpDesignations,
  fetchPayrollDropdown,
  fetchTiming,
} from '../../actions'
import {
  employeePortalEntity,
  employeePortalUI,
  fetchEmpHrControlEntities,
  fetchEmpHrControlUI,
  fetchUserDetailsEntities,
} from '../../reducers'
import { HeaderHeading } from '../Common/CommonStyles'
import CreateTiming from './CreateTiming'

const style = {
  customBox: {
    overflow: 'auto',
    fontFamily: 'Montserrat-Bold',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    background: 'white',
    opacity: '1',
    textAlign: 'left',
    margin: '20px',
    padding: '10px 25px 25px 25px',
    borderRadius: '10px',
    width: '93%',
    minHeight: '69vh', // Allow height to shrink if content is less
    border: '1px solid #DDDDDD',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)', // Optional for a subtle shadow
    '@media (max-width: 768px)': {
      width: '100%',
      padding: '15px',
    },
    '@media print': {
      '@page': {
        size: '1280px',
      },
    },
  },
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const AdminTimingControl = ({
  getCreateTiming,
  fetchTimingData,
  timingData,
  isTimingDataDone,
  isTimingDataLoader,
  fetchPayrollTypeDropDown,
  payrollDropDown,
  createTiming,
  createTimingReset,
  isCreateTimingDone,
  isCreateTimingLoader,
}: any) => {
  const navigate = useNavigate()
  const [open, setOpen] = useState({ openStatus: false, isEdit: 0, timing: {} })
  const [rowsPerPage, setRowsPerPage] = useState(100)
  const [page, setPage] = useState(1)
  const [openCustomerForm, setOpenCustomerForm] = useState(false)
  const [openCustomerEditForm, setopenCustomerEditForm] = useState(false)
  const [editCustomerData, setEditCustomerData] = useState<any>({
    id: 0,
    job_level_code: '',
    job_level_name: '',
  })

  useEffect(() => {
    fetchTimingData()
  }, [])

  return (
    <Box sx={style.customBox}>
      <HeaderHeading>Shift Timings</HeaderHeading>
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{
            float: 'right',
            cursor: 'pointer',
            marginTop: '-35px',
          }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          marginBottom: '15px',
        }}
      >
        {/* <SearchBox searchQuery={searchQuery} setSearchQuery={setSearchQuery} />  */}
        <AddCustomerButton
          open={open}
          setOpen={setOpen}
          fetchPayrollTypeDropDown={fetchPayrollTypeDropDown}
          payrollDropDown={payrollDropDown}
        />
      </Box>
      {/* <DiaglogForm
        setOpenCustomerForm={setOpenCustomerForm}
        openCustomerForm={openCustomerForm}
        openCustomerEditForm={openCustomerEditForm}
        setopenCustomerEditForm={setopenCustomerEditForm}
        editCustomerData={editCustomerData}
      /> */}
      <CreateTiming
        getCreateTiming={getCreateTiming}
        fetchTimingData={fetchTimingData}
        open={open}
        setOpen={setOpen}
        payrollDropDown={payrollDropDown}
        createTiming={createTiming}
        createTimingReset={createTimingReset}
        isCreateTimingDone={isCreateTimingDone}
        isCreateTimingLoader={isCreateTimingLoader}
      />

      <Box marginTop='5px'>
        <Cards
          getCreateTiming={getCreateTiming}
          isTimingDataLoader={isTimingDataLoader}
          page={page}
          setOpen={setOpen}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          timingData={timingData}
          setopenCustomerEditForm={setopenCustomerEditForm}
          setEditCustomerData={setEditCustomerData}
          createTiming={createTiming}
        />
      </Box>
    </Box>
  )
}

const mapStateToProp = (state: any) => {
  return {
    getEmpDesignations: fetchEmpHrControlEntities.fetchHrControlData(state).getEmpDesignations,
    isEmpDesignationDone: fetchEmpHrControlUI.fetchHrControlData(state).isEmpDesignationDone,
    timingData: employeePortalEntity.getEmployeePortal(state).getTimingData,
    isTimingDataDone: employeePortalUI.getAllEmpPortalUI(state).isTimingDataDone,
    isTimingDataLoader: employeePortalUI.getAllEmpPortalUI(state).isTimingDataLoader,
    payrollDropDown: fetchUserDetailsEntities.fetchUserData(state).payrollDropDown,
    getCreateTiming: fetchEmpHrControlEntities.fetchHrControlData(state).getCreateTiming,
    isCreateTimingDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateTimingDone,
    isCreateTimingLoader: fetchEmpHrControlUI.fetchHrControlData(state).isCreateTimingLoader,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    fetchEmpDesignations: (data: any) => dispatch(fetchEmpDesignations.request(data)),
    fetchTimingData: () => dispatch(fetchTiming.request()),
    fetchPayrollTypeDropDown: (data: any) => dispatch(fetchPayrollDropdown.request(data)),
    createTiming: (data: any) => dispatch(createTiming.request(data)),
    createTimingReset: () => dispatch(createTiming.reset()),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(AdminTimingControl)
