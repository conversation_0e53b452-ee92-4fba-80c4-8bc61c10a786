.create_IDSR_wrapper {
  margin: 0px;
  /* This selector targets the editable element (excluding comments). */
}
.create_IDSR_wrapper .project_wrapper {
  height: auto;
  margin-left: 16px;
  margin-top: 30px;
  border: 2px solid rgba(25, 60, 109, 0.5);
  padding-right: 32px;
  border-radius: 15px;
}
.create_IDSR_wrapper .project_wrapper .delete_wrapper {
  margin-top: 60px;
  margin-left: 12px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  -moz-column-gap: 10px;
       column-gap: 10px;
  align-items: center;
}
.create_IDSR_wrapper .idsr_heading_box {
  margin-left: 16px;
  margin-top: 5px;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  align-items: center;
  margin-bottom: 5px;
  -moz-column-gap: 50px;
       column-gap: 50px;
}
.create_IDSR_wrapper .idsr_heading_box .delete_icon {
  cursor: pointer;
}
.create_IDSR_wrapper .add_delete_button_wrapper {
  display: flex;
  -moz-column-gap: 20px;
       column-gap: 20px;
  justify-content: flex-end;
  margin-bottom: -12px;
  margin-top: -12px;
}
.create_IDSR_wrapper .button_wrapper {
  display: flex;
  align-items: center;
  align-content: center;
  padding: 16px;
}
.create_IDSR_wrapper .divider_wrapper {
  width: 100%;
  padding: 16px 0px 16px 16px;
}
.create_IDSR_wrapper .custom-ckeditor-wrapper {
  border-radius: 20px 20px 20px 20px;
}
.create_IDSR_wrapper .custom_quill .ql-toolbar.ql-snow {
  border-radius: 20px 20px 0px 0px;
}
.create_IDSR_wrapper .custom_quill .ql-container.ql-snow {
  border-radius: 0px 0px 20px 20px;
}
.create_IDSR_wrapper .custom_ck {
  height: 417px;
}
.create_IDSR_wrapper .custom_ck .ck-toolbar_grouping {
  border-top-left-radius: 20px !important;
  border-top-right-radius: 20px !important;
}
.create_IDSR_wrapper .custom_ck .ck-editor__editable_inline:not(.ck-comment__input *) {
  height: 410px;
  border-radius: 0px 0px 20px 20px;
  overflow-y: auto;
}/*# sourceMappingURL=IDSR.css.map */