import React, { useEffect, useRef, useState } from 'react'
import { Box, Button, CircularProgress, Dialog, InputLabel, MenuItem } from '@mui/material'
import { Tree, TreeNode } from 'react-organizational-chart'
import { RootState } from '../../configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { departmentBasedOrgChart } from '../../actions'
import { orgChartEntity, orgChartUI } from '../../reducers'
import { SelectField } from '../Common/ComponentCommonStyles'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import designations from './Designation.json'
import { loaderProps } from '../Common/CommonStyles'
import {
  Label,
  Name,
  NodeProps,
  StyledFormControl,
  StyledNode,
  StyledPaper,
  styles,
} from './OrgStyles'
import Loader from '../Common/Loader'

const Node: React.FC<NodeProps> = ({ designation, name }) => (
  <StyledNode>
    <Name>{name}</Name>
    <Label>{designation}</Label>
  </StyledNode>
)

const renderTreeNodes = (node: NodeProps) => (
  <TreeNode key={node.level} label={<Node {...node} />}>
    {node?.drs?.map((child) => renderTreeNodes(child))}
  </TreeNode>
)

const DepartmentBasedCharts: React.FC = (props: any) => {
  const {
    departmentBasedOrgChartAPI,
    departmentBasedOrgChartData,
    isGettingDepartmentBasedOrgData,
  } = props

  const [department, setDepartment] = useState<string>('Admin')

  const chartRef = useRef<HTMLDivElement>(null)

  const handleDepartment = (event: any) => {
    setDepartment(event.target.value)
  }

  useEffect(() => {
    departmentBasedOrgChartAPI({ department: department })
  }, [department])

  const exportToPDF = () => {
    if (chartRef.current) {
      const chart = chartRef.current
      const originalWidth = chart.scrollWidth
      const originalOverflow = chart.style.overflow

      chart.style.width = `${originalWidth}px`
      chart.style.overflow = 'visible'

      html2canvas(chart, {
        scrollX: 0,
        scrollY: 0,
        useCORS: true,
        width: originalWidth,
        scale: 2,
      }).then((canvas) => {
        const imgData = canvas.toDataURL('image/jpeg', 0.8)
        const pdf = new jsPDF({
          orientation: 'landscape',
          unit: 'px',
          format: [canvas.width, canvas.height],
        })

        const pdfWidth = pdf.internal.pageSize.getWidth()
        const pdfHeight = (canvas.height * pdfWidth) / canvas.width

        pdf.addImage(imgData, 'JPEG', 0, 0, pdfWidth, pdfHeight, '', 'FAST')

        pdf.save('org-chart.pdf')

        chart.style.width = ''
        chart.style.overflow = originalOverflow
      })
    }
  }

  return (
    <>
      <Box sx={styles.selectBox}>
        <StyledFormControl>
          <InputLabel id='demo-simple-select-readonly-label'>Select Level</InputLabel>
          <SelectField
            sx={styles.selectStyle}
            variant='outlined'
            type='small'
            label='Select Level'
            value={department}
            onChange={handleDepartment}
          >
            {designations?.designations.map((designation: any, index: any) => (
              <MenuItem key={index} value={designation.name}>
                {designation.name}
              </MenuItem>
            ))}
          </SelectField>
        </StyledFormControl>
        <Button onClick={exportToPDF} sx={styles.buttonStyles} variant='contained' color='primary'>
          Export as PDF
        </Button>
      </Box>
      <StyledPaper>
        {isGettingDepartmentBasedOrgData ? (
          <Loader state={isGettingDepartmentBasedOrgData} />

        ) : (
          <div ref={chartRef}>
            <Tree
              lineWidth={'var(--line-width)'}
              lineColor={'var(--line-color)'}
              lineBorderRadius={'var(--line-border-radius)'}
              label={<Node {...departmentBasedOrgChartData} />}
            >
              {departmentBasedOrgChartData?.subordinate?.map((child: any) =>
                renderTreeNodes(child),
              )}
            </Tree>
          </div>
        )}
      </StyledPaper>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    departmentBasedOrgChartData:
      orgChartEntity.getAllOrgChartState(state).fetchDepartmentBasedOrgChartState,
    isGettingDepartmentBasedOrgData:
      orgChartUI.getAllOrgChartUI(state).isGettingDepartmentBasedOrgData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    departmentBasedOrgChartAPI: (data: any) => dispatch(departmentBasedOrgChart.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(DepartmentBasedCharts)
