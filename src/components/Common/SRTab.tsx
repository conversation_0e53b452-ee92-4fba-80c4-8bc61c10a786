import * as React from 'react'
import Tabs from '@mui/material/Tabs'
import Tab from '@mui/material/Tab'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import { styled } from '@mui/material'
import styles from '../../utils/styles.json';
import { useNavigate } from 'react-router-dom'


interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_MEDIUM,
}))

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  }
}

export default function SRTab(props: any) {
  const { SrCountDataMyRequest, SrCountDataAssignedRequest } = props



  const [value, setValue] = React.useState(0)
  const navigate = useNavigate(); 

  React.useEffect(() => {
    if (window.location.pathname === '/home/<USER>') {
      setValue(1)
    } else {
      setValue(0)
    }
  }, [])
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const isAssignable = localStorage.getItem('is_assignable')
  return (
    <>
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between' }}>
          <Tabs value={value} onChange={handleChange} aria-label='basic tabs example'>
            <Tab
              label='My Request'
              {...a11yProps(0)}
              onClick={() => navigate('/home/<USER>')} // Client-side navigation
            />
            {isAssignable === 'true' ? (
              <Tab
                label='Assigned Request'
                {...a11yProps(1)}
                onClick={() => navigate('/home/<USER>')} // Client-side navigation
              />
            ) : null}
          </Tabs>

          <Box>
            <Box sx={{ display: 'flex', paddingRight: '20px'}}>
              <StyledTypography>( Open : {SrCountDataMyRequest?.pendingRequest ?? SrCountDataAssignedRequest?.pendingRequest}, </StyledTypography>
              <StyledTypography>&nbsp; Closed : {SrCountDataMyRequest?.closedRequest ?? SrCountDataAssignedRequest?.closedRequest}, </StyledTypography>
              <StyledTypography>&nbsp; In Progress : {SrCountDataMyRequest?.inProgressRequest ?? SrCountDataAssignedRequest?.inProgressRequest}, </StyledTypography>
              <StyledTypography>&nbsp; Re-Open : {SrCountDataMyRequest?.reOpenRequest ?? SrCountDataAssignedRequest?.reOpenRequest}, </StyledTypography>
              <StyledTypography>&nbsp; Won't Fix : {SrCountDataMyRequest?.wontFixedRequest ?? SrCountDataAssignedRequest?.wontFixedRequest},</StyledTypography>
              <StyledTypography>&nbsp; Total : {SrCountDataMyRequest?.totalRequest ?? SrCountDataAssignedRequest?.totalRequest} )</StyledTypography>
            </Box>
          </Box>
        </Box>
        {/* <CustomTabPanel value={value} index={0}>
        My Request
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        Assigned Request
      </CustomTabPanel> */}
      </Box>
    </>
  )
}
