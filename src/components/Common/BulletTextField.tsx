import React, { useState, useRef, useEffect } from 'react'
import { TextField, TextFieldProps } from '@mui/material'

interface BulletTextFieldProps extends Omit<TextFieldProps, 'onChange' | 'value'> {
  value: string
  onChange: (value: string) => void
  bulletSymbol?: string
}

const BulletTextField: React.FC<BulletTextFieldProps> = ({
  value,
  onChange,
  bulletSymbol = '• ',
  ...textFieldProps
}) => {
  const textFieldRef = useRef<HTMLTextAreaElement>(null)

  // Initialize with bullet point if empty
  useEffect(() => {
    if (!value) {
      onChange(bulletSymbol)
    }
  }, [])

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = event.target.value
    
    // Ensure first line always starts with bullet point
    const lines = newValue.split('\n')
    if (lines.length > 0 && lines[0] !== '' && !lines[0].startsWith(bulletSymbol)) {
      lines[0] = bulletSymbol + lines[0]
      newValue = lines.join('\n')
    } else if (lines.length > 0 && lines[0] === '') {
      lines[0] = bulletSymbol
      newValue = lines.join('\n')
    }
    
    onChange(newValue)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const textarea = textFieldRef.current
    if (!textarea) return

    // Handle backspace to protect first line bullet point
    if (event.key === 'Backspace') {
      const cursorPosition = textarea.selectionStart
      const cursorEndPosition = textarea.selectionEnd
      
      // If there's a selection, let the default behavior handle it (unless it includes first bullet)
      if (cursorPosition !== cursorEndPosition) {
        const selectedText = value.substring(cursorPosition, cursorEndPosition)
        const lines = value.split('\n')
        const firstLine = lines[0] || ''
        
        // Check if selection includes the first line's bullet point
        if (cursorPosition < bulletSymbol.length && firstLine.startsWith(bulletSymbol)) {
          // If trying to delete the first bullet point, prevent it
          event.preventDefault()
          // Delete everything except the first bullet point
          const newValue = bulletSymbol + value.substring(Math.max(cursorEndPosition, bulletSymbol.length))
          onChange(newValue)
          setTimeout(() => {
            textarea.setSelectionRange(bulletSymbol.length, bulletSymbol.length)
            textarea.focus()
          }, 0)
          return
        }
      } else {
        // Single character deletion
        const lines = value.split('\n')
        const textBeforeCursor = value.substring(0, cursorPosition)
        const currentLineStart = textBeforeCursor.lastIndexOf('\n') + 1
        const isOnFirstLine = currentLineStart === 0
        
        // If on first line and trying to delete the bullet point, prevent it
        if (isOnFirstLine && cursorPosition <= bulletSymbol.length && cursorPosition > 0) {
          event.preventDefault()
          return
        }
      }
    }

    // Handle Enter key
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()

      const cursorPosition = textarea.selectionStart
      const textBeforeCursor = value.substring(0, cursorPosition)
      const textAfterCursor = value.substring(cursorPosition)
      
      // Add new line with bullet point
      const newText = textBeforeCursor + '\n' + bulletSymbol + textAfterCursor
      onChange(newText)

      // Set cursor position after the bullet point on the new line
      setTimeout(() => {
        if (textarea) {
          const newCursorPosition = cursorPosition + 1 + bulletSymbol.length
          textarea.setSelectionRange(newCursorPosition, newCursorPosition)
          textarea.focus()
        }
      }, 0)
    }

    // Call original onKeyDown if provided
    if (textFieldProps.onKeyDown) {
      textFieldProps.onKeyDown(event)
    }
  }

  return (
    <TextField
      {...textFieldProps}
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      inputRef={textFieldRef}
      multiline
      inputProps={{
        ...textFieldProps.inputProps,
        style: {
          fontFamily: 'monospace',
          ...textFieldProps.inputProps?.style,
        },
      }}
    />
  )
}

// Helper function to format bullet point text for display
export const formatBulletText = (text: string, bulletSymbol: string = '• '): React.ReactNode => {
  if (!text) return '-'
  
  return text.split('\n').map((line, index) => (
    <React.Fragment key={index}>
      {line}
      {index < text.split('\n').length - 1 && <br />}
    </React.Fragment>
  ))
}

export default BulletTextField 