import React from 'react'
import {
  Paper,
  SxProps,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  tableCellClasses,
} from '@mui/material'
import Loader from './Loader'
import styled from '@emotion/styled'
import style from '../../utils/styles.json'

const StyledTableRow = styled(TableRow)(({}) => ({
  cursor: 'pointer',
}))

const StyledTableCell = styled(TableCell)(({}) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    textWrap: 'nowrap',
    padding: '6px 16px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    padding: '5px',
  },
}))

type sxPropsType = {
  sx?: SxProps<Theme>
  props?: any
}

type cellSxAndProps = { [key: string]: sxPropsType }

type sxAndPropsType = {
  tableContainer?: sxPropsType
  table?: sxPropsType
  tableHead?: sxPropsType
  tableHeadStyledTableRow?: sxPropsType
  tableHeadStyledTableCell?: sxPropsType | cellSxAndProps
  tableBody?: sxPropsType
  tableBodyStyledTableRow?: sxPropsType
  tableBodyStyledTableCell?: sxPropsType | cellSxAndProps
}

type TablePropsType<T> = {
  loaderState?: boolean
  rowData: T[]
  heading: { [key: string]: string }
  cellRenderer?: { [key: string]: React.FC<{ data: T }> }
  sxAndProps?: sxAndPropsType
  handleRowClick?: (data: T) => void
}

const CustomTable = <T,>(props: TablePropsType<T>) => {
  const { loaderState, rowData, heading, cellRenderer, sxAndProps, handleRowClick } = props
  const headingKeys: string[] = Object.keys(heading ?? {})
  const getTableProps = (headingKey: string) =>
    typeof sxAndProps?.tableHeadStyledTableCell === 'object' &&
    sxAndProps?.tableHeadStyledTableCell !== null &&
    headingKey in sxAndProps?.tableHeadStyledTableCell
      ? (sxAndProps?.tableHeadStyledTableCell as cellSxAndProps)?.[headingKey]?.props
      : (sxAndProps?.tableHeadStyledTableCell as sxPropsType)?.props

  return (
    <TableContainer
      component={Paper}
      sx={{ maxHeight: '68vh', ...sxAndProps?.tableContainer?.sx }}
      {...sxAndProps?.tableContainer?.props}
    >
      {loaderState && <Loader state={loaderState} />}
      <Table
        sx={{ minWidth: 700, ...sxAndProps?.table?.sx }}
        size='small'
        aria-label='customized table'
        {...sxAndProps?.table?.props}
      >
        <TableHead
          sx={{ ...sxAndProps?.tableHead?.sx }}
          {...sxAndProps?.tableHead?.props}
          style={{ position: 'sticky', top: '0px', zIndex: '2' }}
        >
          <StyledTableRow
            sx={{ ...sxAndProps?.tableHeadStyledTableRow?.sx }}
            {...sxAndProps?.tableHeadStyledTableRow?.props}
          >
            {headingKeys?.map((headingKey: string) => {
              return (
                <StyledTableCell
                  sx={{ ...sxAndProps?.tableHeadStyledTableCell?.sx }}
                  {...(typeof sxAndProps?.tableHeadStyledTableCell === 'object' &&
                  sxAndProps?.tableHeadStyledTableCell !== null &&
                  headingKey in sxAndProps?.tableHeadStyledTableCell
                    ? (sxAndProps?.tableHeadStyledTableCell as cellSxAndProps)?.[headingKey]?.props
                    : (sxAndProps?.tableHeadStyledTableCell as sxPropsType)?.props)}
                >
                  {heading[headingKey]}
                </StyledTableCell>
              )
            })}
          </StyledTableRow>
        </TableHead>
        <TableBody sx={{ ...sxAndProps?.tableBody?.sx }} {...sxAndProps?.tableBody?.props}>
          {!loaderState &&
            rowData?.map((row: T) => {
              return (
                <StyledTableRow
                  sx={{ ...sxAndProps?.tableBodyStyledTableRow?.sx }}
                  {...sxAndProps?.tableBodyStyledTableRow?.props}
                  onClick={() => handleRowClick?.(row)}
                >
                  {headingKeys?.map((headingKey: string) => {
                    if (cellRenderer?.[headingKey]) {
                      const CellRendererComponent: React.FC<{ data: T }> =
                        cellRenderer?.[headingKey]
                      return (
                        <StyledTableCell
                          sx={{ ...sxAndProps?.tableBodyStyledTableCell?.sx }}
                          {...getTableProps(headingKey)}
                        >
                          <CellRendererComponent data={row} />
                        </StyledTableCell>
                      )
                    }
                    return (
                      <StyledTableCell
                        sx={{ ...sxAndProps?.tableBodyStyledTableCell?.sx }}
                        {...getTableProps(headingKey)}
                      >
                        {String(row?.[headingKey as keyof T] ?? '')}
                      </StyledTableCell>
                    )
                  })}
                </StyledTableRow>
              )
            })}
          {!loaderState && rowData?.length === 0 && (
            <StyledTableRow
              sx={{ ...sxAndProps?.tableBodyStyledTableRow?.sx }}
              {...sxAndProps?.tableBodyStyledTableRow?.props}
            >
              <StyledTableCell align='center' colSpan={headingKeys.length}>
                <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                  No records found
                </Typography>
              </StyledTableCell>
            </StyledTableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  )
}

export default CustomTable
