import React, { useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { style } from '../ProjectManagement/mandate/ConfirmDeleteDialogStyles'
import styled from 'styled-components'
import WarningIcon from '@mui/icons-material/Warning'
import styles from './../../utils/styles.json'

const AlertPopUp = ({
  open,
  setOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmButtonText = 'CONFIRM',
  cancelButtonText = 'CANCEL',
}: any) => {
  const handleClose = () => {
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} aria-labelledby='alert-dialog-title'>
      <Box>
        <DialogTitle sx={style.dialogTitleStyle}>
          <WarningIcon sx={{color: 'red'}}/>
          <Typography
            sx={{
              marginLeft:'5px',
              fontSize: '20px',
              textAlign: 'center',
              marginTop:'-4px',
              fontFamily: styles.FONT_BOLD,
              letterSpacing: '0px',
              '@media (max-width: 500px)': {
                fontSize: '5vw',
              },
            }}
          >
            {' '}
            {title}
          </Typography>
          <Box marginLeft='5px'>
            <IconButton
              aria-label='close'
              onClick={() => setOpen(false)}
              sx={{
                ...style.iconButtonStyle,
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            minWidth: '400px',
            maxWidth: '600px',
          }}
        >
          <Box sx={style.dialogContentBoxStyle}>
            <Box sx={style.dialogContentMsgBox}>
              <Typography sx={style.secondTypographyInContent}>{message}</Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={style.dialogActions}>
          <Button
            sx={{
              background: '#E2E2E2',
              color: '#000000',
              fontSize: '13px',
              height: '35px',
              fontFamily: styles.FONT_BOLD,
              width: '25%',
              borderRadius: '20px',
              '&:hover': {
                background: '#E2E2E2',
                color: '#000000',
              },
            }}
            onClick={() => setOpen(false)}
          >
            {cancelButtonText}
          </Button>
          <Button
            sx={{
              fontSize: '13px',
              height: '35px',
              fontFamily: styles.FONT_BOLD,
              width: '25%',
              borderRadius: '20px',
            }}
            onClick={() => {
              onConfirm(title)
            }}
          >
            {confirmButtonText}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  )
}

export default AlertPopUp
