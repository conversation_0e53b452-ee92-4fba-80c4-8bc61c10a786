import { Dialog } from "@mui/material";
import LoaderGif from "../../assets/images/LoaderFinal.gif";

const Loader = (prop: any) => {
  const { state } = prop;
  return (
    <Dialog
      open={state}
      PaperProps={{
        style: {
          backgroundColor: "transparent",
          boxShadow: "none",
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          paddingLeft: "60px",
        },
      }}
    >
      <img src={LoaderGif} width="130px" />
    </Dialog>
  );
};

export default Loader;
