import { styled } from '@mui/material/styles'
import Paper from '@mui/material/Paper'
import Grid from '@mui/material/Grid'
import { FormControlLabel, FormLabel, Radio, RadioGroup, TextField } from '@mui/material'

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '92%',
  padding: '15px 25px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '25px',
  marginLeft: 'auto',
  marginRight: 'auto',
}))

const InputField = styled(TextField)({
  margin: '0',
})

const Label = styled(FormLabel)({
  font: 'normal normal 600 16px/17px Montserrat',
  letterSpacing: '0px',
  color: '#000000',
  opacity: '1',
})

const PersonalInfo = () => {
  return (
    <StyledPaper elevation={0}>
      <Grid container spacing={2}>
        <Grid item xs={4} sm={4}>
          <Label>First Name</Label>
          {/* <InputField sx={{}} /> */}
          <InputField size='small' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Middle Name</Label>
          <InputField size='small' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Last Name</Label>
          <InputField size='small' type='password' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Father's Name</Label>
          <InputField size='small' type='password' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Mother's Name</Label>
          <InputField size='small' type='password' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Employee Id</Label>
          <InputField size='small' type='password' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label id='demo-row-radio-buttons-group-label'>Contract Employee</Label>
          <RadioGroup
            row
            aria-labelledby='demo-row-radio-buttons-group-label'
            name='row-radio-buttons-group'
          >
            <FormControlLabel value='Yes' control={<Radio />} label='Yes' />
            <FormControlLabel value='No' control={<Radio />} label='No' />
          </RadioGroup>
        </Grid>
      </Grid>
    </StyledPaper>
  )
}

const EmployeeCredential = () => {
  return (
    <StyledPaper elevation={0}>
      <Grid container spacing={2}>
        <Grid item xs={4} sm={4}>
          <Label>Login Id</Label>
          {/* <InputField sx={{}} /> */}
          <InputField size='small' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Password</Label>
          <InputField size='small' />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Label>Confirm Password</Label>
          <InputField size='small' type='password' />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Label id='demo-row-radio-buttons-group-label'>Status</Label>
          <RadioGroup
            row
            aria-labelledby='demo-row-radio-buttons-group-label'
            name='row-radio-buttons-group'
          >
            <FormControlLabel value='Active' control={<Radio />} label='Active' />
            <FormControlLabel value='InActive' control={<Radio />} label='InActive' />
          </RadioGroup>
        </Grid>
      </Grid>
    </StyledPaper>
  )
}

export default function BasicInfo() {
  return (
    <div style={{ backgroundColor: 'rgb(231, 235, 240)', width: '100%', height: '100%' }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <PersonalInfo />
          <EmployeeCredential />
        </Grid>
      </Grid>
    </div>
  )
}
