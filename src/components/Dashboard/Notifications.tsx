import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  List, 
  ListItem, 
  ListItemAvatar, 
  Avatar, 
  ListItemText, 
  Divider, 
  Button, 
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  IconButton
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import CloseIcon from '@mui/icons-material/Close';
import axios from 'axios';
import { useSelector } from 'react-redux';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { ReactComponent as NoItemFoundIcon } from '../../assets/images/NoItemIcon.svg';
import styles from '../../utils/styles.json';
import { toast } from 'react-toastify';

// Use environment variable for API URL
const API_URL = process.env.REACT_APP_BASE_URL;
// const API_URL = 'http://localhost:3001';

// Styled components for buttons
const CancelButton = styled(Button)({
  background: 'transparent',
  color: '#f44336',
  fontSize: '0.7rem',
  height: '24px',
  fontFamily: styles.FONT_BOLD,
  minWidth: '70px',
  borderRadius: '12px',
  border: '1px solid #f44336',
  padding: '2px 8px',
  '&:hover': {
    background: 'rgba(244, 67, 54, 0.04)',
    color: '#d32f2f',
    borderColor: '#d32f2f',
  },
});

const ActionButton = styled(Button)({
  fontSize: '0.7rem',
  height: '24px',
  fontFamily: styles.FONT_BOLD,
  minWidth: '70px',
  borderRadius: '12px',
  padding: '2px 8px',
});

const Notifications = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const token = localStorage.getItem('token');
  const navigate = useNavigate();
  
  // State for service request details popup
  const [serviceRequestDetails, setServiceRequestDetails] = useState<any>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  
  // Get user ID from Redux store
  const userId = useSelector(
    (state: any) => state?.entities?.dashboard?.getUserDetails?.id
  );
  
  // Fetch notifications function
  const fetchNotifications = useCallback(async () => {
    if (!userId || !token) return;
    
    setLoading(true);
    try {
      const response = await axios.get(`${API_URL}/notifications`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data && Array.isArray(response.data)) {
        setNotifications(response.data as any[]);
      } else {
        console.error('Invalid notification data format:', response.data);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, token]);
  
  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);
  
  // Fetch service request details
  const fetchServiceRequestDetails = async (serviceRequestId: number) => {
    if (!serviceRequestId || !token) return;
    
    setLoadingDetails(true);
    try {
      const response = await axios.get(`${API_URL}/service-request?id=${serviceRequestId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data && response.data.data) {
        setServiceRequestDetails(response.data.data);
        setDetailsDialogOpen(true);
      } else {
        console.error('Invalid service request data format:', response.data);
      }
    } catch (error) {
      console.error('Failed to fetch service request details:', error);
    } finally {
      setLoadingDetails(false);
    }
  };
  
  // Close details dialog
  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false);
  };
  
  const handleNotificationClick = async (notification: any) => {
    try {
      // Mark notification as read
      await axios.patch(`${API_URL}/notifications/${notification.id}/read`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Update local state to mark notification as read
      setNotifications((prevNotifications) => 
        prevNotifications.map((n) => 
          n.id === notification.id ? { ...n, is_read: true } : n
        )
      );
      
      // If it's a service request notification, show details popup
      if (notification.service_request_id) {
        // Remove any conditional check that might be preventing the popup from showing
        fetchServiceRequestDetails(notification.service_request_id);
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleServiceRequestAction = async (notification: any, isApproved: boolean, event: React.MouseEvent) => {
    if (event) {
      event.stopPropagation(); // Prevent the notification click handler from firing
    }
    
    try {
      // Update service request status
      if (notification.service_request_id) {
        // Use consistent status codes: 1 for approved, 0 for rejected
        const newStatus = isApproved ? 1 : 0;
        
        const response = await axios.patch(
          `${API_URL}/service-requests/${notification.service_request_id}/status`, 
          { status: newStatus },
          { headers: { Authorization: `Bearer ${token}` } }
        );
        
        console.log(`Service request ${notification.service_request_id} ${isApproved ? 'approved' : 'rejected'}`);
        
        // Delete the notification from the database after successful action
        await axios.delete(`${API_URL}/notifications/${notification.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        // Remove notification from local state immediately
        setNotifications((prevNotifications) => 
          prevNotifications.filter((n) => n.id !== notification.id)
        );
        
        // Show success message
        const actionText = isApproved ? 'approved' : 'rejected';
        toast.success(
          `Service request #${notification.service_request_id} ${actionText} successfully!`,
          {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
          }
        );
        
        // Also show a brief info about the notification sent to employee
        setTimeout(() => {
          toast.info(
            `Employee has been notified about the ${actionText} request.`,
            {
              position: "top-right",
              autoClose: 4000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
            }
          );
        }, 1000);
      }
      
    } catch (error: any) {
      console.error(`Failed to ${isApproved ? 'approve' : 'reject'} service request:`, error);
      
      // Show error message
      const actionText = isApproved ? 'approve' : 'reject';
      const errorMessage = error.response?.data?.message || `Failed to ${actionText} service request. Please try again.`;
      
      toast.error(
        `${errorMessage}`,
        {
          position: "top-right",
          autoClose: 6000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );
    }
  };
  
  // Format the notification time
  const formatTime = (timestamp: string) => {
    const now = moment();
    const notificationTime = moment(timestamp);
    
    if (now.diff(notificationTime, 'days') < 1) {
      return notificationTime.fromNow(); // e.g. "2 hours ago"
    } else if (now.diff(notificationTime, 'days') < 7) {
      return notificationTime.format('ddd [at] h:mm A'); // e.g. "Mon at 2:30 PM"
    } else {
      return notificationTime.format('MMM D, YYYY'); // e.g. "Jan 15, 2023"
    }
  };
  
  // Get the profile picture for the notification
  const getProfilePicture = (notification: any) => {
    // First try to use the creator's image if available
    if (notification.creator?.image_path) {
      return notification.creator.image_path;
    }
    
    // Then try to use the user_picture field
    if (notification.user_picture) {
      return notification.user_picture;
    }
    
    // Default avatar
    return '/assets/images/default-avatar.png';
  };

  // Function to determine if a notification needs approval buttons
  const needsApproval = (notification: any) => {
    // Check if this is a service request notification
    if (!notification.service_request_id) {
      return false;
    }
    
    // Check if the notification is for a new service request
    const isNewRequest = notification.message.toLowerCase().includes('created a new service request');
    
    // Only show approval buttons if it's a new request AND the service request is still open/pending
    if (!isNewRequest) {
      return false;
    }
    
    // Check the service request status - only show buttons for open (1) or pending (3) requests
    const serviceRequest = notification.service_request;
    if (serviceRequest) {
      const status = serviceRequest.status;
      // Status 1 = Open, Status 3 = Pending - these can be approved/rejected
      // Status 0 = Rejected, Status 5 = Closed - these are already processed
      return status === 1 || status === 3;
    }
    
    // If we can't determine the status, show the buttons (fallback for new requests)
    return true;
  };
  
  // Get status label
  const getStatusLabel = (statusValue: number) => {
    const statusMap: {[key: number]: string} = {
      0: 'Rejected',
      1: 'Open',
      2: 'In Progress',
      3: 'Pending',
      4: "Won't Fix",
      5: 'Closed',
      6: 'Re-opened'
    };
    return statusMap[statusValue] || 'Unknown';
  };
  
  // Get priority label
  const getPriorityLabel = (priorityValue: number) => {
    const priorityMap: {[key: number]: string} = {
      1: 'Low',
      2: 'Medium',
      3: 'High',
      4: 'Critical'
    };
    return priorityMap[priorityValue] || 'Unknown';
  };
  
  // Get status color
  const getStatusColor = (statusValue: number) => {
    const colorMap: {[key: number]: string} = {
      0: 'error',
      1: 'info',
      2: 'primary',
      3: 'warning',
      4: 'default',
      5: 'success',
      6: 'secondary'
    };
    return colorMap[statusValue] || 'default';
  };
  
  // Get priority color
  const getPriorityColor = (priorityValue: number) => {
    const colorMap: {[key: number]: string} = {
      1: 'success',
      2: 'info',
      3: 'warning',
      4: 'error'
    };
    return colorMap[priorityValue] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Paper elevation={1} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ p: 2, borderBottom: '1px solid #eee', bgcolor: '#f9f9f9' }}>
          <Typography variant="h5" fontWeight="bold">
            All Notifications
          </Typography>
        </Box>
        
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" p={5}>
            <CircularProgress />
          </Box>
        ) : notifications.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            p={5}
          >
            <NoItemFoundIcon />
            <Typography
              fontFamily="Montserrat-Regular"
              fontSize="1rem"
              color="#444444"
              marginTop="1rem"
            >
              No notifications yet
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
            {notifications.map((notification: any) => (
              <ListItem 
                key={notification.id}
                alignItems="flex-start" 
                sx={{ 
                  cursor: 'pointer',
                  backgroundColor: notification.is_read ? 'white' : '#f0f7ff',
                  padding: '16px 20px',
                  minHeight: '100px',
                  borderRadius: '8px',
                  border: '1px solid #e0e0e0',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': { 
                    backgroundColor: '#f5f5f5',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => handleNotificationClick(notification)}
              >
                  <Box sx={{ display: 'flex', width: '100%', alignItems: 'flex-start', gap: 2 }}>
                    {/* Left side: Avatar */}
                    <ListItemAvatar sx={{ minWidth: '50px', mt: 0.5 }}>
                      <Avatar 
                        src={getProfilePicture(notification)} 
                        alt={notification.creator?.name || "User"}
                        sx={{ width: 45, height: 45 }}
                      />
                    </ListItemAvatar>
                    
                    {/* Middle: Content */}
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 1, overflow: 'hidden' }}>
                      {/* Main message */}
                      <Typography
                        component="div"
                        variant="body1"
                        color="text.primary"
                        fontSize="0.9rem"
                        sx={{ 
                          fontWeight: notification.is_read ? 'normal' : '600',
                          lineHeight: 1.4,
                          wordBreak: 'break-word'
                        }}
                      >
                        {notification.message}
                      </Typography>
                      
                      {/* Priority and Project Group chips */}
                      {notification.service_request && (notification.service_request.priority || notification.service_request.project_group) && (
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
                          {notification.service_request.priority && (
                            <Chip
                              label={`Priority: ${getPriorityLabel(notification.service_request.priority)}`}
                              color={getPriorityColor(notification.service_request.priority) as any}
                              size="small"
                              sx={{ fontSize: '0.75rem', height: '24px' }}
                            />
                          )}
                          {notification.service_request.project_group && (
                            <Chip
                              label={`Group: ${notification.service_request.project_group}`}
                              color="info"
                              variant="outlined"
                              size="small"
                              sx={{ fontSize: '0.75rem', height: '24px' }}
                            />
                          )}
                        </Box>
                      )}
                      
                      {/* Time and TSR number */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                        <Typography
                          component="span"
                          variant="caption"
                          color="text.secondary"
                          fontSize="0.8rem"
                          sx={{ fontWeight: '500' }}
                        >
                          {formatTime(notification.created_at)}
                        </Typography>
                        {notification.service_request_id && (
                          <Chip
                            label={`TSR #${notification.service_request_id}`}
                            color="primary"
                            size="small"
                            sx={{ fontSize: '0.75rem', height: '22px' }}
                          />
                        )}
                      </Box>
                    </Box>
                    
                    {/* Right side: Action buttons */}
                    {needsApproval(notification) && (
                      <Box sx={{ 
                        display: 'flex', 
                        flexDirection: 'column',
                        gap: 1,
                        alignItems: 'flex-end',
                        minWidth: '140px'
                      }}>
                        <ActionButton
                          size="small"
                          variant="contained"
                          color="success"
                          startIcon={<CheckCircleIcon sx={{ fontSize: '1rem' }} />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleServiceRequestAction(notification, true, e);
                          }}
                          sx={{ 
                            fontSize: '0.8rem',
                            height: '32px',
                            width: '100px',
                            borderRadius: '16px'
                          }}
                        >
                          Accept
                        </ActionButton>
                        <CancelButton
                          size="small"
                          startIcon={<CancelIcon sx={{ fontSize: '1rem' }} />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleServiceRequestAction(notification, false, e);
                          }}
                          sx={{ 
                            fontSize: '0.8rem',
                            height: '32px',
                            width: '100px',
                            borderRadius: '16px'
                          }}
                        >
                          Reject
                        </CancelButton>
                      </Box>
                    )}
                  </Box>
                </ListItem>
            ))}
          </List>
        )}
      </Paper>
      
      {/* Service Request Details Dialog */}
      <Dialog 
        open={detailsDialogOpen} 
        onClose={handleCloseDetailsDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #eee',
          pb: 1
        }}>
          <Box>
            Service Request Details
            {serviceRequestDetails && (
              <Typography variant="subtitle1" color="primary">
                TSR #{serviceRequestDetails.id}
              </Typography>
            )}
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleCloseDetailsDialog}
            size="small"
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {loadingDetails ? (
            <Box display="flex" justifyContent="center" alignItems="center" p={3}>
              <CircularProgress size={24} />
            </Box>
          ) : serviceRequestDetails ? (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {serviceRequestDetails.title}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Chip 
                  label={getStatusLabel(serviceRequestDetails.status)} 
                  color={getStatusColor(serviceRequestDetails.status) as any}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Priority
                </Typography>
                <Chip 
                  label={getPriorityLabel(serviceRequestDetails.priority)} 
                  color={getPriorityColor(serviceRequestDetails.priority) as any}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Department
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.department?.dept_name || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Issue Type
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.issues?.title || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created By
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.employeeName || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created At
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.created_at ? 
                    new Date(serviceRequestDetails.created_at).toLocaleString() : 
                    'N/A'}
                </Typography>
              </Grid>
              
              {serviceRequestDetails.project_group && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Project Group
                  </Typography>
                  <Chip 
                    label={serviceRequestDetails.project_group} 
                    color="info"
                    variant="outlined"
                    size="small"
                    sx={{ mt: 0.5 }}
                  />
                </Grid>
              )}
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Description
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                  {serviceRequestDetails.description || 'No description provided.'}
                </Typography>
              </Grid>
            </Grid>
          ) : (
            <Typography>Failed to load service request details.</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetailsDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Notifications;
