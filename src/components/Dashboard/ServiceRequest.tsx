import React, { useState, useEffect } from 'react'
import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell, { tableCellClasses } from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Paper from '@mui/material/Paper'
import { Box, Button, CircularProgress, Dialog, Pagination, Typography } from '@mui/material'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import NewServiceRequestDialog from './NewRequestDialog'
import {
  SearchBoxCustom,
  SearchIconStyle,
  loaderProps,
} from '../Common/CommonStyles'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { fetchDepartmentList, fetchSR } from '../../actions'
import { SRUI, dashboardEntity, dashboardUI } from '../../reducers'
import getHardCodedData from './ServiceRequestData.json'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import SRTab from '../Common/SRTab'
import style from '../../utils/styles.json'
import Loader from '../Common/Loader'
import styles from '../../utils/styles.json';


const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '20px',
  border: '1px solid #DDDDDD',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}

const SearchBox: React.CSSProperties = {
  width: '250px',
}

const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    padding: '11px 0px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
  },
}))

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_MEDIUM,
}))

const ActionButton = styled(Button)(() => ({
  fontSize: '13px',
  height: '42px',
  float: 'right',
  marginTop: '15px',
  borderRadius: '20px',
  padding: '5px 20px',
  fontFamily: style.FONT_MEDIUM,
}))

const StyledTableRow = styled(TableRow)(() => ({
  left: '160px',
  width: '1719px',
  height: '60px',
  boxShadow: '0px 10px 3px #6c6c6c10',
  opacity: '1',
}))

const ServiceRequestTableData = ({
  serviceRequestData,
  DepartmentList,
  total,
  page,
  handleChangePage,
  fetchSRData1,
  SRData
}: any) => {
  const { getAllPriority } = getHardCodedData
  const { getAllStatus } = getHardCodedData
  const getPriorityLabel = (priorityValue: any) => {
    const priority = getAllPriority.find((p: any) => p.value === priorityValue)
    return priority ? priority.label : 'Unknown'
  }
  const getStatusLabel = (statusValue: any) => {
    const status = getAllStatus.find((p: any) => p.value === statusValue)
    return status ? status.label : 'Unknown'
  }
  const rowsPerPage = 100
  const [totalPages, setTotalPages] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [serviceRequestList, setServiceRequestList] = useState<any[]>(
    serviceRequestData?.length ? serviceRequestData : [],
  )
  const [selectedRowData, setSelectedRowData] = useState(null)
  const [open, setOpen] = useState(false)
  const navigate = useNavigate()
  useEffect(() => {
    const serviceList =
      serviceRequestData && serviceRequestData.length ? [...serviceRequestData] : []
    const filtered = searchQuery
      ? serviceList?.filter((row: any) =>
          Object.values(row).some((value) =>
            String(value).toLowerCase().includes(searchQuery.toLowerCase()),
          ),
        )
      : serviceList
    setServiceRequestList(filtered)
  }, [searchQuery])
  useEffect(() => {
    const totalRows = total || 0
    const totalPages = Math.ceil(totalRows / rowsPerPage)

    setTotalPages(totalPages)
  }, [total, rowsPerPage])

  const handleClickOpen = (rowData: any) => {
    setSelectedRowData(rowData.id)
    setOpen(true)
  }

  const handleClose = () => {
    setOpen(false)
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setSearchQuery(e.target.value)
  }  

  return (
    <>
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{ float: 'right', pr: '30px', mt: '0px', cursor: 'pointer' }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <SRTab SrCountDataMyRequest={SRData} />
      <SearchBoxCustom
        id='outlined-basic'
        placeholder='Search by Title'
        variant='outlined'
        size='small'
        value={searchQuery}
        onChange={handleSearch}
        style={SearchBox}
        InputProps={{
          startAdornment: <SearchIconStyle />,
        }}
      />

      <ActionButton
        variant='outlined'
        startIcon={<AddTwoToneIcon sx={{ width: 24, height: 24 }} />}
        onClick={handleClickOpen}
      >
        NEW REQUEST
      </ActionButton>

      <NewServiceRequestDialog
        open={open}
        fetchSRData1={fetchSRData1}
        onClose={handleClose}
        rowId={selectedRowData}
        DepartmentList={DepartmentList}
      />

      <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>
        <Table sx={{ minWidth: 700 }} aria-label='customized table'>
          <TableHead>
            <TableRow>
              <StyledTableCell>Ticket No</StyledTableCell>
              <StyledTableCell>Title</StyledTableCell>
              <StyledTableCell>Department</StyledTableCell>
              <StyledTableCell>Issue Type</StyledTableCell>
              <StyledTableCell>Priority</StyledTableCell>
              <StyledTableCell>Status</StyledTableCell>
              <StyledTableCell>Raised By</StyledTableCell>
              <StyledTableCell>Raised On</StyledTableCell>
              <StyledTableCell>Due By</StyledTableCell>
            </TableRow>
          </TableHead>
          {serviceRequestList && serviceRequestList?.length ? (
            <TableBody>
              {serviceRequestList.map((request: any) => (
                <StyledTableRow key={`${request?.srId}${request?.employeeName}`} onClick={() => handleClickOpen(request)}>
                  <StyledTableCell component='th' scope='row'>
                    {request?.srId}
                  </StyledTableCell>
                  <StyledTableCell>{request?.title}</StyledTableCell>
                  <StyledTableCell>{request?.department?.dept_name}</StyledTableCell>
                  <StyledTableCell>{request?.issues?.title}</StyledTableCell>
                  <StyledTableCell>{getPriorityLabel(request?.priority)}</StyledTableCell>
                  <StyledTableCell>{getStatusLabel(request?.status)}</StyledTableCell>
                  <StyledTableCell>{request?.employeeName}</StyledTableCell>
                  <StyledTableCell>
                    {new Date(request?.created_at).toLocaleDateString('en-US')}
                  </StyledTableCell>
                  <StyledTableCell>{request?.dueBy}</StyledTableCell>
                </StyledTableRow>
              ))}
            </TableBody>
          ) : (
            <TableBody>
              <StyledTableRow>
                <StyledTableCell align='center' colSpan={10}>
                  <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                    No matching records found.
                  </Typography>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
        <Pagination
          count={totalPages}
          color='primary'
          page={page + 1}
          onChange={(_, newPage) => handleChangePage(null, newPage - 1)}
        />
      </Box>
    </>
  )
}

const ServiceRequest = (props: any) => {
  const {
    fetchSRData,
    SRData,
    loaderState,
    fetchDepartmentList,
    DepartmentList,
    isCreateServiceRequest,
  } = props
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(100)
  useEffect(() => {
    fetchDepartmentList()
  }, [])

  useEffect(() => {
    fetchSRData({
      page,
      pageSize,
    })
  }, [page])

  

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, page: any) => {
    setPage(page)
  }

  const fetchSRData1 = () => {
    fetchSRData({
      page,
      pageSize,
    })
  }

  const handleChangeRowsPerPage = (event: any) => {
    setPageSize(event.target.value)
  }

  return (
    <>
      {(loaderState || isCreateServiceRequest) && (

        <Loader state={loaderState || isCreateServiceRequest} />

      )}
      {!loaderState ? (
        <div style={MainContainer}>
          <StyledPaper>
            <ServiceRequestTableData
              serviceRequestData={SRData?.data}
              isCreateServiceRequest={isCreateServiceRequest}
              fetchSRData1={fetchSRData1}
              page={page}
              DepartmentList={DepartmentList}
              total={SRData.totalCount}
              handleChangePage={handleChangePage}
              handleChangeRowsPerPage={handleChangeRowsPerPage}
              SRData={SRData?.serviceRequestsCount}
            />
          </StyledPaper>
        </div>
      ) : null}
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    SRData: dashboardEntity.getDashboard(state).getSRData,
    loaderState: dashboardUI.getDashboard(state).isServiceRequestData,
    DepartmentList: dashboardEntity.getDashboard(state).getDepartmentList,
    isCreateServiceRequest: SRUI.getServiceRequestCreate(state).isCreateServiceRequest,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchSRData: (data: any) => dispatch(fetchSR.request(data)),
    fetchDepartmentList: (data: any) => dispatch(fetchDepartmentList.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ServiceRequest)
