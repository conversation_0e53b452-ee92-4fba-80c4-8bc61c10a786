import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import Paper from '@mui/material/Paper'
import { Box, CircularProgress, Dialog, Typography } from '@mui/material'
import { HeaderHeading, StyledTableCell, StyledTableRow, loaderProps } from '../Common/CommonStyles'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { useEffect } from 'react'
import { fetchAsset } from '../../actions'
import { dashboardEntity, dashboardUI } from '../../reducers'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import Loader from '../Common/Loader'
import moment from 'moment'

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '25px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '70px',
  border: '1px solid #DDDDDD',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}



const AssetTable = (props: any) => {
  const navigate = useNavigate()
  
  return (
    <>
      <StyledPaper>
        <Box sx={{ textAlign: 'left' }}>
          <Box
            onClick={() => navigate(-1)}
            sx={{ float: 'right', pr: '30px', mt: '0px', cursor: 'pointer' }}
          >
            <ArrowBack />
          </Box>
        </Box>

        <HeaderHeading>Hardware Asset Details</HeaderHeading>

        <TableContainer
          sx={{
            margin: '20px 0',
          }}
          component={Paper}
        >
          <Table
            sx={{
              minWidth: 700,
            }}
            aria-label='customized table'
          >
            <TableHead>
              <StyledTableRow>
                <StyledTableCell>ID</StyledTableCell>
                <StyledTableCell>Ram</StyledTableCell>
                <StyledTableCell>Hard Disk</StyledTableCell>
                <StyledTableCell>Make</StyledTableCell>
                <StyledTableCell>Assigned On</StyledTableCell>
              </StyledTableRow>
            </TableHead>
            {props.data.laptopDetails && props.data?.laptopDetails?.length > 0 ? (
              <TableBody>
                {props.data?.laptopDetails?.map((data: any) => (
                  <StyledTableRow key={`${data.laptop_no}${data.laptopName}`}>
                    <StyledTableCell component='th' scope='row'>
                      {data.laptop_no}
                    </StyledTableCell>
                    <StyledTableCell align='left'>{data.ram_size}</StyledTableCell>
                    <StyledTableCell align='left'>{data.hard_disk_size}</StyledTableCell>
                    <StyledTableCell align='left'>{data.laptopName}</StyledTableCell>
                    <StyledTableCell align="left">
                      {data.assigned_on
                        ? moment(data.assigned_on).format("MM/DD/YYYY")
                        : "NA"} 
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            ) : (
              <TableBody>
                <StyledTableRow>
                  <StyledTableCell align='center' colSpan={10}>
                    <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                      No matching records found.
                    </Typography>
                  </StyledTableCell>
                </StyledTableRow>
              </TableBody>
            )}
          </Table>
        </TableContainer>

        {props.data.softwareDetails && props.data?.softwareDetails?.length > 0 && (
        <>
        <HeaderHeading sx={{marginRight:'40px'}}>Software Asset Details</HeaderHeading>
        <TableContainer
          sx={{
            margin: '20px 0',
          }}
          component={Paper}
        >
          <Table
            sx={{
              minWidth: 700,
            }}
            aria-label='customized table'
          >
            <TableHead>
              <StyledTableRow>
                <StyledTableCell>ID</StyledTableCell>
                <StyledTableCell>Name</StyledTableCell>
                <StyledTableCell>Version</StyledTableCell>
                <StyledTableCell>License</StyledTableCell>
                <StyledTableCell>Expiry Date</StyledTableCell>
                <StyledTableCell>Assigned On</StyledTableCell>
              </StyledTableRow>
            </TableHead>
            {props.data.softwareDetails && props.data?.softwareDetails?.length > 0 ? (
              <TableBody>
                {props.data?.softwareDetails?.map((data: any) => (
                  <StyledTableRow key={`${data.laptop_no}${data.laptopName}`}>
                    <StyledTableCell component='th' scope='row'>
                      {data.laptop_no}
                    </StyledTableCell>
                    <StyledTableCell align='left'>{data.software_name}</StyledTableCell>
                    <StyledTableCell align='left'>{data.version}</StyledTableCell>
                    <StyledTableCell align='left'>{data.license_key}</StyledTableCell>
                    <StyledTableCell align="left">
                      {data.license_expiry_date
                        ? moment(data.license_expiry_date).format("MM/DD/YYYY")
                        : "NA"} 
                    </StyledTableCell>
                    <StyledTableCell align="left">
                      {data.assigned_on
                        ? moment(data.assigned_on).format("MM/DD/YYYY")
                        : "NA"} 
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            ) : (
              <TableBody>
                <StyledTableRow>
                  <StyledTableCell align='center' colSpan={10}>
                    <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                      No matching records found.
                    </Typography>
                  </StyledTableCell>
                </StyledTableRow>
              </TableBody>
            )}
          </Table>
        </TableContainer>
        </>
        )}
      </StyledPaper>
    </>
  )
}

const AssignedAsset = (props: any) => {
  const { fetchAssetData, AssetData, loaderState } = props

  console.log("AssetData",AssetData);
  

  useEffect(() => {
    fetchAssetData()
  }, [])
  return (
    <>
      {loaderState && (
        <Loader state={loaderState} />
      )}
      {!loaderState && AssetData && (
        <div style={MainContainer}>
          <AssetTable data={AssetData} />
        </div>
      )}
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    AssetData: dashboardEntity.getDashboard(state).getAssetData,
    loaderState: dashboardUI.getDashboard(state).isAssetsData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAssetData: () => dispatch(fetchAsset.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssignedAsset)
