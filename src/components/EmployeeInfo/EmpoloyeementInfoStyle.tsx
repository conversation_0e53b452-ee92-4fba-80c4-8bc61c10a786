import { But<PERSON>, <PERSON>uItem, TextField } from '@mui/material'
import { makeStyles } from '@mui/styles'
import { DatePicker } from '@mui/x-date-pickers'
import styled from 'styled-components'
import style from '../../utils/styles.json'

export const InputField = styled(TextField)(({ theme }) => ({
  marginBottom: '1px !important',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px !important', // Adjust the font size here
    fontFamily: `${style.FONT_MEDIUM} !important`,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'white',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
    width: '91.5%',
    height: '40px',
    marginLeft: '15px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginLeft: '15px',

    fontSize: '13px', // Adjust the font size here
    fontFamily: style.FONT_MEDIUM,
    lineHeight: '1.8em',
  },
}))

export const SelectField = styled(TextField)(({ theme }) => ({
  marginBottom: '1px !important',

  '& .MuiOutlinedInput-input': {
    marginLeft: '2px',
    padding: '13.5px 14px', // Match the padding with InputField
    fontSize: '13px', // Adjust the font size here
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-root': {
    marginLeft: '15px',
    marginTop: '2px',
    fontSize: '13px', // Adjust the font size here
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    marginLeft: '15px',
    borderRadius: '20px',
    height: '40px', // Same height as InputField
    width: '92%', // Ensure full width
  },
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: style.FONT_MEDIUM,
}))

export const useStyles = makeStyles((theme: { spacing: (arg0: number) => any }) => ({
  root: {
    width: '100%',
  },
  backButton: {
    marginRight: theme.spacing(1),
  },
  instructions: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
}))

export const CommonButton = styled(Button)(({ theme }) => ({
  fontSize: '16px !important',
  fontFamily: `${style.FONT_BOLD}!important`,
  width: '20%',
  borderRadius: '20px !important',
}))

export const DateField = styled(DatePicker)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root, & .MuiInputLabel-root': {
    lineHeight: '1em',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))
