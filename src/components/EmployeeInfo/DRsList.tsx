import { useState, useEffect } from 'react'
import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import PendingIcon from '@mui/icons-material/Pending'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import Paper from '@mui/material/Paper'
import {
  FormControl,
  InputLabel,
  MenuItem,
  Tooltip,
  Typography,
  Box,
  Checkbox,
  Switch,
  TableCell,
  tableCellClasses,
  Select,
} from '@mui/material'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import { makeStyles } from '@mui/styles'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogTitle from '@mui/material/DialogTitle'
import {
  HeaderHeading,
  SearchBoxCustom,
  SearchIconStyle,
  StyledMenuItem,
  StyledTableCell,
  StyledTableRow,
  loaderProps,
} from '../Common/CommonStyles'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { acceptLeaveData, getLeaveData, getLeaveTimesheets, rejectLeaveData } from '../../actions'
import { dashboardEntity, dashboardUI } from '../../reducers'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import { SelectField } from '../Common/ComponentCommonStyles'
import moment from 'moment'
import { toast } from 'react-toastify'
import style from '../../utils/styles.json'
import Loader from '../Common/Loader'

// Custom styled table cells with specific widths
const StyledTableCellCheckbox = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '60px',
    maxWidth: '60px',
    minWidth: '60px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '60px',
    maxWidth: '60px',
    minWidth: '60px',
    padding: '5px 8px',
  },
}))

const StyledTableCellEmployeeId = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '100px',
    maxWidth: '100px',
    minWidth: '100px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '100px',
    maxWidth: '100px',
    minWidth: '100px',
    padding: '5px 8px',
  },
}))

const StyledTableCellEmployeeName = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '140px',
    maxWidth: '140px',
    minWidth: '140px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '140px',
    maxWidth: '140px',
    minWidth: '140px',
    padding: '5px 8px',
  },
}))

const StyledTableCellDate = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '120px',
    maxWidth: '120px',
    minWidth: '120px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '120px',
    maxWidth: '120px',
    minWidth: '120px',
    padding: '5px 8px',
  },
}))

const StyledTableCellDescription = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '300px',
    maxWidth: '300px',
    minWidth: '300px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '300px',
    maxWidth: '300px',
    minWidth: '300px',
    padding: '5px 8px',
  },
}))

const StyledTableCellInfo = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '200px',
    maxWidth: '200px',
    minWidth: '200px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '200px',
    maxWidth: '200px',
    minWidth: '200px',
    padding: '5px 8px',
  },
}))

const StyledTableCellStatus = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '80px',
    maxWidth: '80px',
    minWidth: '80px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '80px',
    maxWidth: '80px',
    minWidth: '80px',
    padding: '5px 8px',
  },
}))

const StyledTableCellAction = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    width: '100px',
    maxWidth: '100px',
    minWidth: '100px',
    padding: '6px 8px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '100px',
    maxWidth: '100px',
    minWidth: '100px',
    padding: '5px 8px',
  },
}))

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '93%',
  padding: '23px 23px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '20px',
  border: '1px solid #DDDDDD',
}))

const StyledFormControl = styled(FormControl)(({ theme }) => ({}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: style.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '42px',
  fontFamily: style.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}

const DrsListTableData = ({
  acceptLeave,
  isLeaveAcceptOrReject,
  isGettingLeaveData,
  LeaveData,
  timesheetData,
  fetchLeaveData,
  isFinancial,
  isLeaveUpdated,
  acceptLeaveReset,
  acceptLeaveData,
}: {
  acceptLeave: any
  rejectLeave: any
  acceptLeaveData: any
  rejectLeaveData: any
  isLeaveAcceptOrReject: any
  isGettingLeaveData: any
  LeaveData: any
  timesheetData: any
  fetchLeaveData: any
  isFinancial: boolean
  isLeaveUpdated: boolean
  acceptLeaveReset: any
}) => {
  const useStyles = makeStyles({
    checkIcon: {
      color: 'green',
      marginLeft: 12,
    },
    crossIcon: {
      color: 'red',
      marginLeft: 12,
    },
    pendingIcon: {
      marginLeft: 12,
      color: 'brown',
    },

    heading: {
      marginLeft: 80,
    },
    headingFinancial: {
      marginTop: 50,
    },
    textField: {
      borderRadius: '4px',
    },
    dialogTitle: {
      fontWeight: 'normal',
    },
    dialogTitleName: {
      fontWeight: 'bold',
    },
    boxContainer: {
      textAlign: 'left',
    },
    boxSubContainer: {
      float: 'right',
      marginRight: '8px',
      marginTop: '0px',
    },
    noCursor: {
      cursor: 'auto !important',
    },
    cursor: {
      cursor: 'pointer !important',
    },
  })
  const classes = useStyles()
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)
  const [actionType, setActionType] = useState('')
  const [employeeName, setEmployeeName] = useState('')
  const [leaveId, setLeaveId] = useState('')
  const [selectedLeaveDateOption, setLeaveDateOption] = useState('')
  const [status, setStatus] = useState(0)
  const [timeSheetId, setTimesheetId] = useState()
  const [multiSelectIds, setMultiSelectIds] = useState<number[]>([])
  const [financialStatus, setFinancialStatus] = useState<boolean | null>(null)
  const [wfh, setWfh] = useState<boolean>(false)
  const [filterType, setFilterType] = useState<string>('all')

  useEffect(() => {
    if (isGettingLeaveData) {
      let date = new Date()
      let currentMonth = date.getMonth()
      let data = timesheetData.find(
        (sheet: any) => new Date(sheet.start_date).getMonth() === currentMonth,
      )
      fetchLeaveData({
        status,
        timeSheetId: data?.id,
        leave_type: getLeaveTypeFromFilter(filterType),
        filter_type: filterType,
      })
      setTimesheetId(data?.id)
      setLeaveDateOption(data?.id)
    }
    if (isLeaveAcceptOrReject) {
      fetchLeaveData({ 
        status, 
        timeSheetId, 
        leave_type: getLeaveTypeFromFilter(filterType),
        filter_type: filterType,
      })
    }
  }, [isGettingLeaveData, isLeaveAcceptOrReject])

  useEffect(() => {
    if (timeSheetId)
      fetchLeaveData({
        status,
        timeSheetId,
        leave_type: getLeaveTypeFromFilter(filterType),
        filter_type: filterType,
      })
  }, [filterType])

  const getLeaveTypeFromFilter = (filter: string) => {
    switch (filter) {
      case 'wfh':
        return 1
      case 'leaves':
        return 0
      case 'coming_late':
        return 2
      default:
        return 0 // Default to leaves for 'all' option
    }
  }

  const getDisplayLabel = (filter: string) => {
    switch (filter) {
      case 'wfh':
        return 'WFH'
      case 'leaves':
        return 'Leave'
      case 'coming_late':
        return 'Coming Late'
      default:
        return 'DRs'
    }
  }

  const handleLeaveStatusChange = (event: any) => {
    const status = event.target.value
    setStatus(status)
    fetchLeaveData({
      status,
      timeSheetId,
      leave_type: getLeaveTypeFromFilter(filterType),
      filter_type: filterType,
    })
    setMultiSelectIds([])
  }

  const handleLeaveDateChange = (event: any, timesheets: any[]) => {
    const selectedId = event.target.value
    const selectedTimesheet = timesheets?.find((timesheet) => timesheet?.id === selectedId)
    if (selectedTimesheet) {
      setLeaveDateOption(selectedTimesheet?.id)
      setTimesheetId(selectedTimesheet?.id)
      fetchLeaveData({
        status,
        timeSheetId: selectedTimesheet?.id,
        leave_type: getLeaveTypeFromFilter(filterType),
        filter_type: filterType,
      })
    }
    setMultiSelectIds([])
  }

  const handleFilterTypeChange = (event: any) => {
    const newFilterType = event.target.value
    setFilterType(newFilterType)
    setWfh(newFilterType === 'wfh') // Keep backward compatibility
    setMultiSelectIds([])
  }

  const handleOpen = (type: any, employeeName: any, leaveId: any, financialStatus: boolean) => {
    setActionType(type)
    setOpen(true)
    setEmployeeName(employeeName)
    setLeaveId(leaveId)
    setFinancialStatus(financialStatus)
  }

  const handleClose = () => {
    setOpen(false)
    setFinancialStatus(null)
  }

  const handleConfirm = () => {
    if (actionType === 'accept') {
      acceptLeave({
        leaveId: leaveId?.toString(),
        leaveStatus: 1,
        is_financial_year: financialStatus,
        wfh: filterType === 'wfh' ? 1 : 0,
        filter_type: filterType,
      })
      setLeaveId('')
    } else if (actionType === 'reject') {
      acceptLeave({
        leaveId: leaveId?.toString(),
        leaveStatus: 2,
        is_financial_year: financialStatus,
        wfh: filterType === 'wfh' ? 1 : 0,
        filter_type: filterType,
      })
      setLeaveId('')
    } else if (actionType === 'approveAll') {
      const ids = multiSelectIds?.join(',') ?? ''
      acceptLeave({
        leaveId: ids,
        leaveStatus: 1,
        is_financial_year: financialStatus,
        wfh: filterType === 'wfh' ? 1 : 0,
        filter_type: filterType,
      })
    } else if (actionType === 'rejectAll') {
      const ids = multiSelectIds?.join(',') ?? ''
      acceptLeave({
        leaveId: ids,
        leaveStatus: 2,
        is_financial_year: financialStatus,
        wfh: filterType === 'wfh' ? 1 : 0,
        filter_type: filterType,
      })
    }
    setOpen(false)
  }

  const [searchQuery, setSearchQuery] = useState('')
  const [filteredRows, setFilteredRows] = useState<any[]>([])

  useEffect(() => {
    const filteredLeaveData = LeaveData?.leaveData?.filter((row: any) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    )
    setFilteredRows(filteredLeaveData)
  }, [searchQuery, LeaveData])

  useEffect(() => {
    if (isLeaveUpdated) {
      toast.success(acceptLeaveData?.message ?? 'Leaves updated successfully!')
      acceptLeaveReset()
      setMultiSelectIds([])
      
      // Refresh the leave data after successful approval/rejection
      if (timeSheetId) {
        console.log('Refreshing leave data after approval with params:', {
          status,
          timeSheetId,
          leave_type: getLeaveTypeFromFilter(filterType),
          filter_type: filterType,
        });
        
        // Force a small delay to ensure the backend has processed the update
        setTimeout(() => {
          fetchLeaveData({
            status: status,
            timeSheetId: timeSheetId,
            leave_type: getLeaveTypeFromFilter(filterType),
            filter_type: filterType,
          });
          console.log('Leave data refresh triggered successfully');
        }, 1500); // Increased delay to 1.5 seconds for better reliability
      }
    }
  }, [isLeaveUpdated, status, timeSheetId, filterType, fetchLeaveData, acceptLeaveReset])



  const displayedRows = searchQuery ? filteredRows : LeaveData.leaveData
  const rowsToDisplay = displayedRows?.slice()

  const aprilMonthLeave = searchQuery ? filteredRows : LeaveData.AprilData
  const aprilMonthLeaveData = aprilMonthLeave?.slice()

  const handleMultiSelect = (id: number) => {
    if (multiSelectIds?.includes(id))
      setMultiSelectIds(multiSelectIds.filter((item) => item !== id))
    else setMultiSelectIds([...multiSelectIds, id])
  }

  const handleAllSelect = () => {
    if (multiSelectIds?.length === rowsToDisplay?.length) setMultiSelectIds([])
    else setMultiSelectIds(rowsToDisplay?.map((item: { id: number }) => item?.id))
  }

  const handleAllSelected = (type: string) => {
    setActionType(type)
    setOpen(true)
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <DialogTitle className={classes.dialogTitle} id='alert-dialog-title'>
          {actionType === 'accept' && (
            <>
              Are you sure you want to accept{' '}
              <span className={classes.dialogTitleName}>{employeeName}'s</span>{' '}
              {getDisplayLabel(filterType).toLowerCase()}?
            </>
          )}
          {actionType === 'reject' && (
            <>
              Are you sure you want to reject{' '}
              <span className={classes.dialogTitleName}>{employeeName}'s</span>{' '}
              {getDisplayLabel(filterType).toLowerCase()}?
            </>
          )}
          {actionType === 'approveAll' && (
            <>
              Are you sure you want to approve all selected {getDisplayLabel(filterType).toLowerCase()}?
            </>
          )}
          {actionType === 'rejectAll' && (
            <>
              Are you sure you want to reject all selected {getDisplayLabel(filterType).toLowerCase()}?
            </>
          )}
        </DialogTitle>
        <DialogActions>
          <CancelButton onClick={handleClose}>CANCEL</CancelButton>
          <ActionButton onClick={handleConfirm}>OK</ActionButton>
        </DialogActions>
      </Dialog>

      <Box className={classes.boxContainer}>
        <Box className={classes.boxSubContainer} onClick={() => navigate(-1)}>
          <ArrowBack />
        </Box>
      </Box>
      <HeaderHeading className={classes.heading}>{`DRs On ${getDisplayLabel(filterType)} In Selected Month`}</HeaderHeading>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SearchBoxCustom
            id='outlined-basic'
            placeholder='Search'
            variant='outlined'
            size='small'
            fullWidth
            parentWidth='250px !important'
            value={searchQuery}
            onChange={(e: any) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIconStyle />,
            }}
          />
          <Box display={'flex'} alignItems={'center'} sx={{ marginLeft: 2 }}>
            <StyledFormControl size='small'>
              <InputLabel id='filter-type-select-label' sx={{ fontSize: '14px' }}>
                Filter Type
              </InputLabel>
              <Select
                labelId='filter-type-select-label'
                value={filterType}
                label='Filter Type'
                onChange={handleFilterTypeChange}
                sx={{
                  borderRadius: '22px',
                  '& .MuiSelect-select': {
                    padding: '9px 14px',
                  },
                  width: '140px',
                  height: '35px',
                }}
              >
                <MenuItem value='all'>All</MenuItem>
                <MenuItem value='wfh'>WFH</MenuItem>
                <MenuItem value='leaves'>Leaves</MenuItem>
                <MenuItem value='coming_late'>Coming Late</MenuItem>
              </Select>
            </StyledFormControl>
          </Box>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            gap: '10px',
            width: '380px',
          }}
        >
          {filterType === 'leaves' && (
            <Box display={'flex'}>
              {(status === 0 || status === 2) && (
                <Tooltip title='Approve All'>
                  <Button
                    sx={{
                      bgcolor: '#fff',
                      '&:hover': {
                        bgcolor: '#fff',
                      },
                      padding: '0',
                      minWidth: '20px',
                      opacity: !multiSelectIds?.length ? '0.2' : '1',
                    }}
                    disabled={!multiSelectIds?.length}
                  >
                    <CheckIcon
                      className={classes.checkIcon}
                      onClick={() => handleAllSelected('approveAll')}
                    />
                  </Button>
                </Tooltip>
              )}
              {(status === 0 || status === 1) && (
                <Tooltip title='Reject All'>
                  <Button
                    sx={{
                      bgcolor: '#fff',
                      '&:hover': {
                        bgcolor: '#fff',
                      },
                      padding: '0',
                      minWidth: '20px',
                      opacity: !multiSelectIds?.length ? '0.2' : '1',
                    }}
                    disabled={!multiSelectIds?.length}
                  >
                    <CloseIcon
                      className={classes.crossIcon}
                      onClick={() => handleAllSelected('rejectAll')}
                    />
                  </Button>
                </Tooltip>
              )}
            </Box>
          )}
          <Box>
            <StyledFormControl size='small'>
              <InputLabel id='demo-simple-select-readonly-label' sx={{ fontSize: '14px' }}>
                Select Status
              </InputLabel>
              <SelectField
                sx={{
                  borderRadius: '22px',
                  '& .MuiSelect-select': {
                    padding: '9px 14px',
                  },
                  width: '130px',
                  height: '35px',
                }}
                size='small'
                variant='outlined'
                value={status}
                label='Select Status:'
                onChange={(event) => handleLeaveStatusChange(event)}
              >
                <MenuItem value={0} sx={{ fontSize: '14px' }}>
                  Pending
                </MenuItem>
                <MenuItem value={1} sx={{ fontSize: '14px' }}>
                  Approved
                </MenuItem>
                <MenuItem value={2} sx={{ fontSize: '14px' }}>
                  Rejected
                </MenuItem>
              </SelectField>
            </StyledFormControl>
          </Box>

          <Box>
            <StyledFormControl size='small'>
              <InputLabel id='demo-simple-select-readonly-label' sx={{ fontSize: '14px' }}>
                Select Month
              </InputLabel>
              <SelectField
                sx={{
                  borderRadius: '22px',
                  '& .MuiSelect-select': {
                    padding: '9px 14px',
                  },
                  width: '150px',
                  height: '35px',
                }}
                variant='outlined'
                value={selectedLeaveDateOption}
                label='Select Month:'
                onChange={(event) => handleLeaveDateChange(event, timesheetData)}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: '250px',
                    },
                  },
                }}
              >
                {timesheetData?.map((option: any) => (
                  <StyledMenuItem
                    key={`${option?.start_date}+${option?.id}`}
                    value={option?.id}
                    sx={{ fontSize: '14px' }}
                  >
                    {moment(new Date(option?.start_date).toISOString().split('T')[0]).format(
                      'MMMM YYYY',
                    )}
                  </StyledMenuItem>
                ))}
              </SelectField>
            </StyledFormControl>
          </Box>
        </Box>
      </Box>
      <TableContainer component={Paper}>
        <Table
          sx={{
            minWidth: 700,
          }}
          aria-label='customized table'
        >
          <TableHead>
            <StyledTableRow className={classes.noCursor}>
              {filterType === 'leaves' && (
                <StyledTableCellCheckbox>
                  <Checkbox
                    checked={
                      rowsToDisplay?.length > 0 && multiSelectIds?.length === rowsToDisplay?.length
                    }
                    disabled={!rowsToDisplay?.length}
                    onClick={handleAllSelect}
                    size='small'
                    sx={{ color: '#fff', '&.Mui-checked': { color: '#fff' } }}
                  />
                </StyledTableCellCheckbox>
              )}
              <StyledTableCellEmployeeId>Employee ID</StyledTableCellEmployeeId>
              <StyledTableCellEmployeeName>Employee Name</StyledTableCellEmployeeName>
              <StyledTableCellDate>{getDisplayLabel(filterType)} Start Date</StyledTableCellDate>
              <StyledTableCellDate>{getDisplayLabel(filterType)} End Date</StyledTableCellDate>
              <StyledTableCellDescription>Description</StyledTableCellDescription>
              {status === 0 && filterType !== 'coming_late' && (
                <StyledTableCellInfo>{getDisplayLabel(filterType)} Info</StyledTableCellInfo>
              )}
              <StyledTableCellStatus>Status</StyledTableCellStatus>
              <StyledTableCellAction>Action</StyledTableCellAction>
            </StyledTableRow>
          </TableHead>
          {rowsToDisplay && rowsToDisplay?.length ? (
            <TableBody>
              {rowsToDisplay.map((item: any) => (
                <StyledTableRow
                  key={`${item.name}+${item.id_employee}`}
                  className={classes.noCursor}
                >
                  {filterType === 'leaves' && (
                    <StyledTableCellCheckbox>
                      <Checkbox
                        size='small'
                        checked={multiSelectIds?.includes(item?.id) ?? false}
                        onChange={() => handleMultiSelect(item?.id)}
                      />
                    </StyledTableCellCheckbox>
                  )}
                  <StyledTableCellEmployeeId>{item.id_employee}</StyledTableCellEmployeeId>
                  <StyledTableCellEmployeeName>{item.name}</StyledTableCellEmployeeName>
                  <StyledTableCellDate>
                    {new Date(item.leave_start_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    })}
                  </StyledTableCellDate>
                  <StyledTableCellDate>
                    {new Date(item.leave_end_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    })}
                  </StyledTableCellDate>

                  <StyledTableCellDescription>
                    <div>{item.desc}</div>
                  </StyledTableCellDescription>
                  {status === 0 && filterType !== 'coming_late' && (
                    <StyledTableCellInfo>
                      {filterType === 'wfh' ? (
                        <div style={{ fontSize: '12px', lineHeight: '1.2' }}>
                          <div><strong>Availed in Quarter:</strong> {item.wfhInQuarter || 0}</div>
                          <div><strong>Availed in Year:</strong> {item.wfhInFinancialYear || 0}</div>
                        </div>
                      ) : (
                        <div style={{ fontSize: '12px', lineHeight: '1.2' }}>
                          <div><strong>Availed in Quarter:</strong> {item.leavesTakenInQuarter || 0}</div>
                          <div><strong>Availed in Year:</strong> {item.leavesTakenInFinancialYear || 0}</div>
                          <div><strong>Current Balance:</strong> {item.leaveBalance || 0}</div>
                        </div>
                      )}
                    </StyledTableCellInfo>
                  )}
                  <StyledTableCellStatus>
                    {' '}
                    {item.status === 1 ? (
                      <Tooltip title='Approved'>
                        <CheckIcon className={classes.checkIcon} />
                      </Tooltip>
                    ) : item.status === 2 ? (
                      <Tooltip title='Rejected'>
                        <CloseIcon className={classes.crossIcon} />
                      </Tooltip>
                    ) : (
                      <Tooltip title='Pending'>
                        <PendingIcon className={classes.pendingIcon} />
                      </Tooltip>
                    )}
                  </StyledTableCellStatus>
                  <StyledTableCellAction className={classes.cursor}>
                    {filterType === 'coming_late' ? (
                      <Typography sx={{ fontSize: '12px', color: '#666' }}>
                        Auto-approved
                      </Typography>
                    ) : (
                      <>
                        {item.status === 2 ? (
                          <>
                            <Tooltip title='Approve'>
                              <CheckIcon
                                onClick={() => handleOpen('accept', item.name, item?.id, false)}
                                className={classes.checkIcon}
                              />
                            </Tooltip>
                          </>
                        ) : item.status === 1 ? (
                          <>
                            <Tooltip title='Reject'>
                              <CloseIcon
                                onClick={() => handleOpen('reject', item.name, item?.id, false)}
                                className={classes.crossIcon}
                              />
                            </Tooltip>
                          </>
                        ) : (
                          <>
                            <Tooltip title='Approve'>
                              <CheckIcon
                                onClick={() => handleOpen('accept', item.name, item?.id, false)}
                                className={classes.checkIcon}
                              />
                            </Tooltip>
                            <Tooltip title='Reject'>
                              <CloseIcon
                                onClick={() => handleOpen('reject', item.name, item?.id, false)}
                                className={classes.crossIcon}
                              />
                            </Tooltip>
                          </>
                        )}
                      </>
                    )}
                  </StyledTableCellAction>
                </StyledTableRow>
              ))}
            </TableBody>
          ) : (
            <TableBody>
              <StyledTableRow>
                <StyledTableCell align='center' colSpan={filterType === 'leaves' ? (status === 0 ? 9 : 8) : (status === 0 && filterType !== 'coming_late' ? 8 : 7)}>
                  <Typography align='center' variant='subtitle1' sx={{ color: '#483f3f' }}>
                    No matching records found.
                  </Typography>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>
      {isFinancial && filterType !== 'coming_late' && (
        <>
          <HeaderHeading className={classes.heading} sx={{ marginTop: 4 }}>
            Future Financial Year
          </HeaderHeading>
          <TableContainer component={Paper} sx={{ marginTop: 5 }}>
            <Table
              sx={{
                minWidth: 700,
              }}
              aria-label='customized table'
            >
              <TableHead>
                <StyledTableRow className={classes.noCursor}>
                  <StyledTableCellEmployeeId>Employee ID</StyledTableCellEmployeeId>
                  <StyledTableCellEmployeeName>Employee NAME</StyledTableCellEmployeeName>
                  <StyledTableCellDate>{getDisplayLabel(filterType)} Start Date</StyledTableCellDate>
                  <StyledTableCellDate>{getDisplayLabel(filterType)} End Date</StyledTableCellDate>
                  <StyledTableCellDescription>Description</StyledTableCellDescription>
                  {status === 0 && (
                    <StyledTableCellInfo>{getDisplayLabel(filterType)} Info</StyledTableCellInfo>
                  )}
                  <StyledTableCellStatus>Status</StyledTableCellStatus>
                  <StyledTableCellAction>Action</StyledTableCellAction>
                </StyledTableRow>
              </TableHead>
              {aprilMonthLeaveData && aprilMonthLeaveData?.length ? (
                <TableBody>
                  {aprilMonthLeaveData.map((item: any) => (
                    <StyledTableRow
                      key={`${item.name}${item.id_employee}`}
                      className={classes.noCursor}
                    >
                      <StyledTableCellEmployeeId>{item.id_employee}</StyledTableCellEmployeeId>
                      <StyledTableCellEmployeeName>{item.name}</StyledTableCellEmployeeName>
                      <StyledTableCellDate>
                        {new Date(item.leave_start_date).toLocaleDateString('en-GB')}
                      </StyledTableCellDate>{' '}
                      <StyledTableCellDate>
                        {new Date(item.leave_end_date).toLocaleDateString('en-GB')}
                      </StyledTableCellDate>
                      <StyledTableCellDescription>
                        <div>{item.desc}</div>
                      </StyledTableCellDescription>
                      <StyledTableCellInfo>
                        {status === 0 && (
                          filterType === 'wfh' ? (
                            <div style={{ fontSize: '12px', lineHeight: '1.2' }}>
                              <div><strong>Availed in Quarter:</strong> {item.wfhInQuarter || 0}</div>
                              <div><strong>Availed in Year:</strong> {item.wfhInFinancialYear || 0}</div>
                            </div>
                          ) : (
                            <div style={{ fontSize: '12px', lineHeight: '1.2' }}>
                              <div><strong>Availed in Quarter:</strong> {item.leavesTakenInQuarter || 0}</div>
                              <div><strong>Availed in Year:</strong> {item.leavesTakenInFinancialYear || 0}</div>
                              <div><strong>Current Balance:</strong> {item.leaveBalance || 0}</div>
                            </div>
                          )
                        )}
                      </StyledTableCellInfo>
                      <StyledTableCellStatus>
                        {' '}
                        {item.status === 1 ? (
                          <Tooltip title='Approved'>
                            <CheckIcon className={classes.checkIcon} />
                          </Tooltip>
                        ) : item.status === 2 ? (
                          <Tooltip title='Rejected'>
                            <CloseIcon className={classes.crossIcon} />
                          </Tooltip>
                        ) : (
                          <Tooltip title='Pending'>
                            <PendingIcon className={classes.pendingIcon} />
                          </Tooltip>
                        )}
                      </StyledTableCellStatus>
                      <StyledTableCellAction className={classes.cursor}>
                        {item.status === 2 ? (
                          <>
                            <Tooltip title='Approve'>
                              <CheckIcon
                                onClick={() => handleOpen('accept', item.name, item?.id, true)}
                                className={classes.checkIcon}
                              />
                            </Tooltip>
                          </>
                        ) : item.status === 1 ? (
                          <>
                            <Tooltip title='Reject'>
                              <CloseIcon
                                onClick={() => handleOpen('reject', item.name, item?.id, true)}
                                className={classes.crossIcon}
                              />
                            </Tooltip>
                          </>
                        ) : (
                          <>
                            <Tooltip title='Approve'>
                              <CheckIcon
                                onClick={() => handleOpen('accept', item.name, item?.id, true)}
                                className={classes.checkIcon}
                              />
                            </Tooltip>
                            <Tooltip title='Reject'>
                              <CloseIcon
                                onClick={() => handleOpen('reject', item.name, item?.id, true)}
                                className={classes.crossIcon}
                              />
                            </Tooltip>
                          </>
                        )}
                      </StyledTableCellAction>
                    </StyledTableRow>
                  ))}
                </TableBody>
              ) : (
                <TableBody>
                  <StyledTableRow>
                    <StyledTableCell align='center' colSpan={status === 0 ? 8 : 7}>
                      <Typography align='center' variant='subtitle1' sx={{ color: '#483f3f' }}>
                        No matching records found.
                      </Typography>
                    </StyledTableCell>
                  </StyledTableRow>
                </TableBody>
              )}
            </Table>
          </TableContainer>
        </>
      )}
    </>
  )
}
const DrsList = (props: any) => {
  const {
    acceptLeave,
    resetLeaveData,
    rejectLeave,
    rejectLeaveData,
    acceptLeaveData,
    LeaveData,
    fetchLeaveData,
    isLeaveAcceptOrReject,
    isGettingLeaveData,
    timesheetData,
    fetchLeaveTimesheets,
    loaderState,
    isLeaveUpdating,
    isLeaveUpdated,
    acceptLeaveReset,
  } = props

  useEffect(() => {
    fetchLeaveTimesheets()
    return () => {
      resetLeaveData()
    }
  }, [])
  return (
    <>
      {(loaderState || isLeaveUpdating) && <Loader state={loaderState || isLeaveUpdating} />}
      <div style={MainContainer}>
        <StyledPaper style={{ minHeight: '70vh' }}>
          <DrsListTableData
            isLeaveAcceptOrReject={isLeaveAcceptOrReject}
            acceptLeave={acceptLeave}
            rejectLeave={rejectLeave}
            rejectLeaveData={rejectLeaveData}
            acceptLeaveData={acceptLeaveData}
            isGettingLeaveData={isGettingLeaveData}
            LeaveData={LeaveData}
            fetchLeaveData={fetchLeaveData}
            timesheetData={timesheetData}
            isFinancial={LeaveData.isFinancial}
            isLeaveUpdated={isLeaveUpdated}
            acceptLeaveReset={acceptLeaveReset}
          />
        </StyledPaper>
      </div>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    timesheetData: dashboardEntity.getDashboard(state).getLeaveTimesheets,
    loaderState: dashboardUI.getDashboard(state).isLeavesData,
    LeaveData: dashboardEntity.getDashboard(state).getLeaveData,
    isGettingLeaveData: dashboardUI.getDashboard(state).isGettingLeaveData,
    acceptLeaveData: dashboardEntity.getDashboard(state).acceptLeaveData,
    rejectLeaveData: dashboardEntity.getDashboard(state).rejectLeaveData,
    isLeaveAcceptOrReject: dashboardUI.getDashboard(state).isLeaveAcceptOrReject,
    isLeaveUpdating: dashboardUI.getDashboard(state).isAcceptLeaveData,
    isLeaveUpdated: dashboardUI.getDashboard(state).isAcceptLeaveDataSuccess,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchLeaveTimesheets: () => dispatch(getLeaveTimesheets.request()),
    fetchLeaveData: (data: any) => dispatch(getLeaveData.request(data)),
    resetLeaveData: () => dispatch(getLeaveData.reset()),
    acceptLeave: (data: any) => dispatch(acceptLeaveData.request(data)),
    acceptLeaveReset: (data: any) => dispatch(acceptLeaveData.reset()),
    rejectLeave: (data: any) => dispatch(rejectLeaveData.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(DrsList)