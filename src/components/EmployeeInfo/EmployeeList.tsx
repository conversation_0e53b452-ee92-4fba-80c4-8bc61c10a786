import React, { useState, useEffect } from 'react'
import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import Paper from '@mui/material/Paper'
import Pagination from '@mui/material/Pagination'
import { Box, Grid, TextField } from '@mui/material'
import { makeStyles } from '@mui/styles'
import {
  HeaderHeading,
  StyledTableCell,
  StyledTableRow,
  SearchIconStyle,
} from '../Common/CommonStyles'
import { connect } from 'react-redux'
import { RootState } from '../../configureStore'
import { Dispatch } from 'redux'
import { fetchUserData } from '../../actions'
import { dashboardEntity, dashboardUI } from '../../reducers'
import { useNavigate } from 'react-router-dom'
import { CurrentLoginUserProfile } from '../../utils/StaticData'
import style from '../../utils/styles.json'
import Loader from '../Common/Loader'

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '95%',
  padding: '15px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '20px',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}

const SearchBox: React.CSSProperties = {
  width: '250px',
  margin: '0',
}

const SearchBoxCustom = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    width: '250px',
    borderRadius: '30px',
    fontSize: '14px',
    height: '35px',
    '& fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
}))

const EmployeeListTableData = (props: any) => {
  const { fetchUserData, userData, isGetUserData } = props
  const [page, setPage] = useState(1)
  const rowsPerPage = 100
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredRows, setFilteredRows] = useState<any[]>([])

  const navigate = useNavigate()

  const handleChangePage = (event: any, newPage: number) => {
    setPage(newPage)
    window.scrollTo(0, 0)
  }

  useEffect(() => {
    fetchUserData()
  }, [])

  useEffect(() => {
    const filtered = userData.data?.filter((row: any) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    )
    setFilteredRows(filtered)
  }, [searchQuery])

  const useStyles = makeStyles({
    image: {
      width: '50px',
      borderRadius: '50%',
      height: '50px',
      marginLeft: '10px',
      marginTop: '10px',
    },
    customTableCell: {
      width: '1%',
    },
  })

  const classes = useStyles()

  const startIndex = (page - 1) * rowsPerPage
  const endIndex = startIndex + rowsPerPage

  const displayedRows = searchQuery ? filteredRows : userData.data
  const rowsToDisplay = displayedRows?.slice(startIndex, endIndex)

  const handleBasicInfo = (empId: number) => {
    navigate(`/home/<USER>
  }

  return (
    <>
      <Loader state={isGetUserData} />
      <Box display={'flex'} alignItems={'center'} justifyContent={'flex-start'} mb={'15px'}>
        <Box width='calc(50% - 100px)'>
          <SearchBoxCustom
            id='outlined-basic'
            placeholder='Search'
            variant='outlined'
            size='small'
            value={searchQuery}
            onChange={(e: any) => setSearchQuery(e.target.value)}
            style={SearchBox}
            InputProps={{
              startAdornment: <SearchIconStyle />,
            }}
          />
        </Box>
        <HeaderHeading>Employee List</HeaderHeading>
      </Box>

      <TableContainer component={Paper}>
        <Table
          sx={{
            minWidth: 700,
          }}
          aria-label='customized table'
        >
          <TableHead>
            <StyledTableRow>
              <StyledTableCell className={classes.customTableCell}></StyledTableCell>
              <StyledTableCell className={classes.customTableCell}>Employee ID</StyledTableCell>
              <StyledTableCell className={classes.customTableCell}>Name</StyledTableCell>
              <StyledTableCell className={classes.customTableCell}>Email</StyledTableCell>
              {/* <StyledTableCell>ROLE TYPE</StyledTableCell> */}
              <StyledTableCell className={classes.customTableCell}>Manager</StyledTableCell>
              <StyledTableCell className={classes.customTableCell}>Designation</StyledTableCell>
              <StyledTableCell className={classes.customTableCell}>Mobile No</StyledTableCell>
              {/* <StyledTableCell>STATUS</StyledTableCell> */}
            </StyledTableRow>
          </TableHead>

          <TableBody>
            {rowsToDisplay?.map((row: any) => {
              if (row.isActive === 1) {
                return (
                  <StyledTableRow
                    key={row.employeeId}
                    onClick={(e) => handleBasicInfo(row?.userId)}
                  >
                    <Grid sm={1.5}>
                      <img
                        className={classes.image}
                        src={row.image ? row.image : CurrentLoginUserProfile}
                        alt=''
                      />
                    </Grid>
                    <StyledTableCell>{row.employeeId}</StyledTableCell>
                    <StyledTableCell>{row.name}</StyledTableCell>
                    <StyledTableCell>{row.email}</StyledTableCell>
                    {/* <StyledTableCell>
                      {row.role.map((role: string) => (
                        <div key={role}>{role}</div>
                      ))}
                    </StyledTableCell> */}
                    <StyledTableCell>{row.manager}</StyledTableCell>
                    <StyledTableCell>{row.designation}</StyledTableCell>
                    <StyledTableCell>{row.mobileNumber}</StyledTableCell>
                    {/* <StyledTableCell>{row.isActive === 1 ? 'Active' : 'Inactive'}</StyledTableCell> */}
                  </StyledTableRow>
                )
              }
              return null
            })}
          </TableBody>
        </Table>
        <Pagination
          sx={{
            float: 'right',
            margin: '15px 0px 10px 0px',
          }}
          page={page}
          onChange={handleChangePage}
          count={
            searchQuery
              ? Math.ceil(
                  filteredRows?.filter((item: any) => item.isActive === 1)?.length / rowsPerPage,
                )
              : Math.ceil(
                  userData.data?.filter((item: any) => item.isActive === 1)?.length / rowsPerPage,
                )
          }
          color='primary'
        />
      </TableContainer>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    userData: dashboardEntity.getDashboard(state).getUserData,
    isGetUserData: dashboardUI.getDashboard(state).isGetUserData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchUserData: () => dispatch(fetchUserData.request()),
  }
}

const EmployeeListTableDataMapped = connect(
  mapStateToProps,
  mapDispatchToProps,
)(EmployeeListTableData)

const EmployeeList = () => {
  return (
    <div style={MainContainer}>
      <StyledPaper>
        <EmployeeListTableDataMapped />
      </StyledPaper>
    </div>
  )
}

export default EmployeeList