import { Box, CircularProgress, Dialog } from '@mui/material'
import { useEffect, useState } from 'react'
import {
  createEmpDesignations,
  editDesignations,
  fetchEmpDesignations,
  fetchLeavesInfo,
} from '../../../actions/index'
import { connect } from 'react-redux'

import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { fetchEmpHrControlEntities, fetchEmpHrControlUI } from '../../../reducers'
import { HeaderHeading } from '../../Common/CommonStyles'
import { ArrowBack } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import AddLeavesForm from './AddLeavesForm'

const style = {
  customBox: {
    overflow: 'auto',
    fontFamily: 'Montserrat-Bold',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    background: 'white',
    opacity: '1',
    textAlign: 'left',
    margin: '20px',
    padding: '10px 25px 25px 25px',
    borderRadius: '10px',
    width: '93%',
    minHeight: '69vh', // Allow height to shrink if content is less
    border: '1px solid #DDDDDD',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)', // Optional for a subtle shadow
    '@media (max-width: 768px)': {
      width: '100%',
      padding: '15px',
    },
    '@media print': {
      '@page': {
        size: '1280px',
      },
    },
  },
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const AddLeaves = ({ fetchLeavesInfo, getEmpLeavesInfo }: any) => {
  const navigate = useNavigate()

  useEffect(() => {
    fetchLeavesInfo()
  }, [])

  return (
    <Box sx={style.customBox}>
      <HeaderHeading>{`Add Leaves`}</HeaderHeading>
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{
            float: 'right',
            cursor: 'pointer',
            marginTop: '-35px',
          }}
        >
          <ArrowBack />
        </Box>
      </Box>

      <AddLeavesForm getEmpLeavesInfo={getEmpLeavesInfo} />
    </Box>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    getEmpLeavesInfo: fetchEmpHrControlEntities.fetchHrControlData(state).getEmpLeavesInfo,
    getEmpDesignations: fetchEmpHrControlEntities.fetchHrControlData(state).getEmpDesignations,
    isCreateDesignationDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateDesignationDone,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    fetchEmpDesignations: (data: any) => dispatch(fetchEmpDesignations.request(data)),
    createProjectCustomersReset: () => dispatch(createEmpDesignations.reset()),
    fetchLeavesInfo: (data: any) => dispatch(fetchLeavesInfo.request(data)),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(AddLeaves)
