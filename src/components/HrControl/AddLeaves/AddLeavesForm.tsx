import React, { useEffect } from 'react'
import Grid from '@mui/material/Grid'
import Checkbox from '@mui/material/Checkbox'
import TextField from '@mui/material/TextField'
import Autocomplete from '@mui/material/Autocomplete'
import Chip from '@mui/material/Chip'
import Box from '@mui/material/Box'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import styled from 'styled-components'
import style from '../../../utils/styles.json'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { employeeDataRequest } from '../../../reducers/entities'
import { createLeaves, getAllEmployees } from '../../../actions'
import { CircularProgress, Dialog, MenuItem } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { fetchEmpHrControlEntities, fetchEmpHrControlUI } from '../../../reducers'
import { toast } from 'react-toastify'
import dayjs from 'dayjs'
import Loader from '../../Common/Loader'

const icon = <CheckBoxOutlineBlankIcon fontSize='small' />
const checkedIcon = <CheckBoxIcon fontSize='small' />

const InputField = styled(TextField)(({ theme }) => ({
  marginBottom: '1px !important',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px !important', // Adjust the font size here
    fontFamily: `${style.FONT_MEDIUM} !important`,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'white',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
    marginLeft: '15px',
    padding: '0 auto',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginLeft: '15px',

    fontSize: '13px', // Adjust the font size here
    fontFamily: style.FONT_MEDIUM,
    lineHeight: '1.8em',
  },
}))

export const SelectField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-root': {
    marginTop: '-3px',
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: style.FONT_MEDIUM,
}))

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '15px', // Adjust the font size here
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    fontFamily: style.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginLeft: '-5px !important',
    marginTop: '-5px !important',
    fontSize: '15px',
    lineHeight: '1.8em',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '40px',
  fontFamily: style.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '40px',
  fontFamily: style.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const AddLeavesForm = (props: any) => {
  const {
    employeeRecords,
    fetchEmployeeRecords,
    createLeavesAPICall,
    isCreateLeavesLoader,
    isCreateLeavesDone,
    getEmpCreatedLeaves,
    createLeavesAPICallReset,
    getEmpLeavesInfo,
  } = props

  const validationSchema = Yup.object({
    employees: Yup.array()
      .min(1, 'At least one employee must be selected')
      .required('Employee selection is required'),
    quarterId: Yup.string().required('Quarter is required'),
    leaveAllocated: Yup.number().required('Leave allocated must be a number'),
    description: Yup.string().required('Description is required'),
  })

  const formik = useFormik({
    initialValues: {
      employees: [] as any,
      quarterId: 29,
      leaveAllocated: 4,
      description: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {},
  })

  const navigate = useNavigate()

  const handleDelete = (optionToDelete: number) => {
    formik.setFieldValue(
      'employees',
      formik.values.employees.filter((option: any) => option !== optionToDelete),
    )
  }

  const handleSubmit = async () => {
    try {
      await validationSchema.validate(formik.values, { abortEarly: false })
      const entity = {
        user_ids: formik.values.employees,
        quarter_id: formik.values.quarterId,
        leave_allocated: +formik.values.leaveAllocated,
        description: formik.values.description,
      }

      createLeavesAPICall(entity)
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const stepErrors = error.inner.reduce((acc, curr) => {
          if (curr.path) {
            acc[curr.path] = curr.message
          }
          return acc
        }, {} as { [K in keyof any]?: string })

        formik.setTouched(
          Object.keys(stepErrors).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {} as { [K in keyof any]: any }),
        )

        formik.setErrors(stepErrors)
      } else {
        console.error('Unexpected error: ', error)
      }
    }
  }

  React.useEffect(() => {
    fetchEmployeeRecords()
  }, [])

  const onClose = () => {
    navigate(-1)
  }

  const handleSelectAll = (event: any) => {
    if (event.target.checked) {
      const allUserIds =
        getEmpLeavesInfo?.employeeList?.length > 0 &&
        getEmpLeavesInfo?.employeeList.map((e: any) => e.id)
      formik.setFieldValue('employees', allUserIds)
    } else {
      formik.setFieldValue('employees', [])
    }
  }

  useEffect(() => {
    if (isCreateLeavesDone) {
      toast.success('Leaves allocated successfully')
      formik.resetForm()
      createLeavesAPICallReset()
    }
  }, [isCreateLeavesDone])

  useEffect(() => {
    if (getEmpLeavesInfo?.quarters?.length > 0) {
      const currentMonth = dayjs().month();
  
      // Map the months to their respective quarters starting from April
      const quarterMapping = [
        'Quarter4',
        'Quarter4',
        'Quarter4',
        'Quarter1',
        'Quarter1',
        'Quarter1',
        'Quarter2',
        'Quarter2',
        'Quarter2',
        'Quarter3',
        'Quarter3',
        'Quarter3',
      ];
  
      const currentQuarter = quarterMapping[currentMonth];
  
      const matchingQuarter =
        getEmpLeavesInfo?.quarters?.find((q: any) => q.quarter === currentQuarter);
  
      formik.setFieldValue(
        'quarterId',
        matchingQuarter?.id || getEmpLeavesInfo?.quarters[0].id,
      );
    }
  }, [getEmpLeavesInfo?.quarters]);
  

  return (
    <form onSubmit={formik.handleSubmit} noValidate>
      {isCreateLeavesLoader && (
      <Loader state={isCreateLeavesLoader} />
      )}
      <Grid container spacing={2} sx={{ marginTop: '10px' }}>
        <Grid item xs={12} md={6}>
          <Autocomplete
            multiple
            id='checkboxes-tags-demo'
            options={getEmpLeavesInfo?.employeeList ?? []}
            disableCloseOnSelect
            sx={{
              width: '100%',
              height: '40px',
              borderRadius: '10px',
              display: 'flex',
              alignItems: 'center',
            }}
            value={formik.values.employees.map(
              (userId: number) =>
                getEmpLeavesInfo?.employeeList?.length > 0 &&
                getEmpLeavesInfo?.employeeList?.find((emp: any) => emp.id === userId),
            )}
            onChange={(event, newValue) => {
              formik.setFieldValue(
                'employees',
                newValue.map((option: any) => option.id),
              )
            }}
            getOptionLabel={(value: { id: number; name: string; employee_id: string }) => value.employee_id + value?.name}
            renderOption={(props, option) => {
              const isSelected = formik.values.employees.includes(option.id)

              const toggleSelection = () => {
                const currentEmployees = formik.values.employees
                let updatedEmployees
                if (isSelected) {
                  // Remove option if already selected
                  updatedEmployees = currentEmployees.filter((id: number) => id !== option.id)
                } else {
                  // Add option if not selected
                  updatedEmployees = [...currentEmployees, option.id]
                }
                formik.setFieldValue('employees', updatedEmployees)
              }

              return (
                <li
                  {...props}
                  onClick={toggleSelection} // Toggle selection when clicking the entire option
                  style={{ cursor: 'pointer' }} // Add pointer cursor for better UX
                >
                  <Checkbox
                    icon={icon}
                    checkedIcon={checkedIcon}
                    style={{ marginRight: 8 }}
                    checked={isSelected} // Reflect selected state
                    onClick={(e) => {
                      e.stopPropagation() // Prevent `li` click event from firing
                      toggleSelection()
                    }}
                  />
                  {`${option.employee_id}  ${option.name}`} {/* Display only the name */}
                </li>
              )
            }}
            renderInput={(params) => (
              <StyledTextField
                {...params}
                label='Select Employees'
                placeholder='Search Employee'
                error={Boolean(formik.touched.employees && formik.errors.employees)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    paddingTop: '2px !important',
                    paddingBottom: '2px !important',
                  },
                  height: '40px',
                  borderRadius: '10px',
                  display: 'flex',
                  alignItems: 'center',
                }}
                required={true}
                InputProps={{
                  ...params.InputProps,
                  startAdornment: (
                    <>
                      <Checkbox
                        indeterminate={
                          formik.values.employees.length > 0 &&
                          formik.values.employees.length <
                            (getEmpLeavesInfo?.employeeList?.length > 0 &&
                              getEmpLeavesInfo?.employeeList.length)
                        }
                        checked={
                          getEmpLeavesInfo?.employeeList?.length > 0
                            ? formik.values.employees.length ===
                              getEmpLeavesInfo?.employeeList.length
                            : false
                        }
                        onChange={handleSelectAll}
                        style={{ marginRight: 8 }}
                      />
                      Select All
                    </>
                  ),
                }}
              />
            )}
          />

          <SelectField
            select
            fullWidth
            label='Quarter Id'
            name='quarterId'
            value={formik.values.quarterId}
            onChange={(event) => {
              formik.setFieldValue('quarterId', event.target.value)
              const leaveAllocation =
                getEmpLeavesInfo?.quarters?.length &&
                getEmpLeavesInfo?.quarters
                  .filter((data: any) => data.id === event.target.value)
                  .map((data: any) => data.total_leaves)[0]

              formik.setFieldValue('leaveAllocated', leaveAllocation)
            }}
            onBlur={formik.handleBlur}
            sx={{ marginTop: '30px' }}
            SelectProps={{
              MenuProps: {
                PaperProps: {
                  style: {
                    maxHeight: 200,
                    overflowY: 'auto',
                  },
                },
              },
            }}
            error={formik.touched.quarterId && Boolean(formik.errors.quarterId)}
            required
          >
            {getEmpLeavesInfo?.quarters?.length > 0 &&
              getEmpLeavesInfo?.quarters?.map((q: any) => (
                <StyledMenuItem
                  value={q.id}
                  sx={{ fontSize: '15px', fontFamily: style.FONT_MEDIUM }}
                >
                  {q.quarter}
                </StyledMenuItem>
              ))}
          </SelectField>

          <StyledTextField
            label='Leave Allocated'
            name='leaveAllocated'
            fullWidth
            sx={{ marginTop: '10px' }}
            value={formik.values.leaveAllocated}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            required
            error={formik.touched.leaveAllocated && Boolean(formik.errors.leaveAllocated)}
          />

          <StyledTextField
            label='Description'
            name='description'
            fullWidth
            multiline
            sx={{ marginTop: '10px' }}
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            required
            error={formik.touched.description && Boolean(formik.errors.description)}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Box
            sx={{
              height: '260px',
              border: '1px solid #ccc',
              borderRadius: 4,
              padding: 2,
              overflowY: 'auto',
            }}
          >
            <Typography
              sx={{
                fontFamily: style.FONT_BOLD,
                fontSize: '15px',
                color: 'gray',
             
              }}
            >
              Selected Employees
            </Typography>
            {formik.values.employees.map((selectedId: number) => {
              const employee =
                getEmpLeavesInfo?.employeeList?.length > 0 &&
                getEmpLeavesInfo?.employeeList.find((emp: any) => emp.id === selectedId) // Find the matching employee object
              if (!employee) return null // Skip if no matching employee is found

              const displayName = employee.name

              return (
                <Chip
                  sx={{
                    fontFamily: style.FONT_MEDIUM,
                    fontSize: '13px',
                    backgroundColor: '#C1BAA1',
                    m: 0.8,
                  }}
                  key={employee.id}
                  label={displayName} // Display only the extracted name
                  onDelete={() => handleDelete(selectedId)} // Pass `selectedId` for deletion
                />
              )
            })}
          </Box>
        </Grid>
      </Grid>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          margin: '15px -20px 15px 15px',
          gap: '10px',
        }}
      >
        <Button
          sx={{
            background: '#E2E2E2',
            color: '#000000',
            fontSize: '13px',
            height: '40px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            borderRadius: '20px',
            '&:hover': {
              background: '#E2E2E2',
              color: '#000000',
            },
          }}
          onClick={onClose}
          autoFocus
        >
          CANCEL
        </Button>

        <Button
          sx={{
            fontSize: '13px',
            height: '40px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            borderRadius: '20px',
            '&.Mui-disabled': {
              opacity: 0.5,
              color: '#ffffff',
            },
          }}
          autoFocus
          type='submit'
          onClick={handleSubmit}
        >
          SAVE
        </Button>
      </Box>
    </form>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    employeeRecords: employeeDataRequest.getAllEmployeesList(state).getAllEmployeesDetails,
    isCreateLeavesLoader: fetchEmpHrControlUI.fetchHrControlData(state).isCreateLeavesLoader,
    isCreateLeavesDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateLeavesDone,
    getEmpCreatedLeaves: fetchEmpHrControlEntities.fetchHrControlData(state).getEmpCreatedLeaves,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    fetchEmployeeRecords: () => dispatch(getAllEmployees.request()),

    createLeavesAPICall: (data: any) => dispatch(createLeaves.request(data)),
    createLeavesAPICallReset: () => dispatch(createLeaves.reset()),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(AddLeavesForm)
