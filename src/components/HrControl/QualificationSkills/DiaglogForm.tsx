import React, { useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  TextField,
  Typography,
  Box,
  DialogTitleProps,
  MenuItem,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { styled } from '@mui/material/styles'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import styles from '../../../utils/styles.json'
import { style } from '../../components/HrControl/Designations/projectCustomersStyle'
import { toast } from 'react-toastify'

// Styled Components
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: '30px 40px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },
}))

const CloseButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  right: 8,
  top: 8,
  color: theme.palette.grey[500],
}))

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    fontFamily: styles.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginTop: '-5px',
    marginLeft: '-5px !important',
    fontSize: '15px',
    lineHeight: '1.8em',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const StyledButton = styled(Button)(({ theme }) => ({
  fontSize: '14px',
  borderRadius: '20px',
  padding: '10px 20px',
}))

// Validation Schema

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
}))

export const SelectField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '15px',
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-root': {
    marginTop: '-3px',
    fontSize: '15px',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: styles.FONT_MEDIUM,
}))

function BootstrapDialogTitle(props: any) {
  const { children, onClose, ...other } = props

  return (
    <DialogTitle sx={{ m: 0, padding: 0, paddingTop: 1 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label='close'
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 15,
            top: 10,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  )
}

const DialogForm = (props: any) => {
  const {
    open,
    setOpen,
    createHolidayForAdmin,
    isCreateHolidayLoader,
    isCreateHolidayDone,
    createHolidayForAdminReset,
    fetchHolidaysData,
    getEmpCreatedHolidays,

    qualificationData,
    createQualification,
    isCreateQualificationLoader,
    isCreateQualificationDone,
    createQualificationReset,
    getCreateQualification,
  } = props

  const validationSchema = Yup.object({
    skillName: Yup.string().required(),
    qualification: Yup.date().required(),
  })

  const formik = useFormik({
    initialValues: {
      skillName: '',
      qualification: qualificationData?.length > 0 ? qualificationData[0].id : '',
    },
    validationSchema,
    onSubmit: (values) => {
    },
  })
  const onClose = () => {
    setOpen({ openStatus: false, isEdit: 0, qualifications: {} })
    setTimeout(() => {
      formik.resetForm()
    }, 500)
  }

  useEffect(() => {
    if (isCreateQualificationDone) {
      onClose()
    }
  }, [isCreateQualificationDone])

  const handleSubmit = async () => {
    try {
      await validationSchema.validate(formik.values, { abortEarly: false })
      const entity = {
        id: open.qualifications?.id ? open.qualifications?.id : open.isEdit,
        qualificationSkillName: formik.values.skillName,
        idQualificationSet: formik.values.qualification,
      }

      createQualification(entity)
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const stepErrors = error.inner.reduce((acc, curr) => {
          if (curr.path) {
            acc[curr.path] = curr.message
          }
          return acc
        }, {} as { [K in keyof any]?: string })

        formik.setTouched(
          Object.keys(stepErrors).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {} as { [K in keyof any]: any }),
        )

        formik.setErrors(stepErrors)
      } else {
        console.error('Unexpected error:', error)
      }
    }
  }

  useEffect(() => {
    if (open.isEdit === 1) {
      const qualificationSet = qualificationData?.find(
        (data: any) => data.id === open.qualifications?.id_qualification_set,
      )
      formik.setFieldValue('skillName', open.qualifications.qualification_skill_name)
      formik.setFieldValue('qualification', qualificationSet?.id)
    }
  }, [open.qualifications])

  return (
    <>
      <Dialog
        open={open?.openStatus}
        onClose={onClose}
        sx={{
          backdropFilter: 'blur(1px)',
          padding: '50px',
          flex: '1 1 auto',
          overflowY: 'auto',
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          '& .MuiDialog-paper': {
            width: '40%', // Set the dialog width to 40% of the screen
            maxWidth: 'none', // Disable the default max width constraints
          },
        }}
      >
        <BootstrapDialogTitle id='customized-dialog-title' onClose={onClose}>
          <Heading>
            {localStorage.getItem('qualification') === 'edit'
              ? 'Edit Qualification Skills'
              : 'Add Qualification Skills'}{' '}
          </Heading>
        </BootstrapDialogTitle>
        <DialogContent>
          <form onSubmit={formik.handleSubmit}>
            <StyledTextField
              label='Skill Name'
              name='skillName'
              fullWidth
              value={formik.values.skillName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.skillName && Boolean(formik.errors.skillName)}
            />
            <SelectField
              select
              fullWidth
              label='Qualification Set'
              name='qualification'
              value={formik.values.qualification}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              sx={{
                marginTop: '10px',
                '& .MuiFormLabel-asterisk': {
                  color: 'red',
                },
              }}
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    style: {
                      maxHeight: 200, // Maximum height for the dropdown
                      overflowY: 'auto', // Enable vertical scrolling
                    },
                  },
                },
              }}
              error={formik.touched.qualification && Boolean(formik.errors.qualification)}
              required
            >
              <StyledMenuItem
                value=''
                disabled
                sx={{ fontSize: '15px', fontFamily: styles.FONT_MEDIUM }}
              >
                Select Qualification
              </StyledMenuItem>
              {qualificationData?.length > 0 ? (
                qualificationData.map((data: any) => (
                  <StyledMenuItem
                    sx={{ textTransform: 'capitalize' }}
                    key={data.id}
                    value={data.id}
                  >
                    {data.qualification
                      .toLowerCase()
                      .replace(/(^\w|\s\w)/g, (letter: any) => letter.toUpperCase())}
                  </StyledMenuItem>
                ))
              ) : (
                <StyledMenuItem disabled>No Qualification available</StyledMenuItem>
              )}
            </SelectField>
          </form>
        </DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: '-15px',
            marginBottom: '20px',
            gap: '10px',
          }}
        >
          <CancelButton onClick={onClose} autoFocus>
            CANCEL
          </CancelButton>
          <ActionButton autoFocus type='submit' onClick={handleSubmit}>
            SAVE
          </ActionButton>
        </Box>
      </Dialog>
    </>
  )
}

export default DialogForm
