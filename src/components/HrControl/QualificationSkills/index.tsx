import { Box, CircularProgress, Dialog } from '@mui/material';
import { useEffect, useState } from 'react';
import {
  createEmpDesignations,
  createQualification,
  editDesignations,
  fetchEmpDesignations,
  fetchMandateType,
  fetchProjectCustomersList,
  fetchQualification,
  fetchQualificationSkill,
  updateMandateType,
} from '../../../actions/index';
import { connect } from 'react-redux';
import styles from '../../../utils/styles.json';
import DiaglogForm from './DiaglogForm';
import AddCustomerButton from './AddCustomerButton';
import SearchBox from './SearchBox';
import { Dispatch } from 'redux';
import { RootState } from '../../../configureStore';
import { FormValues, DesignationPropType, getProjectCustomersType } from './ProjectCustomersTypes';
import Cards from './Cards';
import {
  employeePortalEntity,
  employeePortalUI,
  fetchEmpHrControlEntities,
  fetchEmpHrControlUI,
  projectManagementEntity,
  projectManagementUI,
} from '../../../reducers';
import { HeaderHeading } from '../../Common/CommonStyles';
import { ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const style = {
  customBox: {
    overflow: 'auto',
    fontFamily: 'Montserrat-Bold',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    background: 'white',
    opacity: '1',
    textAlign: 'left',
    margin: '20px',
    padding: '10px 25px 25px 25px',
    borderRadius: '10px',
    width: '93%',
    minHeight: '69vh',
    border: '1px solid #DDDDDD',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
    '@media (max-width: 768px)': {
      width: '100%',
      padding: '15px',
    },
  },
};

const QualificationSkills = ({
  isQualificationSkillsDataLoader,
  fetchQualificationSkill,
  qualificationSkill,
  fetchQualification,
  qualificationData,
  getCreateQualification,
  isCreateQualificationDone,
  createQualificationReset,
  createQualificationAPI
}: any) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredQualificationSkill, setFilteredQualificationSkill] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(100);
  const [page, setPage] = useState(1);
  const [open, setOpen] = useState({ openStatus: false, isEdit: 0, qualifications: {} });

  useEffect(() => {
    fetchQualification();
    fetchQualificationSkill();
  }, []);

  useEffect(() => {
    if (isCreateQualificationDone) {
      toast.success(getCreateQualification?.message);
      fetchQualificationSkill();
      createQualificationReset();
    }
  }, [isCreateQualificationDone]);

  // Filter qualification skills based on the search query
  useEffect(() => {
    const filtered = qualificationSkill?.filter((item: any) =>
      item.qualification_skill_name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredQualificationSkill(filtered);
  }, [searchQuery, qualificationSkill]);

  return (
    <Box sx={style.customBox}>
      <HeaderHeading>{`Qualification Skills`}</HeaderHeading>
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{
            float: 'right',
            cursor: 'pointer',
            marginTop: '-35px',
          }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <SearchBox searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
        <AddCustomerButton setOpen={setOpen} />
      </Box>
      <DiaglogForm
        setOpen={setOpen}
        open={open}
        qualificationData={qualificationData}
        createQualification={createQualificationAPI}
        isCreateQualificationDone={isCreateQualificationDone}
      />
      <Box marginTop="5px">
        <Cards
          isQualificationSkillsDataLoader={isQualificationSkillsDataLoader}
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          searchQuery={searchQuery}
          qualificationSkill={filteredQualificationSkill}
          qualificationData={qualificationData}
          setOpen={setOpen}
          createQualification={createQualificationAPI}
        />
      </Box>
    </Box>
  );
};

const mapStateToProp = (state: RootState) => ({
  isQualificationSkillsDataLoader: employeePortalUI.getAllEmpPortalUI(state).isQualificationSkillsDataLoader,
  qualificationSkill: employeePortalEntity.getEmployeePortal(state).getQualificationSkill,
  qualificationData: employeePortalEntity.getEmployeePortal(state).getQualification,
  getCreateQualification: fetchEmpHrControlEntities.fetchHrControlData(state).getCreateQualification,
  isCreateQualificationDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateQualificationDone,
});

const mapDispatchToProp = (dispatch: Dispatch) => ({
  fetchQualificationSkill: () => dispatch(fetchQualificationSkill.request()),
  fetchQualification: () => dispatch(fetchQualification.request()),
  createQualificationAPI: (data:any) => dispatch(createQualification.request(data)),
  createQualificationReset: () => dispatch(createQualification.reset()),
});

export default connect(mapStateToProp, mapDispatchToProp)(QualificationSkills);
