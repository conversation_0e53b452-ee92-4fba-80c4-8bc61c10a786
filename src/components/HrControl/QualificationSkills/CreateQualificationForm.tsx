import React, { useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  TextField,
  Typography,
  Box,
  DialogTitleProps,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { styled } from '@mui/material/styles'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import dayjs from 'dayjs'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import styles from '../../utils/styles.json'
import { style } from '../../components/HrControl/Designations/projectCustomersStyle'
import { toast } from 'react-toastify'

// Styled Components
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: '30px 40px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },
}))

const CloseButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  right: 8,
  top: 8,
  color: theme.palette.grey[500],
}))

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    fontFamily: styles.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginTop: '-5px',
    marginLeft: '-5px !important',
    fontSize: '15px',
    lineHeight: '1.8em',
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const StyledButton = styled(Button)(({ theme }) => ({
  fontSize: '14px',
  borderRadius: '20px',
  padding: '10px 20px',
}))

// Validation Schema
const validationSchema = Yup.object({
  holidayName: Yup.string().required('Holiday Name is required'),
  holidayDate: Yup.date().required('Holiday Date is required'),
  holidayDescription: Yup.string().required('Holiday Description is required'),
})

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
}))

function BootstrapDialogTitle(props: any) {
  const { children, onClose, ...other } = props

  return (
    <DialogTitle sx={{ m: 0, padding: 0, paddingTop: 1 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label='close'
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 15,
            top: 10,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  )
}

const CreateHolidayForm = (props: any) => {
  const {
    open,
    setOpen,
    createHolidayForAdmin,
    isCreateHolidayLoader,
    isCreateHolidayDone,
    createHolidayForAdminReset,
    fetchHolidaysData,
    getEmpCreatedHolidays,
  } = props
  const formik = useFormik({
    initialValues: {
      holidayName: '',
      holidayDate: null,
      holidayDescription: '',
    },
    validationSchema,
    onSubmit: (values) => {
    },
  })
  const onClose = () => {
    setOpen({ openStatus: false, isEdit: 0, holiday: {} })
    setTimeout(() => {
      formik.resetForm()
    }, 500)
  }

  const handleSubmit = async () => {
    try {
      await validationSchema.validate(formik.values, { abortEarly: false })
      const entity = {
        id: open.isEdit,
        name: formik.values.holidayName,
        date: formik.values.holidayDate,
        desc: formik.values.holidayDescription,
      }
      createHolidayForAdmin(entity)
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const stepErrors = error.inner.reduce((acc, curr) => {
          if (curr.path) {
            acc[curr.path] = curr.message
          }
          return acc
        }, {} as { [K in keyof any]?: string })

        formik.setTouched(
          Object.keys(stepErrors).reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {} as { [K in keyof any]: any }),
        )

        formik.setErrors(stepErrors)
      } else {
        console.error('Unexpected error:', error)
      }
    }
  }

  useEffect(() => {
    if (isCreateHolidayDone) {
      fetchHolidaysData()
      toast.success(
        getEmpCreatedHolidays?.is_deleted
          ? 'Holiday Deleted Successfully'
          : 'Holiday Updated Successfully',
      )
      createHolidayForAdminReset()
      onClose()
    }
  }, [isCreateHolidayDone])

  useEffect(() => {
    if (open.isEdit === 1) {
      formik.setFieldValue('holidayName', open.holiday.name)
      formik.setFieldValue('holidayDate', open.holiday.date)
      formik.setFieldValue('holidayDescription', open.holiday.desc)
    }
  }, [open.holiday])

  return (
    <>
      <Dialog
        open={open?.openStatus}
        onClose={onClose}
        sx={{
          backdropFilter: 'blur(1px)',
          padding: '50px',
          flex: '1 1 auto',
          overflowY: 'auto',
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          '& .MuiDialog-paper': {
            width: '40%', // Set the dialog width to 40% of the screen
            maxWidth: 'none', // Disable the default max width constraints
          },
        }}
      >
        {/* <DialogTitle>
          Create Holiday
          <CloseButton onClick={onClose}>
            <CloseIcon />
          </CloseButton>
        </DialogTitle> */}

        <BootstrapDialogTitle id='customized-dialog-title' onClose={onClose}>
          <Heading>
            {localStorage.getItem('holiday') === 'edit' ? 'Edit Holiday' : 'Create Holiday'}{' '}
          </Heading>
        </BootstrapDialogTitle>
        <DialogContent>
          <form onSubmit={formik.handleSubmit}>
            <StyledTextField
              label='Holiday Name'
              name='holidayName'
              fullWidth
              value={formik.values.holidayName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.holidayName && Boolean(formik.errors.holidayName)}
            />

            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DemoContainer
                components={['DatePicker']}
                sx={{
                  overflow: 'visible',
                }}
              >
                <DatePicker
                  label='Holiday Date'
                  format='MM-DD-YYYY'
                  value={formik.values.holidayDate ? dayjs(formik.values.holidayDate) : null}
                  onChange={(date: any) => {
                    formik.setFieldValue('holidayDate', date?.format('YYYY-MM-DD'))
                  }}
                  slotProps={{
                    textField: {
                      error: formik.touched.holidayDate && Boolean(formik.errors.holidayDate),
                    },
                  }}
                  sx={{
                    border: 'none',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '20px',
                      height: '45px',
                      fontSize: '14px',
                      fontFamily: styles.FONT_MEDIUM,
                      boxSizing: 'border-box',
                    },
                    '& .MuiInputLabel-root': {
                      fontSize: '14px',
                      fontFamily: styles.FONT_MEDIUM,
                      marginTop: '-2px',
                      '& .MuiFormLabel-asterisk': {
                        color: 'red',
                      },
                    },
                    flex: 1,
                  }}
                />
              </DemoContainer>
            </LocalizationProvider>
            <StyledTextField
              label='Holiday Description'
              name='holidayDescription'
              fullWidth
              multiline
              value={formik.values.holidayDescription}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.holidayDescription && Boolean(formik.errors.holidayDescription)}
            />
          </form>
        </DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: '-15px',
            marginBottom: '20px',
            gap: '10px',
          }}
        >
          <CancelButton onClick={onClose} autoFocus>
            CANCEL
          </CancelButton>
          <ActionButton autoFocus type='submit' onClick={handleSubmit}>
            <Typography>SAVE</Typography>
          </ActionButton>
        </Box>
      </Dialog>
    </>
  )
}

export default CreateHolidayForm
