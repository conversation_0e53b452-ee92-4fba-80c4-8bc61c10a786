import { Box, IconButton, InputAdornment, styled } from '@mui/material'
import { Dispatch, SetStateAction, useState, useEffect, useCallback } from 'react'
import { SearchBoxCustom, SearchIconStyle } from '../../Common/CommonStyles'
import ClearIcon from '@mui/icons-material/Clear'
import { style } from './projectCustomersStyle'
import _ from 'lodash' // Import lodash for debounce

type SearchBoxType = {
  searchQuery: string
  setSearchQuery: Dispatch<SetStateAction<string>>
}

const CustomSearchBox = styled(SearchBoxCustom)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    '& fieldset': {
      borderColor: '#777777',
    },
  },
}))

const SearchBox = (props: any) => {
  const { setSearchQuery, searchQuery } = props

  const handleClear = () => {
    setSearchQuery('')
  }

  return (
    <Box width='300px' sx={style.serachBoxContainer}>
      <CustomSearchBox
        id='outlined-basic'
        placeholder='Search Qualification Skills'
        variant='outlined'
        size='small'
        value={searchQuery}
        onChange={(e) => {
          setSearchQuery(e.target.value)
        }}
        width='100%'
        InputProps={{
          startAdornment: <SearchIconStyle />,
          endAdornment: (
            <InputAdornment position='end'>
              <IconButton aria-label='clear-icon' onClick={handleClear} edge='end'>
                {searchQuery && <ClearIcon />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  )
}

export default SearchBox
