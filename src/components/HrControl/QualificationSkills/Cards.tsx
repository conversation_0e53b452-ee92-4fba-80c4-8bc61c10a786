import {
  Box,
  Card,
  CardContent,
  Grid,
  IconButton,
  Pagination,
  Tooltip,
  Typography,
} from '@mui/material'
import { ReactComponent as EditIcon } from '../../../assets/images/editIcon.svg'
import { ReactComponent as DeleteIcon } from '../../../assets/images/deleteIcon.svg'
import { useEffect, useState } from 'react'
import { FormValues, deleteProjectCustomerType } from './ProjectCustomersTypes'
import { connect } from 'react-redux'
import { RootState } from '../../../configureStore'
import { deleteMandateType, editDesignations } from '../../../actions'
import { Dispatch } from 'redux'
import { NoCards } from './NoCards'
import { style } from './projectCustomersStyle'
import CircularProgress from '../../Common/CircularProgress'
import Loader from '../../Common/Loader'

const Cards = ({
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  getEmpDesignations,
  isQualificationSkillsDataLoader,
  deleteProjectCustomer,
  setEditCustomerData,
  setopenCustomerEditForm,
  fetchEditDesignations,

  qualificationSkill,
  qualificationData,
  createQualification,
  setOpen,
}: any) => {
  const [filteredRows, setFilteredRows] = useState<FormValues[]>()
  const [actionDialog, setActionDialog] = useState(false)
  const diaglogTitle: string = 'Delete Mandate'
  const [isDeleted, setIsDeleted] = useState(false)
  const [forRowDeleteId, setForRowDeleteId] = useState(0)
  const [overflowingTexts, setOverflowingTexts] = useState<Record<number, boolean>>({})

  useEffect(() => {
    setFilteredRows(qualificationSkill)
  })

  const deleteOpenConfirmationFunction = (rowId: number) => {
    createQualification({ id: rowId, is_deleted: 1 })
  }

  const handleChangePage = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  useEffect(() => {
    const updatedOverflowingTexts: Record<number, boolean> = {}
    filteredRows?.forEach((row) => {
      const element = document.getElementById(`customer-name-${row.id}`)
      if (element) {
        updatedOverflowingTexts[row.id] = element.scrollWidth > element.clientWidth
      }
    })
    setOverflowingTexts(updatedOverflowingTexts)
  }, [filteredRows])

  const UserRole = localStorage.getItem('roles')
  const isDisabled = !UserRole?.includes('Admin')

  const styles = {
    iconButtonStyles: {
      opacity: isDisabled ? 0.5 : 1,
    },
  }

  const qualificationName = (qualificationData: any, row: any) => {
    return qualificationData
      ?.filter((data: any) => data?.id === row.id_qualification_set)
      .map((item: any) =>
        item.qualification.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase()),
      )
      .join(', ')
  }

  return (
    <>
      {isQualificationSkillsDataLoader ? (
          <Loader state={isQualificationSkillsDataLoader} />
      ) : (
        <Box sx={style.cardBox}>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 2 }}>
            <Grid item xs={12} sm={12} md={12}>
              <Card sx={style.cardMainStyleHeader}>
                <CardContent sx={style.cardContentStyle}>
                  <Box sx={style.cardContentBoxStyle}>
                    <Box width='81%'>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '20px',
                        }}
                      >
                        <Typography sx={style.cardContentTypoStyleC}>Skill Name</Typography>
                        <Typography sx={style.cardContentTypoStyleD}>Qualification Set</Typography>
                      </Box>
                    </Box>
                    <Box sx={style.iconBoxStyle}>
                      <Typography sx={style.cardContentTypoStyleE}>Actions</Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            {!filteredRows?.length ? (
              <NoCards />
            ) : (
              filteredRows?.map((row: any) => (
                <Grid item xs={12} sm={12} md={12} key={`${row.id}`}>
                  <Card sx={style.cardStyle}>
                    <CardContent sx={style.cardContentStyle}>
                      <Box sx={style.cardContentBoxStyle}>
                        <Box width='81%'>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '20px',
                            }}
                          >
                            <Typography sx={style.cardContentTypoStyle}>
                              {`${row.qualification_skill_name}`}
                            </Typography>
                            <Typography sx={style.cardContentTypoStyleB}>
                              {qualificationName(qualificationData, row)}
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={style.iconBoxStyle}>
                          <Box sx={style.editIconsStyle}>
                            <IconButton
                              onClick={() => {
                                localStorage.setItem('qualification', 'edit')
                                setOpen({ openStatus: true, isEdit: 1, qualifications: row })
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Box>
                          <Box sx={style.iconsStyle}>
                            <Tooltip title="Delete" arrow>
                            <IconButton
                              onClick={() => {
                                deleteOpenConfirmationFunction(row.id)
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>
      )}

    </>
  )
}

const mapStateToProp = (state: RootState) => {
  return {}
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    deleteProjectCustomer: (data: deleteProjectCustomerType) =>
      dispatch(deleteMandateType.request(data)),
    fetchEditDesignations: (data: any) => dispatch(editDesignations.request({ data })),
  }
}
export default connect(mapStateToProp, mapDispatchToProp)(Cards)
