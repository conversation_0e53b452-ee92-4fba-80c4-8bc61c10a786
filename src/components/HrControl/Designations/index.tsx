import { Box, CircularProgress, Dialog } from '@mui/material'
import { useEffect, useState } from 'react'
import {
  createEmpDesignations,
  editDesignations,
  fetchEmpDesignations,
  fetchMandateType,
  fetchProjectCustomersList,
  updateMandateType,
} from '../../../actions/index'
import { connect } from 'react-redux'
import styles from '../../../utils/styles.json'
import DiaglogForm from './DiaglogForm'
import AddCustomerButton from './AddCustomerButton'
import SearchBox from './SearchBox'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { FormValues, DesignationPropType, getProjectCustomersType } from './ProjectCustomersTypes'
import Cards from './Cards'
import {
  fetchEmpHrControlEntities,
  fetchEmpHrControlUI,
  projectManagementEntity,
  projectManagementUI,
} from '../../../reducers'
import { HeaderHeading } from '../../Common/CommonStyles'
import { ArrowBack } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

const style = {
  customBox: {
    overflow: 'auto',
    fontFamily: 'Montserrat-Bold',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    background: 'white',
    opacity: '1',
    textAlign: 'left',
    margin: '20px',
    padding: '10px 25px 25px 25px',
    borderRadius: '10px',
    width: '93%',
    minHeight: '69vh', // Allow height to shrink if content is less
    border: '1px solid #DDDDDD',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)', // Optional for a subtle shadow
    '@media (max-width: 768px)': {
      width: '100%',
      padding: '15px',
    },
    '@media print': {
      '@page': {
        size: '1280px',
      },
    },
  },
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const Designations = ({
  fetchEmpDesignations,
  getEmpDesignations,
  isEmpDesignationDone,
  isEmpDesignationLoader,
  isCreateDesignationLoader,
  isCreateDesignationDone,
  isEditDesignationDone,
  isEditDesignationLoader,
  createProjectCustomersReset,
  updateProjectCustomerReset,
}: any) => {
  const navigate = useNavigate()
  const [searchQuery, setSearchQuery] = useState('')
  const [rowsPerPage, setRowsPerPage] = useState(100)
  const [page, setPage] = useState(1)
  const [openCustomerForm, setOpenCustomerForm] = useState(false)
  const [openCustomerEditForm, setopenCustomerEditForm] = useState(false)
  const [editCustomerData, setEditCustomerData] = useState<FormValues>({
    id: 0,
    job_level_code: '',
    job_level_name: '',
  })

  useEffect(() => {
    fetchEmpDesignations({ limit: rowsPerPage, search: searchQuery, page: page })
  }, [searchQuery, rowsPerPage, page])

  useEffect(() => {
    setPage(1)
  }, [rowsPerPage, searchQuery])

  useEffect(() => {
    if (isCreateDesignationDone || isEditDesignationDone) {
      isCreateDesignationDone && toast.success('Designation Created Successfully')
      isEditDesignationDone && toast.success('Designation Updated Successfully')
      fetchEmpDesignations({ limit: rowsPerPage, search: searchQuery, page: page })
      isCreateDesignationDone && createProjectCustomersReset()
      isEditDesignationDone && updateProjectCustomerReset()
    }
  }, [isCreateDesignationDone, isEditDesignationDone])

  return (
    <Box sx={style.customBox}>
      <HeaderHeading>{`Designations`}</HeaderHeading>
      <Box sx={{ textAlign: 'left' }}>
        <Box
          onClick={() => navigate(-1)}
          sx={{
            float: 'right',
            cursor: 'pointer',
            marginTop: '-35px',
          }}
        >
          <ArrowBack />
        </Box>
      </Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <SearchBox searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
        <AddCustomerButton setOpenCustomerForm={setOpenCustomerForm} />
      </Box>
      <DiaglogForm
        setOpenCustomerForm={setOpenCustomerForm}
        openCustomerForm={openCustomerForm}
        openCustomerEditForm={openCustomerEditForm}
        setopenCustomerEditForm={setopenCustomerEditForm}
        editCustomerData={editCustomerData}
      />

      <Box marginTop='5px'>
        <Cards
          isEmpDesignationLoader={isEmpDesignationLoader}
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          searchQuery={searchQuery}
          getEmpDesignations={getEmpDesignations}
          setopenCustomerEditForm={setopenCustomerEditForm}
          setEditCustomerData={setEditCustomerData}
        />
      </Box>
    </Box>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    getEmpDesignations: fetchEmpHrControlEntities.fetchHrControlData(state).getEmpDesignations,
    isEmpDesignationDone: fetchEmpHrControlUI.fetchHrControlData(state).isEmpDesignationDone,
    isEmpDesignationLoader: fetchEmpHrControlUI.fetchHrControlData(state).isEmpDesignationLoader,
    isCreateDesignationLoader:
      fetchEmpHrControlUI.fetchHrControlData(state).isCreateDesignationLoader,
    isCreateDesignationDone: fetchEmpHrControlUI.fetchHrControlData(state).isCreateDesignationDone,
    isEditDesignationDone: fetchEmpHrControlUI.fetchHrControlData(state).isEditDesignationDone,
    isEditDesignationLoader: fetchEmpHrControlUI.fetchHrControlData(state).isEditDesignationLoader,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    fetchEmpDesignations: (data: any) => dispatch(fetchEmpDesignations.request(data)),
    createProjectCustomersReset: () => dispatch(createEmpDesignations.reset()),
    updateProjectCustomerReset: () => dispatch(editDesignations.reset()),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(Designations)
