import { Formik, Form, Field, FieldProps } from 'formik'
import { Box, Button, DialogContent, Grid, Typography, styled } from '@mui/material'
import { style } from './projectCustomersStyle'
import * as Yup from 'yup'
import { useEffect, useState } from 'react'
import { FormikFormPropType } from './ProjectCustomersTypes'
import styles from '../../../utils/styles.json'
import { InputField } from '../../UserDetails/UserDetailsStyle'

export const validationSchema = Yup.object({
  job_level_code: Yup.string()
    .required('Job Code is required')
    .test('is-empty-or-whitespace', 'Customer Name cannot be empty or whitespace', (value: any) => {
      return value?.trim().length > 0
    }),

  job_level_name: Yup.string()
    .required('Job Name is required')
    .test('is-empty-or-whitespace', 'Customer Name cannot be empty or whitespace', (value: any) => {
      return value?.trim().length > 0
    }),
})

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '35px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '35px',
  fontFamily: styles.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const FormikForm = ({
  initialValues,
  onSubmit,
  handleClickCloseDiaglogForm,
  openCustomerEditForm,
}: FormikFormPropType) => {
  const [isDisabled, setIsDisabled] = useState(true)

  useEffect(() => {
    if (openCustomerEditForm && initialValues.job_level_code?.trim()) {
      setIsDisabled(false)
    } else {
      setIsDisabled(true)
    }
  }, [openCustomerEditForm, initialValues.job_level_code])

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
      validate={(values) => {
        const errors = {}
        if (!values.job_level_code || !values.job_level_code.trim()) {
          setIsDisabled(true)
        } else {
          setIsDisabled(false)
        }
        return errors
      }}
    >
      {({ values, errors, touched }) => (
        <Form>
          <DialogContent dividers>
            <Grid container sx={style.formGridContainerStyle}>
              <Box sx={style.fieldBoxStyle}>
                <Field name='job_level_code'>
                  {({ field }: any) => (
                    <InputField
                      {...field}
                      label='Job Level Code'
                      variant='outlined'
                      placeholder='Enter Job Level Code'
                      size='small'
                      error={touched.job_level_code && !!errors.job_level_code}
                      helperText={
                        touched.job_level_code && typeof errors.job_level_code === 'string'
                          ? errors.job_level_code
                          : ''
                      }
                      inputProps={{
                        autoComplete: 'new-password',
                        form: { autoComplete: 'off' },
                      }}
                    />
                  )}
                </Field>

                <Field name='job_level_name'>
                  {({ field }: FieldProps<string>) => (
                    <InputField
                      {...field}
                      label='Job Level Name'
                      variant='outlined'
                      placeholder='Enter Job Level Name'
                      size='small'
                      error={touched.job_level_name && !!errors.job_level_name}
                      helperText={
                        touched.job_level_name && typeof errors.job_level_name === 'string'
                          ? errors.job_level_name
                          : ''
                      }
                      sx={{ marginTop: '5px' }}
                      inputProps={{
                        autoComplete: 'new-password',
                        form: { autoComplete: 'off' },
                      }}
                    />
                  )}
                </Field>
              </Box>
            </Grid>
          </DialogContent>
          <Box sx={style.diaglogActionBoxStyle}>
            <CancelButton onClick={handleClickCloseDiaglogForm} autoFocus>
              CANCEL
            </CancelButton>
            <ActionButton autoFocus type='submit' disabled={isDisabled}>
              SAVE
            </ActionButton>
          </Box>
        </Form>
      )}
    </Formik>
  )
}

export default FormikForm
