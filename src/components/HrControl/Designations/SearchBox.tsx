import { Box, IconButton, InputAdornment, styled } from '@mui/material'
import { Dispatch, SetStateAction, useState, useEffect, useCallback } from 'react'
import { SearchBoxCustom, SearchIconStyle } from '../../Common/CommonStyles'
import ClearIcon from '@mui/icons-material/Clear'
import { style } from './projectCustomersStyle'
import _ from 'lodash' // Import lodash for debounce

type SearchBoxType = {
  searchQuery: string
  setSearchQuery: Dispatch<SetStateAction<string>>
}

const CustomSearchBox = styled(SearchBoxCustom)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    '& fieldset': {
      borderColor: '#777777',
    },
  },
}))

const SearchBox = (props: SearchBoxType) => {
  const { setSearchQuery } = props
  const [value, setValue] = useState('')

  // Debounced function for setting the search query
  const debouncedSetSearchQuery = useCallback(
    _.debounce((query: string) => {
      setSearchQuery(query)
    }, 800), // Adjust the debounce delay as needed (e.g., 300ms)
    [setSearchQuery]
  )

  useEffect(() => {
    debouncedSetSearchQuery(value)
    return () => {
      debouncedSetSearchQuery.cancel() // Clean up debounce on unmount
    }
  }, [value, debouncedSetSearchQuery])

  const handleClear = () => {
    setValue('')
    setSearchQuery('')
  }

  return (
    <Box width='240px' sx={style.serachBoxContainer}>
      <CustomSearchBox
        id='outlined-basic'
        placeholder='Search Designation'
        variant='outlined'
        size='small'
        value={value}
        onChange={(e) => {
          setValue(e.target.value)
        }}
        width='100%'
        InputProps={{
          startAdornment: <SearchIconStyle />,
          endAdornment: (
            <InputAdornment position='end'>
              <IconButton aria-label='clear-icon' onClick={handleClear} edge='end'>
                {value && <ClearIcon />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  )
}

export default SearchBox
