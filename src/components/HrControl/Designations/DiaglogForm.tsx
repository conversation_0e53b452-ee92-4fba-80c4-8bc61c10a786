import { useEffect } from 'react'
import {
  <PERSON>,
  Dialog,
  DialogTitle,
  IconButton,
  Backdrop,
  Grid,
  styled,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { connect } from 'react-redux'
import {
  createEmpDesignations,
  createMandateType,
  updateMandateType,
  editDesignations,
} from '../../../actions'
import { style } from './projectCustomersStyle'
import FormikForm from './FormikForm'
import { FormValues, DiaglogFormPropsType } from './ProjectCustomersTypes'
import { Dispatch } from 'redux'
import { FormikHelpers } from 'formik'
import styles from '../../../utils/styles.json'

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '22px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
}))

const DiaglogForm = (props: any) => {
  const {
    openCustomerForm,
    setOpenCustomerForm,
    openCustomerEditForm,
    setopenCustomerEditForm,
    createEmpDesignations,
    fetchEditDesignations,
    editCustomerData
  } = props

  const handleClickCloseDiaglogForm = () => {
    setOpenCustomerForm(false)
    setopenCustomerEditForm(false)
  }

  const initialValues = {
    id: openCustomerEditForm ? editCustomerData.id : 0,
    job_level_code: openCustomerEditForm ? editCustomerData.job_level_code : '',
    job_level_name: openCustomerEditForm ? editCustomerData.job_level_name : '',
  }

  const handleSubmit = (values: any, actions: FormikHelpers<FormValues>) => {
    if (!values.job_level_code) {
      actions.setErrors({
        job_level_code: !values.job_level_code ? 'Required' : '',
        job_level_name: !values.job_level_name ? 'Required' : '',
      })
    } else {
      if (openCustomerEditForm) {
        const updateValues = {
          id: values.id,
          is_deleted : 0,
          grade_name: values.job_level_code,
          desc: values.job_level_name,
        }
        fetchEditDesignations(updateValues)
        actions.setSubmitting(false)
        handleClickCloseDiaglogForm()
      } else {
        const data: any = {
          grade_name: values.job_level_code,
          desc: values.job_level_name,
        }
        createEmpDesignations(data)
        actions.setSubmitting(false)
        handleClickCloseDiaglogForm()
      }
    }
  }

  return (
    <Box>
      <Dialog
        open={openCustomerForm || openCustomerEditForm}
        onClose={handleClickCloseDiaglogForm}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
        BackdropComponent={Backdrop}
        sx={style.BackdropDiaglogStyle}
      >
        <Box>
          <Grid item xs={12} sm={12} md={12} />
          <DialogTitle id='alert-dialog-title'>
            <Heading>{openCustomerEditForm ? 'Edit ' : 'ADD '}Designation</Heading>
            <IconButton
              aria-label='close'
              onClick={handleClickCloseDiaglogForm}
              sx={{ ...style.iconButtonStyle }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <FormikForm
            initialValues={initialValues}
            onSubmit={handleSubmit}
            handleClickCloseDiaglogForm={handleClickCloseDiaglogForm}
            openCustomerEditForm={openCustomerEditForm}
          />
        </Box>
      </Dialog>
    </Box>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  createEmpDesignations: (data: FormValues) => dispatch(createEmpDesignations.request({ data })),
  fetchEditDesignations: (data: any) => dispatch(editDesignations.request({ data })),
})

export default connect(null, mapDispatchToProps)(DiaglogForm)
