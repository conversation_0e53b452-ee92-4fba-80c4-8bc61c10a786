import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Grid,
  IconButton,
  <PERSON><PERSON>ation,
  Tooltip,
  Typography,
} from '@mui/material'
import { ReactComponent as EditIcon } from '../../../assets/images/editIcon.svg'
import { ReactComponent as DeleteIcon } from '../../../assets/images/deleteIcon.svg'
import { useEffect, useState } from 'react'
import { CardsPropType, FormValues, deleteProjectCustomerType } from './ProjectCustomersTypes'
import { connect } from 'react-redux'
import { projectManagementUI } from '../../../reducers'
import { RootState } from '../../../configureStore'
import { deleteMandateType, deleteProjectCustomer, editDesignations } from '../../../actions'
import { Dispatch } from 'redux'
import ShowEntries from './ShowEntries'
import { NoCards } from './NoCards'
import { style } from './projectCustomersStyle'
import ConfirmDeleteDialog from './ConfirmDeleteDialog'
import Loader from '../../Common/Loader'

const Cards = ({
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  getEmpDesignations,
  isEmpDesignationLoader,
  deleteProjectCustomer,
  setEditCustomerData,
  setopenCustomerEditForm,
  fetchEditDesignations,
}: any) => {
  const [filteredRows, setFilteredRows] = useState<FormValues[]>()
  const [actionDialog, setActionDialog] = useState(false)
  const diaglogTitle: string = 'Delete Mandate'
  const [isDeleted, setIsDeleted] = useState(false)
  const [forRowDeleteId, setForRowDeleteId] = useState(0)
  const [overflowingTexts, setOverflowingTexts] = useState<Record<number, boolean>>({})

  useEffect(() => {
    setFilteredRows(getEmpDesignations?.data)
  })

  const deleteOpenConfirmationFunction = (rowId: number, isDeleted: boolean) => {
    fetchEditDesignations({ id: rowId, is_delete: isDeleted ? 1 : 0 })
  }

  const handleChangePage = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  useEffect(() => {
    const updatedOverflowingTexts: Record<number, boolean> = {}
    filteredRows?.forEach((row) => {
      const element = document.getElementById(`customer-name-${row.id}`)
      if (element) {
        updatedOverflowingTexts[row.id] = element.scrollWidth > element.clientWidth
      }
    })
    setOverflowingTexts(updatedOverflowingTexts)
  }, [filteredRows])

  const UserRole = localStorage.getItem('roles')
  const isDisabled = !UserRole?.includes('Admin')

  const styles = {
    iconButtonStyles: {
      opacity: isDisabled ? 0.5 : 1,
    },
  }

  return (
    <>
      {isEmpDesignationLoader ? (
        <Loader state={isEmpDesignationLoader} />
      ) : (
        <Box sx={style.cardBox}>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 2 }}>
            {!filteredRows?.length ? (
              <NoCards />
            ) : (
              filteredRows?.map((row: any) => (
                <Grid item xs={12} sm={6} md={4} key={`${row.id}${row.mandate_name}`}>
                  <Card sx={style.cardStyle}>
                    <CardContent sx={style.cardContentStyle}>
                      <Box sx={style.cardContentBoxStyle}>
                        <Box width='70%'>
                          {overflowingTexts[row.id] ? (
                            <Tooltip
                              title={<Typography fontSize='1rem'> {row.mandate_name} </Typography>}
                            >
                              <Typography sx={style.cardContentTypoStyle}>
                                {`${row.grade_name}-${row.desc}`}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <Typography
                              id={`customer-name-${row.id}`}
                              noWrap
                              sx={style.cardContentTypoStyle}
                            >
                              {`${row.grade_name}-${row.desc}`}
                            </Typography>
                          )}
                        </Box>
                        <Box sx={style.iconBoxStyle}>
                          <Box sx={style.editIconsStyle}>
                            <IconButton
                              onClick={() => {
                                setEditCustomerData({
                                  id: row.id,
                                  job_level_code: row && row.grade_name,
                                  job_level_name: row && row.desc,
                                })
                                if (actionDialog === false) {
                                  setopenCustomerEditForm(true)
                                }
                              }}
                            >
                              <Tooltip title="Edit" arrow>
                              <EditIcon />
                              </Tooltip>
                            </IconButton>
                          </Box>
                          <Box sx={style.iconsStyle}>
                            <Tooltip title="Delete" arrow>
                            <IconButton
                              onClick={() => {
                                deleteOpenConfirmationFunction(row.id, true)
                              }}
                              sx={styles.iconButtonStyles}
                            >
                              <DeleteIcon />
                            </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>
      )}
      {filteredRows && filteredRows?.length > 0 && (
        <Box sx={style.selectAndPaginationBox}>
          <Box>
            <ShowEntries rowsPerPage={rowsPerPage} setRowsPerPage={setRowsPerPage} />
          </Box>
          <Pagination
            count={Math.ceil(getEmpDesignations?.count / rowsPerPage)}
            page={page}
            onChange={handleChangePage}
            color='primary'
          />
        </Box>
      )}
    </>
  )
}

const mapStateToProp = (state: RootState) => {
  return {}
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    deleteProjectCustomer: (data: deleteProjectCustomerType) =>
      dispatch(deleteMandateType.request(data)),
    fetchEditDesignations: (data: any) => dispatch(editDesignations.request({ data })),
  }
}
export default connect(mapStateToProp, mapDispatchToProp)(Cards)
