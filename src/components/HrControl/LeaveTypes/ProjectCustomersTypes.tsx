import { FormikHelpers } from 'formik'

export type FormValues = {
  id: number
  job_level_code: string
  job_level_name: string
}

export type DiaglogFormPropsType = {
  openCustomerForm: boolean
  setOpenCustomerForm: (value: boolean) => void
  openCustomerEditForm: boolean
  setopenCustomerEditForm: (value: boolean) => void
  createEmpDesignations: (data: FormValues) => void
  editCustomerData: FormValues
  updateProjectCustomer: (data: any) => void
}

export type DesignationPropType = {
  fetchEmpDesignations: (data: any) => void
  getEmpDesignations: any
  isEmpDesignationDone: boolean
  isEmpDesignationLoader: boolean
  isCreateDesignationLoader: boolean
  isCreateDesignationDone: boolean
}

export type FormikFormPropType = {
  initialValues: FormValues
  onSubmit: (values: FormValues, actions: FormikHelpers<FormValues>) => void
  handleClickCloseDiaglogForm: () => void
  openCustomerEditForm: boolean
}

export type getProjectCustomersType = {
  data: {
    limit: number
    search: string
    page: number
  }
}

export type deleteProjectCustomerType = {
  data: {
    id: number
  }
}

export type TableBodyRowPropType = {
  row: FormValues
  setEditCustomerData: (data: FormValues) => void
  setopenCustomerEditForm: (value: boolean) => void
  deleteProjectCustomer: (data: deleteProjectCustomerType) => void
}

export type ProjectCustomersSearchProps = {
  searchQuery: string
  setSearchQuery: (value: string) => void
}

export type ProjectCustomersEntriesProp = {
  rowsPerPage: number
  setRowsPerPage: (value: number) => void
}

export type AddButtonPropType = {
  setOpenCustomerForm: (open: boolean) => void
}

export type CardsPropType = {
  getEmpDesignations: any
  rowsPerPage: number
  setRowsPerPage: (value: number) => void
  searchQuery: string
  setopenCustomerEditForm: (value: boolean) => void
  setEditCustomerData: (data: FormValues) => void
  page: number
  setPage: (value: number) => void
  isGettingingProjectCustomers: boolean
  deleteProjectCustomer: (data: deleteProjectCustomerType) => void
}
