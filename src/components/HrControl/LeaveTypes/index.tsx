import { Box, CircularProgress, Dialog } from '@mui/material'
import { useEffect, useState } from 'react'
import {
  createEmpDesignations,
  createLeaveType,
  createQualification,
  editDesignations,
  fetchEmpDesignations,
  fetchLeaveTypeData,
  fetchMandateType,
  fetchProjectCustomersList,
  fetchQualification,
  fetchQualificationSkill,
  getLeaveCategoryData,
  updateMandateType,
} from '../../../actions/index'
import { connect } from 'react-redux'
import styles from '../../../utils/styles.json'
import DiaglogForm from './DiaglogForm'
import AddCustomerButton from './AddCustomerButton'
import SearchBox from './SearchBox'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { FormValues, DesignationPropType, getProjectCustomersType } from './ProjectCustomersTypes'
import Cards from './Cards'
import {
  employeePortalEntity,
  fetchEmpHrControlEntities,
  fetchEmpHrControlUI,
  projectManagementEntity,
  projectManagementUI,
} from '../../../reducers'
import { HeaderHeading } from '../../Common/CommonStyles'
import { ArrowBack } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import AddLeaves from '../AddLeaves/AddLeaves'

const style = {
  customBox: {
    overflow: 'auto',
    fontFamily: 'Montserrat-Bold',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    background: 'white',
    opacity: '1',
    textAlign: 'left',
    margin: '20px',
    padding: '10px 25px 25px 25px',
    borderRadius: '10px',
    width: '93%',
    minHeight: '69vh',
    border: '1px solid #DDDDDD',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
    '@media (max-width: 768px)': {
      width: '100%',
      padding: '15px',
    },
  },
}

const LeaveTypes = ({
  fetchLeaveTypeData,
  leaveTypesData,
  getLeaveCategoryData,
  qualificationData,
  getCategoryData,
  isFetchLeaveTypesDone,
  createLeaveTypeReset,
  createLeaveTypeAPICall,
  isFetchLeaveTypesLoader,

  isAddLeaveTypesLoader,
  isAddLeaveTypesDone,
  getCreateLeaveTypes,
}: any) => {
  const navigate = useNavigate()
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredQualificationSkill, setFilteredQualificationSkill] = useState([])
  const [rowsPerPage, setRowsPerPage] = useState(100)
  const [page, setPage] = useState(1)
  const [open, setOpen] = useState({ openStatus: false, isEdit: 0, qualifications: {} })

  useEffect(() => {
    getLeaveCategoryData()
    fetchLeaveTypeData({ page: page, searchQuery: searchQuery })
  }, [])

  useEffect(() => {
    if (isAddLeaveTypesDone) {
      toast.success(getCreateLeaveTypes?.message)
      fetchLeaveTypeData({ page: page, searchQuery: searchQuery })
      createLeaveTypeReset()
    }
  }, [isAddLeaveTypesDone])

  // Filter qualification skills based on the search query
  useEffect(() => {
    const filtered = leaveTypesData?.data?.filter((item: any) =>
      item.leave_type_name.toLowerCase().includes(searchQuery.toLowerCase()),
    )
    setFilteredQualificationSkill(filtered)
  }, [searchQuery, leaveTypesData])

  return (
    <>
      <Box sx={style.customBox}>
        {/* <AddLeaves /> */}
        <HeaderHeading>Leave Type</HeaderHeading>
        <Box sx={{ textAlign: 'left' }}>
          <Box
            onClick={() => navigate(-1)}
            sx={{
              float: 'right',
              cursor: 'pointer',
              marginTop: '-35px',
            }}
          >
            <ArrowBack />
          </Box>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <SearchBox searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
          <AddCustomerButton setOpen={setOpen} />
        </Box>
        <DiaglogForm
          setOpen={setOpen}
          open={open}
          getCategoryData={getCategoryData}
          qualificationData={qualificationData}
          createLeaveType={createLeaveTypeAPICall}
          isFetchLeaveTypesDone={isFetchLeaveTypesDone}
          isFetchLeaveTypesLoader={isFetchLeaveTypesLoader}
          isAddLeaveTypesDone={isAddLeaveTypesDone}
        />
        <Box marginTop='5px'>
          <Cards
            isFetchLeaveTypesLoader={isFetchLeaveTypesLoader}
            page={page}
            setPage={setPage}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
            searchQuery={searchQuery}
            leaveTypesData={filteredQualificationSkill}
            qualificationData={qualificationData}
            setOpen={setOpen}
            createLeaveType={createLeaveTypeAPICall}
            isAddLeaveTypesDone={isAddLeaveTypesDone}
          />
        </Box>
      </Box>
    </>
  )
}

const mapStateToProp = (state: RootState) => ({
  leaveTypesData: fetchEmpHrControlEntities.fetchHrControlData(state).getLeaveTypes,

  qualificationData: employeePortalEntity.getEmployeePortal(state).getQualification,
  getCreateLeaveTypes: fetchEmpHrControlEntities.fetchHrControlData(state).getCreateLeaveTypes,
  getCategoryData: fetchEmpHrControlEntities.fetchHrControlData(state).getCategoryData,
  isFetchLeaveTypesLoader: fetchEmpHrControlUI.fetchHrControlData(state).isFetchLeaveTypesLoader,
  isFetchLeaveTypesDone: fetchEmpHrControlUI.fetchHrControlData(state).isFetchLeaveTypesDone,

  isAddLeaveTypesLoader: fetchEmpHrControlUI.fetchHrControlData(state).isAddLeaveTypesLoader,
  isAddLeaveTypesDone: fetchEmpHrControlUI.fetchHrControlData(state).isAddLeaveTypesDone,
})

const mapDispatchToProp = (dispatch: Dispatch) => ({
  fetchLeaveTypeData: (data: any) => dispatch(fetchLeaveTypeData.request(data)),
  getLeaveCategoryData: () => dispatch(getLeaveCategoryData.request()),
  createLeaveTypeAPICall: (data: any) => dispatch(createLeaveType.request(data)),

  createQualificationAPI: (data: any) => dispatch(createQualification.request(data)),
  createLeaveTypeReset: () => dispatch(createLeaveType.reset()),
})

export default connect(mapStateToProp, mapDispatchToProp)(LeaveTypes)
