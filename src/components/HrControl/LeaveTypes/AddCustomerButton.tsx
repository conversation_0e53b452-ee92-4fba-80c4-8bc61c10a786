import { Button, styled } from '@mui/material'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import { style } from './projectCustomersStyle'
import { AddButtonPropType } from './ProjectCustomersTypes'
import styles from '../../../utils/styles.json'

const ActionButton = styled(Button)(() => ({
  fontSize: '13px',
  height: '42px',
  float: 'right',
  margin: '0',
  borderRadius: '20px',
  padding: '5px 15px',
  fontFamily: styles.FONT_MEDIUM,
}))

export const AddCustomerButton = ({ setOpen }: any) => {
  const handleClickOpenDiaglogForm = () => {
    localStorage.setItem('qualification', 'create')
    setOpen({ openStatus: true, isEdit: 0, qualifications: {} })
  }
  return (
    <>
      <ActionButton
        variant='outlined'
        startIcon={<AddTwoToneIcon sx={style.AddTwoToneIconStyle} />}
        onClick={handleClickOpenDiaglogForm}
      >
        ADD LEAVE TYPE
      </ActionButton>
    </>
  )
}

export default AddCustomerButton
