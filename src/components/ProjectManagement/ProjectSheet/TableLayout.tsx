import {
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import TableRowContainer from './TableRowContainer'
import { style } from './SheetStyles'
import { StyledTableCell } from '../../Common/CommonStyles'

const getColumnData = [
  { id: '1', label: 'Project Name' },
  { id: '2', label: 'Clients' },
  { id: '3', label: 'Source' },
  { id: '4', label: 'Domain' },
  { id: '5', label: 'Location' },
  { id: '6', label: 'Type' },
  { id: '7', label: 'Status' },
  { id: '8', label: 'Start Date' },
  { id: '9', label: 'End Date' },
  { id: '10', label: 'Billable Dev' },
  { id: '11', label: 'Billable QA' },
  { id: '12', label: 'Billable BA' },
  { id: '13', label: 'Billable Designer' },
  { id: '14', label: 'Billable Lead' },
  { id: '15', label: 'Billable Reviewer' },
  { id: '16', label: 'Total Billable' },
]

const TableLayout = (props: { rowsToDisplay: Object[] }) => {
  const { rowsToDisplay } = props
  return (
    <Box overflow='scroll' width='100%' margin='10px  0px' sx={style.table}>
      <TableContainer component={Paper} id='export' sx={style.tableContainer}>
        <Table>
          <TableHead id='head'>
            <TableRow>
              {getColumnData?.map((column: any) => (
                <StyledTableCell sx={{padding:'6px'}} key={column.id}>{column.label}</StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rowsToDisplay?.length > 0 &&
              rowsToDisplay?.map((row, idx) => <TableRowContainer key={idx} row={row} idx={idx} />)}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  )
}

export default TableLayout
