import * as React from 'react'
import Button from '@mui/material/Button'
import { ReactComponent as ExportToPDFIcon } from '../../../../assets/images/exportToPDF.svg'
import { ReactComponent as ExportToExcelIcon } from '../../../../assets/images/exportToExcel.svg'
import { ReactComponent as ExportToPrintScreenIcon } from '../../../../assets/images/printScreen.svg'
import { Box } from '@mui/material'
import { exportToExcel } from './ExportAsExcel'
import { exportAsPDF } from './ExportAsPdf'


const style = {
    
  buttonStyle: {
    fontFamily: 'Montserrat-Regular',
    fontSize: '1rem',
    padding: '0',
    width: '35px',
    height: '35px',
    minWidth: 'fit-content',
    borderRadius: '50%',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'white',
    },
    '@media only screen and (max-width:780px)': {
      fontSize: '0.9rem',
    },
  },
  container:{
    '@media only screen and (max-width:1230px)': {
      width:"200px"
    },
  }
}

const ExportButton = () => {
  
  const printTable = () => {
    window.print()
  }

  return (
    <Box display='flex' justifyContent='space-evenly' alignItems='center' width='160px' sx={style.container}>
      <Box>
        <Button
          onClick={() => {
            exportAsPDF()
          }}
          sx={style.buttonStyle}
          variant='outlined'
        >
          <ExportToPDFIcon />
        </Button>
      </Box>
      <Box>
        <Button
          onClick={() => {
            exportToExcel()
          }}
          sx={style.buttonStyle}
          variant='outlined'
        >
          <ExportToExcelIcon />
        </Button>
      </Box>
      <Box>
        <Button
          onClick={() => {
            printTable()
          }}
          sx={style.buttonStyle}
          variant='outlined'
        >
          <ExportToPrintScreenIcon />
        </Button>
      </Box>
    </Box>
  )
}

export default ExportButton
