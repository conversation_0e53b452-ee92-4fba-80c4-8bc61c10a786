import { Formik, Form, Field, FieldProps } from 'formik'
import {
  Autocomplete,
  Box,
  Button,
  DialogContent,
  Grid,
  TextField,
  Tooltip,
  Typography,
  styled,
  Popper,
  FormControl,
  OutlinedInput,
  InputLabel,
  MenuItem,
  Checkbox,
  Select,
} from '@mui/material'
import { style } from './projectCustomersStyle'
import { InputField } from '../../Common/ComponentCommonStyles'
import * as Yup from 'yup'
import { useEffect, useState } from 'react'
import { FormikFormPropType } from './ProjectCustomersTypes'
import styles from '../../../utils/styles.json'
import { countryName } from '../../Common/countryName'
import { connect, useSelector } from 'react-redux'
import { RootState } from '../../../configureStore'
import { Dispatch } from 'redux'
import { projectManagementEntity } from '../../../reducers'

export const validationSchema = Yup.object({
  customer_name: Yup.string()
    .required('Client Name is required')
    .test('is-empty-or-whitespace', 'Client Name cannot be empty or whitespace', (value: any) => {
      return value?.trim().length > 0
    }),
})

const stylle = {
  checkBoxItem: { fontSize: '14px', padding: '3px 12px' },
  menuItemStyle: { padding: '3px 16px', fontSize: '14px' },
  autoCompleteStyle: {
    '.MuiInputBase-root': {
      padding: '19px 11px',
      borderRadius: '20px',
      fontSize: '13px',
      fontFamily: styles.FONT_MEDIUM,
      height: '42px',
    },
    '& .MuiFormControl-root': {
      margin: '0',
      marginTop: '5px',
    },
    '& .MuiFormLabel-root ': {
      backgroundColor: 'white',
      width: '165px',
    },
    '.MuiFormLabel-asterisk': {
      color: 'red',
    },
  },
}

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '120px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}))

const StyledSelectField = styled(Select)(({ theme }) => ({
  borderRadius: '20px',
  '& .MuiSelect-select': {
    padding: '9px 11px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '&&.MuiInputBase-root.MuiOutlinedInput-root.MuiSelect-root': {
    borderRadius: '20px',
    padding: '9px 11px',
    marginLeft: '-1px',
  },
}))

const CustomFormControll = styled(FormControl)(() => ({
  marginLeft: '1px',
  marginTop: '5px',
}))
const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
}

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '40px',
  fontFamily: styles.FONT_BOLD,
  width: '120px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const FormikForm = ({
  initialValues,
  onSubmit,
  handleClickCloseDiaglogForm,
  openCustomerEditForm,
  porjectDomainList,
  value,
  setValue,
}: FormikFormPropType) => {
  const [isDisabled, setIsDisabled] = useState(true)
  const [projectDomain, setProjectDomain] = useState<string[]>([])

  useEffect(() => {
    if (openCustomerEditForm && initialValues.customer_name?.trim()) {
      setIsDisabled(false)
    } else {
      setIsDisabled(true)
    }
  }, [openCustomerEditForm, initialValues.customer_name])

  // const handleProjectDomainChange = (event: any) => {
  //   const {
  //     target: { value },
  //   } = event
  //   setFieldValue('project_domain', typeof value === 'string' ? value.split(',') : value)
  // }

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
      validate={(values) => {
        const errors = {}
        if (!values.customer_name || !values.customer_name.trim()) {
          setIsDisabled(true)
        } else {
          setIsDisabled(false)
        }
        return errors
      }}
    >
      {({ values, setFieldValue, errors, touched }) => (
        <Form style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <DialogContent
            dividers
            style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
          >
            <Grid container sx={style.formGridContainerStyle}>
              <Box sx={style.fieldBoxStyle}>
                <Field name='customer_name'>
                  {({ field }: FieldProps<string>) => (
                    <InputField
                      {...field}
                      label='Client Name'
                      variant='outlined'
                      placeholder='Enter Client Name'
                      size='small'
                      error={touched.customer_name && !!errors.customer_name}
                      helperText={
                        touched.customer_name && typeof errors.customer_name === 'string'
                          ? errors.customer_name
                          : ''
                      }
                      inputProps={{
                        autoComplete: 'new-password',
                        form: { autoComplete: 'off' },
                      }}
                    />
                  )}
                </Field>
              </Box>
              <Autocomplete
                autoFocus={false}
                size='small'
                disablePortal={false}
                id='select-working-employees'
                options={countryName}
                getOptionLabel={(option: any) => option?.name ?? ''}
                value={countryName.find((option) => option.code === values.country_code) || null}
                sx={{
                  width: '85%',
                  paddingBottom: '15px',
                  '.MuiInputBase-root': {
                    padding: '10px 11px',
                    borderRadius: '20px',
                    fontSize: '13px',
                    fontFamily: 'Montserrat-Medium',
                    height: '45px',
                  },
                  '& .MuiFormControl-root': {
                    margin: '0',
                    marginTop: '5px',
                  },
                  '& .MuiFormLabel-root ': {
                    backgroundColor: 'white',
                    width: '125px',
                  },
                  '.MuiFormLabel-asterisk': {
                    color: 'white',
                  },
                }}
                renderInput={(params: any) => (
                  <TextField {...params} label='Search Country' required= {false} variant='outlined' />
                )}
                onChange={(event: any, newValue: any) => {
                  if (newValue && newValue?.code) {
                    setFieldValue('country_code', newValue?.code)
                  } else {
                    setFieldValue('country_code', '')
                  }
                }}
                ListboxProps={{
                  style: {
                    maxHeight: '200px',
                  },
                }}
                PopperComponent={(props) => (
                  <Popper
                    {...props}
                    style={{
                      zIndex: 1500,
                      width: props.style?.width,
                      ...props.style,
                    }}
                  />
                )}
              />            
              <Box sx={style.fieldBoxStyle}>
              <CustomFormControll>
                <InputLabel id='demo-multiple-checkbox-label' sx={{ marginTop: -0.7 }}>
                  Project Domain
                </InputLabel>
                <StyledSelectField
                  labelId='demo-multiple-checkbox-label'
                  id='demo-multiple-checkbox'
                  variant='outlined'
                  label='Project Domain'
                  multiple
                  required= {false}
                  input={<OutlinedInput label='Project Domain' />}
                  value={values.project_domain || []}
                  onChange={(event) => {
                    const {
                      target: { value },
                    } = event;
                    setFieldValue(
                      'project_domain',
                      typeof value === 'string' ? value.split(',') : value
                    );
                  }}
                  renderValue={(selected: any) =>
                    porjectDomainList
                      .filter((domain: any) => selected.includes(domain.id))
                      .map((domain: any) => domain.domain_name)
                      .join(', ')
                  }                  
                  // renderValue={(selected: any) => selected.join(', ')}
                  // MenuProps={MenuProps}
                >
                  {porjectDomainList.map((domain: any) => (
                    <MenuItem key={domain.id} value={domain.id} sx={stylle.checkBoxItem}>
                      <Checkbox
                        checked={values.project_domain.includes(domain.id)}
                        size='small'
                        sx={{ padding: '2px 5px 2px 0' }}
                      />
                      {domain.domain_name}
                    </MenuItem>
                  ))}
                </StyledSelectField>
              </CustomFormControll>
              </Box>
            </Grid>
          </DialogContent>
          <Box sx={style.diaglogActionBoxStyle}>
            <CancelButton onClick={handleClickCloseDiaglogForm} autoFocus>
              CANCEL
            </CancelButton>
            <ActionButton autoFocus type='submit' disabled={isDisabled}>
              {openCustomerEditForm ? (
                <Typography>Update</Typography>
              ) : (
                <Typography>Create</Typography>
              )}
            </ActionButton>
          </Box>
        </Form>
      )}
    </Formik>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    porjectDomainList: projectManagementEntity.getAllProjectManagement(state).fetchProjectDomain,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {

  }
}

export default connect(mapStateToProps, mapDispatchToProps)(FormikForm)
