import { Box } from '@mui/material'
import { useEffect, useState } from 'react'
import { fetchMandateType } from '../../../actions/index'
import { connect } from 'react-redux'
import { style } from './projectCustomersStyle'
import DiaglogForm from './DiaglogForm'
import AddCustomerButton from './AddCustomerButton'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { FormValues, ProjectCustomersPropType } from './ProjectCustomersTypes'
import Cards from './Cards'
import { projectManagementEntity, projectManagementUI } from '../../../reducers'
import DebouncedSearchedBox from '../../Common/DebouncedSearchBox'

const Mandate = ({
  getMandateType,
  projectCustomersData,
  isProjectCustomersCreated,
  isProjectCustomerDeleted,
  isProjectCustomerUpdated,
}: ProjectCustomersPropType) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [rowsPerPage, setRowsPerPage] = useState(100)
  const [page, setPage] = useState(1)
  const [openCustomerForm, setOpenCustomerForm] = useState(false)
  const [openCustomerEditForm, setopenCustomerEditForm] = useState(false)
  const [editCustomerData, setEditCustomerData] = useState<FormValues>({
    id: 0,
    mandate_name: '',
  })

  useEffect(() => {
    getMandateType({
      data: { pageSize: rowsPerPage, search: searchQuery, page: page },
    })
  }, [
    searchQuery,
    rowsPerPage,
    page,
    isProjectCustomersCreated,
    isProjectCustomerUpdated,
    isProjectCustomerDeleted,
  ])

  useEffect(() => {
    setPage(1)
  }, [rowsPerPage, searchQuery])

  return (
    <>
      <Box sx={style.customBox}>
        <Box>
          <DebouncedSearchedBox placeHolder='Search Manadate' setSearchQuery={setSearchQuery} />
        </Box>
        <Box>
          <AddCustomerButton setOpenCustomerForm={setOpenCustomerForm} />
        </Box>
      </Box>
      <DiaglogForm
        setOpenCustomerForm={setOpenCustomerForm}
        openCustomerForm={openCustomerForm}
        openCustomerEditForm={openCustomerEditForm}
        setopenCustomerEditForm={setopenCustomerEditForm}
        editCustomerData={editCustomerData}
      />
      <Box>
        <Cards
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          searchQuery={searchQuery}
          projectCustomersData={projectCustomersData}
          setopenCustomerEditForm={setopenCustomerEditForm}
          setEditCustomerData={setEditCustomerData}
        />
      </Box>
    </>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    projectCustomersData: projectManagementEntity.getAllProjectManagement(state).getAllMandate,
    isProjectCustomersCreated: projectManagementUI.getProjectManagemet(state).isMandateTypeCreated,
    isProjectCustomerDeleted: projectManagementUI.getProjectManagemet(state).isMandateTypeDeleted,
    isProjectCustomerUpdated: projectManagementUI.getProjectManagemet(state).isMandateTypeUpdated,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    getMandateType: (data: any) => dispatch(fetchMandateType.request(data)),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(Mandate)
