import {
  Box,
  Typography,
  Autocomplete,
  TextField,
  Button,
  CircularProgress,
  Dialog,
} from '@mui/material'
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import dayjs, { Dayjs } from 'dayjs'
import { desgnationGraphData, designationList } from '../../../actions'
import { useEffect, useState } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { RootState } from '../../../configureStore'
import { projectManagementEntity, projectManagementUI } from '../../../reducers'
import * as echarts from 'echarts'
import { loaderProps } from '../../Common/CommonStyles'
import styles from '../../../utils/styles.json'
import Loader from '../../Common/Loader'

const style = {
  autoCompleteStyle: {
    width: '200px',
    marginTop: '-8px',
    '.MuiInputBase-root': {
      borderRadius: '20px',
      fontSize: '13px',
      fontFamily: styles.FONT_MEDIUM,
      height: '42px',
    },
    '& .MuiFormControl-root': {
      margin: '0',
    },
    '& .MuiFormLabel-root ': {
      backgroundColor: 'white',
      width: '110px',
    },
    '.MuiFormLabel-asterisk': {
      color: 'red',
    },
  },
  graphContainer: {
    border: '1px solid #ebebeb',
    paddingTop: '10px',
    paddingBottom: '20px',
    paddingRight: '10px',
    borderRadius: '10px',
    boxShadow: ' rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px',
  },
  resultBtnStyle: {
    fontSize: '13px',
    letterSpacing: '0px',
    borderRadius: '20px',
    padding: '5px 20px',
    height: '40px',
    whiteSpace: 'nowrap',
    fontFamily: styles.FONT_MEDIUM,
    marginTop: '-10px',
  },
  datePickerStyle: {
    marginTop: '0',
    '.MuiFormLabel-asterisk': {
      color: 'red',
    },
    '& .MuiOutlinedInput-root': {
      borderRadius: '20px',
      height: '40px',
    },
    '& .MuiInputLabel-root': {
      fontSize: '14px',
    },
    '& .MuiPickersDay-dayDisabled': {
      cursor: 'not-allowed',
    },
    width: '150px!important',
  },
}

const DesignationWiseBarGraph = (props: any) => {
  const {
    selectedTab,
    designationList,
    fetchDesignationList,
    fetchDesignationGraphData,
    designationGraphData,
    isGettingDesignationGraphData,
  } = props
  const [designation, setDesignation] = useState('Director')
  const [designationStartDate, setDesignationStartDate] = useState<Dayjs>(dayjs().startOf('month'))
  const [designationEndDate, setDesignationEndDate] = useState<Dayjs | null>(dayjs().endOf('month'))

  useEffect(() => {
    if (selectedTab === 4) {
      fetchDesignationList()
      fetchDesignationGraphData({
        designation: designation,
        startDate: designationStartDate?.format('YYYY-MM-DD'),
        endDate: designationEndDate?.format('YYYY-MM-DD'),
      })
    }
  }, [selectedTab])

  useEffect(() => {
    if (selectedTab === 4) {
      var designationChartBlock = document.getElementById('designationGraph')
      var designationBarChart = echarts.init(designationChartBlock)

      const designationDatesData = designationGraphData?.map(
        (entry: { date: string | number | Date }) => new Date(entry.date).toLocaleDateString(),
      )
      const totalSum: number[] = []

      designationGraphData?.forEach((data: any, index: number) => {
        totalSum[index] =
          data?.data?.billable?.count +
          data?.data?.nonbillable?.count +
          data?.data?.additional?.count +
          data?.data?.free?.count
      })

      let designationGraphOption = {
        title: {
          show: !isGettingDesignationGraphData && designationGraphData.length === 0,
          textStyle: {
            color: '#EE6666',
            fontSize: 30,
          },
          text: 'No Data To Display',
          left: 'center',
          top: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
          formatter: function (value: any, params: number) {
            return (
              'Date: ' +
              value[0].name +
              `<br />Designation: <span style=\"display:inline-blocck;font-weight:600;\">${designation}</span>` +
              '<br />' +
              value[0].marker +
              `<span style=\"display:inline-block;margin-left:5px;width:100px;\">${value[0].seriesName}</span>` +
              '<span style="display:inline-block;font-weight:600;">' +
              value[0].value +
              ' employees (' +
              designationGraphData[value[0].dataIndex]?.data?.billable?.percentage +
              '%)</span>' +
              '<br />' +
              value[1].marker +
              `<span style=\"display:inline-block;margin-left:5px;width:100px;\">${value[1].seriesName.replace(
                ' ',
                '-',
              )}</span>` +
              '<span style="display:inline-block;font-weight:600;">' +
              value[1].value +
              ' employees (' +
              designationGraphData[value[1].dataIndex]?.data?.nonbillable?.percentage +
              '%)</span>' +
              '<br />' +
              value[2].marker +
              `<span style=\"display:inline-block;margin-left:5px;width:100px;\">${value[2].seriesName.replace(
                ' ',
                '-',
              )}</span>` +
              '<span style="display:inline-block;font-weight:600;">' +
              value[2].value +
              ' employees (' +
              designationGraphData[value[2].dataIndex]?.data?.free?.percentage +
              '%)</span>' +
              '<br />' +
              value[3].marker +
              `<span style=\"display:inline-block;margin-left:5px;width:100px;\">${value[3].seriesName.replace(
                ' ',
                '-',
              )}</span>` +
              '<span style="display:inline-block;font-weight:600;">' +
              value[3].value +
              ' employees (' +
              designationGraphData[value[3].dataIndex]?.data?.additional?.percentage +
              '%)</span>'
            )
          },
        },
        legend: {
          selectedMode: false,
        },
        yAxis: {
          show: designationGraphData.length > 0,
          type: 'value',
          min: 0,
          name: "Employee's Count",
          nameRotate: 90,
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            fontWeight: '500',
            fontSize: '16px',
            color: styles.PRIMARY_COLOR,
          },
        },
        xAxis: {
          show: designationGraphData.length > 0,
          type: 'category',
          data: designationDatesData,
          axisPointer: {
            type: 'shadow',
          },
        },
        series: [
          {
            name: 'Billable',
            type: 'bar',
            stack: 'total',
            barWidth: '60%',
            label: {
              show: true,
              formatter: (params: any) => (params.value <= 0 ? '' : params.value),
            },
            data: designationGraphData?.map((data: any) => data?.data?.billable?.count),
          },
          {
            name: 'Non Billable',
            type: 'bar',
            stack: 'total',
            barWidth: '60%',
            label: {
              show: true,
              formatter: (params: any) => (params.value <= 0 ? '' : params.value),
            },
            data: designationGraphData?.map((data: any) => data?.data?.nonbillable?.count),
          },
          {
            name: 'Free',
            type: 'bar',
            stack: 'total',
            barWidth: '60%',
            label: {
              show: true,
              formatter: (params: any) => (params.value <= 0 ? '' : params.value),
            },
            data: designationGraphData?.map((data: any) => data?.data?.free?.count),
          },
          {
            name: 'Additional',
            type: 'bar',
            stack: 'total',
            barWidth: '60%',
            label: {
              show: true,
              formatter: (params: any) => (params.value <= 0 ? '' : params.value),
            },
            data: designationGraphData?.map((data: any) => data?.data?.additional?.count),
          },
        ],
      }

      designationBarChart.setOption(designationGraphOption)

      return () => {
        designationBarChart.dispose()
      }
    }
  }, [selectedTab, designationGraphData])

  const handleShowResult = (type: string) => {
    switch (type) {
      case 'employeeGraph':
        return
      case 'designationGraph':
        return (
          designation &&
          fetchDesignationGraphData({
            designation: designation,
            startDate: designationStartDate?.format('YYYY-MM-DD'),
            endDate: designationEndDate?.format('YYYY-MM-DD'),
          })
        )
    }
  }
  return (
    <Box sx={{ ...style.graphContainer, marginTop: '20px' }}>

      <Loader state={isGettingDesignationGraphData} />

      <Box display='flex' alignItems='center' justifyContent='flex-end' columnGap='12px'>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DemoContainer components={['DatePicker']}>
            <Box>
              <DatePicker
                sx={style.datePickerStyle}
                label='Start Date'
                format='MM-DD-YYYY'
                value={designationStartDate}
                onChange={(date: Dayjs | null) =>
                  setDesignationStartDate((prev: Dayjs) => date ?? prev)
                }
                shouldDisableDate={(date) =>
                  designationEndDate ? date.isAfter(designationEndDate, 'day') : false
                }
              />
            </Box>
          </DemoContainer>
        </LocalizationProvider>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DemoContainer components={['DatePicker']}>
            <Box>
              <DatePicker
                sx={style.datePickerStyle}
                label='End Date'
                format='MM-DD-YYYY'
                value={designationEndDate}
                onChange={(date: Dayjs | null) => setDesignationEndDate(date)}
                shouldDisableDate={(date) =>
                  designationStartDate ? date.isBefore(designationStartDate, 'day') : false
                }
              />
            </Box>
          </DemoContainer>
        </LocalizationProvider>
        <Box>
          <Autocomplete
            size='small'
            disablePortal
            id='designation'
            options={designationList.map((data: { desc: string }) => data.desc)}
            sx={style.autoCompleteStyle}
            renderInput={(params) => <TextField {...params} label='Designation' />}
            value={designation}
            onChange={(event: any, value: any) => {
              setDesignation(value)
            }}
          />
        </Box>
        <Button
          sx={style.resultBtnStyle}
          onClick={() => handleShowResult('designationGraph')}
          color='primary'
        >
          SHOW RESULT
        </Button>
      </Box>
      <Box
        id='designationGraph'
        style={{ width: 'auto', height: '400px', marginLeft: '0px'}}
      ></Box>
      <Box textAlign='center' marginTop='-30px'>
        <Typography fontSize='20px' fontFamily={styles.FONT_BOLD} color={styles.PRIMARY_COLOR}>
          Designation-Wise Employee's Count
        </Typography>
      </Box>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    designationList: projectManagementEntity.getAllProjectManagement(state).fetchDesignationList,
    designationGraphData:
      projectManagementEntity.getAllProjectManagement(state).fetchDesignationGraphData,
    isGettingDesignationGraphData:
      projectManagementUI.getProjectManagemet(state).isGettingDesignationGraphData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchDesignationGraphData: (data: any) => dispatch(desgnationGraphData.request({ data })),
    fetchDesignationList: () => dispatch(designationList.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(DesignationWiseBarGraph)
