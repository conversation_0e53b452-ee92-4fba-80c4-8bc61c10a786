import {
  Box,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import { connect } from 'react-redux'
import { RootState } from '../../configureStore'
import {  projectManagementEntity, projectManagementUI } from '../../reducers'
import { Dispatch } from 'redux'
import { FC, useEffect } from 'react'
import { StyledTableCell } from '../Common/CommonStyles'
import { fetchDesignations } from '../../actions'
import Loader from '../Common/Loader'

type DesginationType = {
  designationData: [{ designation: string; count: number }]
  fetchDesignationData: () => void
  isGettingDesignationData: boolean
}

const style = {
  tableRow: {
    boxShadow: '0px 5px 3px #6c6c6c10',
  },
}

const Designation: FC<DesginationType> = ({
  designationData,
  fetchDesignationData,
  isGettingDesignationData,
}) => {
  
  useEffect(() => {
    fetchDesignationData()
  }, [])

  return !isGettingDesignationData ? (
    <Loader state={!isGettingDesignationData} />

  ) : (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow sx={style.tableRow}>
            <StyledTableCell>Designation</StyledTableCell>
            <StyledTableCell>Count</StyledTableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {designationData.map((value) => {
            return (
              <TableRow sx={style.tableRow} key={value.designation}>
                <StyledTableCell>{value.designation}</StyledTableCell>
                <StyledTableCell>{value.count}</StyledTableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </TableContainer>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    designationData:  projectManagementEntity.getAllProjectManagement(state).getOrganizationDesignations,
    isGettingDesignationData: projectManagementUI.getProjectManagemet(state).isGettingDesignationState,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchDesignationData: () => dispatch(fetchDesignations.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Designation)
