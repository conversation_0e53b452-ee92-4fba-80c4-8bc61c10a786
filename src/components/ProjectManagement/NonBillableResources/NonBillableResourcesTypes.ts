export type NonBillableDataType ={
  id: number
  name: string
  checked: boolean
}

export type NonBillableProps ={
  fetchNonBillableResources: () => void
  updateNonBillableResources: (data: { Employee_id: number[] }) => void
  nonBillableResourceData: { response:  NonBillableDataType[]}
  isGetNonBillableData: boolean
  isUpdatedNonBillableResources: boolean
}

export type FilteredItemListPropsType ={
  items: NonBillableDataType[]
  handleCheckboxChange: (id: number) => void
  handleSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export type AlphabetPropType ={
  selectedLetter:string
  handleFilterByLetter:(letter:string)=>void
}