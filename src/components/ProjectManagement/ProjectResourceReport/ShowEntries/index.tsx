import * as React from 'react'
import MenuItem from '@mui/material/MenuItem'
import { Box, FormControl, Select, Typography } from '@mui/material'
import { useState } from 'react'
import style from '../../../../utils/styles.json'

type EnteriesPropType = {
  setRowsPerPage: React.Dispatch<React.SetStateAction<number>>
  setPage: React.Dispatch<React.SetStateAction<number>>
}
const styling = {
  selectComponent: {
    width: '5rem',
    fontFamily: style.FONT_MEDIUM,
    height: '35px',
    borderRadius: '20px',
    textAlign: 'center',
    marginLeft: 1,
    '@media (max-width: 1180px)': {
      width: '90px'
    }
  }
}
const ShowEnteries = (props: EnteriesPropType) => {
  const [selectedValue, setSelectedValue] = useState<number>(100);
  const handleClose = (e: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
    const htmlEle = e.target as HTMLElement
    props.setRowsPerPage(parseInt(htmlEle.innerText) ? parseInt(htmlEle.innerText) : 100)
    const root = document.getElementById('root')
    if (root) {
      root.style.position = 'static'
      root.style.height = '100%'
    }
  }
  const handleChange = (event: any) => {
    setSelectedValue(event.target.value as number);
    props.setPage(1)
  };
  return (
    <Box width="250px" display="flex" alignItems="center" justifyContent="center">
      <Box>
        <Typography fontFamily={style.FONT_MEDIUM} color={'#353665'} fontSize={'0.975rem'}>
          Show Entries:
        </Typography>
      </Box>
      <Box>
        <FormControl >
          <Select sx={styling.selectComponent}
            value={selectedValue}
            onChange={handleChange}
            displayEmpty
          >
            <MenuItem value = '100' onClick={handleClose} disableRipple>
              100
            </MenuItem>
            <MenuItem value = '200' disableRipple>
              200
            </MenuItem>
            <MenuItem value = '300' onClick={handleClose} disableRipple>
              300
            </MenuItem>
            <MenuItem value = '400' onClick={handleClose} disableRipple>
              400
            </MenuItem>
            <MenuItem value = '500' onClick={handleClose} disableRipple>
              500
            </MenuItem>
          </Select>
        </FormControl>
      </Box>
    </Box>
  )
}
export default ShowEnteries
