import {
  Autocomplete,
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { formLabel, textFileds } from '../CommontStyleForResource'
import { Dispatch } from 'redux'
import { RootState } from '../../../../configureStore'
import { projectManagementEntity } from '../../../../reducers'
import {
  fetchProjectLocation,
  getAllResourceReport,
  getAllResourceReportDropdownData,
  projectSource,
} from '../../../../actions'
import { connect, useSelector } from 'react-redux'
import style from '../../../../utils/styles.json'

export const styleForMenuItem = {
  fontSize: '0.950rem',
  fontFamily: style.FONT_MEDIUM,
}

const mainContainer = {
  display: 'flex',
  width: '100%',
  height: 'fit-content',
  justifyContent: 'space-between',
}

const innerMainContainer = {
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  padding: '0 1%',
  gap: 0,
  '@media (max-width: 1067px)': {
    width: '60%',
    height: 'fit-content',
    gap: '15px',
  },
  '@media (max-width: 893px)': {
    width: '70%',
  },
  '@media (max-width: 770px)': {
    width: '80%',
  },
  '@media (max-width: 710px)': {
    width: '95%',
  },
}

function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  } as T
}

interface PropValue {
  setProjectState: any
  setLocation: any
  setEmploymentType: any
  setPage: (data: number) => void
  selectData: any
  selectForSource: string
  projectState: string
  location: string
  employmentType: string
  setSelectForSource: any
  fetchProjectLocation: any
  projectLocation: any
  getAllProjectResourceReport: any
  fetchResourceReport: any
  projectResourceDropdownAPI: any
  resourceReportDropdownData: any
  fetchProjectSource: any
}
const ProjectsSelectTab = (props: PropValue) => {
  const {
    selectForSource,
    setSelectForSource,
    setProjectState,
    setPage,
    projectState,
    location,
    employmentType,
    setEmploymentType,
    setLocation,
    projectLocation,
    fetchProjectLocation,
    projectResourceDropdownAPI,
    resourceReportDropdownData,
    fetchProjectSource,
  } = props

  const [searchQuery, setSearchQuery] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [active, setActive] = useState(false)
  const projectSourceList = useSelector(
    (state: any) => state.entities.projectManagement.fetchProjectSource,
  )
  useEffect(() => {
    fetchProjectLocation()
    fetchProjectSource()
  }, [])

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, newInputValue: string) => {
      setSearchQuery((prev) => e?.target?.value ?? prev)
    },
    800,
  )

  useEffect(() => {
    projectResourceDropdownAPI({ search: searchTerm ? searchTerm : 'all' })
  }, [searchQuery])

  const handleProjects = (event: { target: { value: string } }) => {
    setProjectState(event.target.value)
    setPage(1)
  }

  const handleSource = (event: { target: { value: string } }) => {
    setSelectForSource(event.target.value)
    setPage(1)
  }

  const handleEmpType = (event: { target: { value: string } }) => {
    setEmploymentType(event.target.value)
    setPage(1)
  }

  const handleLocation = (event: { target: { value: string } }) => {
    setLocation(event.target.value)
    setPage(1)
  }

  const handleStatus = (event: any) => {
    setActive((pre: any) => !pre)
  }

  return (
    <Box sx={mainContainer}>
      <Box sx={innerMainContainer}>
        <Box display='flex' justifyContent='space-between' marginLeft='6px' alignItems='center'>
          <FormGroup row>
            <FormControlLabel
              control={<Checkbox name={'projectStatus'} checked={active} onChange={handleStatus} />}
              label='Active Projects'
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: '13.7px',
                  fontFamily: style.FONT_MEDIUM,
                },
              }}
            />
          </FormGroup>
          <Autocomplete
            autoFocus={false}
            size='small'
            disablePortal
            clearIcon={null} // Remove the clear (delete) icon
            id='select-projects'
            options={[
              { id: 'all', name: 'All' }, // Add the "All" option here
              ...resourceReportDropdownData?.map(
                (project: { id: string; project_name: string }) => ({
                  id: project.id,
                  name: project.project_name,
                }),
              ),
            ]}
            inputValue={searchTerm}
            onInputChange={(e, newInputValue) => {
              functionDebounce(
                e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
                newInputValue,
              )
              setSearchTerm(newInputValue)
            }}
            getOptionLabel={(option) => option.name || ''}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            renderInput={(params) => <TextField {...params} label='Projects' variant='outlined' />}
            defaultValue={{ id: 'all', name: 'All' }}
            onChange={(event: any, newValue: { id: string; name: string } | null) => {
              setProjectState(newValue?.id || 'all')
            }}
            ListboxProps={{
              style: {
                maxHeight: '150px',
                overflowY: 'auto',
              },
            }}
            sx={{
              '& label.Mui-focused': {
                color: '#666666',
                width: '80px',
                bgcolor: 'white',
              },
              '& .MuiOutlinedInput-root': {
                height: '35px',
                fontSize: '13px',
                '&.Mui-focused fieldset': {
                  borderRadius: '20px',
                },
                '& fieldset': {
                  borderRadius: '20px',
                },

                '& .MuiInputBase-input': {
                  borderRadius: '15px',
                  minWidth: '150px',
                  fontFamily: style.FONT_MEDIUM,
                  letterSpacing: '0',
                },
              },
              svg: {
                color: '#666666',
              },
            }}
          />
        </Box>

        <Box height='100%' display='flex' justifyContent='space-between' alignItems='center'>
          <FormControl sx={textFileds}>
            <InputLabel sx={formLabel}>Project Source</InputLabel>
            <Select
              label='Project Source'
              value={selectForSource === '' ? 'all' : selectForSource}
              onChange={handleSource}
              MenuProps={{ PaperProps: { sx: { maxHeight: '150px', overflowY: 'auto' } } }}
              size='small'
              sx={{ '.MuiFormControl-root': { display: 'none' } }}
            >
              <MenuItem value='all'>{`All`}</MenuItem>
              {projectSourceList.map((source: { id: number; source_name: string }) => {
                return <MenuItem value={`${source.id}`}>{source.source_name}</MenuItem>
              })}
            </Select>
          </FormControl>
        </Box>

        <Box
          width='auto'
          height='100%'
          display='flex'
          justifyContent='space-between'
          alignItems='center'
        >
          <FormControl sx={textFileds}>
            <InputLabel sx={formLabel}>Employment Type</InputLabel>
            <Select
              value={employmentType === '' ? 'all' : employmentType}
              onChange={handleEmpType}
              label='Employment Type'
              MenuProps={{ PaperProps: { sx: { maxHeight: '150px', overflowY: 'auto' } } }}
            >
              <MenuItem value='all'>All</MenuItem>
              <MenuItem value='Billable'>Billable</MenuItem>
              <MenuItem value='Non-Billable'>Non-Billable</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Box
          width='auto'
          height='100%'
          display='flex'
          justifyContent='space-between'
          alignItems='center'
          sx={{
            '@media (max-width: 1020px)': { marginTop: '10px' },
            '@media (max-width: 1010px)': { marginTop: '0px' },
          }}
        >
          <FormControl sx={textFileds}>
            <InputLabel sx={formLabel}>Project Location</InputLabel>
            <Select
              value={location === '' ? 'all' : location}
              onChange={handleLocation}
              label='Project Location'
              MenuProps={{ PaperProps: { sx: { maxHeight: '150px', overflowY: 'auto' } } }}
            >
              <MenuItem value='all'>All</MenuItem>
              {projectLocation?.map((location: any) => (
                <MenuItem key={`${location?.id}`} value={location.id}>
                  {location.location}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    projectLocation: projectManagementEntity.getAllProjectManagement(state).fetchAllProjectLocation,
    fetchResourceReport:
      projectManagementEntity.getAllProjectManagement(state).getResourceReportData,
    resourceReportDropdownData:
      projectManagementEntity.getAllProjectManagement(state).getResourceReportDropdowndata,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchProjectLocation: () => dispatch(fetchProjectLocation.request()),
    getAllProjectResourceReport: (data: {
      startDate: string
      endDate: string
      project: string
      projectSource: string
      location: string
      employmentType: string
    }) => dispatch(getAllResourceReport.request(data)),
    projectResourceDropdownAPI: (data: any) =>
      dispatch(getAllResourceReportDropdownData.request(data)),
    fetchProjectSource: () => dispatch(projectSource.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ProjectsSelectTab)
