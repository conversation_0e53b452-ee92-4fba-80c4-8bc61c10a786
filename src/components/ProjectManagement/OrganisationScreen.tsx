import {
  Box,
  CircularProgress,
  Paper,
  Tab,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { StyledTableCell } from '../Common/CommonStyles'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { fetchOrganisationDesignationData } from '../../actions'
import { projectManagementEntity, projectManagementUI } from '../../reducers'
import styles from '../../utils/styles.json'
import Loader from '../Common/Loader'

type PropType = {
  isGettingDesignationData: boolean
  fetchOrganisationDesignationData: (data: { hierarchyName: string }) => {}
  designationData: { employessCountInHierarchy: SeniorAdminExecutive[] }
}

interface DesignationBand {
  band_title: string
  count: number
}

interface SeniorAdminExecutive {
  level: number
  levelName: string
  designation_band: DesignationBand[]
}

const style = {
  tableRow: {
    boxShadow: '0px 5px 3px #6c6c6c10',
  },
  noDataRow: {
    widht: '100%',
    height: '56px',
    fontFamily: styles.FONT_BOLD,
    color: '#a5a5a5',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
}

const sortBands = (a: DesignationBand, b: DesignationBand) => {
  if (a.band_title < b.band_title) return 1
  else if (a.band_title > b.band_title) return -1
  return 0
}

const OrganisationScreen = (props: PropType) => {
  const { isGettingDesignationData, fetchOrganisationDesignationData, designationData } = props
  const [selectedTab, setSelectedTab] = useState(0)
  const [hierarchyName, setHierarchyName] = useState('Software Development')
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue)
  }
  useEffect(() => {
    fetchOrganisationDesignationData({ hierarchyName: hierarchyName })
  }, [selectedTab])  
  return (
    <Box width='100%'>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label='Tabs for different tables'
        variant='scrollable'
        scrollButtons={false}
      >
        <Tab
          label='SOFTWARE DEVELOPMENT'
          onClick={() => {
            setHierarchyName('Software Development')
            return setSelectedTab(0)
          }}
        />
        <Tab
          label='SUPPORT'
          onClick={() => {
            setHierarchyName('Support')
            return setSelectedTab(1)
          }}
        />
        <Tab
          label='DEVOPS'
          onClick={() => {
            setHierarchyName('DevOPS')
            return setSelectedTab(2)
          }}
        />
        <Tab
          label='PROJECT MANAGEMENT'
          onClick={() => {
            setHierarchyName('Project Management')
            return setSelectedTab(3)
          }}
        />
        <Tab
          label='QA'
          onClick={() => {
            setHierarchyName('QA')
            return setSelectedTab(4)
          }}
        />
        <Tab
          label='DESIGNER'
          onClick={() => {
            setHierarchyName('Designer')
            return setSelectedTab(5)
          }}
        />
        <Tab
          label='NETWORK'
          onClick={() => {
            setHierarchyName('Network')
            return setSelectedTab(6)
          }}
        />
        <Tab
          label='FINANCE'
          onClick={() => {
            setHierarchyName('Finance')
            return setSelectedTab(7)
          }}
        />
        <Tab
          label='HUMAN RESOURCE'
          onClick={() => {
            setHierarchyName('Human Resource')
            return setSelectedTab(8)
          }}
        />
        <Tab
          label='RECRUITMENT'
          onClick={() => {
            setHierarchyName('Recruitment')
            return setSelectedTab(9)
          }}
        />
        <Tab
          label='MARKETING'
          onClick={() => {
            setHierarchyName('Marketing')
            return setSelectedTab(10)
          }}
        />
        <Tab
          label='SALES'
          onClick={() => {
            setHierarchyName('Sales')
            return setSelectedTab(11)
          }}
        />
        <Tab
          label='ADMIN'
          onClick={() => {
            setHierarchyName('Admin')
            return setSelectedTab(12)
          }}
        />
      </Tabs>
      <Box marginTop='1rem'>
        {!isGettingDesignationData ? (
         <Loader state={!isGettingDesignationData} />

        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={style.tableRow}>
                  <StyledTableCell>Level</StyledTableCell>
                  <StyledTableCell>Designation</StyledTableCell>
                  <StyledTableCell>Band</StyledTableCell>
                  <StyledTableCell>Count</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {designationData?.employessCountInHierarchy?.map((value: SeniorAdminExecutive) => {
                  return (
                    <>
                      {value?.designation_band?.sort(sortBands).map((band: DesignationBand) => {
                        return (
                          <TableRow
                            sx={style.tableRow}
                            key={`${value?.levelName}${band?.band_title}`}
                          >
                            <StyledTableCell>{value?.level}</StyledTableCell>
                            <StyledTableCell>{value?.levelName}</StyledTableCell>
                            <StyledTableCell>{band?.band_title}</StyledTableCell>
                            <StyledTableCell>{band?.count}</StyledTableCell>
                          </TableRow>
                        )
                      })}
                    </>
                  )
                })}
              </TableBody>
            </Table>
            {(!designationData || designationData?.employessCountInHierarchy?.length <= 0) && (
              <Box sx={style.noDataRow}>No Data To Display</Box>
            )}
          </TableContainer>
        )}
      </Box>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    designationData:
      projectManagementEntity.getAllProjectManagement(state).getOrganizationDesignationsData,
    isGettingDesignationData:
      projectManagementUI.getProjectManagemet(state).isGettingDesignationDataState,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchOrganisationDesignationData: (data: { hierarchyName: string }) =>
      dispatch(fetchOrganisationDesignationData.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(OrganisationScreen)
