import React, { useState } from 'react'
import styled from 'styled-components'
import { Box, Paper, Tab, Tabs } from '@mui/material'
import Projects from './Projects/Projects'
import ProjectReports from './ProjectSheet/ProjectSheet'
import NonBillableResources from './NonBillableResources'
import ProjectCustomers from './ProjectCustomers'
import EmployeeCountGraph from './ProjectGraphs/EmployeeCountGraph'
import Mandate from './mandate'
import Domain from './Domain'
import Certificates from './Certificates'

const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '0 25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '10px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '10px',
  border: '1px solid #DDDDDD',
}))

const ProjectGraph = (props: any) => {
  const [selectedTab, setSelectedTab] = useState(() => {
    const savedTab = localStorage.getItem('selectedTab')
    return savedTab ? parseInt(savedTab, 10) : 0
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue)
    localStorage.setItem('selectedTab', newValue.toString())
  }

  return (
    <StyledPaper>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
        }}
      >
        <Tabs sx={{ '& .MuiTabs-scroller': {height:"40px"}, minHeight:'40px' }}  value={selectedTab} onChange={handleTabChange} aria-label='Tabs for different tables'>
          <Tab label='Projects' onClick={() => setSelectedTab(0)} />
          <Tab label='Clients' onClick={() => setSelectedTab(1)} />
          <Tab label='Non-Billable Resources' onClick={() => setSelectedTab(2)} />
          <Tab label='Project Reports' onClick={() => setSelectedTab(3)} />
          <Tab label='Project Graphs' onClick={() => setSelectedTab(4)} />
          <Tab label='Mandate' onClick={() => setSelectedTab(5)} />
          <Tab label='Domain' onClick={() => setSelectedTab(6)} />
          {/* <Tab label='CERTIFICATES' onClick={() => setSelectedTab(7)} /> */}
        </Tabs>
      </Box>
      {selectedTab === 0 && (
        <>
          <Projects />
        </>
      )}
      {selectedTab === 4 && (
        <Box paddingTop='20px'>
          <EmployeeCountGraph selectedTab={selectedTab} />
          {/* <DesignationWiseBarGraph selectedTab={selectedTab} /> */}
        </Box>
      )}

      {selectedTab === 3 && <ProjectReports />}

      {selectedTab === 2 && (
        <StyledPaper>
          <NonBillableResources />
        </StyledPaper>
      )}

      {selectedTab === 1 && (
        <StyledPaper>
          <ProjectCustomers />
        </StyledPaper>
      )}

      {selectedTab === 5 && (
        <StyledPaper>
          <Mandate />
        </StyledPaper>
      )}
      {selectedTab === 6 && (
        <StyledPaper>
          <Domain />
        </StyledPaper>
      )}
      {selectedTab === 7 && (
        <StyledPaper>
          <Certificates />
        </StyledPaper>
      )}
    </StyledPaper>
  )
}

export default ProjectGraph
