import { TableHead, TableRow } from '@mui/material'
import { TableHeadColumnsPropType } from './projectQAReportTypes'
import { StyledTableCell } from '../../Common/CommonStyles'

export const style = {
  tableHeadCell: {
    color: 'white',
    width: '300px',
  },
  typographyStyle: {
    textAlign: 'center',
    width: '100%',
  },
  border: {
    borderLeft: '1px solid #E9E9E9',
    borderRight: '1px solid #E9E9E9',
  },
}

const TableHeadColumns = ({ selectType }: TableHeadColumnsPropType) => {
  const styles = {
    tableHeadStyles: {
      Padding: '2px 6px',
    },
  }
  return (
    <TableHead id='head'>
      <TableRow>
        {selectType === 'project-wise' ? (
          <>
            <StyledTableCell sx={styles.tableHeadStyles}>Project</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Status</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>QA</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Is Billable</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Employment Type</StyledTableCell>
          </>
        ) : (
          <>
            <StyledTableCell sx={styles.tableHeadStyles}>Employee ID</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Employee Name</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Projects</StyledTableCell>
            <StyledTableCell sx={styles.tableHeadStyles}>Total Assigned Projects</StyledTableCell>
          </>
        )}
      </TableRow>
    </TableHead>
  )
}

export default TableHeadColumns
