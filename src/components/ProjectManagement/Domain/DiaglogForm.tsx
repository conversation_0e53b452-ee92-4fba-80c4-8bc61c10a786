import { useEffect } from 'react'
import { <PERSON>, Dialog, DialogTitle, IconButton, Backdrop, Grid, styled, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { connect } from 'react-redux'
import { createDomainType, createMandateType, updateDomainType, updateMandateType, updateProjectCustomer } from '../../../actions'
import { style } from './projectCustomersStyle'
import FormikForm from './FormikForm'
import { FormValues, DiaglogFormPropsType } from './ProjectCustomersTypes'
import { Dispatch } from 'redux'
import { FormikHelpers } from 'formik';
import styles from '../../../utils/styles.json'


const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '22px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
}))

const DiaglogForm = ({
  openCustomerForm,
  setOpenCustomerForm,
  openCustomerEditForm,
  setopenCustomerEditForm,
  createNewDomain,
  editCustomerData,
  updateNewDomain,
}: DiaglogFormPropsType) => {
  useEffect(() => {}, [editCustomerData])

  const handleClickCloseDiaglogForm = () => {
    setOpenCustomerForm(false)
    setopenCustomerEditForm(false)
  }

  const initialValues = {
    id: openCustomerEditForm ? editCustomerData.id : 0,
    domain_name: openCustomerEditForm ? editCustomerData.domain_name : '',
  }
  

  const handleSubmit = (values: any, actions: FormikHelpers<FormValues>) => {
    
    if (!values.domain_name) {
      actions.setErrors({
        domain_name: !values.domain_name ? 'Required' : '',
      })
    } else {
      if (openCustomerEditForm) {
        const updateValues = {
          id: values.id,
          domain_name: values.domain_name
        };        
        updateNewDomain(updateValues)
        actions.setSubmitting(false)
        handleClickCloseDiaglogForm()
      } else {
        createNewDomain(values)
        actions.setSubmitting(false)
        handleClickCloseDiaglogForm()
      }
    }
  }

  return (
        <Box>
          <Dialog
            open={openCustomerForm || openCustomerEditForm}
            onClose={handleClickCloseDiaglogForm}
            aria-labelledby='alert-dialog-title'
            aria-describedby='alert-dialog-description'
            BackdropComponent={Backdrop}
            sx={style.BackdropDiaglogStyle}
          >
            <Box>
              <Grid item xs={12} sm={12} md={12}/>
              <DialogTitle id='alert-dialog-title'>
                <Heading>{openCustomerEditForm ? ('Edit '):('Create ')}Domain</Heading>
                <IconButton
                  aria-label='close'
                  onClick={handleClickCloseDiaglogForm}
                  sx={{ ...style.iconButtonStyle }}
                >
                  <CloseIcon />
                </IconButton>
              </DialogTitle>
              <FormikForm
                initialValues={initialValues}
                onSubmit={handleSubmit}
                handleClickCloseDiaglogForm={handleClickCloseDiaglogForm}
                openCustomerEditForm={openCustomerEditForm}
              />
            </Box>
          </Dialog>
        </Box>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  createNewDomain: (data: FormValues) => dispatch(createDomainType.request({ data })),
  updateNewDomain: (data: any) => dispatch(updateDomainType.request({ data })),
})

export default connect(null, mapDispatchToProps)(DiaglogForm)
