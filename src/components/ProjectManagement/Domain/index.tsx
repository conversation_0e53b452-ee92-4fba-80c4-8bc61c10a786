import { Box } from '@mui/material'
import { useEffect, useState } from 'react'
import { fetchDomainType } from '../../../actions/index'
import { connect } from 'react-redux'
import { style } from './projectCustomersStyle'
import DiaglogForm from './DiaglogForm'
import AddCustomerButton from './AddCustomerButton'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { FormValues, ProjectCustomersPropType } from './ProjectCustomersTypes'
import Cards from './Cards'
import { projectManagementEntity, projectManagementUI } from '../../../reducers'
import Loader from '../../Common/Loader'
import DebouncedSearchedBox from '../../Common/DebouncedSearchBox'

const Domain = ({
  getDomainType,
  projectCustomersData,
  isProjectCustomersCreated,
  isProjectCustomerUpdated,
  isGettingProjectDomain,
  isCreatingNewDomain,
  isUpdatingDomain,
  isDeletingDomain,
}: ProjectCustomersPropType) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [rowsPerPage, setRowsPerPage] = useState(100)
  const [page, setPage] = useState(1)
  const [openCustomerForm, setOpenCustomerForm] = useState(false)
  const [openCustomerEditForm, setopenCustomerEditForm] = useState(false)
  const [editCustomerData, setEditCustomerData] = useState<FormValues>({
    id: 0,
    domain_name: '',
  })

  useEffect(() => {
    getDomainType({
      data: { pageSize: rowsPerPage, search: searchQuery, page: page },
    })
  }, [searchQuery, rowsPerPage, page, isProjectCustomersCreated, isProjectCustomerUpdated])

  useEffect(() => {
    setPage(1)
  }, [rowsPerPage, searchQuery])

  return (
    <>
      <Loader
        state={
          isGettingProjectDomain || isCreatingNewDomain || isUpdatingDomain || isDeletingDomain
        }
      />
      <Box sx={style.customBox}>
        <Box>
          <DebouncedSearchedBox placeHolder='Search Domain' setSearchQuery={setSearchQuery} />
        </Box>
        <Box>
          <AddCustomerButton setOpenCustomerForm={setOpenCustomerForm} />
        </Box>
      </Box>
      <DiaglogForm
        setOpenCustomerForm={setOpenCustomerForm}
        openCustomerForm={openCustomerForm}
        openCustomerEditForm={openCustomerEditForm}
        setopenCustomerEditForm={setopenCustomerEditForm}
        editCustomerData={editCustomerData}
      />
      <Box>
        <Cards
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          searchQuery={searchQuery}
          projectCustomersData={projectCustomersData}
          setopenCustomerEditForm={setopenCustomerEditForm}
          setEditCustomerData={setEditCustomerData}
        />
      </Box>
    </>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    projectCustomersData: projectManagementEntity.getAllProjectManagement(state).getAllDomain,
    isGettingProjectDomain: projectManagementUI.getProjectManagemet(state).isGettingProjectDomain,
    isProjectCustomersCreated: projectManagementUI.getProjectManagemet(state).isDomainTypeCreated,
    isProjectCustomerDeleted: projectManagementUI.getProjectManagemet(state).isMandateTypeDeleted,
    isProjectCustomerUpdated: projectManagementUI.getProjectManagemet(state).isDomainTypeUpdated,
    isCreatingNewDomain: projectManagementUI.getProjectManagemet(state).isCreatingNewDomain,
    isUpdatingDomain: projectManagementUI.getProjectManagemet(state).isUpdatingDomain,
    isDeletingDomain: projectManagementUI.getProjectManagemet(state).isDeletingDomain,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    getDomainType: (data: any) => dispatch(fetchDomainType.request(data)),
  }
}

export default connect(mapStateToProp, mapDispatchToProp)(Domain)
