import { Formik, Form, Field, FieldProps } from 'formik';
import {
  <PERSON>,
  Button,
  DialogContent,
  Grid,
  Typography,
  styled,
} from '@mui/material';
import { style } from './projectCustomersStyle';
import { InputField } from '../../Common/ComponentCommonStyles';
import * as Yup from 'yup';
import { useEffect, useState } from 'react';
import { FormikFormPropType } from './ProjectCustomersTypes';
import styles from '../../../utils/styles.json'

export const validationSchema = Yup.object({
  domain_name: Yup.string()
    .required('Domain Name is required')
    .test('is-empty-or-whitespace', 'Domain Name cannot be empty or whitespace', (value:any) => {
      return value?.trim().length > 0;
    }),
});

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '32px',
  fontFamily: styles.FONT_BOLD,
  width: '90px',
  borderRadius: '20px',
  '&.Mui-disabled': {
    opacity: 0.5,
    color: '#ffffff',
  },
}));

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '32px',
  fontFamily: styles.FONT_BOLD,
  width: '90px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}));

const FormikForm = ({
  initialValues,
  onSubmit,
  handleClickCloseDiaglogForm,
  openCustomerEditForm,
}: FormikFormPropType) => {

  const [isDisabled,setIsDisabled]=useState(true);

  useEffect(() => {
    if (openCustomerEditForm && initialValues.domain_name?.trim()) {
      setIsDisabled(false);
    } else {
      setIsDisabled(true);
    }
  }, [openCustomerEditForm, initialValues.domain_name]);

  return (
    <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit} validate={values => {
      const errors = {};
      if (!values.domain_name || !values.domain_name.trim()) {
        setIsDisabled(true);
      } else {
        setIsDisabled(false);
      }
      return errors;
    }}>
      {({ values, errors, touched }) => (
        <Form>
          <DialogContent dividers>
            <Grid container sx={style.formGridContainerStyle}>
              <Box sx={style.fieldBoxStyle}>
                <Field name='domain_name'>
                  {({ field }: FieldProps<string>) => (
                    <InputField
                      {...field}
                      label='Domain Name'
                      variant='outlined'
                      placeholder='Enter Domain Name'
                      size='small'
                      error={touched.domain_name && !!errors.domain_name}
                      helperText={
                        touched.domain_name && typeof errors.domain_name === 'string'
                          ? errors.domain_name
                          : ''
                      }
                      inputProps={{
                        autoComplete: 'new-password',
                        form: { autoComplete: 'off' },
                      }}
                    />
                  )}
                </Field>
              </Box>
            </Grid>
          </DialogContent>
          <Box sx={style.diaglogActionBoxStyle}>
            <CancelButton onClick={handleClickCloseDiaglogForm} autoFocus>CANCEL</CancelButton>
            <ActionButton
              
              autoFocus
              type='submit'
              disabled={isDisabled}
            >
              {openCustomerEditForm ? (
                <Typography>Update</Typography>
              ) : (
                <Typography>Create</Typography>
              )}
            </ActionButton>
          </Box>
        </Form>
      )}
    </Formik>
  )
}

export default FormikForm;
