import {
  Box,
  CircularProgress,
  IconButton,
  InputAdornment,
  Paper,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { style } from './ProjectManagementReportStyle'
import SearchBox from './SearchBox'
import { Dispatch } from 'redux'
import { connect, useSelector } from 'react-redux'
import DateRangePicker from './DateRangePicker'
import TableLayout from './TableLayout'
import ExportButtons from './ExportButtons'
import SelectResources from './SelectResources'
import CardSection from './CardSection'
import { RootState } from '../../../configureStore'
import { projectManagementEntity, projectManagementUI } from '../../../reducers'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import { fetchProjectManagementReports } from '../../../actions'
import stylee from '../../../utils/styles.json'
import SearchIcon from '@mui/icons-material/Search'
import { ClearIcon } from '@mui/x-date-pickers'
import Loader from '../../Common/Loader'

function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  } as T
}

const styles = {
  backNavigate: {
    position: 'absolute',
    top: 90,
    right: 40,
    margin: '20px',
    cursor: 'pointer',
  },
}

const SearchIconStyle = styled(SearchIcon)({
  color: stylee.PRIMARY_COLOR,
  paddingRight: '8px',
})

interface ISearchBoxCustom {
  width?: string
}

const SearchBoxCustom = styled(TextField)<ISearchBoxCustom>(({ width = '250px' }) => ({
  float: 'left',
  '& .MuiOutlinedInput-root': {
    height: '35px',
    width: `${width}`,
    borderRadius: '20px',
    fontSize:'13px',
    '& fieldset': {
      borderColor: stylee.PRIMARY_COLOR,
    },
  },
}))

const ProjectManagementReport = (props: any) => {
  const { tableData, fetchTableData, isGettingTableData } = props
  const [startDate, setStartDate] = useState(dayjs(new Date()))
  const [searchQuery, setSearchQuery] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(300)
  const [tabView, setTabView] = useState('billable')

  const handleChangePage = (event: React.ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage)
  }

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchQuery(e.target.value)
    },
    1200,
  )

  const clearSearch = () => {
    setSearchTerm('')
    setSearchQuery('')
  }

  useEffect(() => {
    fetchTableData({
      date: startDate.format('YYYY-MM-DD').toString(),
      type: tabView,
      page: page,
      limit: rowsPerPage,
      search: searchQuery,
    })
  }, [startDate, tabView, searchQuery, rowsPerPage, page])

  const rowsToDisplay = tableData?.resources

  const navigate = useNavigate()

  const CompanyName = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { company_name: string } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.company_name,
  )

  return (
    <Paper sx={style.container}>
      <Box sx={styles.backNavigate} onClick={() => navigate(-1)}>
        <ArrowBack />
      </Box>
      <Box sx={style.pageHead}>
        <Box sx={style.headings}>
          <Typography className='heading' width='fit-content' sx={style.primaryHeading}>
            {CompanyName}
          </Typography>
          <Typography className='heading' width='fit-content' sx={style.headerHeading}>
            Project Management Report As On {startDate.format('MM/DD/YYYY')}
          </Typography>
        </Box>
      </Box>
      <Box sx={style.actionBarConatiner}>
        <Box sx={style.actionBar}>
          <Box>
            <SearchBoxCustom
              id='outlined-basic'
              placeholder='Search by Emp Id or Name'
              variant='outlined'
              size='small'
              width='100%'
              value={searchTerm}
              onChange={(e) => {
                const value = e.target.value
                setSearchTerm(value)
                functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)
              }}
              InputProps={{
                startAdornment: <SearchIconStyle />,
                endAdornment: (
                  <InputAdornment position='end'sx={{ width: '24px', display: 'flex', justifyContent: 'center' }}>
                    <IconButton aria-label='clear-icon' onClick={clearSearch}>
                      {searchTerm && <ClearIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <CardSection tabView={tabView} tableData={tableData} />
          <Box sx={style.rightGroup}>
            <DateRangePicker startDate={startDate} setStartDate={setStartDate} />
            <SelectResources setPage={setPage} setTabView={setTabView} />
          </Box>
          <ExportButtons />
        </Box>
      </Box>
      {isGettingTableData ? (
        <Loader state={isGettingTableData} />
      ) : (
        <TableLayout
          tableData={tableData}
          tabView={tabView}
          rowsToDisplay={rowsToDisplay}
          page={page}
          handleChangePage={handleChangePage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          setPage={setPage}
          projectManagement={1}
        />
      )}
    </Paper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    tableData: projectManagementEntity.getAllProjectManagement(state).getProjectManagementReports,
    isGettingTableData:
      projectManagementUI.getProjectManagemet(state).isGettingProjectManagementReportsList,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchTableData: (data: any) => dispatch(fetchProjectManagementReports.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ProjectManagementReport)
