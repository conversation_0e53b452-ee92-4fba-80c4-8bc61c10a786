import TabContext from '@mui/lab/TabContext'
import TabList from '@mui/lab/TabList'
import { Box, Tab } from '@mui/material'
import React, { useState } from 'react'
import styles from '../../../utils/styles.json'

type PropType = {
  setTabView:(value:string)=>void
}

const style = {
  tabStyle: {
    '.MuiTabs-flexContainer': {
      width: '100%',
      display: 'flex',
      flexWrap: 'wrap',
      alignItems: "center",
      justifyContent: 'space-between',
      '@media only screen and (max-width:860px)':{
        justifyContent:"center",
      }
    },
    '.MuiTab-root': {
      fontFamily: styles.FONT_MEDIUM,
      marginTop: '10px',
      textTransform: 'none',
      width: '240px',
      height: '44px',
      border: '1px solid #e0e0e0',
      borderRadius: '20px',
      padding: '10px 20px',
      fontSize: '0.9rem',
      '&.Mui-selected': {
        borderColor: styles.PRIMARY_COLOR,
      },
      '@media only screen and (max-width:860px)':{
        marginLeft:"32px",
        padding: '5px 10px',
        height: '40px',
      }
    },
    '.MuiTabs-indicator': {
      display: 'none',
    },
  },
  tabContainer:{
    '@media only screen and (max-width:860px)':{
      display:"flex",
      justifyContent:"center",
      alignItems:"center",
    }
  }
}

const TabView = (props:PropType) => {
  const [value, setValue] = useState<string>('billable')
  const {setTabView}=props

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue)
    setTabView(newValue)
  }
  return (
    <Box margin="8px 16px 0" >
      <TabContext value={value}>
        <Box sx={style.tabContainer}>
          <TabList onChange={handleChange} aria-label='Tabs Example'>
            <Tab label='Billable Resources' value='billable' />
            <Tab label='Additional Resources' value='additional' />
            <Tab label='Non-Billable Resources' value='nonbillable' />
            <Tab label='Free Resources' value='free' />
          </TabList>
        </Box>
      </TabContext>
    </Box>
  )
}
export default TabView
