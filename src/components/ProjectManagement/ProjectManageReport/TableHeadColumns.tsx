import { TableHead, TableRow, styled } from '@mui/material'
import { style } from './ProjectManagementReportStyle'
import data from './ProjectManagementReportData.json'
import { StyledTableCell } from '../../Common/CommonStyles'

const TableHeadColumns = (props: any) => {
  const {tabView}=props
  const { getColumnData } = data
  return (
    <TableHead id='head'>
      <TableRow>
        {getColumnData.map((column) => (
          <StyledTableCell key={column.id} sx={style.cellWidth}>
           {tabView === 'free' && column.label === 'Project Details'
              ? 'Time Count'
              : column.label}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}

export default TableHeadColumns
