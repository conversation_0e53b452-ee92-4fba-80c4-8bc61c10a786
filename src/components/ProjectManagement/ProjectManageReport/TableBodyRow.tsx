import { StyledTableCell } from '../../Common/CommonStyles'
import { Box, TableRow, styled } from '@mui/material'

const TableBodyRow = (Props: { row: any }) => {
  const { row } = Props
  const renderProjectDetails = (projectDetails: any[]) => {
    if (projectDetails.length === 0) {
      return 'NA'
    }
    return projectDetails.map((project, index) => (
      <Box key={index}>
        {project.project ?? 'NA'} | {project.type ?? 'NA'} | {project.role ?? 'NA'}
        {project.hours ? ` (${project.hours})` : ''}
      </Box>
    ))
  }

  const technologies = row.technologies.join(', ')

  return (
    <TableRow key={row.emp_id} id='body' sx={{ padding: '5px' }}>
      <StyledTableCell>{row.emp_id ?? 'NA'}</StyledTableCell>
      <StyledTableCell>{row.name ?? 'NA'}</StyledTableCell>
      <StyledTableCell>{row.manager ?? 'NA'}</StyledTableCell>
      {row?.time_count || row?.time_count === 0 ? (
        <StyledTableCell>
          {row?.time_count ? `Hours left: ${row.time_count}` : 'NA'}
        </StyledTableCell>
      ) : (
        <StyledTableCell>{renderProjectDetails(row.project_details)}</StyledTableCell>
      )}

      <StyledTableCell>{technologies ? technologies : 'NA'}</StyledTableCell>
    </TableRow>
  )
}

export default TableBodyRow
