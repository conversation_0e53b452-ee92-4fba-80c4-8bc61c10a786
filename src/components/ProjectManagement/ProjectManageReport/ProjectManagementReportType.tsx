export type TableDataType ={
    billable: {
        resources: Employee[];
        total: number;
    };
    nonbillable: {
        resources: Employee[];
        total: number;
    };
    free: {
        resources: Employee[];
        total: number;
    };
    additional: {
        resources: Employee[];
        total: number;
    };
}

export type GetDataType={
    date:string
}

export type ProjectManagementReportPropType = {
    fetchTableData: (data: {
      date: string
      type: string
    }) => {}
    tableData:{
        billable: {
            resources: Employee[];
            total: number;
        };
        nonbillable: {
            resources: Employee[];
            total: number;
        };
        free: {
            resources: Employee[];
            total: number;
        };
        additional: {
            resources: Employee[];
            total: number;
        };
    }
    isGettingTableData: boolean
  }

export type Employee = {
    employee_id: number;
    name: string;
    manager: string;
    project_details?: string;
    technologies: string[];
}


 