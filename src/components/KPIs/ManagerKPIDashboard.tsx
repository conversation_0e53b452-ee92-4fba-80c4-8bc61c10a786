import React, { useState, useEffect, useCallback, useRef } from 'react'
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Grid,
  alpha,
  styled,
  SelectChangeEvent,
  Button,
  TextField,
  Tooltip,
  CircularProgress,
} from '@mui/material'
import { toast } from 'react-toastify'
import { useDispatch, useSelector } from 'react-redux'
import { 
  assignTemplate, 
  getAssignedTemplate, 
  editTemplateValues,
  getKPITemplates,
  assignTemplateReset,
  getAssignedTemplateReset,
  editTemplateValuesReset,
  getKPITemplatesReset,
  loadTemplateOrOptions,
  loadTemplateOrOptionsReset
} from '../../actions'
import { getDrKpiTemplates } from '../../reducers/entities/drKpiTemplates'
import style from '../../utils/styles.json'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import AssignmentIcon from '@mui/icons-material/Assignment'
import BarChartIcon from '@mui/icons-material/BarChart'
import EditIcon from '@mui/icons-material/Edit'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import WarningIcon from '@mui/icons-material/Warning'
import SaveIcon from '@mui/icons-material/Save'
import CancelIcon from '@mui/icons-material/Cancel'
import StarIcon from '@mui/icons-material/Star'
import TrendingDownIcon from '@mui/icons-material/TrendingDown'
import PlaylistAddCheckIcon from '@mui/icons-material/PlaylistAddCheck'
import FeedbackIcon from '@mui/icons-material/Feedback'
import Loader from '../Common/Loader'
import BulletTextField, { formatBulletText } from '../Common/BulletTextField'

const Maincontainer = {
  backgroundColor: 'transparent',
  width: '100%',
}

// Styled components
const StyledPaper = styled(Paper)(() => ({
  width: '95%',
  padding: '15px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '25px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '20px',
  border: '1px solid #DDDDDD',
}))

const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '24px',
  textAlign: 'left',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '16px',
  padding: '0 4px',
}))

const StyledFormControl = styled(FormControl)(() => ({
  minWidth: '200px',
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: '#FFFFFF',
    height: '40px',
    '&.Mui-focused fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#DDDDDD',
  },
  '& .MuiInputLabel-root': {
    fontFamily: style.FONT_MEDIUM,
    fontSize: '13px',
    color: '#666666',
    '&.Mui-focused': {
      color: style.PRIMARY_COLOR,
    },
  },
}))

const SummaryCard = styled(Card)(({ theme }) => ({
  minHeight: '100px',
  borderRadius: '6px',
  border: '1px solid #E0E0E0',
  boxShadow: '0 1px 4px rgba(0,0,0,0.08)',
  transition: 'all 0.2s ease',
  backgroundColor: '#FFFFFF',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
    borderColor: style.PRIMARY_COLOR,
    transform: 'translateY(-1px)',
  },
}))

const SummaryCardContent = styled(CardContent)(() => ({
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  justifyContent: 'center',
  '&:last-child': {
    paddingBottom: '12px',
  },
}))

const SummaryIcon = styled(Box)(() => ({
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '6px',
  '& svg': {
    fontSize: '18px',
    color: style.PRIMARY_COLOR,
  },
}))

const SummaryValue = styled(Typography)(() => ({
  fontSize: '20px',
  fontFamily: style.FONT_BOLD,
  color: style.PRIMARY_COLOR,
  marginBottom: '2px',
  lineHeight: 1,
}))

const SummaryLabel = styled(Typography)(() => ({
  fontSize: '10px',
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  textAlign: 'center',
  lineHeight: 1.2,
}))

const KPICard = styled(Card)<{ isEditing?: boolean }>(({ theme, isEditing }) => ({
  minHeight: isEditing ? '140px' : '110px', // Use minHeight with consistent values
  maxHeight: isEditing ? '180px' : '130px', // Add maxHeight to prevent over-stretching
  borderRadius: '6px',
  border: `1px solid ${isEditing ? style.PRIMARY_COLOR : '#E0E0E0'}`,
  boxShadow: isEditing ? `0 2px 8px ${alpha(style.PRIMARY_COLOR, 0.2)}` : '0 1px 4px rgba(0,0,0,0.08)',
  transition: 'all 0.2s ease',
  backgroundColor: '#FFFFFF',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
    borderColor: style.PRIMARY_COLOR,
    transform: 'translateY(-1px)',
  },
}))

const KPICardContent = styled(CardContent)(() => ({
  padding: '10px',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  justifyContent: 'space-between',
  '&:last-child': {
    paddingBottom: '10px',
  },
}))

const CategoryHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 16px',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.04),
  color: style.PRIMARY_COLOR,
  borderRadius: '6px',
  marginBottom: '16px',
  border: `1px solid ${alpha(style.PRIMARY_COLOR, 0.2)}`,
  boxShadow: '0 1px 3px rgba(0,0,0,0.08)',
}))

const CategoryHeaderLeft = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
}))

const CategoryTitle = styled(Typography)(() => ({
  fontSize: '16px',
  fontFamily: style.FONT_BOLD,
  color: style.PRIMARY_COLOR,
  letterSpacing: '0.3px',
}))

const CategoryCount = styled(Typography)(() => ({
  fontSize: '12px',
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  marginTop: '2px',
}))

const KPITitle = styled(Typography)(() => ({
  fontSize: '12px',
  fontFamily: style.FONT_BOLD,
  color: '#333333',
  marginBottom: '2px',
  lineHeight: 1.1,
  minHeight: '16px',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
}))

const KPIType = styled(Typography)(() => ({
  fontSize: '10px',
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  marginBottom: '4px',
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  backgroundColor: alpha('#666666', 0.1),
  padding: '2px 6px',
  borderRadius: '10px',
  alignSelf: 'flex-start',
}))

const KPIValueContainer = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: 'auto',
  gap: '6px',
  padding: '6px',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.05),
  borderRadius: '4px',
}))

const KPIValue = styled(Typography)(() => ({
  fontSize: '18px',
  fontFamily: style.FONT_BOLD,
  color: style.PRIMARY_COLOR,
}))

const KPITarget = styled(Typography)(() => ({
  fontSize: '13px',
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
}))

const NoTemplateMessage = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '40px 20px',
  textAlign: 'center',
  backgroundColor: 'transparent',
  borderRadius: '8px',
  border: '2px dashed #DDDDDD',
  minHeight: '240px',
  margin: '16px 0',
}))

const CompactBox = styled(Box)(() => ({
  '& .MuiGrid-container': {
    margin: 0,
    width: '100%',
    marginBottom: '16px',
  },
  '& .MuiGrid-item': {
    paddingTop: '8px',
    paddingLeft: '8px',
  },
}))

const FeedbackCard = styled(Card)<{ isEditing?: boolean }>(({ theme, isEditing }) => ({
  minHeight: isEditing ? '200px' : '180px', // Larger height for feedback cards
  borderRadius: '6px',
  border: `1px solid ${isEditing ? style.PRIMARY_COLOR : '#E0E0E0'}`,
  boxShadow: isEditing ? `0 2px 8px ${alpha(style.PRIMARY_COLOR, 0.2)}` : '0 1px 4px rgba(0,0,0,0.08)',
  transition: 'all 0.2s ease',
  backgroundColor: '#FFFFFF',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
    borderColor: style.PRIMARY_COLOR,
    transform: 'translateY(-1px)',
  },
}))

const FeedbackCardContent = styled(CardContent)(() => ({
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  '&:last-child': {
    paddingBottom: '12px',
  },
}))

interface ManagerKPIDashboardProps {
  userId: number
  tId: number
  onMonthChange?: () => void
}

interface AssignedTemplate {
  id: number
  name: string
  highlights?: string
  lowlights?: string
  actions_taken?: string
  categories: {
    id: number
    name: string
    subcategories: {
      id: number
      title: string
      type: string
      defaultTarget: string
      value?: string
    }[]
  }[]
}

type Category = AssignedTemplate['categories'][0]
type Subcategory = Category['subcategories'][0]

interface KPITemplate {
  id: string
  title: string
  categories: {
    name: string
    subcategories: {
      title: string
      type: 'Qualitative' | 'Quantitative'
      defaultTarget: string
    }[]
  }[]
}

const ManagerKPIDashboard: React.FC<ManagerKPIDashboardProps> = ({ 
  userId, 
  tId, 
  onMonthChange 
}) => {
  const dispatch = useDispatch()
  
  // Redux state
  const {
    assignedTemplate,
    availableTemplates,
    isLoading,
    isAssigning,
    isEditing,
    isFetchingTemplates,
    isLoadingTemplateOrOptions,
    error,
    assignError,
    editError,
    templatesError,
    loadTemplateOrOptionsError,
  } = useSelector(getDrKpiTemplates)
  
  // Get current user ID from Redux store
  const currentUserId = useSelector(
    (state: any) => state?.entities?.dashboard?.getUserDetails?.id
  )
  
  // Check if current user is authorized to access KPI features
  const isAuthorized = [562, 1259, 3106].includes(currentUserId)
  
  // Redirect unauthorized users
  useEffect(() => {
    if (!isAuthorized) {
      window.location.href = '/home'
    }
  }, [isAuthorized])

  // Track edit state for auto-refresh
  const [isLocalEditMode, setIsLocalEditMode] = useState(false)
  const [isLocalAssigning, setIsLocalAssigning] = useState(false)
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null)
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false)
  const [isManualRefresh, setIsManualRefresh] = useState(false)
  const isMounted = useRef(true)
  const previousParams = useRef<{userId?: number, tId?: number}>({})
  const lastApiCall = useRef<{userId?: number, tId?: number, timestamp?: number}>({})
  const onMonthChangeRef = useRef(onMonthChange)

  // Local state
  const [isEditMode, setIsEditMode] = useState(false)
  const [editValues, setEditValues] = useState<{ [key: string]: string }>({})
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // State for template-level feedback
  const [isFeedbackEditMode, setIsFeedbackEditMode] = useState(false)
  const [feedbackExpanded, setFeedbackExpanded] = useState(false)
  const [editTemplateHighlights, setEditTemplateHighlights] = useState('')
  const [editTemplateLowlights, setEditTemplateLowlights] = useState('')
  const [editTemplateActions, setEditTemplateActions] = useState('')

  // Update the ref when prop changes
  useEffect(() => {
    onMonthChangeRef.current = onMonthChange
  }, [onMonthChange])

  // Single useEffect to load template data - prevents multiple API calls
  useEffect(() => {
    console.log('ManagerKPIDashboard: useEffect triggered with userId:', userId, 'tId:', tId, 'isLoading:', isLoadingTemplateOrOptions)
    
    // Check if params have actually changed to prevent unnecessary calls
    const hasParamsChanged = previousParams.current.userId !== userId || previousParams.current.tId !== tId
    
    // Check if we've made this API call recently (within 2 seconds)
    const now = Date.now()
    const lastCall = lastApiCall.current
    const recentlyCalled = lastCall.userId === userId && lastCall.tId === tId && lastCall.timestamp && (now - lastCall.timestamp) < 2000
    
    // Only load if component is mounted, params are valid, params have changed, not recently called, and not already loading/assigning/editing
    if (isMounted.current && userId && tId && hasParamsChanged && !recentlyCalled && !isLoadingTemplateOrOptions && !isAssigning && !isLocalEditMode && !isLocalAssigning && !isManualRefresh) {
      console.log('ManagerKPIDashboard: Dispatching loadTemplateOrOptions with:', { userId, tId })
      dispatch(loadTemplateOrOptions({ userId, tId }))
      setHasInitiallyLoaded(true)
      
      // Update previous params and last API call
      previousParams.current = { userId, tId }
      lastApiCall.current = { userId, tId, timestamp: now }
    } else if (!userId || !tId) {
      console.warn('ManagerKPIDashboard: Missing userId or tId:', { userId, tId })
    } else if (!hasParamsChanged) {
      console.log('ManagerKPIDashboard: Params unchanged, skipping API call')
    } else if (recentlyCalled) {
      console.log('ManagerKPIDashboard: API called recently, skipping duplicate call')
    } else if (isLoadingTemplateOrOptions || isAssigning || isLocalEditMode || isLocalAssigning || isManualRefresh) {
      console.log('ManagerKPIDashboard: Skipping API call - operation in progress')
    } else if (!isMounted.current) {
      console.log('ManagerKPIDashboard: Component unmounted, skipping API call')
    }
  }, [userId, tId, isLoadingTemplateOrOptions, isAssigning, isLocalEditMode, isLocalAssigning, isManualRefresh, dispatch]) // Removed unnecessary dependencies

  // Removed secondary refresh useEffect to prevent any potential loops

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout)
      }
    }
  }, [saveTimeout])

  // Monitor assignedTemplate changes for debugging and UI updates
  useEffect(() => {
    console.log('🔍 ManagerKPIDashboard: assignedTemplate changed:', {
      hasTemplate: !!assignedTemplate,
      templateId: assignedTemplate?.id,
      categoriesCount: assignedTemplate?.categories?.length || 0,
      timestamp: new Date().toISOString()
    })
    
    // Reset edit values when template changes - this ensures UI reflects latest data
    if (assignedTemplate?.categories) {
      const newEditValues: { [key: string]: string } = {}
      
      assignedTemplate.categories.forEach((category: any, categoryIndex: number) => {
        if (category.subcategories) {
        category.subcategories.forEach((subcategory: any, subIndex: number) => {
          const key = `${categoryIndex}-${subIndex}`
          
          // Set initial value from subcategory data, don't auto-set for qualitative KPIs
          let initialValue = subcategory.value || ''
          
          // Note: Removed auto-setting for qualitative KPIs to allow user selection
          // The dropdown will show "Select Status" for empty qualitative KPIs
          
          newEditValues[key] = initialValue
          console.log(`📊 KPI Value [${categoryIndex}-${subIndex}]: ${subcategory.title} = "${initialValue}"`)
      })
        }
      })
      
      setEditValues(newEditValues)
      setHasUnsavedChanges(false) // Reset unsaved changes when template updates
    }
    
    // Set template-level feedback values
    if (assignedTemplate) {
      setEditTemplateHighlights(assignedTemplate.highlights || '')
      setEditTemplateLowlights(assignedTemplate.lowlights || '')
      setEditTemplateActions(assignedTemplate.actions_taken || '')
      // Clear local states when template is loaded/updated (e.g., after save or assignment)
      setIsLocalEditMode(false)
      setIsLocalAssigning(false)
      setIsManualRefresh(false)
    }
  }, [assignedTemplate])

  // Removed duplicate useEffect - now handled by the single useEffect above

  // Removed auto-refresh as it was causing infinite loops and is not necessary for basic functionality

  // Reset states on cleanup and initialize
  useEffect(() => {
    isMounted.current = true
    // Initialize previousParams to prevent initial false positive
    previousParams.current = { userId: undefined, tId: undefined }
    
    return () => {
      isMounted.current = false
      if (saveTimeout) {
        clearTimeout(saveTimeout)
      }
      dispatch(assignTemplateReset())
      dispatch(getAssignedTemplateReset())
      dispatch(editTemplateValuesReset())
      dispatch(getKPITemplatesReset())
      dispatch(loadTemplateOrOptionsReset())
    }
  }, [dispatch, saveTimeout])

  const handleAssignTemplate = useCallback((templateId: number) => {
    if (!userId || !tId || !templateId) {
      toast.error('Missing required information to assign template')
      return
    }

    if (isAssigning || isLocalAssigning) {
      console.log('Already assigning template, skipping...')
      return
    }

    // Set local assigning state to show loader
    setIsLocalAssigning(true)
    dispatch(assignTemplate({ userId, tId, templateId }))
    
    // Manually refresh after assignment completes (with delay to ensure backend processing)
    setTimeout(() => {
      if (isMounted.current) {
        console.log('🔄 ManagerKPIDashboard: Refreshing template data after assignment...')
        setIsManualRefresh(true)
        lastApiCall.current = { userId, tId, timestamp: Date.now() }
        dispatch(loadTemplateOrOptions({ userId, tId }))
        setTimeout(() => setIsManualRefresh(false), 1000)
      }
    }, 1500) // Allow time for assignment to complete
  }, [dispatch, userId, tId, isAssigning, isLocalAssigning])

  const handleToggleEditMode = useCallback(() => {
    if (isEditMode && hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to exit edit mode?')) {
        setIsEditMode(false)
        setHasUnsavedChanges(false)
        // Reset edit values to current values
        if (assignedTemplate) {
          const currentValues: { [key: string]: string } = {}
          assignedTemplate.categories.forEach((category: any, categoryIndex: number) => {
            category.subcategories.forEach((subcategory: any, subIndex: number) => {
              const key = `${categoryIndex}-${subIndex}`
              
              // Set initial value from subcategory data, don't auto-set for qualitative KPIs
              let initialValue = subcategory.value || ''
              
              // Note: Removed auto-setting for qualitative KPIs to allow user selection
              
              currentValues[key] = initialValue
            })
          })
          setEditValues(currentValues)
          // Reset template-level feedback
          setEditTemplateHighlights(assignedTemplate.highlights || '')
          setEditTemplateLowlights(assignedTemplate.lowlights || '')
          setEditTemplateActions(assignedTemplate.actions_taken || '')
        }
      }
    } else {
      setIsEditMode(!isEditMode)
      setHasUnsavedChanges(false)
    }
  }, [isEditMode, hasUnsavedChanges, assignedTemplate])

  const handleEditValueChange = useCallback((key: string, value: string) => {
    setEditValues(prev => ({
      ...prev,
      [key]: value
    }))
    setHasUnsavedChanges(true)
  }, [])



  // Monitor template feedback changes for unsaved changes detection
  useEffect(() => {
    if (!assignedTemplate || !isEditMode) return

    const templateHighlightsChanged = editTemplateHighlights !== (assignedTemplate.highlights || '')
    const templateLowlightsChanged = editTemplateLowlights !== (assignedTemplate.lowlights || '')
    const templateActionsChanged = editTemplateActions !== (assignedTemplate.actions_taken || '')

    // Check for KPI value changes
    let hasKPIChanges = false
    assignedTemplate.categories.forEach((category: any, categoryIndex: number) => {
      if (category.subcategories && !hasKPIChanges) {
        category.subcategories.forEach((subcategory: any, subIndex: number) => {
          const key = `${categoryIndex}-${subIndex}`
          const newValue = editValues[key] || ''
          const currentValue = subcategory.value || ''
          if (newValue !== currentValue) {
            hasKPIChanges = true
          }
        })
      }
    })

    const hasTemplateChanges = templateHighlightsChanged || templateLowlightsChanged || templateActionsChanged
    const hasAnyChanges = hasKPIChanges || hasTemplateChanges

    if (hasAnyChanges !== hasUnsavedChanges) {
      setHasUnsavedChanges(hasAnyChanges)
    }
  }, [assignedTemplate, editValues, editTemplateHighlights, editTemplateLowlights, editTemplateActions, isEditMode, hasUnsavedChanges])

  const handleSaveChanges = useCallback(() => {
    if (!assignedTemplate || !userId || !tId) {
      toast.error('Missing template or user information')
      return
    }

    const changes: Array<{
      userId: number
      tId: number
      templateId: number
      categoryId: number
      subcategoryId: number
      value: string
      highlights?: string
      lowlights?: string
      actions_taken?: string
    }> = []

    // Check for template-level feedback changes
    const templateHighlightsChanged = editTemplateHighlights !== (assignedTemplate.highlights || '')
    const templateLowlightsChanged = editTemplateLowlights !== (assignedTemplate.lowlights || '')
    const templateActionsChanged = editTemplateActions !== (assignedTemplate.actions_taken || '')

    let hasTemplateChanges = templateHighlightsChanged || templateLowlightsChanged || templateActionsChanged

    // Collect all KPI value changes
    assignedTemplate.categories.forEach((category: any, categoryIndex: number) => {
      if (category.subcategories) {
        category.subcategories.forEach((subcategory: any, subIndex: number) => {
          const key = `${categoryIndex}-${subIndex}`
          const newValue = editValues[key]
          const currentValue = subcategory.value || ''
          
          // Check if KPI value has changed
          if (newValue !== currentValue) {
            const changeData: any = {
              userId,
              tId,
              templateId: assignedTemplate.id,
              categoryId: category.id,
              subcategoryId: subcategory.id,
              value: newValue
            }

            // Include template-level feedback in the first change (if there are template changes)
            if (hasTemplateChanges && changes.length === 0) {
              if (templateHighlightsChanged) {
                changeData.highlights = editTemplateHighlights
              }
              if (templateLowlightsChanged) {
                changeData.lowlights = editTemplateLowlights
              }
              if (templateActionsChanged) {
                changeData.actions_taken = editTemplateActions
              }
              hasTemplateChanges = false // Only include in first change
            }

            changes.push(changeData)
          }
        })
      }
    })

    // If only template-level changes exist (no KPI value changes), create a dummy change for the first KPI
    if (hasTemplateChanges && changes.length === 0) {
      const firstCategory = assignedTemplate.categories[0]
      if (firstCategory?.subcategories?.[0]) {
        const firstSubcategory = firstCategory.subcategories[0]
        const changeData: any = {
          userId,
          tId,
          templateId: assignedTemplate.id,
          categoryId: firstCategory.id,
          subcategoryId: firstSubcategory.id,
          value: firstSubcategory.value || '', // Keep existing value
        }

        if (templateHighlightsChanged) {
          changeData.highlights = editTemplateHighlights
        }
        if (templateLowlightsChanged) {
          changeData.lowlights = editTemplateLowlights
        }
        if (templateActionsChanged) {
          changeData.actions_taken = editTemplateActions
        }

        changes.push(changeData)
      }
    }

    if (changes.length === 0) {
      toast.info('No changes to save')
      return
    }

    console.log('💾 ManagerKPIDashboard: Saving changes (including template feedback):', changes)
    
    // Set local edit mode to show loader during save process
    setIsLocalEditMode(true)
    
    // Clear any existing timeout
    if (saveTimeout) {
      clearTimeout(saveTimeout)
    }
    
    // Save each change individually as the backend expects
    changes.forEach((change, index) => {
      console.log(`🔄 ManagerKPIDashboard: Dispatching edit ${index + 1}/${changes.length}:`, change)
      dispatch(editTemplateValues(change))
    })
    
    // Single refresh after all saves complete
    const timeoutId = setTimeout(() => {
      if (isMounted.current) {
        console.log('🔄 ManagerKPIDashboard: Refreshing template data after save...')
        // Set manual refresh flag to prevent duplicate calls
        setIsManualRefresh(true)
        // Update last API call timestamp
        lastApiCall.current = { userId, tId, timestamp: Date.now() }
        // Only call loadTemplateOrOptions - it handles both assigned template and available templates
        dispatch(loadTemplateOrOptions({ userId, tId }))
        // Clear local edit mode after refresh
        setIsLocalEditMode(false)
        // Clear manual refresh flag after a short delay
        setTimeout(() => setIsManualRefresh(false), 1000)
      }
    }, 2000) // Longer delay for multiple requests
    
    setSaveTimeout(timeoutId)
    setHasUnsavedChanges(false)
    setIsEditMode(false) // Exit edit mode after saving
  }, [assignedTemplate, editValues, editTemplateHighlights, editTemplateLowlights, editTemplateActions, userId, tId, dispatch, saveTimeout])

  const calculateSummaryStats = useCallback(() => {
    if (!assignedTemplate || !assignedTemplate.categories) {
      return { completed: 0, needsAttention: 0, progressScore: 0 }
    }

    let completed = 0
    let needsAttention = 0
    let totalKPIsWithValues = 0

    assignedTemplate.categories.forEach((category: any) => {
      if (category.subcategories) {
        category.subcategories.forEach((subcategory: any) => {
          const value = subcategory.value
          const target = subcategory.defaultTarget

          console.log(`🔍 DEBUG KPI: ${subcategory.title}, Type: "${subcategory.type}", Value: "${value}", Target: "${target}"`)

          // Only evaluate KPIs that have values
          if (value && value.trim() !== '') {
            totalKPIsWithValues++
            console.log(`🔄 Processing KPI: ${subcategory.title}, Type: "${subcategory.type}", Value: "${value}"`)
            if (isQualitativeKPI(subcategory)) {
              // For qualitative: 1 = Achieved (completed), 0 = Not Achieved (needs attention)
              const numValue = parseFloat(value) || 0
              console.log(`Qualitative KPI: ${subcategory.title}, Value: ${numValue} (1=Achieved, 0=Not Achieved)`)
              if (numValue === 1) {
                completed++
                console.log(`✅ Completed: ${subcategory.title}`)
            } else {
                needsAttention++
                console.log(`⚠️ Needs Attention: ${subcategory.title}`)
              }
            } else {
              // For quantitative: check if actual meets target criteria
              const numValue = parseFloat(value) || 0
              const isCompleted = evaluateQuantitativeTarget(numValue, target)
              console.log(`Quantitative KPI: ${subcategory.title}, Value: ${numValue}, Target: ${target}, Completed: ${isCompleted}`)
              if (isCompleted) {
                completed++
                console.log(`✅ Completed: ${subcategory.title}`)
              } else {
                needsAttention++
                console.log(`⚠️ Needs Attention: ${subcategory.title}`)
            }
          }
          } else {
            console.log(`🚫 No value for KPI: ${subcategory.title}`)
          }
          // Note: KPIs without values are not counted in either completed or needsAttention
        })
      }
    })

    const progressScore = totalKPIsWithValues > 0 ? Math.round((completed / totalKPIsWithValues) * 100) : 0

    console.log(`📈 SUMMARY - Total KPIs with Values: ${totalKPIsWithValues}, Completed: ${completed}, Needs Attention: ${needsAttention}, Progress: ${progressScore}%`)

    return { completed, needsAttention, progressScore }
  }, [assignedTemplate])

  // Helper function to evaluate quantitative targets
  const evaluateQuantitativeTarget = (actualValue: number, target: string): boolean => {
    if (!target || target.trim() === '') {
      console.log(`❌ Empty target for value: ${actualValue}`)
      return false
    }
    
    const targetStr = target.trim()
    console.log(`🎯 Evaluating: ${actualValue} against condition: "${targetStr}"`)
    
    // Handle new condition format (e.g., <10, >=20, >30, <=40, =50)
    // First check for compound operators
    if (targetStr.startsWith('>=')) {
      const targetNum = parseFloat(targetStr.substring(2).trim()) || 0
      const result = actualValue >= targetNum
      console.log(`📊 >= Logic: ${actualValue} >= ${targetNum} = ${result}`)
      return result
    } else if (targetStr.startsWith('<=')) {
      const targetNum = parseFloat(targetStr.substring(2).trim()) || 0
      const result = actualValue <= targetNum
      console.log(`📊 <= Logic: ${actualValue} <= ${targetNum} = ${result}`)
      return result
    } else if (targetStr.startsWith('<')) {
      const targetNum = parseFloat(targetStr.substring(1).trim()) || 0
      const result = actualValue < targetNum
      console.log(`📊 < Logic: ${actualValue} < ${targetNum} = ${result}`)
      return result
    } else if (targetStr.startsWith('>')) {
      const targetNum = parseFloat(targetStr.substring(1).trim()) || 0
      const result = actualValue > targetNum
      console.log(`📊 > Logic: ${actualValue} > ${targetNum} = ${result}`)
      return result
    } else if (targetStr.startsWith('=')) {
      const targetNum = parseFloat(targetStr.substring(1).trim()) || 0
      const result = actualValue === targetNum
      console.log(`📊 = Logic: ${actualValue} === ${targetNum} = ${result}`)
      return result
    }
    
    // Fallback: Handle legacy formats
    if (targetStr.includes('≤')) {
      const targetNum = parseFloat(targetStr.replace(/[≤]/g, '').trim()) || 0
      const result = actualValue <= targetNum
      console.log(`📊 ≤ Logic (legacy): ${actualValue} <= ${targetNum} = ${result}`)
      return result
    } else if (targetStr.includes('≥')) {
      const targetNum = parseFloat(targetStr.replace(/[≥]/g, '').trim()) || 0
      const result = actualValue >= targetNum
      console.log(`📊 ≥ Logic (legacy): ${actualValue} >= ${targetNum} = ${result}`)
      return result
    } else if (targetStr.includes('100 %') || targetStr.includes('100%')) {
      const result = actualValue >= 100
      console.log(`📊 100% Logic (legacy): ${actualValue} >= 100 = ${result}`)
      return result
    } else {
      // For other legacy formats, extract number and assume >= logic
      const match = targetStr.match(/(\d+(?:\.\d+)?)/);
      if (match) {
        const targetNum = parseFloat(match[1]) || 0
        const result = actualValue >= targetNum
        console.log(`📊 Pattern match (legacy ≥): ${actualValue} >= ${targetNum} = ${result}`)
        return result
      }
    }
    
    console.log(`❌ Could not parse condition: "${targetStr}"`)
    return false
  }

  // Helper function to check if KPI is qualitative
  const isQualitativeKPI = (subcategory: any): boolean => {
    if (!subcategory.type) return false
    const kpiType = subcategory.type.toString().toUpperCase()
    return kpiType === 'QUALITATIVE'
  }

  // Helper function to get normalized target display for any KPI
  const getTargetDisplay = (subcategory: any): string => {
    if (isQualitativeKPI(subcategory)) {
      return 'Achieved'
    }
    return subcategory.defaultTarget || ''
  }

  // Helper function to get KPI status
  const getKPIStatus = (subcategory: any): 'completed' | 'needs-attention' | 'no-data' => {
    const value = subcategory.value
    if (!value || value.trim() === '') {
      return 'no-data'
    }

    if (isQualitativeKPI(subcategory)) {
      const numValue = parseFloat(value) || 0
      return numValue === 1 ? 'completed' : 'needs-attention'
    } else {
      const numValue = parseFloat(value) || 0
      const isCompleted = evaluateQuantitativeTarget(numValue, subcategory.defaultTarget)
      return isCompleted ? 'completed' : 'needs-attention'
    }

    return 'no-data'
  }

  // Helper function to get status icon and color
  const getStatusDisplay = (status: 'completed' | 'needs-attention' | 'no-data') => {
    switch (status) {
      case 'completed':
        return {
          icon: <CheckCircleIcon sx={{ fontSize: '14px', color: '#4CAF50' }} />,
          color: '#4CAF50',
          bgColor: alpha('#4CAF50', 0.1),
          text: 'Completed'
        }
      case 'needs-attention':
        return {
          icon: <WarningIcon sx={{ fontSize: '14px', color: '#FF9800' }} />,
          color: '#FF9800',
          bgColor: alpha('#FF9800', 0.1),
          text: 'Needs Attention'
        }
      default:
        return {
          icon: null,
          color: '#999999',
          bgColor: alpha('#999999', 0.1),
          text: 'No Data'
        }
    }
  }

  const renderSummaryCards = () => {
    const stats = calculateSummaryStats()

    return (
      <Box sx={{ marginBottom: '32px' }}>
        <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <CheckCircleIcon />
              </SummaryIcon>
              <SummaryValue>{stats.completed}</SummaryValue>
              <SummaryLabel>Completed KPIs</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>
        <Grid item xs={12} sm={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <WarningIcon />
              </SummaryIcon>
              <SummaryValue>{stats.needsAttention}</SummaryValue>
              <SummaryLabel>Needs Attention</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>
        <Grid item xs={12} sm={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <TrendingUpIcon />
              </SummaryIcon>
              <SummaryValue>{stats.progressScore}%</SummaryValue>
              <SummaryLabel>Progress Score</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>
      </Grid>
      </Box>
    )
  }

  const renderAssignTemplateDropdown = () => {
    if (isLoadingTemplateOrOptions) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CircularProgress size={20} />
          <Typography sx={{ fontFamily: style.FONT_MEDIUM }}>Loading templates...</Typography>
        </Box>
      )
    }

    if (loadTemplateOrOptionsError) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <WarningIcon sx={{ color: '#ff9800' }} />
          <Typography sx={{ color: '#ff9800' }}>
            Failed to load templates: {loadTemplateOrOptionsError}
          </Typography>
        </Box>
      )
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, marginBottom: 2 }}>
        <StyledFormControl>
          <InputLabel>Select Template</InputLabel>
          <Select
            value=""
            onChange={(e) => handleAssignTemplate(Number(e.target.value))}
            label="Select Template"
            disabled={isAssigning}
          >
            <MenuItem value="" disabled>
              <Typography sx={{ color: '#666666' }}>Choose a template to assign...</Typography>
            </MenuItem>
            {(availableTemplates || []).map((template: any) => (
              <MenuItem key={template.id} value={template.id}>
                {template.title}
              </MenuItem>
            ))}
          </Select>
        </StyledFormControl>
        {isAssigning && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CircularProgress size={20} />
            <Typography sx={{ 
              fontSize: '14px', 
              fontFamily: style.FONT_MEDIUM, 
              color: '#666666' 
            }}>
              Assigning...
            </Typography>
          </Box>
        )}
      </Box>
    )
  }

  const renderNoTemplate = () => (
    <NoTemplateMessage>
      <AssignmentIcon sx={{ fontSize: '40px', color: '#CCCCCC', marginBottom: '12px' }} />
      <Typography variant="h6" sx={{ 
        marginBottom: '6px', 
        color: '#666666',
        fontSize: '16px',
        fontFamily: style.FONT_BOLD,
      }}>
        No Template Selected
      </Typography>
      <Typography variant="body2" sx={{ 
        marginBottom: '16px', 
        color: '#888888',
        fontSize: '13px',
        fontFamily: style.FONT_MEDIUM,
        lineHeight: 1.4,
      }}>
        Please select a template from the dropdown below to view KPI subcategories and start tracking performance metrics.
      </Typography>
      {renderAssignTemplateDropdown()}
    </NoTemplateMessage>
  )

  const renderAssignedTemplate = () => {
    if (!assignedTemplate) return null

    return (
      <Box 
        key={`template-${assignedTemplate.id}-${assignedTemplate.categories?.map((c: Category) => c.subcategories?.map((s: Subcategory) => s.value).join('-')).join('_')}`}
        sx={{ width: '100%' }}
      >
        {/* Template Header */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '24px',
          padding: '14px 18px',
          backgroundColor: alpha(style.PRIMARY_COLOR, 0.04),
          borderRadius: '6px',
          border: `1px solid ${alpha(style.PRIMARY_COLOR, 0.2)}`,
          boxShadow: '0 1px 4px rgba(0,0,0,0.08)',
        }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Typography variant="h6" sx={{ 
                color: style.PRIMARY_COLOR, 
                fontFamily: style.FONT_BOLD,
                marginBottom: '2px',
                fontSize: '18px',
              }}>
                {assignedTemplate.name}
              </Typography>
              {(isLoadingTemplateOrOptions || isLocalEditMode) && (
                <CircularProgress 
                  size={16} 
                  sx={{ color: style.PRIMARY_COLOR }}
                />
              )}
            </Box>
            <Typography sx={{
              color: '#666666',
              fontFamily: style.FONT_MEDIUM,
              fontSize: '12px',
            }}>
              Template assigned • {assignedTemplate.categories?.length || 0} categories • {
                assignedTemplate.categories?.reduce((total: number, cat: any) => total + (cat.subcategories?.length || 0), 0) || 0
              } KPIs
              {isLoadingTemplateOrOptions && (
                <Typography component="span" sx={{ 
                  color: style.PRIMARY_COLOR, 
                  marginLeft: '6px',
                  fontSize: '11px',
                  fontWeight: 'bold',
                }}>
                  • Loading...
                </Typography>
              )}
              {isLocalEditMode && !isLoadingTemplateOrOptions && (
                <Typography component="span" sx={{ 
                  color: '#ff9800', 
                  marginLeft: '6px',
                  fontSize: '11px',
                  fontWeight: 'bold',
                }}>
                  • Saving...
                </Typography>
              )}
            </Typography>
          </Box>
          {isEditMode ? (
            <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
                startIcon={isEditing ? <CircularProgress size={14} color="inherit" /> : <SaveIcon />}
              onClick={handleSaveChanges}
              disabled={!hasUnsavedChanges || isEditing}
              sx={{
                  borderRadius: '20px',
                fontFamily: style.FONT_MEDIUM,
                  padding: '8px 16px',
                  fontSize: '13px',
                textTransform: 'none',
                  boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
                '&:disabled': {
                  backgroundColor: '#CCCCCC',
                },
              }}
            >
              {isEditing ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={handleToggleEditMode}
                disabled={isLoadingTemplateOrOptions || isLocalEditMode}
              sx={{
                  borderRadius: '20px',
                fontFamily: style.FONT_MEDIUM,
                  padding: '8px 16px',
                  fontSize: '13px',
                textTransform: 'none',
                  boxShadow: 'none',
              }}
            >
                Cancel Edit
            </Button>
          </Box>
          ) : (
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleToggleEditMode}
              disabled={isLoadingTemplateOrOptions || isLocalEditMode}
              sx={{
                borderRadius: '20px',
                fontFamily: style.FONT_MEDIUM,
                padding: '8px 16px',
                fontSize: '13px',
                textTransform: 'none',
                boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
              }}
            >
              Edit KPIs
            </Button>
          )}
        </Box>

        {/* Summary Cards */}
        {renderSummaryCards()}

        {/* KPI Categories */}
        <Box>
        {(assignedTemplate.categories || []).map((category: any, categoryIndex: number) => (
            <Box key={category.id} sx={{ marginBottom: '28px' }}>
            <CategoryHeader>
              <CategoryHeaderLeft>
                  <BarChartIcon sx={{ fontSize: '20px' }} />
                <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <CategoryTitle>{category.name}</CategoryTitle>
                  <CategoryCount>{(category.subcategories || []).length} KPI{(category.subcategories || []).length !== 1 ? 's' : ''}</CategoryCount>
                </Box>
              </CategoryHeaderLeft>
            </CategoryHeader>

                <Grid container spacing={2}>
              {(category.subcategories || []).map((subcategory: any, subIndex: number) => {
                const key = `${categoryIndex}-${subIndex}`
                const editValue = editValues[key] || ''
                const displayValue = isEditMode ? editValue : (subcategory.value || '')
                  const status = getKPIStatus(subcategory)
                  const statusDisplay = getStatusDisplay(status)

                return (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={subcategory.id}>
                    <KPICard isEditing={isEditMode}>
                      <KPICardContent>
                          {/* Header Section - Compact and consistent */}
                          <Box sx={{ 
                            display: 'flex', 
                            justifyContent: 'space-between', 
                            alignItems: 'flex-start', 
                            marginBottom: '6px',
                            minHeight: '24px' // More compact header height
                          }}>
                            <KPITitle sx={{ maxWidth: '70%' }}>{subcategory.title}</KPITitle>
                            {!isEditMode && (
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                                {statusDisplay.icon && (
                                  <Tooltip title={statusDisplay.text}>
                                    {statusDisplay.icon}
                                  </Tooltip>
                                )}
                                {status !== 'no-data' && (
                                  <Box sx={{
                                    padding: '2px 6px',
                                    borderRadius: '8px',
                                    backgroundColor: statusDisplay.bgColor,
                                    border: `1px solid ${statusDisplay.color}`,
                                  }}>
                                    <Typography sx={{
                                      fontSize: '8px',
                                      fontFamily: style.FONT_BOLD,
                                      color: statusDisplay.color,
                                      textTransform: 'uppercase',
                                      letterSpacing: '0.3px',
                                    }}>
                                      {status === 'completed' ? 'ACHIEVED' : 'NOT ACHIEVED'}
                                    </Typography>
                                  </Box>
                                )}
                              </Box>
                            )}
                          </Box>
                          
                        {isEditMode ? (
                            <Box sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              flex: 1,
                              position: 'relative',
                              paddingBottom: '22px' // Space for target section
                            }}>
                              {/* Input Section - Always at top */}
                              <Box sx={{ marginTop: '6px', marginBottom: '10px' }}>
                                {isQualitativeKPI(subcategory) ? (
                                  <FormControl size="small" fullWidth>
                                    <InputLabel 
                                      sx={{ 
                                        fontSize: '11px',
                                        fontFamily: style.FONT_MEDIUM,
                                      }}
                                    >
                                      Status
                                    </InputLabel>
                                    <Select
                                      value={editValue}
                                      label="Status"
                                      onChange={(e) => handleEditValueChange(key, e.target.value)}
                                      sx={{
                                        fontSize: '12px',
                                        fontFamily: style.FONT_MEDIUM,
                                        borderRadius: '4px',
                                        backgroundColor: '#FAFAFA',
                                        height: '36px',
                                        '&:hover .MuiOutlinedInput-notchedOutline': {
                                          borderColor: style.PRIMARY_COLOR,
                                        },
                                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                          borderColor: style.PRIMARY_COLOR,
                                        },
                                      }}
                                    >
                                      <MenuItem value="">Select Status</MenuItem>
                                      <MenuItem value="1">Achieved</MenuItem>
                                      <MenuItem value="0">Not Achieved</MenuItem>
                                    </Select>
                                  </FormControl>
                                ) : (
                                  <TextField
                                    size="small"
                                    value={editValue}
                                    onChange={(e) => handleEditValueChange(key, e.target.value)}
                                    placeholder={`Enter KPI value...`}
                                    label="KPI Value"
                                    variant="outlined"
                                    fullWidth
                                    sx={{
                                      '& .MuiOutlinedInput-root': {
                                        fontSize: '12px',
                                        fontFamily: style.FONT_MEDIUM,
                                        borderRadius: '4px',
                                        backgroundColor: '#FAFAFA',
                                        height: '36px',
                                        '&:hover fieldset': {
                                          borderColor: style.PRIMARY_COLOR,
                                        },
                                        '&.Mui-focused fieldset': {
                                          borderColor: style.PRIMARY_COLOR,
                                        },
                                      },
                                      '& .MuiInputLabel-root': {
                                        fontSize: '11px',
                                        fontFamily: style.FONT_MEDIUM,
                                      },
                                    }}
                                  />
                                )}
                              </Box>
                              
                              {/* Target Section - Always at bottom */}
                              <Box sx={{
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0,
                                padding: '4px 6px',
                                backgroundColor: alpha(style.PRIMARY_COLOR, 0.08),
                                borderRadius: '3px',
                                textAlign: 'center',
                              }}>
                                <Typography sx={{ 
                                  fontSize: '9px', 
                                  color: style.PRIMARY_COLOR, 
                                  fontFamily: style.FONT_MEDIUM,
                                }}>
                                  Target: {getTargetDisplay(subcategory)}
                                </Typography>
                              </Box>
                            </Box>
                        ) : (
                            <Box sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              justifyContent: 'center',
                              flex: 1,
                              gap: '6px'
                            }}>
                              {/* KPI Type */}
                              <Box sx={{ textAlign: 'center' }}>
                                <KPIType sx={{ 
                                  marginBottom: 0,
                                  fontSize: '9px',
                                  padding: '1px 4px'
                                }}>{subcategory.type}</KPIType>
                              </Box>
                              
                              {/* KPI Value - Consistent center alignment for both types */}
                              <Box sx={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                alignItems: 'center', 
                                padding: '6px',
                                backgroundColor: alpha(style.PRIMARY_COLOR, 0.05),
                                borderRadius: '4px',
                                borderLeft: `3px solid ${statusDisplay.color}`,
                                minHeight: '32px', // More compact minimum height
                              }}>
                                {isQualitativeKPI(subcategory) ? (
                                  <KPIValue sx={{ 
                                    fontSize: '13px',
                                    textAlign: 'center',
                                    lineHeight: 1.2,
                                  }}>
                                    {displayValue === '1' ? 'Achieved' : displayValue === '0' ? 'Not Achieved' : (displayValue || '-')}
                                  </KPIValue>
                                ) : (
                                  <KPIValue sx={{ 
                                    fontSize: '13px',
                                    textAlign: 'center',
                                    lineHeight: 1.2,
                                  }}>
                                    {displayValue || '-'}
                                  </KPIValue>
                                )}
                              </Box>
                            </Box>
                        )}
                      </KPICardContent>
                    </KPICard>
                  </Grid>
                )
              })}
                </Grid>
          </Box>
        ))}
        </Box>

        {/* Template Feedback Section */}
        {assignedTemplate && (
          <Box sx={{ marginTop: '32px' }}>
            <CategoryHeader>
              <CategoryHeaderLeft>
                <FeedbackIcon sx={{ fontSize: '20px' }} />
                <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <CategoryTitle>Feedback & Actions</CategoryTitle>
                  <CategoryCount>Template-level insights</CategoryCount>
                </Box>
              </CategoryHeaderLeft>
            </CategoryHeader>

            <Grid container spacing={2}>
              {/* Highlights Card */}
              <Grid item xs={12} md={4}>
                <FeedbackCard isEditing={isEditMode}>
                  <FeedbackCardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: '8px', gap: '8px' }}>
                      <StarIcon sx={{ fontSize: '16px', color: '#4CAF50' }} />
                      <KPITitle sx={{ color: '#4CAF50' }}>Highlights</KPITitle>
                    </Box>
                    
                    {isEditMode ? (
                      <BulletTextField
                        rows={4}
                        value={editTemplateHighlights}
                        onChange={(value) => setEditTemplateHighlights(value)}
                        placeholder="Enter key achievements and highlights..."
                        variant="outlined"
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            fontSize: '13px',
                            fontFamily: style.FONT_MEDIUM,
                            borderRadius: '6px',
                            backgroundColor: '#FAFAFA',
                            '&:hover fieldset': {
                              borderColor: '#4CAF50',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#4CAF50',
                              borderWidth: '2px',
                            },
                          },
                        }}
                      />
                    ) : (
                      <Box sx={{
                        padding: '12px',
                        backgroundColor: alpha('#4CAF50', 0.05),
                        borderRadius: '6px',
                        border: `1px solid ${alpha('#4CAF50', 0.2)}`,
                        height: '140px',
                        overflowY: 'auto',
                        '&::-webkit-scrollbar': {
                          width: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          background: alpha('#4CAF50', 0.1),
                          borderRadius: '3px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: alpha('#4CAF50', 0.3),
                          borderRadius: '3px',
                          '&:hover': {
                            backgroundColor: alpha('#4CAF50', 0.5),
                          },
                        },
                      }}>
                        <Typography sx={{
                          fontSize: '13px',
                          fontFamily: style.FONT_MEDIUM,
                          color: '#2E7D32',
                          lineHeight: 1.5,
                        }}>
                          {formatBulletText(assignedTemplate.highlights || '')}
                        </Typography>
                      </Box>
                    )}
                  </FeedbackCardContent>
                </FeedbackCard>
              </Grid>

              {/* Lowlights Card */}
              <Grid item xs={12} md={4}>
                <FeedbackCard isEditing={isEditMode}>
                  <FeedbackCardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: '8px', gap: '8px' }}>
                      <TrendingDownIcon sx={{ fontSize: '16px', color: '#FF9800' }} />
                      <KPITitle sx={{ color: '#FF9800' }}>Lowlights</KPITitle>
                    </Box>
                    
                    {isEditMode ? (
                      <BulletTextField
                        rows={4}
                        value={editTemplateLowlights}
                        onChange={(value) => setEditTemplateLowlights(value)}
                        placeholder="Enter areas that need improvement..."
                        variant="outlined"
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            fontSize: '13px',
                            fontFamily: style.FONT_MEDIUM,
                            borderRadius: '6px',
                            backgroundColor: '#FAFAFA',
                            '&:hover fieldset': {
                              borderColor: '#FF9800',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF9800',
                              borderWidth: '2px',
                            },
                          },
                        }}
                      />
                    ) : (
                      <Box sx={{
                        padding: '12px',
                        backgroundColor: alpha('#FF9800', 0.05),
                        borderRadius: '6px',
                        border: `1px solid ${alpha('#FF9800', 0.2)}`,
                        height: '140px',
                        overflowY: 'auto',
                        '&::-webkit-scrollbar': {
                          width: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          background: alpha('#FF9800', 0.1),
                          borderRadius: '3px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: alpha('#FF9800', 0.3),
                          borderRadius: '3px',
                          '&:hover': {
                            backgroundColor: alpha('#FF9800', 0.5),
                          },
                        },
                      }}>
                        <Typography sx={{
                          fontSize: '13px',
                          fontFamily: style.FONT_MEDIUM,
                          color: '#E65100',
                          lineHeight: 1.5,
                        }}>
                          {formatBulletText(assignedTemplate.lowlights || '')}
                        </Typography>
                      </Box>
                    )}
                  </FeedbackCardContent>
                </FeedbackCard>
              </Grid>

              {/* Action Items Card */}
              <Grid item xs={12} md={4}>
                <FeedbackCard isEditing={isEditMode}>
                  <FeedbackCardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: '8px', gap: '8px' }}>
                      <PlaylistAddCheckIcon sx={{ fontSize: '16px', color: '#2196F3' }} />
                      <KPITitle sx={{ color: '#2196F3' }}>Action Items</KPITitle>
                    </Box>
                    
                    {isEditMode ? (
                      <BulletTextField
                        rows={4}
                        value={editTemplateActions}
                        onChange={(value) => setEditTemplateActions(value)}
                        placeholder="Enter specific actions taken or planned..."
                        variant="outlined"
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            fontSize: '13px',
                            fontFamily: style.FONT_MEDIUM,
                            borderRadius: '6px',
                            backgroundColor: '#FAFAFA',
                            '&:hover fieldset': {
                              borderColor: '#2196F3',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#2196F3',
                              borderWidth: '2px',
                            },
                          },
                        }}
                      />
                    ) : (
                      <Box sx={{
                        padding: '12px',
                        backgroundColor: alpha('#2196F3', 0.05),
                        borderRadius: '6px',
                        border: `1px solid ${alpha('#2196F3', 0.2)}`,
                        height: '140px',
                        overflowY: 'auto',
                        '&::-webkit-scrollbar': {
                          width: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          background: alpha('#2196F3', 0.1),
                          borderRadius: '3px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: alpha('#2196F3', 0.3),
                          borderRadius: '3px',
                          '&:hover': {
                            backgroundColor: alpha('#2196F3', 0.5),
                          },
                        },
                      }}>
                        <Typography sx={{
                          fontSize: '13px',
                          fontFamily: style.FONT_MEDIUM,
                          color: '#1565C0',
                          lineHeight: 1.5,
                        }}>
                          {formatBulletText(assignedTemplate.actions_taken || '')}
                        </Typography>
                      </Box>
                    )}
                  </FeedbackCardContent>
                </FeedbackCard>
              </Grid>
            </Grid>
          </Box>
        )}
      </Box>
    )
  }

  // Determine if any operation is in progress
  const isAnyOperationInProgress = isLoadingTemplateOrOptions || isAssigning || isEditing || isFetchingTemplates || isLoading || isLocalAssigning

  // Show loader when any operation is in progress
  if (isAnyOperationInProgress) {
    return (
      <>
        <Loader state={isAnyOperationInProgress} />
        <div style={Maincontainer}>
      <StyledPaper>
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center', 
              padding: '40px 20px',
              minHeight: '200px',
        }}>
          <Typography sx={{
            color: '#666666',
            fontFamily: style.FONT_MEDIUM,
                fontSize: '14px',
                textAlign: 'center',
          }}>
                {isLoadingTemplateOrOptions && 'Loading KPI template data...'}
                {(isAssigning || isLocalAssigning) && 'Assigning template...'}
                {isEditing && 'Saving KPI changes...'}
                {isFetchingTemplates && 'Fetching available templates...'}
                {isLoading && 'Loading...'}
          </Typography>
        </Box>
      </StyledPaper>
        </div>
      </>
    )
  }

  return (
          <>
      <Loader state={isAnyOperationInProgress} />
      <div style={Maincontainer}>
    <StyledPaper>
      
      {loadTemplateOrOptionsError && (
        <Box sx={{ 
              marginBottom: '16px', 
              padding: '12px 16px', 
          backgroundColor: '#FFEBEE', 
              borderRadius: '6px',
          border: '1px solid #FFCDD2',
          display: 'flex',
          alignItems: 'center',
              gap: '8px',
        }}>
              <WarningIcon sx={{ color: '#C62828', fontSize: '18px' }} />
          <Box>
            <Typography sx={{ 
              color: '#C62828', 
                  fontSize: '13px',
              fontFamily: style.FONT_MEDIUM,
                  marginBottom: '2px',
            }}>
              Error Loading Template Data
            </Typography>
            <Typography sx={{ 
              color: '#D32F2F', 
                  fontSize: '11px',
              fontFamily: style.FONT_MEDIUM,
            }}>
              {loadTemplateOrOptionsError}
            </Typography>
          </Box>
        </Box>
      )}

      {assignedTemplate ? renderAssignedTemplate() : (isEditing || isLocalEditMode || isAssigning || isLocalAssigning) ? (
        <>
          <Loader state={true} />
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center', 
            padding: '40px 20px',
            minHeight: '200px',
          }}>
            <Typography sx={{
              color: '#666666',
              fontFamily: style.FONT_MEDIUM,
              fontSize: '14px',
              textAlign: 'center',
            }}>
              {(isEditing || isLocalEditMode) ? 'Saving KPI changes...' : 'Assigning template...'}
            </Typography>
          </Box>
        </>
      ) : renderNoTemplate()}
    </StyledPaper>
      </div>
    </>
  )
}

export default ManagerKPIDashboard 