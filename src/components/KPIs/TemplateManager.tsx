import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../configureStore';
import { 
  fetchTemplates, 
  createTemplate, 
  updateTemplate, 
  deleteTemplate, 
  searchTemplates 
} from '../../actions';
import {
  getTemplates,
  getTemplatesLoading,
  getTemplatesError,
  getSearchResults
} from '../../reducers/entities/templates';
import {
  Box,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  alpha,
  styled,
  Tooltip,
  Collapse,
  FormControl,
  InputLabel,
  Divider,
  Select,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import style from '../../utils/styles.json';
import { toast } from 'react-toastify';
import Loader from '../Common/Loader';
import BulletTextField, { formatBulletText } from '../Common/BulletTextField';

// Styled components
const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '20px auto',
  border: '1px solid #DDDDDD',
}));

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
};

const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '28px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '20px',
}));

const StyledButton = styled(Button)(() => ({
  fontSize: '13px',
  height: '42px',
  borderRadius: '20px',
  padding: '5px 20px',
  fontFamily: style.FONT_MEDIUM,
  background: style.PRIMARY_COLOR,
  color: '#FFFFFF',
  '&:hover': {
    background: alpha(style.PRIMARY_COLOR, 0.8),
  },
}));

const CancelButton = styled(Button)(() => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: style.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#D0D0D0',
    color: '#000000',
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(even)': {
    backgroundColor: '#FFFFFF',
  },
  '&:nth-of-type(odd)': {
    backgroundColor: '#FFFFFF',
  },
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
  },
}));

const StyledTableHeaderRow = styled(TableRow)(({ theme }) => ({
  backgroundColor: style.PRIMARY_COLOR,
  '&:hover': {
    backgroundColor: style.PRIMARY_COLOR,
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  padding: '16px',
  fontSize: '14px',
  fontFamily: style.FONT_MEDIUM,
  color: '#483F3F',
}));

const StyledHeaderTableCell = styled(TableCell)(({ theme }) => ({
  padding: '16px',
  fontSize: '14px',
  fontFamily: style.FONT_BOLD,
  color: '#FFFFFF',
  backgroundColor: style.PRIMARY_COLOR,
  fontWeight: 'bold',
}));

const StyledTextField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '25px',
    fontSize: '14px',
    fontFamily: style.FONT_MEDIUM,
    backgroundColor: '#FFFFFF',
    '&.Mui-focused fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#DDDDDD',
  },
  '& .MuiInputLabel-root': {
    fontFamily: style.FONT_MEDIUM,
    fontSize: '14px',
    color: '#666666',
    '&.Mui-focused': {
      color: style.PRIMARY_COLOR,
    },
  },
  '& input::placeholder': {
    color: '#999999',
    opacity: 0.8,
    fontStyle: 'italic',
  }
}));

const AddCategoryButton = styled(Button)(() => ({
  borderColor: style.PRIMARY_COLOR,
  color: '#FFFFFF',
  backgroundColor: style.PRIMARY_COLOR,
  borderRadius: '20px',
  textTransform: 'none',
  fontFamily: style.FONT_MEDIUM,
  padding: '8px 16px',
  fontSize: '14px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  '&:hover': {
    backgroundColor: alpha(style.PRIMARY_COLOR, 0.9),
    boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
  },
}));

const AddSubcategoryButton = styled(IconButton)(() => ({
  color: '#FFFFFF',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.9),
  width: '28px',
  height: '28px',
  '&:hover': {
    backgroundColor: style.PRIMARY_COLOR,
  },
}));

const CategorySection = styled(Box)(({ theme }) => ({
  backgroundColor: '#F8F9FA',
  border: '1px solid #E0E0E0',
  borderRadius: '12px',
  padding: '20px',
  marginBottom: '16px',
  boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
}));

const SubcategorySection = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFFFFF',
  border: '1px solid #E0E0E0',
  borderRadius: '8px',
  padding: '12px',
  marginTop: '8px',
  marginBottom: '6px',
  boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.05)',
}));

interface KPISubcategory {
  id: string;
  title: string;
  type: 'Qualitative' | 'Quantitative';
  defaultTarget: string;
}

interface KPICategory {
  id: string;
  name: string;
  subcategories: KPISubcategory[];
  expanded: boolean;
}

interface DialogTitleProps {
  id: string;
  children?: React.ReactNode;
  onClose: () => void;
}

interface TemplateManagerProps {
  open?: boolean;
  onClose?: () => void;
  isModal?: boolean;
}

// Template interface for API responses
export interface Template {
  id: string;
  title: string;
  description?: string;
  highlights?: string;
  lowlights?: string;
  actions_taken?: string;
  categories: {
    name: string;
    subcategories: {
      title: string;
      type: 'Qualitative' | 'Quantitative';
      defaultTarget: string;
    }[];
  }[];
  created_at?: Date;
}

// Add new interface for expanded descriptions
interface ExpandedDescriptions {
  [templateId: string]: boolean;
}

const BootstrapDialogTitle = React.memo((props: DialogTitleProps) => {
  const { children, onClose, ...other } = props;

  return (
    <DialogTitle sx={{ m: 0, p: 2, borderBottom: '1px solid #E0E0E0' }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  );
});

// Custom debounce hook
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Debounced text field component
const DebouncedTextField = React.memo(({ 
  value, 
  onChange, 
  debounceMs = 300,
  ...props 
}: any) => {
  const [localValue, setLocalValue] = useState(value || '');
  const debouncedValue = useDebounce(localValue, debounceMs);

  // Update local value when external value changes
  useEffect(() => {
    setLocalValue(value || '');
  }, [value]);

  // Call onChange when debounced value changes
  useEffect(() => {
    if (debouncedValue !== value) {
      onChange(debouncedValue);
    }
  }, [debouncedValue, onChange, value]);

  return (
    <StyledTextField
      {...props}
      value={localValue}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setLocalValue(e.target.value)}
    />
  );
});

// Direct input component for immediate feedback in forms
const DirectTextField = React.memo(({ 
  value, 
  onChange, 
  ...props 
}: any) => {
  return (
    <StyledTextField
      {...props}
      value={value || ''}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
    />
  );
});

// Memoized Category Component to prevent unnecessary re-renders
const CategoryComponent = React.memo(({ 
  category, 
  categoryIndex, 
  categoriesLength,
  onCategoryNameChange,
  onRemoveCategory,
  onAddSubcategory,
  onRemoveSubcategory,
  onSubcategoryChange,
  onSubcategoryTypeChange 
}: {
  category: KPICategory;
  categoryIndex: number;
  categoriesLength: number;
  onCategoryNameChange: (id: string, value: string) => void;
  onRemoveCategory: (id: string) => void;
  onAddSubcategory: (categoryId: string) => void;
  onRemoveSubcategory: (categoryId: string, subcategoryId: string) => void;
  onSubcategoryChange: (categoryId: string, subcategoryId: string, field: keyof KPISubcategory, value: string) => void;
  onSubcategoryTypeChange: (categoryId: string, subcategoryId: string, newType: 'Qualitative' | 'Quantitative') => void;
}) => {

  return (
    <Box sx={{ 
      mb: 3,
      border: '1px solid #E0E0E0', 
      borderRadius: '8px', 
      p: 2,
      backgroundColor: '#FFFFFF',
      overflow: 'hidden',
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
    }}>
      {/* Category Input and Remove Button */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1.5, 
        mb: 2,
        width: '100%',
      }}>
        <Box sx={{ width: '75%' }}>
          <DirectTextField
            label={`Category ${categoryIndex + 1} Name`}
            variant="outlined"
            fullWidth
            placeholder="Attendance and Presence"
            value={category.name}
            onChange={(value: string) => onCategoryNameChange(category.id, value)}
          />
        </Box>
        <Box sx={{ 
          width: '25%', 
          display: 'flex', 
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '56px',
        }}>
          {categoriesLength > 1 && (
            <Tooltip title="Remove Category">
              <IconButton 
                onClick={() => onRemoveCategory(category.id)}
                sx={{ 
                  color: '#FF4D4F',
                  '&:hover': {
                    backgroundColor: alpha('#FF4D4F', 0.1),
                  },
                }}
              >
                <RemoveIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Subcategories Section */}
      {category.subcategories.map((subcategory, subIndex) => (
        <SubcategorySection key={subcategory.id} sx={{ 
          mb: subIndex === category.subcategories.length - 1 ? 0 : 2,
        }}>
          {/* Subcategory Header with Remove Button */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 1.5,
          }}>
            <Typography variant="h6" sx={{ 
              fontSize: '16px',
              fontFamily: style.FONT_MEDIUM,
              color: '#333333',
            }}>
              Subcategory {subIndex + 1}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {/* Add Subcategory button (only show on first subcategory) */}
              {subIndex === 0 && (
                <Tooltip title="Add Subcategory">
                  <AddSubcategoryButton
                    onClick={() => onAddSubcategory(category.id)}
                  >
                    <AddIcon fontSize="small" />
                  </AddSubcategoryButton>
                </Tooltip>
              )}
              
              {/* Remove button for additional subcategories */}
              {subIndex > 0 && (
                <Tooltip title="Remove Subcategory">
                  <IconButton 
                    onClick={() => onRemoveSubcategory(category.id, subcategory.id)}
                    sx={{ 
                      color: '#FF4D4F',
                      '&:hover': {
                        backgroundColor: alpha('#FF4D4F', 0.1),
                      },
                    }}
                  >
                    <RemoveIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {/* Subcategory Fields */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            {/* Title */}
            <DirectTextField
              label="Title"
              variant="outlined"
              fullWidth
              placeholder="Attendance Status"
              value={subcategory.title}
              onChange={(value: string) => onSubcategoryChange(category.id, subcategory.id, 'title', value)}
            />

            {/* Type and Default Target in one row */}
            <Box sx={{ display: 'flex', gap: 1.5, alignItems: 'center' }}>
              {/* Type Dropdown */}
              <FormControl sx={{ width: '50%' }}>
                <InputLabel 
                  sx={{ 
                    fontFamily: style.FONT_MEDIUM,
                    fontSize: '15px',
                    color: '#666666',
                    '&.Mui-focused': {
                      color: style.PRIMARY_COLOR,
                    },
                  }}
                >
                  Type
                </InputLabel>
                <Select
                  value={subcategory.type}
                  label="Type"
                  onChange={(e) => {
                    onSubcategoryTypeChange(category.id, subcategory.id, e.target.value as 'Qualitative' | 'Quantitative');
                  }}
                  sx={{
                    borderRadius: '20px',
                    fontSize: '13px',
                    fontFamily: style.FONT_MEDIUM,
                    backgroundColor: '#FFFFFF',
                    height: '56px', // Fixed height for alignment
                    '& .MuiSelect-select': {
                      padding: '14px 12px',
                      display: 'flex',
                      alignItems: 'center',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: style.PRIMARY_COLOR,
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#DDDDDD',
                    },
                  }}
                >
                  <MenuItem value="Qualitative">Qualitative</MenuItem>
                  <MenuItem value="Quantitative">Quantitative</MenuItem>
                </Select>
              </FormControl>

              {/* Dynamic Default Target Field based on type */}
              {subcategory.type === 'Qualitative' ? (
                <TextField
                  label="Target Value"
                  variant="outlined"
                  sx={{ 
                    width: '50%',
                    '& .MuiOutlinedInput-root': {
                      height: '56px', // Fixed height for alignment
                      backgroundColor: '#f5f5f5',
                    },
                    '& .MuiOutlinedInput-input': {
                      padding: '14px 12px',
                      fontSize: '13px',
                      fontFamily: style.FONT_MEDIUM,
                      color: '#666666',
                    },
                    '& .MuiFormLabel-root': {
                      fontSize: '15px',
                      fontFamily: style.FONT_MEDIUM,
                    },
                    '& .MuiInputBase-root.MuiOutlinedInput-root': {
                      borderRadius: '20px',
                    },
                  }}
                  value="Achieved (1)"
                  disabled
                />
              ) : (
                <TextField
                  label="Condition (e.g., <10, >=20)"
                  variant="outlined"
                  sx={{ 
                    width: '50%',
                    '& .MuiOutlinedInput-root': {
                      height: '56px', // Fixed height for alignment
                    },
                    '& .MuiOutlinedInput-input': {
                      padding: '14px 12px',
                      fontSize: '13px',
                      fontFamily: style.FONT_MEDIUM,
                    },
                    '& .MuiFormLabel-root': {
                      fontSize: '15px',
                      fontFamily: style.FONT_MEDIUM,
                    },
                    '& .MuiInputBase-root.MuiOutlinedInput-root': {
                      borderRadius: '20px',
                    },
                  }}
                  placeholder=">=10"
                  value={subcategory.defaultTarget}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Allow operators and numbers: >, <, >=, <=, =, followed by numbers
                    if (value === '' || /^[<>=]*\d*\.?\d*$/.test(value)) {
                      onSubcategoryChange(category.id, subcategory.id, 'defaultTarget', value);
                    }
                  }}
                  inputProps={{
                    maxLength: 10
                  }}
                />
              )}
            </Box>
          </Box>
        </SubcategorySection>
      ))}
    </Box>
  );
});

const TemplateManager: React.FC<TemplateManagerProps> = ({ 
  open = false, 
  onClose, 
  isModal = false 
}) => {
  const dispatch = useDispatch();
  
  // Redux state
  const templates = useSelector((state: RootState) => getTemplates(state));
  const loading = useSelector((state: RootState) => getTemplatesLoading(state));
  const error = useSelector((state: RootState) => getTemplatesError(state));
  const searchResults = useSelector((state: RootState) => getSearchResults(state));
  
  // Get user ID from Redux store
  const userId = useSelector(
    (state: any) => state?.entities?.dashboard?.getUserDetails?.id
  );
  
  // Check if current user is authorized to access KPI features
  const isAuthorized = [562, 1259, 3106].includes(userId)
  
  // Redirect unauthorized users
  useEffect(() => {
    if (!isAuthorized) {
      window.location.href = '/home'
    }
  }, [isAuthorized])
  
  // Local state
  const [search, setSearch] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [openTemplateModal, setOpenTemplateModal] = useState(isModal ? open : false);
  const [templateTitle, setTemplateTitle] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [highlights, setHighlights] = useState('');
  const [lowlights, setLowlights] = useState('');
  const [actionsTaken, setActionsTaken] = useState('');
  const [kpiCategories, setKpiCategories] = useState<KPICategory[]>([]);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);
  const [expandedDescriptions, setExpandedDescriptions] = useState<ExpandedDescriptions>({});
  const [viewTemplateModal, setViewTemplateModal] = useState(false);
  const [viewingTemplate, setViewingTemplate] = useState<Template | null>(null);
  
  // Loading states for individual operations
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Function to toggle description expansion
  const toggleDescriptionExpansion = useCallback((templateId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [templateId]: !prev[templateId]
    }));
  }, []);

  // Function to truncate description
  const getTruncatedDescription = useCallback((description: string, maxLength: number = 100) => {
    if (!description) return 'No description available';
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  }, []);

  // Function to handle template name click for viewing
  const handleViewTemplate = useCallback((template: Template) => {
    setViewingTemplate(template);
    setViewTemplateModal(true);
  }, []);

  const handleCloseViewModal = useCallback(() => {
    setViewTemplateModal(false);
    setViewingTemplate(null);
  }, []);

  // Fetch templates on component mount
  useEffect(() => {
    dispatch(fetchTemplates());
  }, [dispatch]);

  // Search functionality with debouncing
  const debouncedSearch = useDebounce(search, 500);

  useEffect(() => {
    if (debouncedSearch.trim()) {
      dispatch(searchTemplates({ query: debouncedSearch.trim() }));
      }
  }, [debouncedSearch, dispatch]);

  const handleCreateTemplate = useCallback((templateData: Omit<Template, 'id' | 'created_at'>) => {
    setIsCreating(true);
    dispatch(createTemplate(templateData));
    // Refetch templates after successful creation
    setTimeout(() => {
      dispatch(fetchTemplates());
      setIsCreating(false);
    }, 500);
  }, [dispatch]);

  const handleUpdateTemplate = useCallback((id: string, templateData: Partial<Template>) => {
    setIsUpdating(true);
    dispatch(updateTemplate({ id, templateData }));
    // Refetch templates after successful update
    setTimeout(() => {
      dispatch(fetchTemplates());
      setIsUpdating(false);
    }, 500);
  }, [dispatch]);

  const handleDeleteConfirm = useCallback(() => {
    if (templateToDelete) {
      setIsDeleting(true);
      dispatch(deleteTemplate({ id: templateToDelete }));
      // Refetch templates after successful deletion
      setTimeout(() => {
        dispatch(fetchTemplates());
        setIsDeleting(false);
      setTemplateToDelete(null);
      setDeleteConfirmOpen(false);
      }, 500);
    }
  }, [dispatch, templateToDelete]);

  const handleDeleteTemplate = useCallback((id: string) => {
    setTemplateToDelete(id);
    setDeleteConfirmOpen(true);
  }, []);

  const handleOpenTemplateModal = useCallback(() => {
    setEditingTemplate(null);
    setTemplateTitle('');
    setTemplateDescription('');
    setHighlights('');
    setLowlights('');
    setActionsTaken('');
    setKpiCategories([{
      id: `cat-${Date.now()}`,
      name: '',
      subcategories: [              { 
                id: `sub-${Date.now()}`, 
                title: '',
                type: 'Qualitative',
                defaultTarget: 'Achieved (1)'
              }],
      expanded: true,
    }]);
    setOpenTemplateModal(true);
  }, []);


  
  const handleEditTemplate = useCallback((template: Template) => {
    setEditingTemplate(template);
    setTemplateTitle(template.title);
    setTemplateDescription(template.description || '');
    setHighlights(template.highlights || '');
    setLowlights(template.lowlights || '');
    setActionsTaken(template.actions_taken || '');
    
    // Convert template categories to KPICategory format
    const categories: KPICategory[] = template.categories.map((cat, index) => ({
      id: `cat-${index}-${Date.now()}`,
      name: cat.name,
      expanded: true,
      subcategories: cat.subcategories.map((sub, subIndex) => ({
        id: `sub-${index}-${subIndex}-${Date.now()}`,
        title: sub.title,
        type: sub.type,
        defaultTarget: sub.defaultTarget
      }))
    }));
    
    setKpiCategories(categories);
    setOpenTemplateModal(true);
  }, []);
  
  const handleCloseTemplateModal = useCallback(() => {
    setOpenTemplateModal(false);
    setTemplateTitle('');
    setTemplateDescription('');
          setHighlights('');
      setLowlights('');
      setActionsTaken('');
    setKpiCategories([]);
    setEditingTemplate(null);
    if (onClose) onClose();
  }, [onClose]);

  const handleAddKPICategory = useCallback(() => {
    const newCategory: KPICategory = {
      id: Date.now().toString(),
      name: '',
      subcategories: [{ 
        id: `sub-${Date.now()}`, 
        title: '',
        type: 'Qualitative',
        defaultTarget: 'Achieved (1)'
      }],
      expanded: true,
    };
    setKpiCategories(prev => [...prev, newCategory]);
  }, []);

  const handleRemoveKPICategory = useCallback((id: string) => {
    setKpiCategories(prev => prev.length > 1 ? prev.filter(category => category.id !== id) : prev);
  }, []);

  const handleKPICategoryChange = useCallback((id: string, value: string) => {
    setKpiCategories(prev => prev.map(category => 
      category.id === id ? { ...category, name: value } : category
    ));
  }, []);

  const handleAddSubcategory = useCallback((categoryId: string) => {
    setKpiCategories(prev => prev.map(category => 
      category.id === categoryId 
        ? {
            ...category,
            subcategories: [
              ...category.subcategories,
              { 
                id: `sub-${Date.now()}`, 
                title: '',
                type: 'Qualitative',
                defaultTarget: 'Achieved (1)'
              }
            ]
          }
        : category
    ));
  }, []);

  const handleRemoveSubcategory = useCallback((categoryId: string, subcategoryId: string) => {
    setKpiCategories(prev => prev.map(category => 
      category.id === categoryId 
        ? {
            ...category,
            subcategories: category.subcategories.length > 1 
              ? category.subcategories.filter(sub => sub.id !== subcategoryId)
              : category.subcategories
          }
        : category
    ));
  }, []);

  const handleSubcategoryChange = useCallback((categoryId: string, subcategoryId: string, field: keyof KPISubcategory, value: string) => {
    setKpiCategories(prev => prev.map(category => 
      category.id === categoryId 
        ? {
            ...category,
            subcategories: category.subcategories.map(sub =>
              sub.id === subcategoryId ? { ...sub, [field]: value } : sub
            )
          }
        : category
    ));
  }, []);

  const handleSubcategoryTypeChange = useCallback((categoryId: string, subcategoryId: string, newType: 'Qualitative' | 'Quantitative') => {
    setKpiCategories(prev => prev.map(category => 
      category.id === categoryId 
        ? {
            ...category,
            subcategories: category.subcategories.map(sub =>
              sub.id === subcategoryId 
                ? { 
                    ...sub, 
                    type: newType, 
                    defaultTarget: newType === 'Qualitative' ? 'Achieved (1)' : '' // Set default "Achieved (1)" for qualitative, empty for quantitative
                  }
                : sub
            )
          }
        : category
    ));
  }, []);

  // Memoized form validation to prevent flickering
  const isFormValid = useMemo(() => {
    return true; // Always allow saving
  }, []);

  const handleSaveTemplate = useCallback(async () => {
    // Validate form before submission
    if (!isFormValid) {
      toast.warning('Please fill in all required fields');
      return;
    }

    const templateData = {
      title: templateTitle.trim(),
      description: templateDescription.trim(),
      highlights: highlights.trim(),
      lowlights: lowlights.trim(),
      actions_taken: actionsTaken.trim(),
      categories: kpiCategories
        .filter(cat => cat.name.trim()) // Only include categories with names
        .map(cat => ({
          name: cat.name.trim(),
          subcategories: cat.subcategories
            .filter(sub => sub.title.trim() && sub.defaultTarget.trim()) // Only include complete subcategories
            .map(sub => ({
              title: sub.title.trim(),
              type: sub.type,
              defaultTarget: sub.defaultTarget.trim()
            }))
        }))
        .filter(cat => cat.subcategories.length > 0) // Only include categories with at least one subcategory
    };
    
    // Final validation - ensure we have at least one valid category with subcategories
    if (templateData.categories.length === 0) {
      toast.warning('Please add at least one category with subcategories');
      return;
    }
    
    try {
      if (editingTemplate) {
        // Update existing template
        handleUpdateTemplate(editingTemplate.id, templateData);
      } else {
        // Create new template
        handleCreateTemplate(templateData);
      }
      handleCloseTemplateModal();
    } catch (error) {
      // Error is already handled in the API functions with toast
      console.error('Error saving template:', error);
    }
  }, [templateTitle, templateDescription, highlights, lowlights, actionsTaken, kpiCategories, editingTemplate, handleUpdateTemplate, handleCreateTemplate, handleCloseTemplateModal, isFormValid]);

  const TemplateDialog = useMemo(() => (
    <Dialog 
      open={openTemplateModal} 
      onClose={handleCloseTemplateModal}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-container': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        '& .MuiDialog-paper': {
          width: '70%',
          maxWidth: '700px',
          borderRadius: '25px',
          maxHeight: '90vh',
          overflowY: 'auto',
        },
        '& .MuiDialogContent-root': {
          padding: '20px 30px',
        },
        '& .MuiDialogActions-root': {
          justifyContent: 'center',
          gap: '15px',
          padding: '20px 0',
        },
      }}
    >
      <BootstrapDialogTitle 
        id="customized-dialog-title" 
        onClose={handleCloseTemplateModal}
      >
        <Typography
          sx={{ 
            fontSize: '26px',
            textAlign: 'center',
            fontFamily: style.FONT_BOLD,
            letterSpacing: '0px',
            padding: '10px'
          }}
        >
          {editingTemplate ? 'Edit Template for KPI' : 'Create Template for KPI'}
        </Typography>
      </BootstrapDialogTitle>

      <DialogContent>
        {/* Template Name */}
        <Box sx={{ marginTop: '5px', marginBottom: '15px' }}>
          <DirectTextField
            label="Template Name"
            variant="outlined"
            fullWidth
            placeholder="Enter template name..."
            value={templateTitle}
            onChange={(value: string) => setTemplateTitle(value)}
            sx={{
              '& .MuiOutlinedInput-input': {
                padding: '14px 12px',
                fontSize: '13px',
                fontFamily: style.FONT_MEDIUM,
              },
              '& .MuiFormLabel-root': {
                fontSize: '15px',
                fontFamily: style.FONT_MEDIUM,
              },
              '& .MuiInputBase-root.MuiOutlinedInput-root': {
                borderRadius: '20px',
              },
            }}
          />
        </Box>

        {/* Add Category Button */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '15px' }}>
          <Button
            variant="contained"
            onClick={handleAddKPICategory}
            startIcon={<AddIcon />}
            sx={{
              fontSize: '13px',
              height: '42px',
              fontFamily: style.FONT_BOLD,
              borderRadius: '20px',
              textTransform: 'none',
              backgroundColor: style.PRIMARY_COLOR,
              '&:hover': {
                backgroundColor: style.PRIMARY_COLOR,
              }
            }}
          >
            Add Category
          </Button>
        </Box>

        {/* Template Description */}
        <Box sx={{ marginTop: '5px', marginBottom: '15px' }}>
          <DirectTextField
            label="Template Description"
            variant="outlined"
            fullWidth
            multiline
            rows={2}
            placeholder="Enter a brief description of this template..."
            value={templateDescription}
            onChange={(value: string) => setTemplateDescription(value)}
            sx={{
              '& .MuiOutlinedInput-input': {
                padding: '8px 12px',
                fontSize: '13px',
                fontFamily: style.FONT_MEDIUM,
              },
              '& .MuiFormLabel-root': {
                fontSize: '15px',
                fontFamily: style.FONT_MEDIUM,
              },
              '& .MuiInputBase-root.MuiOutlinedInput-root': {
                borderRadius: '20px',
              },
            }}
          />
        </Box>

        {/* Feedback Section */}
        <Box sx={{ 
          marginTop: '10px',
          marginBottom: '15px',
        }}>
          {/* Highlights */}
          <Box sx={{ marginTop: '5px', marginBottom: '15px' }}>
            <BulletTextField
              label="Highlights"
              variant="outlined"
              fullWidth
              rows={2}
              placeholder="Enter key achievements and positive points..."
              value={highlights}
              onChange={(value: string) => setHighlights(value)}
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '8px 12px',
                  fontSize: '13px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiFormLabel-root': {
                  fontSize: '15px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiInputBase-root.MuiOutlinedInput-root': {
                  borderRadius: '20px',
                },
              }}
            />
          </Box>

          {/* Lowlights */}
          <Box sx={{ marginTop: '5px', marginBottom: '15px' }}>
            <BulletTextField
              label="Lowlights"
              variant="outlined"
              fullWidth
              rows={2}
              placeholder="Enter areas needing improvement..."
              value={lowlights}
              onChange={(value: string) => setLowlights(value)}
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '8px 12px',
                  fontSize: '13px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiFormLabel-root': {
                  fontSize: '15px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiInputBase-root.MuiOutlinedInput-root': {
                  borderRadius: '20px',
                },
              }}
            />
          </Box>

          {/* Action Items */}
          <Box sx={{ marginTop: '5px', marginBottom: '15px' }}>
            <BulletTextField
              label="Action Items"
              variant="outlined"
              fullWidth
              rows={2}
              placeholder="Enter specific actions to be taken..."
              value={actionsTaken}
              onChange={(value: string) => setActionsTaken(value)}
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '8px 12px',
                  fontSize: '13px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiFormLabel-root': {
                  fontSize: '15px',
                  fontFamily: style.FONT_MEDIUM,
                },
                '& .MuiInputBase-root.MuiOutlinedInput-root': {
                  borderRadius: '20px',
                },
              }}
            />
          </Box>
        </Box>

        {/* Categories Section */}
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          gap: 2,
        }}>
          {kpiCategories.map((category, index) => (
            <CategoryComponent
              key={category.id}
              category={category}
              categoryIndex={index}
              categoriesLength={kpiCategories.length}
              onCategoryNameChange={handleKPICategoryChange}
              onRemoveCategory={handleRemoveKPICategory}
              onAddSubcategory={handleAddSubcategory}
              onRemoveSubcategory={handleRemoveSubcategory}
              onSubcategoryChange={handleSubcategoryChange}
              onSubcategoryTypeChange={handleSubcategoryTypeChange}
            />
          ))}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          sx={{
            background: '#E2E2E2',
            color: '#000000',
            fontSize: '13px',
            height: '42px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            borderRadius: '20px',
            '&:hover': {
              background: '#E2E2E2',
              color: '#000000',
            },
          }}
          onClick={handleCloseTemplateModal}
        >
          CANCEL
        </Button>
        <Button 
          variant="contained"
          onClick={handleSaveTemplate}
          disabled={!isFormValid || loading}
          sx={{
            fontSize: '13px',
            height: '42px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            borderRadius: '20px',
            '&.Mui-disabled': {
              opacity: 0.5,
              color: '#ffffff',
              cursor: 'not-allowed',
            },
          }}
        >
          {loading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            editingTemplate ? 'UPDATE' : 'CREATE'
          )}
        </Button>
      </DialogActions>
    </Dialog>
  ), [
    openTemplateModal,
    handleCloseTemplateModal,
    editingTemplate,
    templateTitle,
    templateDescription,
    highlights,
    lowlights,
    actionsTaken,
    kpiCategories,
    handleAddKPICategory,
    handleKPICategoryChange,
    handleRemoveKPICategory,
    handleAddSubcategory,
    handleRemoveSubcategory,
    handleSubcategoryChange,
    handleSubcategoryTypeChange,
    handleSaveTemplate,
    isFormValid,
    loading
  ]);
  
  // Template View Modal (Read-only)
  const TemplateViewDialog = useMemo(() => (
    <Dialog 
      open={viewTemplateModal} 
      onClose={handleCloseViewModal}
      maxWidth="lg"
      fullWidth
      sx={{
        '& .MuiDialog-container': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        '& .MuiDialog-paper': {
          width: '80%',
          maxWidth: '900px',
          borderRadius: '25px',
          maxHeight: '90vh',
          overflowY: 'auto',
        },
        '& .MuiDialogContent-root': {
          padding: '20px 30px',
        },
        '& .MuiDialogActions-root': {
          justifyContent: 'center',
          gap: '15px',
          padding: '20px 0',
        },
      }}
    >
      <BootstrapDialogTitle 
        id="template-view-dialog-title" 
        onClose={handleCloseViewModal}
      >
        <Typography
                      sx={{ 
              fontSize: '26px',
              textAlign: 'center',
              fontFamily: style.FONT_BOLD,
              letterSpacing: '0px',
              padding: '10px'
            }}
        >
          {viewingTemplate?.title} Template
        </Typography>
      </BootstrapDialogTitle>

      <DialogContent>
        {viewingTemplate && (
          <Box sx={{ width: '100%', flex: 1 }}>
            {/* Template Description */}
            {viewingTemplate.description && (
              <Box sx={{ marginBottom: '20px' }}>
                <Typography 
                  sx={{ 
                    fontSize: '16px',
                    fontFamily: style.FONT_BOLD,
                    marginBottom: '10px'
                  }}
                >
                  Description
                </Typography>
                <Typography 
                  sx={{ 
                    fontSize: '14px',
                    fontFamily: style.FONT_MEDIUM,
                    color: '#666666'
                  }}
                >
                  {viewingTemplate.description}
                </Typography>
              </Box>
            )}

            {/* Feedback Section */}
            <Box sx={{ marginBottom: '20px' }}>
              <Typography 
                sx={{ 
                  fontSize: '16px',
                  fontFamily: style.FONT_BOLD,
                  marginBottom: '15px'
                }}
              >
                Feedback & Actions
              </Typography>

              {/* Highlights */}
              {viewingTemplate.highlights && (
                <Box sx={{ marginBottom: '15px' }}>
                  <Typography 
                    sx={{ 
                      fontSize: '14px',
                      fontFamily: style.FONT_BOLD,
                      marginBottom: '5px'
                    }}
                  >
                    Highlights
                  </Typography>
                  <Typography 
                    sx={{ 
                      fontSize: '13px',
                      fontFamily: style.FONT_MEDIUM,
                      color: '#666666'
                    }}
                  >
                    {viewingTemplate.highlights}
                  </Typography>
                </Box>
              )}

              {/* Lowlights */}
              {viewingTemplate.lowlights && (
                <Box sx={{ marginBottom: '15px' }}>
                  <Typography 
                    sx={{ 
                      fontSize: '14px',
                      fontFamily: style.FONT_BOLD,
                      marginBottom: '5px'
                    }}
                  >
                    Lowlights
                  </Typography>
                  <Typography 
                    sx={{ 
                      fontSize: '13px',
                      fontFamily: style.FONT_MEDIUM,
                      color: '#666666'
                    }}
                  >
                    {viewingTemplate.lowlights}
                  </Typography>
                </Box>
              )}

              {/* Action Items */}
              {viewingTemplate.actions_taken && (
                <Box sx={{ marginBottom: '15px' }}>
                  <Typography 
                    sx={{ 
                      fontSize: '14px',
                      fontFamily: style.FONT_BOLD,
                      marginBottom: '5px'
                    }}
                  >
                    Action Items
                  </Typography>
                  <Typography 
                    sx={{ 
                      fontSize: '13px',
                      fontFamily: style.FONT_MEDIUM,
                      color: '#666666'
                    }}
                  >
                    {viewingTemplate.actions_taken}
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Categories Table */}
            <Box sx={{ 
              width: '100%', 
              maxHeight: '400px',
              overflowY: 'auto',
              overflowX: 'hidden',
              paddingRight: '4px',
              marginRight: '-4px',
              '&::-webkit-scrollbar': {
                width: '4px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: style.PRIMARY_COLOR,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                backgroundColor: '#555',
              },
            }}>
              <TableContainer component={Paper} elevation={1} sx={{ borderRadius: '8px' }}>
                <Table size="small">
                  <TableHead>
                    <StyledTableHeaderRow>
                      <StyledHeaderTableCell sx={{ width: '30%' }} align="center">Category</StyledHeaderTableCell>
                      <StyledHeaderTableCell sx={{ width: '40%' }} align="center">Subcategory</StyledHeaderTableCell>
                      <StyledHeaderTableCell sx={{ width: '15%' }} align="center">Type</StyledHeaderTableCell>
                      <StyledHeaderTableCell sx={{ width: '15%' }} align="center">Target</StyledHeaderTableCell>
                    </StyledTableHeaderRow>
                  </TableHead>
                  <TableBody>
                    {viewingTemplate.categories.map((category, categoryIndex) => 
                      category.subcategories.map((subcategory, subIndex) => (
                        <StyledTableRow key={`${categoryIndex}-${subIndex}`}>
                          <StyledTableCell 
                            sx={{ 
                              width: '30%',
                              fontWeight: subIndex === 0 ? 'bold' : 'normal',
                              borderRight: '1px solid #E0E0E0',
                              backgroundColor: subIndex === 0 ? '#F8F9FA' : 'inherit',
                            }} 
                            align="center"
                          >
                            {subIndex === 0 ? category.name : ''}
                          </StyledTableCell>
                          <StyledTableCell 
                            sx={{ 
                              width: '40%',
                              borderRight: '1px solid #E0E0E0',
                              fontWeight: 'bold',
                            }} 
                            align="left"
                          >
                            • {subcategory.title}
                          </StyledTableCell>
                          <StyledTableCell 
                            sx={{ 
                              width: '15%',
                              borderRight: '1px solid #E0E0E0',
                            }} 
                            align="center"
                          >
                            <Typography
                              sx={{
                                fontSize: '12px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: subcategory.type === 'Qualitative' ? '#E3F2FD' : '#FFF3E0',
                                color: subcategory.type === 'Qualitative' ? '#1976D2' : '#F57C00',
                                fontFamily: style.FONT_MEDIUM,
                              }}
                            >
                              {subcategory.type}
                            </Typography>
                          </StyledTableCell>
                          <StyledTableCell sx={{ width: '15%' }} align="center">
                            <Typography
                              sx={{
                                fontSize: '12px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#F5F5F5',
                                color: '#333333',
                                fontFamily: style.FONT_BOLD,
                              }}
                            >
                              {subcategory.type === 'Qualitative' ? 'Achieved' : subcategory.defaultTarget}
                            </Typography>
                          </StyledTableCell>
                        </StyledTableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions 
        sx={{ 
          padding: '16px 24px',
          borderTop: '1px solid #E0E0E0',
          backgroundColor: '#FFFFFF',
          justifyContent: 'center',
          flexShrink: 0,
        }}
      >
        <Button
          sx={{
            background: '#E2E2E2',
            color: '#000000',
            fontSize: '13px',
            height: '42px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            borderRadius: '20px',
            '&:hover': {
              background: '#E2E2E2',
              color: '#000000',
            },
          }}
          onClick={handleCloseViewModal}
        >
          CLOSE
        </Button>
      </DialogActions>
    </Dialog>
  ), [viewTemplateModal, handleCloseViewModal, viewingTemplate]);
  
  // Get templates to display - either search results or all templates
  const getDisplayedTemplates = useCallback(() => {
    if (search.trim() === '') {
      return templates;
    }
    return searchResults;
  }, [search, templates, searchResults]);

  // Determine if any operation is in progress
  const isAnyTemplateOperationInProgress = loading || isCreating || isUpdating || isDeleting;

  // If used as a modal, just return the dialog
  if (isModal) {
    return (
      <>
        <Loader state={isAnyTemplateOperationInProgress} />
        {TemplateDialog}
      </>
    );
  }

  // Otherwise, return the full template manager interface
  return (
    <Box sx={MainContainer}>
      <Loader state={isAnyTemplateOperationInProgress} />
      <StyledPaper>
        <HeaderHeading>
          Template Management
        </HeaderHeading>

        {/* Tabs */}
        <Paper elevation={1} sx={{ mb: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={(e, newVal) => setTabValue(newVal)} 
            variant="scrollable"
            sx={{
              '& .MuiTab-root': {
                fontFamily: style.FONT_MEDIUM,
                fontSize: '14px',
                textTransform: 'none',
                minWidth: 120,
              },
              '& .Mui-selected': {
                color: style.PRIMARY_COLOR,
              },
            }}
          >
            <Tab label="My Templates" />
          </Tabs>
        </Paper>

        {/* Search & Filter */}
        <Box display="flex" gap={2} mb={3} justifyContent="space-between" alignItems="center">
          <DebouncedTextField
            variant="outlined"
            size="small"
            placeholder="Search by Title and Role"
            sx={{
              width: 'auto',
              minWidth: '200px',
              maxWidth: '400px',
              '& .MuiOutlinedInput-root': {
                borderRadius: '25px',
                fontSize: '14px',
                fontFamily: style.FONT_MEDIUM,
                backgroundColor: '#FFFFFF',
                '&.Mui-focused fieldset': {
                  borderColor: style.PRIMARY_COLOR,
                },
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#DDDDDD',
              },
            }}
            value={search}
            onChange={(value: string) => setSearch(value)}
            debounceMs={500}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: search && (
                <InputAdornment position="end">
                  <IconButton onClick={() => setSearch('')}>
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <Box display="flex" gap={2}>
            <StyledButton 
              variant="contained" 
              onClick={handleOpenTemplateModal} 
              startIcon={<AddIcon />}
              sx={{ minWidth: 120 }}
            >
              Create
            </StyledButton>
          </Box>
        </Box>

        {/* Table with Title, Description and Action */}
        <TableContainer component={Paper} elevation={1}>
          <Table size="small">
            <TableHead>
              <StyledTableHeaderRow>
                <StyledHeaderTableCell sx={{ width: '33.33%' }} align="center">Title</StyledHeaderTableCell>
                <StyledHeaderTableCell sx={{ width: '33.33%' }} align="center">Description</StyledHeaderTableCell>
                <StyledHeaderTableCell sx={{ width: '33.33%' }} align="center">Action</StyledHeaderTableCell>
              </StyledTableHeaderRow>
            </TableHead>
            <TableBody>
              {getDisplayedTemplates().length === 0 ? (
              <StyledTableRow>
                  <StyledTableCell colSpan={3} align="center" sx={{ py: 4 }}>
                    <Typography sx={{ fontSize: '14px', fontFamily: style.FONT_MEDIUM, color: '#666666' }}>
                      No templates found
                    </Typography>
                  </StyledTableCell>
                </StyledTableRow>
              ) : (
                getDisplayedTemplates()
                  .map((template: Template) => (
                    <StyledTableRow key={template.id}>
                      <StyledTableCell sx={{ width: '33.33%' }} align="center">
                        <Typography
                          sx={{
                            fontSize: '14px',
                            fontFamily: style.FONT_MEDIUM,
                            color: style.PRIMARY_COLOR,
                            cursor: 'pointer',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline',
                              color: alpha(style.PRIMARY_COLOR, 0.8),
                            }
                          }}
                          onClick={() => handleViewTemplate(template)}
                        >
                          {template.title}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell sx={{ width: '33.33%' }} align="left">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography 
                            sx={{ 
                              fontSize: '14px', 
                              fontFamily: style.FONT_MEDIUM, 
                              color: '#666666',
                              flex: 1,
                              wordBreak: 'break-word',
                              lineHeight: 1.4,
                            }}
                          >
                            {expandedDescriptions[template.id] 
                              ? (template.description || 'No description available')
                              : getTruncatedDescription(template.description || '', 80)
                            }
                          </Typography>
                          {template.description && template.description.length > 80 && (
                            <Tooltip title={expandedDescriptions[template.id] ? 'Show less' : 'Show more'}>
                              <IconButton 
                                size="small" 
                                onClick={() => toggleDescriptionExpansion(template.id)}
                                sx={{ 
                                  color: style.PRIMARY_COLOR,
                                  minWidth: 'auto',
                                  p: 0.5,
                                }}
                              >
                                {expandedDescriptions[template.id] ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </StyledTableCell>
                      <StyledTableCell sx={{ width: '33.33%' }} align="center">
                        <Tooltip title="Edit">
                          <IconButton 
                            size="small" 
                            sx={{ color: style.PRIMARY_COLOR, mr: 1 }}
                            onClick={() => handleEditTemplate(template)}
                            disabled={isAnyTemplateOperationInProgress}
                          >
                            {isUpdating ? <CircularProgress size={16} color="inherit" /> : <EditIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton 
                            size="small" 
                            sx={{ color: '#FF4D4F' }}
                            onClick={() => handleDeleteTemplate(template.id)}
                            disabled={isAnyTemplateOperationInProgress}
                          >
                            {isDeleting ? <CircularProgress size={16} color="inherit" /> : <DeleteIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {TemplateDialog}
        {TemplateViewDialog}
        
        {/* Delete Confirmation Dialog */}
        <Dialog 
          open={deleteConfirmOpen}
          onClose={() => setDeleteConfirmOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: '12px',
              padding: '20px',
            }
          }}
        >
          <DialogTitle sx={{ 
            textAlign: 'center', 
            fontSize: '18px',
            fontFamily: style.FONT_BOLD,
            color: '#333333',
            pb: 2,
          }}>
            Confirm Delete
          </DialogTitle>
          <DialogContent>
            <Typography sx={{ 
                textAlign: 'center', 
              fontSize: '14px',
                fontFamily: style.FONT_MEDIUM,
              color: '#666666',
            }}>
              Are you sure you want to delete this template? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'center', gap: 2, pt: 2 }}>
            <CancelButton 
              onClick={() => setDeleteConfirmOpen(false)}
              disabled={isDeleting}
            >
              CANCEL
            </CancelButton>
            <Button
              variant="contained"
              onClick={handleDeleteConfirm}
              disabled={isAnyTemplateOperationInProgress}
              sx={{
                backgroundColor: '#FF4D4F',
                color: '#FFFFFF',
                fontFamily: style.FONT_BOLD,
                fontSize: '13px',
                height: '42px',
                borderRadius: '20px',
                minWidth: '100px',
                '&:hover': {
                  backgroundColor: '#d32f2f',
                },
                '&.Mui-disabled': {
                  opacity: 0.5,
                  color: '#ffffff',
                },
              }}
            >
              {isDeleting ? <CircularProgress size={20} color="inherit" /> : 'DELETE'}
            </Button>
          </DialogActions>
        </Dialog>
      </StyledPaper>
    </Box>
  );
};

export default TemplateManager;