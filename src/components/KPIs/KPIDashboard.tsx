import React, { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Grid,
  Chip,
  alpha,
  styled,
  SelectChangeEvent,
  IconButton,
  Collapse,
  Button,
  TextField,
  Tooltip,
} from '@mui/material'
import { toast } from 'react-toastify'
import { useDispatch, useSelector } from 'react-redux'
import Loader from '../Common/Loader'
import { fetchTemplates, fetchTemplateById } from '../../actions'
import {
  getTemplates,
  getCurrentTemplate,
  getTemplatesLoading,
  getTemplatesError
} from '../../reducers/entities/templates'
import style from '../../utils/styles.json'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import AssignmentIcon from '@mui/icons-material/Assignment'
import BarChartIcon from '@mui/icons-material/BarChart'
import EditIcon from '@mui/icons-material/Edit'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import ExpandLessIcon from '@mui/icons-material/ExpandLess'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import WarningIcon from '@mui/icons-material/Warning'
import TrendingUpOutlinedIcon from '@mui/icons-material/TrendingUpOutlined'
import SaveIcon from '@mui/icons-material/Save'
import CancelIcon from '@mui/icons-material/Cancel'

// Styled components following project UI guidelines
const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '20px',
  background: '#FFFFFF',
  opacity: '1',
  margin: '15px auto',
  border: '1px solid #DDDDDD',
  borderRadius: '10px',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
  minHeight: '100vh',
}

const HeaderHeading = styled(Typography)(({ theme }) => ({
  fontSize: '28px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  color: style.PRIMARY_COLOR,
  marginBottom: '20px',
}))

const StyledFormControl = styled(FormControl)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '25px',
    fontSize: '14px',
  fontFamily: style.FONT_MEDIUM,
    backgroundColor: '#FFFFFF',
    '&.Mui-focused fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#DDDDDD',
  },
  '& .MuiInputLabel-root': {
    fontFamily: style.FONT_MEDIUM,
    fontSize: '14px',
    color: '#666666',
    '&.Mui-focused': {
      color: style.PRIMARY_COLOR,
    },
  },
}))

// Minimized Summary Cards 
const SummaryCard = styled(Card)(({ theme }) => ({
  height: '100%',
  minHeight: '90px', // Reduced from 120px
  borderRadius: '6px',
  border: '1px solid #E0E0E0',
  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  transition: 'all 0.2s ease',
  backgroundColor: '#FFFFFF',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    borderColor: style.PRIMARY_COLOR,
  },
}))

const SummaryCardContent = styled(CardContent)(() => ({
  padding: '12px', // Reduced from 20px
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  height: '100%',
  '&:last-child': {
    paddingBottom: '12px',
  },
}))

const SummaryIcon = styled(Box)(() => ({
  width: '32px', // Reduced from 50px
  height: '32px',
  borderRadius: '50%',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '6px', // Reduced from 12px
  '& svg': {
    fontSize: '18px', // Reduced from 28px
    color: style.PRIMARY_COLOR,
  },
}))

const SummaryValue = styled(Typography)(() => ({
  fontSize: '20px', // Reduced from 28px
  fontFamily: style.FONT_BOLD,
  color: style.PRIMARY_COLOR,
  marginBottom: '4px', // Reduced from 6px
  lineHeight: 1,
}))

const SummaryLabel = styled(Typography)(() => ({
  fontSize: '10px', // Reduced from 12px
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
}))

// Smaller KPI Cards to fit more in a row
const KPICard = styled(Card)<{ isEditing?: boolean }>(({ theme, isEditing }) => ({
  height: '100%',
  minHeight: isEditing ? '180px' : '140px', // Larger when editing
  borderRadius: '6px',
  border: `1px solid ${isEditing ? style.PRIMARY_COLOR : '#E0E0E0'}`,
  boxShadow: isEditing ? `0 2px 8px ${alpha(style.PRIMARY_COLOR, 0.3)}` : '0 1px 3px rgba(0,0,0,0.1)',
  transition: 'all 0.2s ease',
  backgroundColor: '#FFFFFF',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    borderColor: style.PRIMARY_COLOR,
  },
}))

const KPICardContent = styled(CardContent)(() => ({
  padding: '12px', // Reduced from 18px
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  '&:last-child': {
    paddingBottom: '12px',
  },
}))

// Category Header styled with white background and blue text
const CategoryHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 16px', // Reduced from 16px 20px
  backgroundColor: '#FFFFFF',
  color: style.PRIMARY_COLOR,
  borderRadius: '6px',
  marginBottom: '10px', // Reduced from 16px
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  border: `2px solid ${style.PRIMARY_COLOR}`,
  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  '&:hover': {
    backgroundColor: alpha(style.PRIMARY_COLOR, 0.02),
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
  },
}))

const CategoryHeaderLeft = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '10px', // Reduced from 14px
}))

const CategoryIcon = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '30px', // Reduced from 36px
  height: '30px',
  borderRadius: '6px',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.1),
  '& svg': {
    color: style.PRIMARY_COLOR,
    fontSize: '18px', // Reduced from 22px
  },
}))

const CategoryTitle = styled(Typography)(() => ({
  fontSize: '16px', // Reduced from 18px
  fontFamily: style.FONT_BOLD,
  color: style.PRIMARY_COLOR,
}))

const CategoryCount = styled(Typography)(() => ({
  fontSize: '12px', // Reduced from 13px
  fontFamily: style.FONT_MEDIUM,
  color: alpha(style.PRIMARY_COLOR, 0.7),
}))

const KPITitle = styled(Typography)(() => ({
  fontSize: '14px', // Reduced from 15px
  fontFamily: style.FONT_BOLD,
  color: '#333333',
  marginBottom: '8px', // Reduced from 10px
  lineHeight: 1.3,
}))

const KPIDescription = styled(Typography)(() => ({
  fontSize: '12px', // Reduced from 13px
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  marginBottom: '10px', // Reduced from 14px
  lineHeight: 1.4,
  flexGrow: 1,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
}))

const KPIFooter = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginTop: 'auto',
  paddingTop: '8px', // Reduced from 12px
}))

const TypeChip = styled(Chip)<{ kpitype: 'Qualitative' | 'Quantitative' }>(({ kpitype }) => ({
  fontSize: '10px', // Reduced from 11px
  fontFamily: style.FONT_MEDIUM,
  height: '20px', // Reduced from 22px
  backgroundColor: kpitype === 'Qualitative' ? '#E8F5E8' : '#E8F2FF',
  color: kpitype === 'Qualitative' ? '#2E7D2E' : '#1565C0',
  border: `1px solid ${kpitype === 'Qualitative' ? '#C8E6C9' : '#BBDEFB'}`,
  '& .MuiChip-label': {
    paddingLeft: '6px', // Reduced from 8px
    paddingRight: '6px',
  },
}))

const TargetChip = styled(Chip)(() => ({
  fontSize: '10px', // Reduced from 11px
  fontFamily: style.FONT_BOLD,
  height: '20px', // Reduced from 22px
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.1),
  color: style.PRIMARY_COLOR,
  border: `1px solid ${alpha(style.PRIMARY_COLOR, 0.3)}`,
  '& .MuiChip-label': {
    paddingLeft: '6px', // Reduced from 8px
    paddingRight: '6px',
  },
}))

const EmptyState = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '40px 20px', // Reduced from 60px
  textAlign: 'center',
}))

const EmptyStateIcon = styled(Box)(() => ({
  width: '60px', // Reduced from 80px
  height: '60px',
  borderRadius: '50%',
  backgroundColor: alpha(style.PRIMARY_COLOR, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '15px', // Reduced from 20px
  '& svg': {
    fontSize: '30px', // Reduced from 40px
    color: style.PRIMARY_COLOR,
  },
}))

const EmptyStateTitle = styled(Typography)(() => ({
  fontSize: '16px', // Reduced from 18px
  fontFamily: style.FONT_BOLD,
  color: '#333333',
  marginBottom: '6px', // Reduced from 8px
}))

const EmptyStateDescription = styled(Typography)(() => ({
  fontSize: '13px', // Reduced from 14px
  fontFamily: style.FONT_MEDIUM,
  color: '#666666',
  maxWidth: '400px',
}))

// Toggle Edit Button styled with responsive design
const ToggleEditButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '42px',
  borderRadius: '20px',
  padding: '8px 20px',
  fontFamily: style.FONT_MEDIUM,
  textTransform: 'none',
  minWidth: '120px',
  '&:hover': {
    background: alpha(style.PRIMARY_COLOR, 0.8),
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '12px',
    padding: '6px 16px',
    minWidth: '100px',
    height: '38px',
  },
}))

// Inline Edit TextField
const InlineEditTextField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '6px',
    fontSize: '12px',
    fontFamily: style.FONT_MEDIUM,
    '&.Mui-focused fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
  '& .MuiInputLabel-root': {
    fontFamily: style.FONT_MEDIUM,
    fontSize: '12px',
    '&.Mui-focused': {
      color: style.PRIMARY_COLOR,
    },
  },
}))

// Save/Cancel buttons for inline editing
const EditActionButton = styled(IconButton)(() => ({
  width: '24px',
  height: '24px',
  '& svg': {
    fontSize: '16px',
  },
}))

// Interface for KPI subcategory with additional fields for tracking
interface KPISubcategory {
  title: string
  type: 'Qualitative' | 'Quantitative'
  defaultTarget: string
  currentValue?: string
}

// Interface for KPI category
interface KPICategory {
  name: string
  subcategories: KPISubcategory[]
}

// Template interface definition
interface Template {
  id: string
  title: string
  description: string
  categories: KPICategory[]
}

const KPIDashboard: React.FC = () => {
  const dispatch = useDispatch()
  
  // Redux state selectors - Fixed to use proper selectors
  const templates = useSelector(getTemplates)
  const currentTemplate = useSelector(getCurrentTemplate)
  const loading = useSelector(getTemplatesLoading)
  const error = useSelector(getTemplatesError)
  
  // Get user ID from Redux store
  const userId = useSelector(
    (state: any) => state?.entities?.dashboard?.getUserDetails?.id
  )
  
  // Check if current user is authorized to access KPI features
  const isAuthorized = [562, 1259, 3106].includes(userId)
  
  // Redirect unauthorized users
  useEffect(() => {
    if (!isAuthorized) {
      window.location.href = '/home'
    }
  }, [isAuthorized])

  // Local UI state
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('')
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [initialLoading, setInitialLoading] = useState(true)
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])
  const [kpiData, setKpiData] = useState<KPISubcategory[]>([])
  const [isEditMode, setIsEditMode] = useState(false)
  const [editValues, setEditValues] = useState<{ [key: string]: string }>({})

  // Fetch templates on component mount - Fixed action dispatch
  useEffect(() => {
    console.log('KPI Dashboard: Dispatching fetchTemplates action')
    console.log('Token in localStorage:', localStorage.getItem('token'))
    dispatch(fetchTemplates())
  }, [dispatch])

  // Update local templates when Redux state changes
  useEffect(() => {
    console.log('KPI Dashboard: Templates state updated:', { 
      templatesLength: templates.length, 
      loading, 
      error, 
      templates,
      initialLoading
    })
    
    // Set initial loading to false once we've gotten a response (either success or error)
    if (initialLoading && !loading) {
      setInitialLoading(false)
      
      // Only show "no templates" message after we've actually completed the initial fetch
      if (templates.length === 0 && !error) {
        toast.info('No templates available. Please create a template first.')
      }
    }
  }, [templates, loading, initialLoading, error])

  // Handle current template changes from Redux
  useEffect(() => {
    if (currentTemplate) {
      setSelectedTemplate(currentTemplate)
      toast.success(`Template "${currentTemplate.title}" loaded successfully!`)
    }
  }, [currentTemplate])

  // Fetch template details when selection changes
  useEffect(() => {
    if (selectedTemplateId) {
      dispatch(fetchTemplateById({ id: selectedTemplateId }))
    } else {
      setSelectedTemplate(null)
      setKpiData([])
      setIsEditMode(false)
      setEditValues({})
    }
  }, [selectedTemplateId, dispatch])

  // Initialize KPI data when template is selected
  useEffect(() => {
    if (selectedTemplate) {
      initializeKPIData()
    }
  }, [selectedTemplate])

  // Keep categories collapsed by default when template is loaded
  useEffect(() => {
    if (selectedTemplate && selectedTemplate.categories.length > 0) {
      // Start with all categories collapsed
      setExpandedCategories([])
    }
  }, [selectedTemplate])

  // Handle errors from Redux
  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  const initializeKPIData = () => {
    if (!selectedTemplate) return

    const allKPIs: KPISubcategory[] = []
    selectedTemplate.categories.forEach((category: KPICategory) => {
      category.subcategories.forEach((subcategory: KPISubcategory) => {
        allKPIs.push({
          ...subcategory,
          currentValue: '',
        })
      })
    })
    setKpiData(allKPIs)
  }

  const handleTemplateChange = (event: SelectChangeEvent<string>) => {
    const templateId = event.target.value
    setSelectedTemplateId(templateId)
    setIsEditMode(false)
    setEditValues({})
  }

  const handleCategoryToggle = (categoryName: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryName)
        ? prev.filter((name) => name !== categoryName)
        : [...prev, categoryName],
    )
  }

  const getKPIIndex = (categoryIndex: number, subIndex: number) => {
    if (!selectedTemplate) return -1

    let index = 0
    for (let i = 0; i < categoryIndex; i++) {
      index += selectedTemplate.categories[i].subcategories.length
    }
    return index + subIndex
  }

  const getKPIKey = (categoryIndex: number, subIndex: number) => {
    return `${categoryIndex}-${subIndex}`
  }

  const handleToggleEditMode = () => {
    if (isEditMode) {
      // Cancel editing - reset edit values
      setEditValues({})
      setIsEditMode(false)
      toast.info('Edit mode cancelled')
      } else {
      // Enter edit mode - initialize edit values with current values
      const initialEditValues: { [key: string]: string } = {}
      if (selectedTemplate) {
        selectedTemplate.categories.forEach((category: KPICategory, categoryIndex: number) => {
          category.subcategories.forEach((subcategory: KPISubcategory, subIndex: number) => {
            const kpiIndex = getKPIIndex(categoryIndex, subIndex)
            const currentKPI = kpiData[kpiIndex]
            const key = getKPIKey(categoryIndex, subIndex)
            initialEditValues[key] = currentKPI?.currentValue || ''
          })
        })
      }
      setEditValues(initialEditValues)
      setIsEditMode(true)
      toast.info('Edit mode enabled. Click Save to confirm changes or Cancel to discard.')
    }
  }

  const handleSaveChanges = () => {
    // Apply edit values to KPI data
    setKpiData((prev) => {
      const updated = [...prev]
      Object.keys(editValues).forEach((key) => {
        const [categoryIndex, subIndex] = key.split('-').map(Number)
        const kpiIndex = getKPIIndex(categoryIndex, subIndex)
        if (kpiIndex >= 0 && kpiIndex < updated.length) {
          updated[kpiIndex] = {
            ...updated[kpiIndex],
            currentValue: editValues[key],
          }
        }
      })
      return updated
    })

    setIsEditMode(false)
    setEditValues({})
    toast.success('KPI values updated successfully!')
  }

  const handleEditValueChange = (key: string, value: string) => {
    setEditValues((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase()
    if (name.includes('performance') || name.includes('quality')) {
      return <TrendingUpIcon />
    } else if (name.includes('attendance') || name.includes('presence')) {
      return <AssignmentIcon />
    } else {
      return <BarChartIcon />
    }
  }

  const getKPIByIndex = (categoryIndex: number, subIndex: number): KPISubcategory | null => {
    const kpiIndex = getKPIIndex(categoryIndex, subIndex)
    return kpiData[kpiIndex] || null
  }

  const calculateSummaryStats = () => {
    const totalKPIs = kpiData.length
    let completedKPIs = 0
    let needsAttention = 0
    
    // Evaluate each KPI based on type and value
    kpiData.forEach((kpi) => {
      if (kpi.currentValue && kpi.currentValue.trim() !== '') {
        if (kpi.type === 'Qualitative') {
          // For qualitative: if score > 7.5 out of 10, then completed
          const numValue = parseFloat(kpi.currentValue) || 0
          if (numValue > 7.5) {
            completedKPIs++
          } else {
            needsAttention++
          }
        } else if (kpi.type === 'Quantitative') {
          // For quantitative: check if actual meets target criteria
          const numValue = parseFloat(kpi.currentValue) || 0
          const isCompleted = evaluateQuantitativeTarget(numValue, kpi.defaultTarget)
          if (isCompleted) {
            completedKPIs++
          } else {
            needsAttention++
          }
        }
      }
      // Note: KPIs without values are not counted in either completed or needsAttention
    })
    
    const progressScore = totalKPIs > 0 ? Math.round((completedKPIs / totalKPIs) * 100) : 0

    return {
      totalKPIs,
      completedKPIs,
      needsAttention,
      progressScore,
    }
  }

  // Helper function to evaluate quantitative targets
  const evaluateQuantitativeTarget = (actualValue: number, target: string): boolean => {
    if (!target || target.trim() === '') return false
    
    const targetStr = target.trim()
    
    // Handle different target formats
    if (targetStr.includes('≤') || targetStr.includes('<=')) {
      const targetNum = parseFloat(targetStr.replace(/[≤<=]/g, '').trim()) || 0
      return actualValue <= targetNum
    } else if (targetStr.includes('≥') || targetStr.includes('>=')) {
      const targetNum = parseFloat(targetStr.replace(/[≥>=]/g, '').trim()) || 0
      return actualValue >= targetNum
    } else if (targetStr.includes('<')) {
      const targetNum = parseFloat(targetStr.replace(/[<]/g, '').trim()) || 0
      return actualValue < targetNum
    } else if (targetStr.includes('>')) {
      const targetNum = parseFloat(targetStr.replace(/[>]/g, '').trim()) || 0
      return actualValue > targetNum
    } else if (targetStr.includes('=') || targetStr === '100 %' || targetStr === '100%') {
      const targetNum = parseFloat(targetStr.replace(/[=%]/g, '').trim()) || 0
      return actualValue >= targetNum
    } else {
      // For other formats like "≥ 1 / month", "≥ 8 hrs", etc.
      // Extract the number and assume >= logic
      const match = targetStr.match(/(\d+(?:\.\d+)?)/);
      if (match) {
        const targetNum = parseFloat(match[1]) || 0
        return actualValue >= targetNum
      }
    }
    
    return false
  }

  // Helper function to get KPI status
  const getKPIStatus = (kpi: KPISubcategory): 'completed' | 'needs-attention' | 'no-data' => {
    const value = kpi.currentValue
    if (!value || value.trim() === '') {
      return 'no-data'
    }

    if (kpi.type === 'Qualitative') {
      const numValue = parseFloat(value) || 0
      return numValue > 7.5 ? 'completed' : 'needs-attention'
    } else if (kpi.type === 'Quantitative') {
      const numValue = parseFloat(value) || 0
      const isCompleted = evaluateQuantitativeTarget(numValue, kpi.defaultTarget)
      return isCompleted ? 'completed' : 'needs-attention'
    }

    return 'no-data'
  }

  // Helper function to get status icon and color
  const getStatusDisplay = (status: 'completed' | 'needs-attention' | 'no-data') => {
    switch (status) {
      case 'completed':
        return {
          icon: <CheckCircleIcon sx={{ fontSize: '14px', color: '#4CAF50' }} />,
          color: '#4CAF50',
          bgColor: alpha('#4CAF50', 0.1),
          text: 'Completed'
        }
      case 'needs-attention':
        return {
          icon: <WarningIcon sx={{ fontSize: '14px', color: '#FF9800' }} />,
          color: '#FF9800',
          bgColor: alpha('#FF9800', 0.1),
          text: 'Needs Attention'
        }
      default:
        return {
          icon: null,
          color: '#999999',
          bgColor: alpha('#999999', 0.1),
          text: 'No Data'
        }
    }
  }

  const renderSummaryCards = () => {
    const stats = calculateSummaryStats()

    return (
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} md={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <CheckCircleIcon />
              </SummaryIcon>
              <SummaryValue>{stats.completedKPIs}</SummaryValue>
              <SummaryLabel>Completed KPIs</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <WarningIcon />
              </SummaryIcon>
              <SummaryValue>{stats.needsAttention}</SummaryValue>
              <SummaryLabel>Needs Attention</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <SummaryCard>
            <SummaryCardContent>
              <SummaryIcon>
                <TrendingUpOutlinedIcon />
              </SummaryIcon>
              <SummaryValue>{stats.progressScore}%</SummaryValue>
              <SummaryLabel>Progress Score</SummaryLabel>
            </SummaryCardContent>
          </SummaryCard>
        </Grid>
      </Grid>
    )
  }

  const truncateText = (text: string, maxLength: number = 30): string => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  const renderKPIGrid = () => {
    if (!selectedTemplate || !selectedTemplate.categories) {
      return (
        <EmptyState>
          <EmptyStateIcon>
            <BarChartIcon />
          </EmptyStateIcon>
          <EmptyStateTitle>No Template Selected</EmptyStateTitle>
          <EmptyStateDescription>
            Please select a template from the dropdown above to view KPI subcategories and start
            tracking performance metrics.
          </EmptyStateDescription>
        </EmptyState>
      )
    }

    if (selectedTemplate.categories.length === 0) {
  return (
        <EmptyState>
          <EmptyStateIcon>
            <AssignmentIcon />
          </EmptyStateIcon>
          <EmptyStateTitle>No KPIs Available</EmptyStateTitle>
          <EmptyStateDescription>
            This template doesn't contain any KPI categories. Please edit the template to add
            categories and subcategories.
          </EmptyStateDescription>
        </EmptyState>
      )
    }

    return (
      <Box>
        {selectedTemplate.categories.map((category: KPICategory, categoryIndex: number) => (
          <Box key={categoryIndex} sx={{ mb: 2 }}>
            {/* Category Header */}
            <CategoryHeader onClick={() => handleCategoryToggle(category.name)}>
              <CategoryHeaderLeft>
                <CategoryIcon>{getCategoryIcon(category.name)}</CategoryIcon>
                <Box>
                  <CategoryTitle>{category.name}</CategoryTitle>
                  <CategoryCount>
                    {category.subcategories.length} KPI
                    {category.subcategories.length !== 1 ? 's' : ''}
                  </CategoryCount>
          </Box>
              </CategoryHeaderLeft>
              <IconButton sx={{ color: style.PRIMARY_COLOR }}>
                {expandedCategories.includes(category.name) ? (
                  <ExpandLessIcon />
                ) : (
                  <ExpandMoreIcon />
                )}
              </IconButton>
            </CategoryHeader>

            {/* KPI Cards Grid - More cards per row with smaller gaps */}
            <Collapse in={expandedCategories.includes(category.name)} timeout='auto' unmountOnExit>
              <Grid container spacing={1} sx={{ pl: 0.5, pr: 0.5 }}>
                {category.subcategories.map((subcategory: KPISubcategory, subIndex: number) => {
                  const currentKPI = getKPIByIndex(categoryIndex, subIndex)
                  const kpiKey = getKPIKey(categoryIndex, subIndex)
                  const editValue = editValues[kpiKey] || ''

                  return (
                    <Grid item xs={12} sm={6} md={4} lg={3} xl={2.4} key={subIndex}>
                      <KPICard isEditing={isEditMode}>
                        <KPICardContent sx={{ height: 'unset' }}>
                          <KPITitle>{subcategory.title}</KPITitle>

                          {/* Current Value Display or Edit Field */}
                          {isEditMode ? (
                            <Box sx={{ mb: 1 }}>
                              <InlineEditTextField
                                label="Current Value"
                                value={editValue}
                                onChange={(e) => handleEditValueChange(kpiKey, e.target.value)}
                                fullWidth
                                multiline
                                rows={2}
                                size="small"
                                placeholder={`Target: ${subcategory.defaultTarget}`}
                              />
                            </Box>
                          ) : (
                            currentKPI?.currentValue && (
                              <Box sx={{ mb: 1 }}>
              <Typography
                sx={{
                                    fontSize: '11px',
                  fontFamily: style.FONT_BOLD,
                  color: style.PRIMARY_COLOR,
                }}
              >
                                  Current: {currentKPI.currentValue}
              </Typography>
          </Box>
                            )
                          )}

                          <KPIFooter>
                            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                              <TypeChip
                                label={subcategory.type}
                                size='small'
                                kpitype={subcategory.type}
                              />
                              <TargetChip
                                label={`Target: ${subcategory.defaultTarget}`}
                                size='small'
                              />
        </Box>
                          </KPIFooter>
                        </KPICardContent>
                      </KPICard>
                    </Grid>
                  )
                })}
              </Grid>
            </Collapse>
          </Box>
        ))}
      </Box>
    )
  }

  if (initialLoading) {
    return <Loader state={initialLoading} />
  }

  return (
    <Box sx={MainContainer}>
      <Loader state={loading} />
      <StyledPaper>

        {/* Template Selection and Edit Mode Toggle */}
        <Box
                    sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
            gap: { xs: 2, sm: 3 },
            flexDirection: { xs: 'column', sm: 'row' },
            px: { xs: 1, sm: 0 },
          }}
        >
          {/* Left side - Template Selection */}
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 2, 
            flex: 1,
            width: { xs: '100%', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}>
                <Typography
                  sx={{
                fontSize: '16px',
                    fontFamily: style.FONT_MEDIUM,
                color: '#333333',
                minWidth: 'fit-content',
                display: { xs: 'none', sm: 'block' },
                  }}
                >
              Assigned Template:
                </Typography>
            <StyledFormControl sx={{ 
              minWidth: { xs: 250, sm: 280 }, 
              maxWidth: 400,
              width: { xs: '100%', sm: 'auto' },
            }}>
              <InputLabel id='template-select-label'>Select Template</InputLabel>
              <Select
                labelId='template-select-label'
                id='template-select'
                value={selectedTemplateId}
                label='Select Template'
                onChange={handleTemplateChange}
                disabled={templates.length === 0}
              >
                {templates.length === 0 ? (
                  <MenuItem disabled>
                    <em>No templates available</em>
                  </MenuItem>
                ) : (
                  templates.map((template: Template) => (
                    <MenuItem key={template.id} value={template.id}>
                      <Typography
                        sx={{
                          fontSize: '14px',
                          fontFamily: style.FONT_MEDIUM,
                          color: '#333333',
                        }}
                      >
                        {template.title}
                      </Typography>
                    </MenuItem>
                  ))
                )}
              </Select>
            </StyledFormControl>
          </Box>

          {/* Right side - Edit Mode Toggle Buttons */}
          {selectedTemplate && (
            <Box sx={{ 
              mt: { xs: 2, sm: 0 },
              ml: { sm: 2 },
              width: { xs: '100%', sm: 'auto' },
                display: 'flex',
              justifyContent: { xs: 'center', sm: 'flex-end' },
                gap: 1,
            }}>
              {isEditMode ? (
                <>
                  <ToggleEditButton
                    variant="contained"
                    onClick={handleSaveChanges}
                    startIcon={<SaveIcon />}
                    sx={{
                      background: '#4CAF50',
                      '&:hover': { background: alpha('#4CAF50', 0.8) },
                      width: { xs: 'auto', sm: 'auto' },
                    }}
                  >
                    Save
                  </ToggleEditButton>
                  <ToggleEditButton
                    variant="outlined"
                    onClick={handleToggleEditMode}
                    startIcon={<CancelIcon />}
              sx={{
                      borderColor: '#FF4D4F',
                      color: '#FF4D4F',
                      '&:hover': { 
                        backgroundColor: alpha('#FF4D4F', 0.04),
                        borderColor: '#FF4D4F',
                      },
                      width: { xs: 'auto', sm: 'auto' },
                    }}
                  >
                    Cancel
                  </ToggleEditButton>
                </>
              ) : (
                <ToggleEditButton
                  variant="contained"
                  onClick={handleToggleEditMode}
                  startIcon={<EditIcon />}
              sx={{
                    background: style.PRIMARY_COLOR,
                    '&:hover': { background: alpha(style.PRIMARY_COLOR, 0.8) },
                    width: { xs: '100%', sm: 'auto' },
                    maxWidth: { xs: '300px', sm: 'none' },
                  }}
                >
                  Edit KPIs
                </ToggleEditButton>
              )}
            </Box>
          )}
        </Box>

        {/* Summary Cards */}
        {selectedTemplate && kpiData.length > 0 && renderSummaryCards()}

        {/* KPI Grid Container */}
        <Box
              sx={{
            minHeight: '200px',
            px: { xs: 1, sm: 0 },
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: style.PRIMARY_COLOR,
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              backgroundColor: '#555',
            },
          }}
        >
          {renderKPIGrid()}
        </Box>
      </StyledPaper>
    </Box>
  )
}

export default KPIDashboard
