import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import Paper from '@mui/material/Paper'
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Modal,
} from '@mui/material'
import { HeaderHeading, StyledTableCell, StyledTableRow, loaderProps } from '../Common/CommonStyles'
import { Dispatch } from 'redux'
import { RootState } from '../../configureStore'
import { connect } from 'react-redux'
import { useEffect, useRef, useState } from 'react'
import { createNewRCA, fetchProjectsName, getRCAs } from '../../actions'
import { attendanceUI, dashboardEntity } from '../../reducers'
import { useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import { fetchRCAs } from '../../reducers/entities'
import CloseIcon from '@mui/icons-material/Close'
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone'
import 'react-quill/dist/quill.snow.css'
import { toast } from 'react-toastify'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import './Rca.scss'
import { Heading } from '../Pages/Styles'
import RcaFormat from './RcaFormat'
import ReactDOMServer from 'react-dom/server'
import style from '../../utils/styles.json'
import Loader from '../Common/Loader'

const SelectField = styled(Select)(({ theme }) => ({
  borderRadius: '20px',
  '& .MuiSelect-select': {
    padding: '9px 11px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
    borderRadius: '20px',
  },
  '&&.MuiInputBase-root.MuiOutlinedInput-root.MuiSelect-root': {
    borderRadius: '20px',
    padding: '11px 11px',
  },
}))

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '25px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '70px',
  border: '1px solid #DDDDDD',
}))

const StyledFormControl = styled(FormControl)(() => ({
  marginBottom: '10px',
  marginTop: '6px',
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '42px',
  float: 'right',
  marginTop: '15px',
  borderRadius: '20px',
  padding: '5px 20px',
  fontFamily: style.FONT_MEDIUM,
  top: '30px',
  right: '100px',
}))

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '11px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '15px',
    lineHeight: '1.8em',
  },
}))

export interface DialogTitleProps {
  id: string
  children?: React.ReactNode
  onClose: () => void
}

function BootstrapDialogTitle(props: DialogTitleProps) {
  const { children, onClose, ...other } = props

  return (
    <DialogTitle sx={{ m: 0, p: 2 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label='close'
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  )
}

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: style.FONT_BOLD,
  width: '100px',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
}

const RCATable = (props: any) => {
  const navigate = useNavigate()

  return (
    <>
      <StyledPaper>
        <Box sx={{ textAlign: 'left' }}>
          <Box
            onClick={() => navigate(-1)}
            sx={{ float: 'right', pr: '30px', mt: '0px', cursor: 'pointer' }}
          >
            <ArrowBack sx={{ position: 'relative', left: '120px' }} />
          </Box>
        </Box>
        <HeaderHeading sx={{ marginLeft: '100px' }}>My RCAs</HeaderHeading>
        <TableContainer sx={{ margin: '20px 0' }} component={Paper}>
          <Table sx={{ minWidth: 700 }} aria-label='customized table'>
            <TableHead>
              <StyledTableRow>
                <StyledTableCell>DATE</StyledTableCell>
                <StyledTableCell>SUBJECT</StyledTableCell>
                <StyledTableCell>MANAGER</StyledTableCell>
                <StyledTableCell>PROJECT</StyledTableCell>
              </StyledTableRow>
            </TableHead>
            {props.data?.length ? (
              <TableBody>
                {props.data.map((data: any) => (
                  <StyledTableRow
                    key={`${data?.id}`}
                    onClick={() => props.onRowClick(data)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <StyledTableCell component='th' scope='row'>
                      {`${new Date(data.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })}`}{' '}
                    </StyledTableCell>
                    <StyledTableCell align='left'>{data.subject}</StyledTableCell>
                    <StyledTableCell align='left'>{data.manager_name}</StyledTableCell>
                    <StyledTableCell align='left'>
                      {data.project_name === 'Other' ? data.other_project_name : data.project_name}
                    </StyledTableCell>{' '}
                  </StyledTableRow>
                ))}
              </TableBody>
            ) : (
              <TableBody>
                <StyledTableRow>
                  <StyledTableCell align='center' colSpan={10}>
                    <Typography align='center' variant='subtitle1' sx={{ color: '#483f3f' }}>
                      No RCA found
                    </Typography>
                  </StyledTableCell>
                </StyledTableRow>
              </TableBody>
            )}
          </Table>
        </TableContainer>
      </StyledPaper>
    </>
  )
}

const CreateRcaDialog = ({
  open,
  onClose,
  createRCA,
  AllProjectsName,
}: {
  open: boolean
  onClose: () => void
  createRCA: (data: any) => void
  AllProjectsName: string[]
}) => {
  const [to, setTo] = useState('')
  const [toError, setToError] = useState(false)
  const [ccError, setCcError] = useState(false)
  const [cc, setCc] = useState('<EMAIL>')
  const [projectName, setProjectName] = useState('')
  const [body, setBody] = useState(() => {
    return ReactDOMServer.renderToString(<RcaFormat />)
  })
  const [otherProjectName, setOtherProjectName] = useState('')

  const handleCreateRca = () => {
    handleClose()
    let data = {
      to_email: to,
      cc_email: cc,
      body: body,
      project_name: projectName,
      other_project_name: otherProjectName,
    }
    createRCA(data)
  }

  const resetForm = () => {
    setTo('')
    setToError(false)
    setCcError(false)
    setCc('<EMAIL>')
    setProjectName('')
    setOtherProjectName('')
    setBody(() => {
      return ReactDOMServer.renderToString(<RcaFormat />)
    })
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  const emailListRegex = /^[\w\.-]+@[\w\.-]+(?:,\s*[\w\.-]+@[\w\.-]+)*$/

  const handleTo = (value: string) => {
    setToError(!emailListRegex.test(value))
    setTo(value)
  }

  const handleCc = (value: string) => {
    setCcError(!emailListRegex.test(value))
    setCc(value)
  }

  const handleProjectChange = (event: any) => {
    setProjectName(event.target.value)
  }

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (projectName === 'Other' && inputRef.current) {
      inputRef.current.focus()
    }
  }, [projectName])

  return (
    <Dialog open={open} maxWidth='lg'>
      <BootstrapDialogTitle id='customized-dialog-title' onClose={handleClose}>
        <Typography variant='h5' sx={{ textAlign: 'center' }}>
          Create RCA
        </Typography>
      </BootstrapDialogTitle>
      <Box sx={{ padding: '0 16px', height: '450px' }}>
        <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 2 }}>
          <Grid item xs={6} sm={6}>
            <InputField
              id='outlined-required'
              label='To'
              size='small'
              fullWidth
              value={to}
              onChange={(e) => handleTo(e.target.value)}
              margin='normal'
              placeholder='Please enter email addresses separated by commas'
              error={toError}
              helperText={toError ? 'Please enter email separated by commas' : ''}
            />
          </Grid>
          <Grid item xs={6} sm={6}>
            <InputField
              id='outlined-required'
              label='Cc'
              size='small'
              fullWidth
              value={cc}
              onChange={(e) => handleCc(e.target.value)}
              margin='normal'
              placeholder='Please enter email addresses separated by commas'
              error={ccError}
              helperText={ccError ? 'Please enter email separated by commas' : ''}
            />
          </Grid>
          <Grid item xs={6} sm={6}>
            <StyledFormControl>
              <InputLabel id='demo-simple-select-readonly-label' sx={{ marginTop: -0.7 }}>
                Select Project <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <SelectField
                variant='outlined'
                label='Select Project:'
                type='small'
                fullWidth
                value={projectName}
                onChange={handleProjectChange}
              >
                {AllProjectsName.map((project: string) => (
                  <MenuItem key={project} value={project}>
                    {project}
                  </MenuItem>
                ))}
                <MenuItem value='Other'>Other</MenuItem>
              </SelectField>
            </StyledFormControl>
          </Grid>
          {projectName === 'Other' && (
            <Grid item xs={6} sm={6} sx={{ marginTop: '1px' }}>
              <InputField
                id='outlined-required'
                label='Project Name'
                size='small'
                fullWidth
                autoFocus
                value={otherProjectName}
                onChange={(e) => setOtherProjectName(e.target.value)}
                margin='normal'
                inputRef={inputRef}
              />
            </Grid>
          )}
          <Grid item xs={12} sm={12} className='custom_ck'>
            <CKEditor
              editor={ClassicEditor}
              data={body}
              onReady={() => {}}
              onChange={(event, editor) => {
                const data = editor.getData()
                setBody(data)
              }}
            />
          </Grid>
        </Grid>
      </Box>
      <DialogActions
        sx={{ margin: '24px 10px 10px 0', marginTop: '-30px' }}
        style={ccError || toError ? { paddingTop: '30px' } : {}}
      >
        <CancelButton onClick={handleClose}>CANCEL</CancelButton>
        <Button
          sx={{
            borderRadius: '20px',
            fontSize: '13px',
            height: '42px',
            fontFamily: style.FONT_BOLD,
            width: '100px',
            '&.Mui-disabled': {
              opacity: 0.5,
              color: '#ffffff',
              cursor: 'not-allowed',
            },
          }}
          onClick={handleCreateRca}
          disabled={
            !to.length ||
            !cc.length ||
            !body.length ||
            !projectName.length ||
            (projectName === 'Other' && !otherProjectName.length)
          }
        >
          SUBMIT
        </Button>
      </DialogActions>
    </Dialog>
  )
}

const RCAList = (props: any) => {
  const {
    getAllRCAs,
    getAllRCAsList,
    getUserDetails,
    createRCA,
    isCreatingRCA,
    isRCACreated,
    resetRCA,
    fetchProjectsName,
    AllProjectsName,
  } = props
  const [selectedRCA, setSelectedRCA] = useState<any | null>(null)
  const [isNewRCADialogOpen, setIsNewRCADialogOpen] = useState(false)

  const handleRowClick = (data: any) => {
    setSelectedRCA(data)
  }

  const handleCloseModal = () => {
    setSelectedRCA(null)
  }

  const handleOpenNewRCADialog = () => {
    setIsNewRCADialogOpen(true)
  }

  const handleCloseNewRCADialog = () => {
    setIsNewRCADialogOpen(false)
  }

  useEffect(() => {
    if (getUserDetails.id) {
      getAllRCAs(getUserDetails.id)
      fetchProjectsName(getUserDetails.id)
    }
  }, [getUserDetails.id])

  useEffect(() => {
    if (isRCACreated) {
      toast.success('RCA sent successfully')
      resetRCA()
      getAllRCAs(getUserDetails.id)
    }
  }, [isRCACreated])

  return (
    <>
      <Loader state={isCreatingRCA} />
      {/* {getAllRCAsList && getAllRCAsList?.length ? ( */}
      <div style={MainContainer}>
        <ActionButton
          variant='outlined'
          startIcon={<AddTwoToneIcon sx={{ width: 24, height: 24 }} />}
          onClick={handleOpenNewRCADialog}
        >
          NEW RCA
        </ActionButton>
        <CreateRcaDialog
          open={isNewRCADialogOpen}
          createRCA={createRCA}
          AllProjectsName={AllProjectsName}
          onClose={handleCloseNewRCADialog}
        />
        <>
          <RCATable data={getAllRCAsList} onRowClick={handleRowClick} />
          <Modal open={!!selectedRCA} onClose={handleCloseModal}>
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '70vw',
                maxWidth: '75%',
                bgcolor: 'background.paper',
                p: 4,
                maxHeight: '80vh',
                overflowY: 'auto',
                borderRadius: '10px',
              }}
            >
              <IconButton
                aria-label='close'
                onClick={handleCloseModal}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[600],
                  marginTop: '15px',
                }}
              >
                <CloseIcon />
              </IconButton>
              <Heading
                style={{
                  fontSize: '18px',
                  textAlign: 'center',
                  fontFamily: 'sans-serif',
                  letterSpacing: '0px',
                  position: 'relative',
                }}
              >
                {selectedRCA?.subject}
              </Heading>
              <Typography variant='body1' style={{ fontSize: '12px' }}>
                <div dangerouslySetInnerHTML={{ __html: selectedRCA?.body }}></div>
              </Typography>
            </Box>
          </Modal>
        </>
      </div>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    getAllRCAsList: fetchRCAs.getAllRCAsList(state).fetchRCAs,
    getUserDetails: dashboardEntity.getDashboard(state).getUserDetails,
    isCreatingRCA: attendanceUI.getEmpAttendanceList(state).isCreatingRCA,
    isRCACreated: attendanceUI.getEmpAttendanceList(state).isRCACreated,
    AllProjectsName: fetchRCAs.getAllIDSRList(state).fetchProjects,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    getAllRCAs: (userId: any) => dispatch(getRCAs.request({ userId })),
    createRCA: (data: any) => dispatch(createNewRCA.request({ data })),
    resetRCA: () => dispatch(createNewRCA.reset()),
    fetchProjectsName: (userId: any) => dispatch(fetchProjectsName.request({ userId })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(RCAList)
