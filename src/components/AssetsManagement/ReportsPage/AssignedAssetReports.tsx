import { Dispatch } from "redux";
import { RootState } from "../../../configureStore";
import { connect } from "react-redux";
import { getAssignAssetReportData } from "../../../actions";
import { useEffect } from "react";
import { getAssetsPortal } from "../../../reducers";
import { StyledTypography } from "../../Common/ComponentCommonStyles";
import { Box, Button, CircularProgress, Dialog, Pagination, Paper, styled, Table, TableBody, TableCell, tableCellClasses, TableContainer, TableHead, TableRow, Tooltip } from "@mui/material";
import { loaderProps } from "../../Common/CommonStyles";
import style from '../../../utils/styles.json'
import { ReactComponent as ExportToExcelIcon } from '../../../assets/images/exportToExcel.svg'
import { ArrowBack } from "@mui/icons-material";
import { Navigate } from "react-router-dom";



const stylee = {
    buttonStyle: {
      fontFamily: 'Montserrat-Regular',
      fontSize: '1rem',
      padding: '0',
      width: '40px',
      height: '40px',
      minWidth: 'fit-content',
      borderRadius: '50%',
      backgroundColor: 'white',
      '&:hover': {
        backgroundColor: 'white',
      },
      '@media only screen and (max-width:780px)': {
        fontSize: '0.9rem',
        marginLeft: '20px',
      },
    },
    container: {
      '@media only screen and (max-width:1230px)': {
        width: "200px",
      },
      '@media only screen and (max-width:1200px)': {
        marginTop: '-8px'
      },
      '@media only screen and (max-width:700px)': {
        marginTop: '-3px'
      },
    }
  }
  
  const StyledTableCell = styled(TableCell)(() => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: style.PRIMARY_COLOR,
      color: 'White',
      fontFamily: style.FONT_MEDIUM,
      textAlign: 'center',
      fontSize: '13px',
      letterSpacing: '0px',
      padding: '11px 0px',
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 12,
      fontFamily: style.FONT_MEDIUM,
      textAlign: 'center',
      color: '#483f3f',
      letterSpacing: '0px',
    },
  }))
  
  const StyledTableRow = styled(TableRow)(() => ({
    left: '160px',
    width: '1719px',
    height: '60px',
    boxShadow: '0px 10px 3px #6c6c6c10',
    opacity: '1',
  }))
  




const AssignedAssetReports: React.FC = (props: any)  => {
    const {
        fetchAssignedAssetReportData,
        assignedAssetReportsData,
    } = props 

    useEffect ( () => {        
        fetchAssignedAssetReportData();
    }, []) 

    
 

    return (
        <>
            <Paper>
                <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    position: 'relative',
                    justifyContent: 'flex-end',
                    margin: "0px, 10px, 10px, 0px",
                    padding: '0px'
                }}
                >

                    <Box
                        sx={{
                        fontFamily: 'Montserrat, sans-serif',
                        color: 'primary.main',
                        position: 'absolute',
                        left: '50%', 
                        transform: 'translateX(-50%)', 
                        textAlign: 'center',
                        }}
                    >
                        <StyledTypography>Assigned Asset Report</StyledTypography>
                    </Box>

                    <Box sx={{ 
                        flex: '0 1 auto',
                        display: "flex", 
                        alignItems: "center", 
                        gap: "16px", 
                        justifyContent: 'center', 
                        marginRight: '20px' 
                    }}>

                        <Box>
                        <Tooltip title="Export to Excel">
                            <Button
                                sx={stylee.buttonStyle}
                                variant='outlined'
                            >
                                <ExportToExcelIcon />
                            </Button>
                        </Tooltip>
                        </Box>

                        <Box
                            //onClick={handleNavigation}
                            sx={{ cursor: 'pointer' }}
                        >
                            <ArrowBack />
                        </Box>

                    </Box>

                </Box>



                <Box sx={{ margin: '10px' }}>
                    <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>

                        <Table sx={{ minWidth: 1000 }} aria-label='customized table'>
                            <TableHead>
                                <StyledTableRow>
                                    <StyledTableCell>EMPLOYEE NAME</StyledTableCell>
                                    <StyledTableCell>EMPLOYEE ID</StyledTableCell>
                                    <StyledTableCell>EMAIL</StyledTableCell>
                                    <StyledTableCell>EMPLOYEE STATUS</StyledTableCell>
                                    <StyledTableCell>ASSETS LIST</StyledTableCell>
                                    <StyledTableCell>ASSETS COUNT</StyledTableCell>
                                </StyledTableRow>
                            </TableHead>
                            <TableBody>
                            {assignedAssetReportsData?.response?.map( (row: any) => (
                                <StyledTableRow key={row.employee_id}>
                                    <StyledTableCell>{row.employee_name}</StyledTableCell>
                                    <StyledTableCell>{row.employee_id}</StyledTableCell>
                                    <StyledTableCell>{row.email}</StyledTableCell>
                                    <StyledTableCell>{row.employee_status}</StyledTableCell>
                                    <StyledTableCell>{row.asset_list}</StyledTableCell>
                                    <StyledTableCell>{row.asset_count}</StyledTableCell>
                                </StyledTableRow>
                            ))}
                            </TableBody>

                        </Table>
                        
                        <Pagination
                        sx={{
                            float: 'right',
                            margin: '15px 0px 10px 0px',
                        }}
                        color='primary'
                        />
                    </TableContainer>
                </Box>
            </Paper>
        </>
    );
}

const mapStateToProps = (state: RootState) => {
    return {
        assignedAssetReportsData: getAssetsPortal.getAssetsPortal(state).getAssignAssetReportData
    }
  }
  
  const mapDispatchToProps = (dispatch: Dispatch) => {
    return {
        fetchAssignedAssetReportData: () => {dispatch(getAssignAssetReportData.request())},

    }
  }
  
  export default connect(mapStateToProps, mapDispatchToProps)(AssignedAssetReports)
  

