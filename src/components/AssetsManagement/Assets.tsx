import React, { useState } from 'react'
import styled from 'styled-components'
import { Box, Paper, Tab, Tabs } from '@mui/material'
import AllAssets from './AllAssets'
import AssetsCategory from './AssetsCategory'
import AssetsOs from './AssetsOs'
import AssetsMake from './AssetsMake'
import AssetsStatus from './AssetsStatus'
import Technologies from './Technologies'
import Reports from './Reports'

const StyledPaper = styled(Paper)(() => ({
  width: '93%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '20px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '10px',
  border: '1px solid #DDDDDD',
}))

const Assets = (props: any) => {
  
    const [selectedTab, setSelectedTab] = useState(() => {
    const savedTab = localStorage.getItem('selectedTab')
    return savedTab ? parseInt(savedTab, 10) : 0
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue)
    localStorage.setItem('selectedTab', newValue.toString())
  }

  return (
    <StyledPaper>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          marginTop: '-20px',
          marginBottom: '10px',
        }}
      >
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label='Tabs for different tables'>
          <Tab label='Dashboard' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(0)} />
          <Tab label='Assets Category' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(1)} />
          <Tab label='Assets OS' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(2)} />
          <Tab label='Assets Make' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(3)} />
          <Tab label='Assets Status' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(4)} />
          <Tab label='Technologies' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(5)} />
          {/* <Tab label='Reports' sx={{ textTransform: 'none' }} onClick={() => setSelectedTab(6)} /> */}
        </Tabs>
      </Box>

      {selectedTab === 0 && (
        <>
          <AllAssets />
        </>
      )}

      {selectedTab === 1 && (
        <>
          <AssetsCategory />
        </>
      )}

      {selectedTab === 2 && (
        <>
          <AssetsOs />
        </>
      )}

      {selectedTab === 3 && (
        <>
          <AssetsMake />
        </>
      )}

      {selectedTab === 4 && (
        <>
          <AssetsStatus />
        </>
      )}

      {selectedTab === 5 && (
        <>
          <Technologies />
        </>
      )} 

      {/* {selectedTab === 6 && (
        <>
          <Reports />
        </>
      )} */}

    </StyledPaper>
  )
}

export default Assets
