import {
  Box,
  InputAdornment,
  IconButton,
  Paper,
  Pagination,
  Tooltip,
  styled,
  Button,
  TextField,
  Typography,
  MenuItem,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import style from '../../utils/styles.json'
import { SearchIconStyle } from '../Common/CommonStyles'
import { ClearIcon } from '@mui/x-date-pickers'
import TableCell, { tableCellClasses } from '@mui/material/TableCell'
import { TableContainer, Table, TableHead, TableBody, TableRow } from '@mui/material'
import InfoIcon from '@mui/icons-material/Info'
import MoreInfoDialog from './Dialogs/MoreInfoDialog'
import styles from '../../utils/styles.json'
import { getAssetsManagementPortalUI, getAssetsPortal } from '../../reducers'
import { RootState } from '../../configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { fetchAssetsDataAsExcel, getAssetsData } from '../../actions'
import { useLocation, useNavigate } from 'react-router-dom'
import { ArrowBack } from '@mui/icons-material'
import { ReactComponent as ExportToExcelIcon } from '../../assets/images/exportToExcel.svg'
import NoProjectRow from './NoProjectRow'
import Loader from '../Common/Loader'
import moment from 'moment'

interface ISearchBoxCustom {
  width?: string
}

function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  } as T
}

const SearchBoxCustom = styled(TextField)<ISearchBoxCustom>(({ width = '260px' }) => ({
  float: 'left',
  '& .MuiOutlinedInput-root': {
    width: `${width}`,
    borderRadius: '20px',
    '& fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
}))

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_BOLD,
  fontSize: '25px',
  fontWeight: 500,
}))

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: styles.FONT_MEDIUM,
  maxHeight: '140px',
}))

const stylee = {
  buttonStyle: {
    fontFamily: 'Montserrat-Regular',
    fontSize: '1rem',
    padding: '0',
    width: '40px',
    height: '40px',
    minWidth: 'fit-content',
    borderRadius: '50%',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'white',
    },
    '@media only screen and (max-width:780px)': {
      fontSize: '0.9rem',
      marginLeft: '20px',
    },
  },
  container: {
    '@media only screen and (max-width:1230px)': {
      width: '200px',
    },
    '@media only screen and (max-width:1200px)': {
      marginTop: '-8px',
    },
    '@media only screen and (max-width:700px)': {
      marginTop: '-3px',
    },
  },
}

const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    padding: '7px 0px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: '12px',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    padding: '7px 0px 0px 0px',
    verticalAlign: 'middle',
  },
}))

const StyledMenuProps = {
  PaperProps: {
    style: {
      maxHeight: 140,
      borderRadius: 10,
    },
  },
}

const StyledTableRow = styled(TableRow)(() => ({
  left: '160px',
  width: '1719px',
  height: '40px',
  boxShadow: '0px 10px 3px #6c6c6c10',
  opacity: '1',
}))

const SelectField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '8.5px 14px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root': {
    marginTop: '6px',
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    marginTop: '1.5px',
    borderRadius: '20px',
  },
}))

const AssetsList = (props: any) => {
  const { fetchUserDataa, userDataa, excelAssetsData, userDataLoadder } = props

  const location = useLocation()
  const query = location.state?.query
  const [page, setPage] = useState(1)
  const rowsPerPage = 100
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const assetQuery = query
  const [searchby, setSearchby] = useState('0')
  const [assetType, setAssetType] = useState('0')

  const clearSearch = () => {
    setSearchTerm('')
    setSearchQuery('')
  }

  const name = (() => {
    if (query === 0) {
      return 'Unassigned List'
    } else if (query === 1) {
      return 'Assigned List'
    } else {
      return 'All Assets List'
    }
  })()

  const handleChangePage = (event: any, newPage: number) => {
    setPage(newPage)
    window.scrollTo(0, 0)
  }

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchQuery(e.target.value)
    },
    3000,
  )

  const navigate = useNavigate()

  const handleNavigation = () => {
    navigate('/home/<USER>')
  }

  useEffect(() => {
    fetchUserDataa({
      page: page,
      limit: rowsPerPage,
      searchby: searchby,
      asset: assetQuery,
      search: searchQuery,
      type: assetType,
    })
  }, [page, searchQuery, assetQuery, assetType])

  const [moreInfodialogopen, setOpenMoreInfo] = useState(false)
  const [selectRow, setSelectRow] = useState({})

  const handleMoreInfoOpen = (row: any) => {
    setOpenMoreInfo(true)
    setSelectRow(row)
  }

  const handleExportClick = (query: number) => {
    excelAssetsData({ status: query })
  }

  return (
    <>
      <Paper sx={{ margin: '20px' }}>
        <Loader state={userDataLoadder} />
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
            justifyContent: 'space-between',
            margin: '0px, 10px, 10px, 0px',
            padding: '0px',
          }}
        >
          <Box
            sx={{
              flex: '0 1 auto',
              marginLeft: '10px',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Box sx={{ marginRight: '10px' }}>
                <SelectField
                  required={false}
                  id='outlined-required'
                  label='Search by'
                  size='small'
                  select
                  SelectProps={{
                    MenuProps: StyledMenuProps,
                  }}
                  value={searchby}
                  onChange={(event) => setSearchby(event.target.value)}
                  variant='outlined'
                >
                  <StyledMenuItem value='0'>Search by Employee Name</StyledMenuItem>
                  <StyledMenuItem value='1'>Search by Asset Details</StyledMenuItem>
                </SelectField>
              </Box>

              <Box>
                <SearchBoxCustom
                  id='outlined-basic'
                  placeholder={searchby === '0' ? 'Search Employee' : 'Search Asset'}
                  variant='outlined'
                  size='small'
                  width='75%'
                  value={searchTerm}
                  onChange={(e) => {
                    const value = e.target.value
                    setSearchTerm(value)
                    functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)
                  }}
                  InputProps={{
                    startAdornment: <SearchIconStyle />,
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton aria-label='clear-icon' onClick={clearSearch}>
                          {<ClearIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              fontFamily: 'Montserrat, sans-serif',
              color: 'primary.main',
              position: 'absolute',
              left: '50%',
              transform: 'translateX(-50%)',
              textAlign: 'center',
            }}
          >
            <StyledTypography>{name}</StyledTypography>
          </Box>

          <Box
            sx={{
              flex: '0 1 auto',
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              justifyContent: 'center',
              marginRight: '20px',
            }}
          >
            <Box>
              <Box>
                <SelectField
                  required={false}
                  id='outlined-required'
                  label='Asset Type'
                  size='small'
                  select
                  SelectProps={{
                    MenuProps: StyledMenuProps,
                  }}
                  value={assetType}
                  onChange={(event) => {
                    setAssetType(event.target.value);
                    setPage(1);
                  }}                  variant='outlined'
                  sx={{ '& .MuiOutlinedInput-input': { width: '70px' } }}
                >
                  <StyledMenuItem value='0'>Hardware</StyledMenuItem>
                  <StyledMenuItem value='1'>Software</StyledMenuItem>
                </SelectField>
              </Box>
            </Box>
            <Box>
              <Tooltip title='Export to Excel'>
                <Button
                  onClick={() => {
                    handleExportClick(query)
                  }}
                  sx={stylee.buttonStyle}
                  variant='outlined'
                >
                  <ExportToExcelIcon />
                </Button>
              </Tooltip>
            </Box>

            <Box onClick={handleNavigation} sx={{ cursor: 'pointer' }}>
              <ArrowBack />
            </Box>
          </Box>
        </Box>

        <Box sx={{ margin: '10px' }}>
          <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>
            <Table sx={{ minWidth: 1000 }} aria-label='customized table'>
              <TableHead>
                <StyledTableRow>
                  {query !== 0 && <StyledTableCell>Assignee</StyledTableCell>}
                  <StyledTableCell>Assets ID</StyledTableCell>
                  {assetType === '0' ? (
                    <>
                      <StyledTableCell>Service Tag</StyledTableCell>
                      <StyledTableCell>Adapter No</StyledTableCell>
                      <StyledTableCell>Assets OS</StyledTableCell>
                      <StyledTableCell>Hard Disk</StyledTableCell>
                      <StyledTableCell>Ram</StyledTableCell>
                      <StyledTableCell>Status</StyledTableCell>
                      <StyledTableCell>Working Status</StyledTableCell>
                      <StyledTableCell>More Info</StyledTableCell>
                    </>
                  ) : (
                    <>
                      <StyledTableCell>Name</StyledTableCell>
                      <StyledTableCell>License Key</StyledTableCell>
                      <StyledTableCell>Make</StyledTableCell>
                      <StyledTableCell>Version</StyledTableCell>
                      <StyledTableCell>Purchase Date</StyledTableCell>
                      <StyledTableCell>Expiry Date</StyledTableCell>
                      <StyledTableCell>Cost</StyledTableCell>
                    </>
                  )}
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {userDataa?.assetDetail?.map((row: any) => (
                  <StyledTableRow key={row.laptop_no}>
                    {query !== 0 && (
                      <StyledTableCell>
                        <Tooltip title={row?.assigned_user ? `${row.assigned_user}` : '-'} arrow>
                          <span
                            style={{
                              display: 'inline-block',
                              maxWidth: '100px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {row?.assigned_user ? row.assigned_user : '-'}
                          </span>
                        </Tooltip>
                      </StyledTableCell>
                    )}
                    <StyledTableCell>{row?.laptop_no}</StyledTableCell>

                    {assetType === '0' ? (
                      <>
                        <StyledTableCell>
                          <Tooltip title={row?.service_tag_no || '-'} arrow>
                            <span
                              style={{
                                display: 'inline-block',
                                maxWidth: '120px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {row?.service_tag_no || '-'}
                            </span>
                          </Tooltip>
                        </StyledTableCell>

                        <StyledTableCell>
                          <Tooltip title={row?.adapter_no || '-'} arrow>
                            <span
                              style={{
                                display: 'inline-block',
                                maxWidth: '120px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {row?.adapter_no || '-'}
                            </span>
                          </Tooltip>
                        </StyledTableCell>
                        <StyledTableCell>{row?.laptop_os || '-'}</StyledTableCell>
                        <StyledTableCell>{row?.hard_disk_size || '-'}</StyledTableCell>
                        <StyledTableCell>{row?.ram_size || '-'}</StyledTableCell>
                        <StyledTableCell>
                          {row?.status === 1 ? 'Assigned' : row?.status === 0 ? 'Unassigned' : '-'}
                        </StyledTableCell>
                        <StyledTableCell>{row?.working_status || '-'}</StyledTableCell>
                        <StyledTableCell>
                          <Tooltip title={'More Info'} arrow>
                            <span
                              style={{
                                display: 'inline-block',
                                maxWidth: '120px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              <IconButton onClick={() => handleMoreInfoOpen(row)}>
                                <InfoIcon />
                              </IconButton>
                            </span>
                          </Tooltip>
                        </StyledTableCell>
                      </>
                    ) : (
                      <>
                      <StyledTableCell>
                        <Tooltip title={row?.software_name ? `${row.software_name}` : '-'} arrow>
                          <span
                            style={{
                              display: 'inline-block',
                              maxWidth: '100px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {row?.software_name ? row.software_name : '-'}
                          </span>
                        </Tooltip>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Tooltip title={row?.license_key ? `${row.license_key}` : '-'} arrow>
                          <span
                            style={{
                              display: 'inline-block',
                              maxWidth: '100px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {row?.license_key ? row.license_key : '-'}
                          </span>
                        </Tooltip>
                        </StyledTableCell>
                        <StyledTableCell>{row?.laptop_make || '-'}</StyledTableCell>
                        <StyledTableCell>{row?.version || '-'}</StyledTableCell>
                        <StyledTableCell>
                          {row?.purchase_date ? moment(row.purchase_date).format("MM/DD/YYYY") : "-"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {row?.license_expiry_date ? moment(row.license_expiry_date).format("MM/DD/YYYY") : "-"}
                        </StyledTableCell>
                        <StyledTableCell>{row?.cost ? `₹${row.cost}` : "-"}</StyledTableCell>
                      </>
                    )}
                  </StyledTableRow>
                ))}
                {userDataa?.assetDetail?.length === 0 && (
                  <StyledTableRow>
                    <StyledTableCell colSpan={10} align='center'>
                      <NoProjectRow />
                    </StyledTableCell>
                  </StyledTableRow>
                )}
              </TableBody>
            </Table>
            <Pagination
              sx={{
                float: 'right',
                margin: '15px 0px 10px 0px',
              }}
              page={page}
              onChange={handleChangePage}
              count={Math.ceil(+userDataa?.totalPages)}
              color='primary'
            />
          </TableContainer>
          {moreInfodialogopen && (
            <MoreInfoDialog
              moreInfodialogopen={moreInfodialogopen}
              onClose={setOpenMoreInfo}
              userData={selectRow}
            />
          )}
        </Box>
      </Paper>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    userDataa: getAssetsPortal.getAssetsPortal(state).getAssetsData,
    userDataLoadder: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssetsPortalLoader,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchUserDataa: (data: {}) => dispatch(getAssetsData.request({ data })),
    excelAssetsData: (data: {}) => dispatch(fetchAssetsDataAsExcel.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssetsList)
