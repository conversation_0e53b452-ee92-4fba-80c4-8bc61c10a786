import { Box, Button, CircularProgress, Dialog, Grid, IconButton, InputAdornment, Pagination, Paper, styled, Tooltip, } from "@mui/material";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { Dispatch } from "redux";
import { RootState } from "../../configureStore";
import { deleteAssetOs, getAssetOSData } from "../../actions";
import { getAssetsManagementPortalUI, getAssetsPortal } from "../../reducers";
import { ClearIcon } from "@mui/x-date-pickers";
import { SearchBoxCustom, SearchIconStyle } from "../Common/CommonStyles";
import AssetOSDialog from "./Dialogs/AssetOSDialog";



import { style } from '../ProjectManagement/mandate/projectCustomersStyle'
import {
  Card,
  CardContent,
  Typography,
} from '@mui/material'
import { ReactComponent as DeleteIcon } from '../../assets/images/deleteIcon.svg'
import { toast } from "react-toastify";
import { NoCards } from "../HrControl/Designations/NoCards";
import Loader from "../Common/Loader";


function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>;
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const ActionButton = styled(Button)(() => ({
  fontSize: "13px",
  height: "42px",
  borderRadius: "20px",
  padding: "5px 20px",
  fontFamily: "Arial, sans-serif",
}));

const AssetsOs: React.FC = (props: any) =>  {
  const{
    fetchAssetOSData,
    assetOSData,
    isAssetOsLodder,
    updateAssetOsSucsess,
    deleteAssetOs,
    deleteAssetOsSuccess,
    deleteAssetOsReset,
  }= props

  const [page, setPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [assetOsDialogOpen, setAssetOsDialogOpen] = useState(false)

  const handleAssetOsDialogOpen = () => {
    setAssetOsDialogOpen(true); 
  }



  const clearSearch = () => {
    setSearchTerm("");
    setSearchQuery("");
  };

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchQuery(e.target.value);
    },
    1200
  );

  useEffect (() => {
    fetchAssetOSData({ page: page, search: searchQuery, });
  }, [page, searchQuery, updateAssetOsSucsess, deleteAssetOsSuccess])  

  const handleChangePage = (event: any, newPage: number) => {
    setPage(newPage)
    window.scrollTo(0, 0);
  }

  const handleDelete = (row: any) => {
    const deleteData = {
      id: row.id
    }
    deleteAssetOs(deleteData);
  }

  useEffect(()=>{
    if(deleteAssetOsSuccess) {
      toast.success("Asset OS Deleted")
      deleteAssetOsReset()
    }
  }, [deleteAssetOsSuccess])


  
  return (
    <Paper>
      <Loader state={isAssetOsLodder} />

      <Box sx={{
        border: '1px solid', 
        borderRadius: '10px', 
        borderColor: 'grey.500'
      }}>
        <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '10px 20px 0px 20px'
          }}>

          <Box>
            <SearchBoxCustom
              id="outlined-basic"
              placeholder="Search Asset OS"
              variant="outlined"
              size="small"
              width="100%"
              value={searchTerm}
              onChange={(e) => {
                const value = e.target.value;
                setSearchTerm(value);
                functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>);
              }}
              InputProps={{
                startAdornment: <SearchIconStyle />,
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton aria-label="clear-icon" onClick={clearSearch}>
                      {searchTerm && <ClearIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>


          <Box>
            <ActionButton
              variant="outlined"
              onClick={handleAssetOsDialogOpen}
            >
              Add Asset OS
            </ActionButton>
          </Box>
          {assetOsDialogOpen && <AssetOSDialog assetOsDialogOpen={assetOsDialogOpen} assetOsClose={setAssetOsDialogOpen}/>}
          
        </Box>

        <Box sx={style.cardBox}>
            <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 2 }} sx={{padding:'10px'}}>
              {assetOSData?.result?.map((row: any) => (
                <Grid item xs={12} sm={6} md={4}>
                  <Card sx={style.cardStyle}>
                    <CardContent sx={style.cardContentStyle}>
                      <Box sx={style.cardContentBoxStyle}>
                        <Box width='70%'>
                            <Typography
                              noWrap
                              sx={style.cardContentTypoStyle}
                            >
                              {row.name}
                            </Typography>
                        </Box>
                        <Box sx={style.iconBoxStyle}>
                          <Box sx={style.iconsStyle}>
                            <IconButton
                              onClick={() => handleDelete(row)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
              {assetOSData?.result?.length === 0 && (
                <NoCards />
              )}
            </Grid>
          
          <Pagination
              sx={{
                float: 'right',
                margin: '15px 0px 10px 0px',
              }}
              page={page}
              onChange={handleChangePage}
              count={
                Math.ceil(
                  +(assetOSData?.totalPages),
                )
              }
              color='primary'
            />
        </Box>
      </Box>



    </Paper>
  );
};

const mapStateToProps = (state: RootState) => {
  return {
    assetOSData: getAssetsPortal.getAssetsPortal(state).getAssetOSData,
    isAssetOsLodder: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssetOsLoader,
    updateAssetOsSucsess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).UpdateAssetOsSuccess,
    deleteAssetOsSuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).deleteAssetOsSuccess,



  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAssetOSData: (data: {}) => dispatch(getAssetOSData.request({data})),
    deleteAssetOs: (data: {}) => {dispatch(deleteAssetOs.request({data}))},
    deleteAssetOsReset: () => {dispatch(deleteAssetOs.reset())}

  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssetsOs)
