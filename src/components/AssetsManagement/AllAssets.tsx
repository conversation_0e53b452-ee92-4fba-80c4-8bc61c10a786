import React, { useEffect, useState } from 'react'
import { Dispatch } from 'redux'
import {
  Box,
  Paper,
  Typography,
  Divider,
  Card,
  CardContent,
  CardActionArea,
  Button,
  Dialog,
  Grid,
} from '@mui/material'
import { styled } from '@mui/system'
import { connect } from 'react-redux'
import {
  fetchAssetAbbrevationData,
  fetchAssetLocationData,
  fetchAssetMakeData,
  fetchAssetOSData,
  fetchAssetStatusData,
  fetchAssetTechnologiesData,
  fetchNumberOfAssetsData,
  getAssetsAbbrevationCountData,
  getAssetsData,
  getAssetStatusCountData,
  getDownloadAssetsQrData,
  getEmployeeAssetsCountData,
} from '../../actions'
import { FamilyDetailsHeading } from '../Common/CommonStyles'
import AssignAssetDialog from './Dialogs/AssignAssetDialog'
import { RootState } from '../../configureStore'
import { getAssetsPortal } from '../../reducers'
import UnallocateAssetDialog from './Dialogs/UnallocateDialog'
import AddNewAssetDialog from './Dialogs/AddNewAssetDialog'
import { getAssetsManagementPortalUI } from '../../reducers/ui'
import styles from '../../utils/styles.json'

import { useNavigate } from 'react-router-dom'
import EmployeesHavingMoreThanOneAsset from './Charts/EmployeesHavingMoreThanOneAsset'
import AssetAbbrevationChart from './Charts/AssetAbbrevationChart'
import AssetsStatusChart from './Charts/AssetsStatusChart'
import QrCode2Icon from '@mui/icons-material/QrCode2'
import style from '../../utils/styles.json'
import DownloadQrDialog from './Dialogs/DownloadQrDialog'
import CircularProgresss from '../Common/CircularProgresss'

const ActionButton = styled(Button)(() => ({
  fontSize: '13px',
  height: '42px',
  borderRadius: '20px',
  padding: '5px 20px',
  fontFamily: 'Arial, sans-serif',
}))

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_MEDIUM,
}))

const StyledCard = styled(Card)(({ theme }) => ({
  margin: '10px',
  width: '33%',
  borderRadius: '20px',
}))

const AllAssets: React.FC = (props: any) => {
  const {
    numberOfAssetsData,
    fetchNumberOfAssetsData,
    isAssignAssetSuccess,
    isUnallocateAssetSuccess,
    isAddNewAssetSuccess,
    fetchAssetAbiberation,
    fetchAssetLocation,
    fetchAssetMake,
    fetchAssetStatus,
    fetchAssetOS,
    fetchAssetTechnologies,
    fetchEmployeeAssetsCount,
    fetchAssetsAbbrevationCount,
    employeeAssetCountData,
    assetAbbrevationCountData,
    fetchAssetsStatusCount,
    assetStatusCountData,
    fetchDownloadAssetsQrData,
  } = props

  const navigate = useNavigate()

  useEffect(() => {
    fetchNumberOfAssetsData()
  }, [isAssignAssetSuccess, isUnallocateAssetSuccess, isAddNewAssetSuccess])

  useEffect(() => {
    fetchEmployeeAssetsCount()
    fetchAssetsAbbrevationCount()
    fetchAssetsStatusCount()
  }, [])

  const [assignassetopen, setOpenAssign] = useState(false)
  const [unallocatedialogopen, setOpenUnallocate] = useState(false)
  const [addNewAssetdialogopen, setOpenAddNewAsset] = useState(false)
  const [downloadQrDialogOpen, setDownloadQrDialogOpen] = useState(false)

  const handleOpen = (e: any) => {
    e.stopPropagation()
    setOpenAssign(true)
  }

  const handleUnallocateOpen = (e: any) => {
    e.stopPropagation()
    setOpenUnallocate(true)
  }

  const handleAddNewAssetOpen = (e: any) => {
    e.stopPropagation()
    setOpenAddNewAsset(true)
    fetchAssetAbiberation()
    fetchAssetLocation()
    fetchAssetMake()
    fetchAssetStatus()
    fetchAssetOS()
    fetchAssetTechnologies()
  }

  const handleDownloadQrDialogOpen = (e: any) => {
    e.stopPropagation()
    setDownloadQrDialogOpen(true)
    fetchDownloadAssetsQrData()
  }

  const handleNavigation = (query: number) => {
    navigate('/home/<USER>/assetslist', { state: { query: query } })
  }

  return (
    <Paper>
      <Box sx={{ border: '1px solid', borderRadius: '10px', borderColor: 'grey.500' }}>
        <Box
          sx={{
            margin: '10px',
            textAlign: 'center',
            display: 'flex',
            justifyContent: 'space-between',
            gap: '0%',
          }}
        >
          <StyledCard variant='outlined' square>
            <CardActionArea>
              <CardContent onClick={() => handleNavigation(1)}>
                <Box sx={{ textAlign: 'center', width: '100%' }}>
                  {/* <Typography variant="h4" sx={{ color: '#03fc84', paddingBottom: '10px' }}>{numberOfAssetsData?.result?.stats?.assigned}</Typography> */}
                  {numberOfAssetsData?.result?.stats?.assigned !== undefined ? (
                    <Typography variant='h4' sx={{ color: '#03fc84', paddingBottom: '10px' }}>
                      {numberOfAssetsData?.result?.stats?.assigned}
                    </Typography>
                  ) : (
                    <CircularProgresss />
                  )}
                  <StyledTypography variant='body1'>Assigned Asset</StyledTypography>
                  <Divider
                    sx={{
                      width: '80%',
                      margin: '16px auto',
                      borderColor: 'grey',
                      borderWidth: '1px',
                    }}
                  />
                  <ActionButton variant='outlined' onClick={handleOpen}>
                    Assign Asset
                  </ActionButton>
                </Box>
              </CardContent>
            </CardActionArea>
          </StyledCard>
          {assignassetopen && (
            <AssignAssetDialog assignassetopen={assignassetopen} onClose={setOpenAssign} />
          )}

          <StyledCard variant='outlined' square>
            <CardActionArea>
              <CardContent onClick={() => handleNavigation(0)}>
                <Box sx={{ textAlign: 'center', width: '100%' }}>
                  {/* <Typography variant="h4" sx={{ color: '#f5024f', paddingBottom: '10px' }}>{numberOfAssetsData?.result?.stats?.unassigned}</Typography> */}
                  {numberOfAssetsData?.result?.stats?.assigned !== undefined ? (
                    <Typography variant='h4' sx={{ color: '#f5024f', paddingBottom: '10px' }}>
                      {numberOfAssetsData?.result?.stats?.unassigned}
                    </Typography>
                  ) : (
                    <CircularProgresss />
                  )}
                  <StyledTypography variant='body1'>Unassigned Assets</StyledTypography>
                  <Divider
                    sx={{
                      width: '80%',
                      margin: '16px auto',
                      borderColor: 'grey',
                      borderWidth: '1px',
                    }}
                  />
                  <ActionButton variant='outlined' onClick={handleUnallocateOpen}>
                    Unassign Asset
                  </ActionButton>
                </Box>
              </CardContent>
            </CardActionArea>
          </StyledCard>
          {unallocatedialogopen && (
            <UnallocateAssetDialog
              unallocatedialogopen={unallocatedialogopen}
              onClose={setOpenUnallocate}
            />
          )}

          <StyledCard variant='outlined' square>
            <CardActionArea>
              <CardContent onClick={() => handleNavigation(2)}>
                <Box sx={{ textAlign: 'center', width: '100%' }}>
                  {/* <Typography variant="h4" sx={{ color: '#2b293d', paddingBottom: '10px' }}>{numberOfAssetsData?.result?.stats?.total}</Typography> */}
                  {numberOfAssetsData?.result?.stats?.assigned !== undefined ? (
                    <Typography variant='h4' sx={{ color: '#2b293d', paddingBottom: '10px' }}>
                      {numberOfAssetsData?.result?.stats?.total}
                    </Typography>
                  ) : (
                    <CircularProgresss />
                  )}
                  <StyledTypography variant='body1'>All Assets</StyledTypography>
                  <Divider
                    sx={{
                      width: '80%',
                      margin: '16px auto',
                      borderColor: 'grey',
                      borderWidth: '1px',
                    }}
                  />
                  <ActionButton variant='outlined' onClick={handleAddNewAssetOpen}>
                    Add New Asset
                  </ActionButton>
                </Box>
              </CardContent>
            </CardActionArea>
          </StyledCard>
          <AddNewAssetDialog
            addNewAssetdialogopen={addNewAssetdialogopen}
            onClose={setOpenAddNewAsset}
          />

          <StyledCard variant='outlined' square>
            <CardActionArea>
              <CardContent>
                <Box sx={{ textAlign: 'center', width: '100%' }}>
                  <QrCode2Icon
                    fontSize='large'
                    sx={{ color: style.PRIMARY_COLOR, paddingBottom: '10px' }}
                  />
                  <StyledTypography variant='body1'>Generate Asset's QR Code</StyledTypography>
                  <Divider
                    sx={{
                      width: '80%',
                      margin: '16px auto',
                      borderColor: 'grey',
                      borderWidth: '1px',
                    }}
                  />
                  <ActionButton variant='outlined' onClick={handleDownloadQrDialogOpen}>
                    Generate QR Code
                  </ActionButton>
                </Box>
              </CardContent>
            </CardActionArea>
          </StyledCard>
          {downloadQrDialogOpen && (
            <DownloadQrDialog
              downloadQrDialogOpen={downloadQrDialogOpen}
              downloadQrClose={setDownloadQrDialogOpen}
            />
          )}
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6} lg={4}>
            <Box
              sx={{
                height: '75px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <FamilyDetailsHeading>Employees with more than 1 asset</FamilyDetailsHeading>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {employeeAssetCountData?.result?.length > 0 ? (
                <EmployeesHavingMoreThanOneAsset
                  myChartData={employeeAssetCountData.result}
                ></EmployeesHavingMoreThanOneAsset>
              ) : (
                <CircularProgresss />
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6} lg={4}>
            <Box
              sx={{
                height: '75px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <FamilyDetailsHeading>Asset-wise Split-up</FamilyDetailsHeading>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {assetAbbrevationCountData?.laptopNumbers?.length > 0 ? (
                <AssetAbbrevationChart
                  myChartData={assetAbbrevationCountData?.laptopNumbers ?? []}
                ></AssetAbbrevationChart>
              ) : (
                <CircularProgresss />
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6} lg={4}>
            <Box
              sx={{
                height: '75px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <FamilyDetailsHeading>Asset Status</FamilyDetailsHeading>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {assetStatusCountData?.length > 0 ? (
                <AssetsStatusChart myChartData={assetStatusCountData ?? []}></AssetsStatusChart>
              ) : (
                <CircularProgresss />
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    numberOfAssetsData: getAssetsPortal.getAssetsPortal(state).getNumberOfAssetsData,
    isAssignAssetSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssignAssetSuccess,
    isUnallocateAssetSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isUnallocateAssetSuccess,
    isAddNewAssetSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAddNewAssetSuccess,
    isAssetLoader: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssetsPortalLoader,
    employeeAssetCountData: getAssetsPortal.getAssetsPortal(state).getEmployeeAssetsCountData,
    assetAbbrevationCountData: getAssetsPortal.getAssetsPortal(state).getAssetAbbrevationCountData,
    assetStatusCountData: getAssetsPortal.getAssetsPortal(state).getAssetStatusCountData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchNumberOfAssetsData: () => dispatch(fetchNumberOfAssetsData.request()),
    fetchAssetAbiberation: () => dispatch(fetchAssetAbbrevationData.request()),
    fetchAssetLocation: () => dispatch(fetchAssetLocationData.request()),
    fetchAssetMake: () => dispatch(fetchAssetMakeData.request()),
    fetchAssetStatus: () => dispatch(fetchAssetStatusData.request()),
    fetchAssetOS: () => dispatch(fetchAssetOSData.request()),
    fetchAssetTechnologies: () => dispatch(fetchAssetTechnologiesData.request()),
    fetchEmployeeAssetsCount: () => dispatch(getEmployeeAssetsCountData.request()),
    fetchAssetsAbbrevationCount: () => dispatch(getAssetsAbbrevationCountData.request()),
    fetchAssetsStatusCount: () => dispatch(getAssetStatusCountData.request()),
    fetchDownloadAssetsQrData: () => dispatch(getDownloadAssetsQrData.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AllAssets)
