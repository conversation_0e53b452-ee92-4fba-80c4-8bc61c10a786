import * as React from 'react';
import { styled } from '@mui/material/styles'
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Typography from '@mui/material/Typography';
import styles from '../../../utils/styles.json';
import { Box, Button, Grid, Paper } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import TableCell, { tableCellClasses } from '@mui/material/TableCell'
import {
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
} from "@mui/material";



const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialog-paper': {
      maxWidth: '717px',
      maxHeight: '36vh', 
      overflowY: 'hidden',
    },
    '& .MuiDialogContent-root': {
      padding: '30px 40px',
    },
    '& .MuiDialogActions-root': {
      justifyContent: 'flex-end',
      gap: '15px',
      padding: '20px 30px',
    },
  
    '& .MuiButton-root:hover': {},
    '&.MuiButtonBase-root-MuiButton-root:hover': {},
  }))
  
  const Heading = styled(Typography)(({ theme }) => ({
    fontSize: '26px',
    textAlign: 'center',
    fontFamily: styles.FONT_BOLD,
    letterSpacing: '0px',
    padding: '10px'
  }))

  const StyledTableCell = styled(TableCell)(() => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: styles.PRIMARY_COLOR,
      color: 'White',
      fontFamily: styles.FONT_MEDIUM,
      textAlign: 'center',
      fontSize: '13px',
      letterSpacing: '0px',
      padding: '11px 0px',
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 12,
      fontFamily: styles.FONT_MEDIUM,
      textAlign: 'center',
      color: '#483f3f',
      letterSpacing: '0px',
    },
  }))
  
  const StyledTableRow = styled(TableRow)(() => ({
    left: '160px',
    width: '1719px',
    height: '60px',
    boxShadow: '0px 10px 3px #6c6c6c10',
    opacity: '1',
  }))
  

const MoreInfoDialog = (Props: {
    moreInfodialogopen: boolean
    onClose: React.Dispatch<React.SetStateAction<boolean>>
    userData: any
}) => {
    const {moreInfodialogopen, onClose, userData} = Props;

    const handleMoreInfoDialogClose = () => {
        onClose(false);
    };

    return (
        <>
            <BootstrapDialog onClose={handleMoreInfoDialogClose} aria-labelledby='customized-dialog-title' open={moreInfodialogopen}>
                <Heading>
                 {userData?.laptop_no || "-"}
                  <IconButton
                    aria-label="close"
                    onClick={handleMoreInfoDialogClose}
                    sx={{
                      position: 'absolute',
                      right: 16,
                      top: 16,
                      color: (theme) => theme.palette.primary.main,
                    }}
                  >
                    <CloseIcon />
                  </IconButton>
                </Heading>
                <DialogContent dividers>
                  <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
                    <Box>
                        <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>
                            <Table sx={{ minWidth: 700 }} aria-label='customized table'>
                            <TableHead>
                                <StyledTableRow>
                                    <StyledTableCell>Make</StyledTableCell>
                                    <StyledTableCell>Purchased Date</StyledTableCell>
                                    <StyledTableCell>Description</StyledTableCell>
                                </StyledTableRow>
                            </TableHead>
                            <TableBody>
                                <StyledTableRow>
                                    <StyledTableCell>{userData?.laptop_make || "-"}</StyledTableCell>
                                    <StyledTableCell>
                                        {userData?.purchased_date? new Date(userData.purchased_date).toLocaleDateString(): "-"}
                                    </StyledTableCell>
                                    <StyledTableCell>{userData?.comments || "-"}</StyledTableCell>
                                </StyledTableRow>
                            </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                  </Grid>
                </DialogContent>

            </BootstrapDialog>
        </>
      );
}

export default MoreInfoDialog;