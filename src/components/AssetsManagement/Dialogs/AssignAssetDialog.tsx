import * as React from 'react';
import { styled } from '@mui/material/styles'
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Typography from '@mui/material/Typography';
import styles from '../../../utils/styles.json';
import styless from '../../../utils/styles.json'
import { Autocomplete, CircularProgress, Grid, Theme } from '@mui/material';
import { getAssetsManagementPortalUI, getAssetsPortal, projectManagementEntity } from '../../../reducers';
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore';
import { useEffect, useState } from 'react';
import { fetchAssetsData, fetchWorkingEmp, getAllEmployees, getEmpData, updateAssignAsset } from '../../../actions';
import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import { toast } from 'react-toastify';
import { DatePicker } from '@mui/x-date-pickers';
import Loader from '../../Common/Loader';


interface Employee {
  id: string;
  employee_id: string;
  first_name: string;
  middle_name: string;
  last_name: string;
}

interface Asset {
  data: [];
  asset_id: string;
}

interface RequestDataType {

  laptop_no: string | null;
  employee_id: string | null;
  comments: string | null;

}

function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>;
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    width: '45%',
    maxWidth: '1000px',
    maxHeight: '90vh',
    overflowY: 'auto',

  },
  '& .MuiDialogContent-root': {
    padding: '20px 30px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },

  '& .MuiButton-root:hover': {},
  '&.MuiButtonBase-root-MuiButton-root:hover': {},
}))



const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
  padding: '10px'
}))

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '15px',
    lineHeight: '1.8em',
  },
}))

const stylesss = {
  autoCompleteStyle: {
    '.MuiInputBase-root': {
      padding: '19px 11px',
      borderRadius: '20px',
      fontSize: '13px',
      fontFamily: styless.FONT_MEDIUM,
      height: '42px',
    },
    '& .MuiFormControl-root': {
      margin: '0',
      marginTop: '5px',
    },
    '& .MuiFormLabel-root ': {
      backgroundColor: 'white',
      
    },
    '.MuiFormLabel-asterisk': {
      color: 'red',
    },
  },
}

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))




const AssignAssetDialog = (props: {
  assignassetopen: boolean,
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  AllEmpList: Employee[],
  fetchAllEmpAPI: (data?: {}) => { type: string };
  fetchAllEmpAPIReset: () => {}
  AllAssetsList: string[],
  fetchAllAssetsListAPI: (data?: {}) => { type: string };
  fetchAllAssetsAPIReset: () => {}
  assignAsset: (data: RequestDataType) => { type: string; }
  isAssignAssetLoader: boolean
  isAssignAssetSuccess: boolean
  assignAssetReset: any
  assignAssetMessage: any
}) => {

  const {
    assignassetopen,
    onClose,
    AllEmpList,
    fetchAllEmpAPI,
    fetchAllEmpAPIReset,
    AllAssetsList,
    fetchAllAssetsListAPI,
    fetchAllAssetsAPIReset,
    assignAsset,
    isAssignAssetLoader,
    isAssignAssetSuccess,
    assignAssetReset,
    assignAssetMessage,
  } = props;



  const [employee_id, setEmployeeID] = useState<null | string>('')
  const [employeeAnchor, setEmployeeAnchor] = useState(false)
  const [employeeAnchorAsset, setEmployeeAnchorAsset] = useState(false)
  const [laptop_no, setLaptopNo] = useState('');
  const [comments, setComments] = useState('');
  const [searchTerm, setSearchTerm] = useState("")
  const [searchEmp, setSearchEmp] = useState("")
  const [searchAssetTerm, setSearchAssetTerm] = useState("")
  const [searchAsset, setSearchAsset] = useState("")

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchEmp(e.target.value);
    },
    1200
  );

  const functionDebounce2 = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchAsset(e.target.value);
    },
    1200
  );



  const handleClose = () => {
    onClose(false);
  };

  const handleSearchEmp = (value: string) => {
    setSearchTerm(value)
  }

  const handleSearchAsset = (value: string) => {
    setSearchAssetTerm(value)
  }


  useEffect(() => {
    if (searchEmp)
      fetchAllEmpAPI({ searchTerm: searchEmp })
    else
      fetchAllEmpAPIReset()
  }, [searchEmp])



  useEffect(() => {
    if (searchAsset)
      fetchAllAssetsListAPI({ searchAssetTerm: searchAsset })
    else
      fetchAllAssetsAPIReset()
  }, [searchAsset])


  const handleSave = () => {
    const requestData = {
      laptop_no: laptop_no,
      employee_id: employee_id,
      comments: comments,
    }

    assignAsset(requestData);
  }

  useEffect(() => {
    if (isAssignAssetSuccess) {
      toast.success("Laptop assigned successfully")
      assignAssetReset()
      handleClose()
    }
  }, [isAssignAssetLoader])


  return (
    <>
      <BootstrapDialog onClose={handleClose} aria-labelledby='customized-dialog-title' open={assignassetopen}>
        <Loader state={isAssignAssetLoader} />

        <Heading>
          Assign Asset
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers>
          <Grid container rowSpacing={3} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
            <Grid item xs={6} sm={6} sx={{ fontFamily: styles.FONT_MEDIUM, }}>
              <Autocomplete
                autoFocus={false}
                size='small'
                open={employeeAnchor}
                disablePortal
                id='select-employees'
                options={(AllEmpList ?? []).map((employee) => ({
                  label: `${employee.employee_id ?? ''}${employee.employee_id ? '-' : ''}${employee.first_name} ${employee.middle_name} ${employee.last_name}`.trim(),
                  id: employee.id,
                }))}
                inputValue={searchTerm}
                sx={stylesss.autoCompleteStyle}
                renderInput={(params) => (
                  <TextField {...params} label='Search Employee' variant='outlined' />
                )}
                onInputChange={(e, value) => {
                  functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)
                  handleSearchEmp(value);
                  if (value !== '') {
                    setEmployeeAnchor(true);
                  }
                  else {
                    setEmployeeAnchor(false);
                  }
                }}
                onChange={(event, newValue) => {
                  setEmployeeID(newValue?.id ?? null);
                  setEmployeeAnchor(false);
                }}
                ListboxProps={{
                  style: {
                    maxHeight: '140px',
                  },
                }}
              />


            </Grid>

            <Grid item xs={6} sm={6} sx={{ fontFamily: styles.FONT_MEDIUM, }}>
              <Autocomplete
                autoFocus={false}
                open={employeeAnchorAsset}
                size='small'
                disablePortal
                id='select-Asset-No'
                options={AllAssetsList ?? []}
                inputValue={searchAssetTerm}
                sx={stylesss.autoCompleteStyle}
                renderInput={(params) => (
                  <TextField {...params} required label='Search Asset' variant='outlined' />
                )}
                onInputChange={(e, value) => {
                  functionDebounce2(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)
                  handleSearchAsset(value);
                  if (value !== '') {
                    setEmployeeAnchorAsset(true);
                  }
                  else {
                    setEmployeeAnchorAsset(false);
                  }
                }}
                onChange={(event: any, newValue: string | null) => {
                  setLaptopNo(newValue ?? '')
                  setEmployeeAnchorAsset(false)
                }}
                ListboxProps={{
                  style: {
                    maxHeight: '140px',
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={12} sx={{ fontFamily: styles.FONT_MEDIUM, }}>
              <InputField
                required
                id='outlined-required'
                label='Add Comments Here'
                size='small'
                onChange={(e) => setComments(e.target.value)}
              />
            </Grid>



          </Grid>
        </DialogContent>

        <DialogActions>

          <Button
            sx={{
              color: '#fff !important',
              opacity: (employee_id == null || laptop_no === '' || comments === '') ? 0.5 : 1,
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            disabled={(employee_id == null || laptop_no === '' || comments === '')}
            onClick={handleSave}
            autoFocus
          >
            SAVE
          </Button>

          <CancelButton autoFocus onClick={handleClose}>
            CANCEL
          </CancelButton>
        </DialogActions>
      </BootstrapDialog>
    </>
  );
}

const mapStateToProps = (state: RootState) => {
  return {
    AllEmpList: getAssetsPortal.getAssetsPortal(state).getEmpData,
    AllAssetsList: getAssetsPortal.getAssetsPortal(state).getAllAssetList,
    isAssignAssetLoader: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssignAssetLoader,
    isAssignAssetSuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssignAssetSuccess,
    assignAssetMessage: getAssetsPortal.getAssetsPortal(state).getAssignAsset,

  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAllEmpAPI: (data?: {}) => dispatch(getEmpData.request({ data })),
    fetchAllAssetsListAPI: (data?: {}) => dispatch(fetchAssetsData.request({ data })),
    assignAsset: (data: RequestDataType) => dispatch(updateAssignAsset.request({ data })),
    assignAssetReset: () => dispatch(updateAssignAsset.reset()),
    fetchAllEmpAPIReset: () => dispatch(getEmpData.reset()),
    fetchAllAssetsAPIReset: () => dispatch(fetchAssetsData.reset()),


  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssignAssetDialog)
