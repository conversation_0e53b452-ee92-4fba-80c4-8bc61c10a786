import {
  Autocomplete,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  IconButton,
  styled,
  TextField,
  Theme,
  Typography,
} from '@mui/material'
import * as React from 'react'
import CloseIcon from '@mui/icons-material/Close'
import styles from '../../../utils/styles.json'
import { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { getAssetsManagementPortalUI, getAssetsPortal } from '../../../reducers'
import { fetchDownloadQR } from '../../../actions'
import { toast } from 'react-toastify'
import Loader from '../../Common/Loader'

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    width: '50%',
    overflowY: 'hidden',
  },
  '& .MuiDialogContent-root': {
    padding: '20px 30px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },

  '& .MuiButton-root:hover': {},
  '&.MuiButtonBase-root-MuiButton-root:hover': {},
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
  padding: '10px',
}))

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

interface AssetOption {
  laptop_no: string
}

const DownloadQrDialog = (props: {
  downloadQrDialogOpen: boolean
  downloadQrClose: React.Dispatch<React.SetStateAction<boolean>>
  downloadAssetsQrData: any
  downloadQR: any
  downloadQrData: any
  isDownloadQrSuccess: any
  isDownloadQrLoader: any
  downloadQrReset: any
}) => {
  const {
    downloadQrDialogOpen,
    downloadQrClose,
    downloadAssetsQrData,
    downloadQR,
    downloadQrData,
    isDownloadQrSuccess,
    isDownloadQrLoader,
    downloadQrReset,
  } = props

  const [assestNo, setAssestNo] = useState('')

  const handleAssetOsDialogClose = () => {
    downloadQrClose(false)
  }

  const handleSave = () => {
    const requestData = {
      assestNo: assestNo,
    }

    if (selectedOptions?.length > 0) {
      downloadQR(requestData)
    }
  }

  useEffect(() => {
    if (downloadQrData?.data) {
      const dataObject = downloadQrData.data

      // Convert the object to a Uint8Array
      const binaryData = new Uint8Array(Object.keys(dataObject).map((key) => dataObject[key]))

      // Create a Blob from the binary data
      const blob = new Blob([binaryData], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)

      // Create a link and trigger the download
      const a = document.createElement('a')
      a.href = url
      a.download = 'Asset_QR_Code.pdf'
      a.click()

      // Revoke the URL to free up resources
      URL.revokeObjectURL(url)
    }
  }, [downloadQrData])

  useEffect(() => {
    if (isDownloadQrSuccess) {
      toast.success('QR Downloaded successfully')
      downloadQrReset()
      handleAssetOsDialogClose()
    }
  }, [isDownloadQrSuccess])

  const [isAllSelected, setIsAllSelected] = useState(false)
  const [selectedOptions, setSelectedOptions] = useState<AssetOption[]>([])

  useEffect(() => {
    if (!isAllSelected) {
      setSelectedOptions([])
    }
  }, [isAllSelected])

  const handleChange = (event: any, selected: AssetOption[]) => {
    const isAll = selected.some((option) => option.laptop_no === 'All')

    if (isAll) {
      setIsAllSelected(true)
      setSelectedOptions(downloadAssetsQrData)
      setAssestNo('')
    } else {
      setIsAllSelected(false)
      setSelectedOptions(selected)
      const selectedAssetNos = selected.map((option) => option.laptop_no).join(',')
      setAssestNo(selectedAssetNos)
    }
  }

  return (
    <>
      <BootstrapDialog
        onClose={handleAssetOsDialogClose}
        aria-labelledby='customized-dialog-title'
        open={downloadQrDialogOpen}
      >
        <Loader state={isDownloadQrLoader} />
        
        <Heading>
          Generate Asset's QR Code 
          <IconButton
            aria-label='close'
            onClick={handleAssetOsDialogClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
            <Grid item xs={12} sm={12}>
              <Autocomplete
                multiple
                id='checkboxes-tags-demo'
                options={Array.isArray(downloadAssetsQrData) ? downloadAssetsQrData : []}
                disableCloseOnSelect
                getOptionLabel={(option: AssetOption) => option?.laptop_no}
                renderOption={(props, option, { selected }) => {
                  const { key, ...optionProps } = props
                  const isChecked = isAllSelected
                    ? true
                    : selectedOptions.some(
                        (selectedOption) => selectedOption?.laptop_no === option?.laptop_no,
                      )

                  return (
                    <li key={key} {...optionProps}>
                      <Checkbox style={{ marginRight: 8 }} checked={isChecked} />
                      {option?.laptop_no}
                    </li>
                  )
                }}
                value={selectedOptions}
                onChange={handleChange}
                renderInput={(params) => (
                  <TextField
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '25px',
                      },
                      '& .MuiFormLabel-asterisk': {
                        color: 'red',
                      },
                    }}
                    {...params}
                    label='Select Assets'
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button
            sx={{
              color: '#fff !important',
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            onClick={handleSave}
            autoFocus
          >
            GENERATE
          </Button>

          <CancelButton autoFocus onClick={handleAssetOsDialogClose}>
            CANCEL
          </CancelButton>
        </DialogActions>
      </BootstrapDialog>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    downloadAssetsQrData: getAssetsPortal.getAssetsPortal(state).getDownloadAssetsQrData,
    downloadQrData: getAssetsPortal.getAssetsPortal(state).downloadAssetsQr,
    isDownloadQrSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isDownloadQrSuccess,
    isDownloadQrLoader: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isDownloadQrLoader,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    downloadQR: (data: {}) => dispatch(fetchDownloadQR.request(data)),
    downloadQrReset: () => dispatch(fetchDownloadQR.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(DownloadQrDialog)
