import * as React from 'react'
import { styled } from '@mui/material/styles'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import styles from '../../../utils/styles.json'
import styless from '../../../utils/styles.json'
import {
  Autocomplete,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  FormGroup,
  Grid,
  MenuItem,
  Theme,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { employeeAssets, getEmpData, updateUnallocateAsset } from '../../../actions'
import { Dispatch } from 'redux'
import { RootState } from '../../../configureStore'
import { getAssetsManagementPortalUI, getAssetsPortal } from '../../../reducers'
import CloseIcon from '@mui/icons-material/Close'
import IconButton from '@mui/material/IconButton'
import { toast } from 'react-toastify'
import style from '../../../utils/styles.json'
import Loader from '../../Common/Loader'

interface Employee {
  //data: [];
  id: string
  employee_id: string
  first_name: string
  middle_name: string
  last_name: string
}

function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  } as T
}

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: styles.FONT_MEDIUM,
  maxHeight: '140px',
}))

const SelectField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '12px 14px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    width: '45%',
    maxWidth: '1000px',
    maxHeight: '90vh',
    overflowY: 'auto',
  },
  '& .MuiDialogContent-root': {
    padding: '20px 30px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },

  '& .MuiButton-root:hover': {},
  '&.MuiButtonBase-root-MuiButton-root:hover': {},
}))

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_MEDIUM,
}))

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
  padding: '10px',
}))

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '13px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '15px',
    lineHeight: '1.8em',
  },
}))

const stylesss = {
  autoCompleteStyle: {
    '.MuiInputBase-root': {
      padding: '19px 11px',
      borderRadius: '20px',
      fontSize: '13px',
      fontFamily: styless.FONT_MEDIUM,
      height: '42px',
    },
    '& .MuiFormControl-root': {
      margin: '0',
      marginTop: '5px',
    },
    '& .MuiFormLabel-root ': {
      backgroundColor: 'white',
    },
    '.MuiFormLabel-asterisk': {
      color: 'red',
    },
  },
}

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
}))

const UnallocateAssetDialog = (props: {
  unallocatedialogopen: boolean
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  AllEmpList: Employee[]
  fetchAllEmpAPI: (data?: {}) => { type: string }
  fetchAllEmpAPIReset: () => {}
  EmpAllAssetsData: any
  fetchEmpAllAssets: (data?: {}) => { type: string }
  fetchEmpAllAssetsreset: () => {}
  updateUnallocateAssetAPI: (data?: {}) => { type: string }
  updateUnallocateAssetAPIreset: any
  isUnallocateAssetLoader: boolean
  isUnallocateAssetSuccess: boolean
  unallocateAssetMessage: any
  isEmpAssetsDataSuccess: boolean
}) => {
  const {
    unallocatedialogopen,
    onClose,
    AllEmpList,
    fetchAllEmpAPI,
    fetchAllEmpAPIReset,
    EmpAllAssetsData,
    fetchEmpAllAssets,
    fetchEmpAllAssetsreset,
    updateUnallocateAssetAPI,
    updateUnallocateAssetAPIreset,
    isUnallocateAssetLoader,
    isUnallocateAssetSuccess,
    unallocateAssetMessage,
    isEmpAssetsDataSuccess,
  } = props

  const handleUnallocateDialogClose = () => {
    onClose(false)
    fetchEmpAllAssetsreset()
  }

  const [employeeID, setEmployeeID] = useState('')
  const [reasonForUnallocation, setReasonForUnallocation] = useState<string | undefined>('Damaged')
  const [comment, setComment] = useState('')
  const [employeeAnchor, setEmployeeAnchor] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [searchEmp, setSearchEmp] = useState('')
  const [assetsToUnallocate, setAssetsToUnallocate] = useState<string[]>([])

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchEmp(e.target.value)
    },
    1200,
  )

  useEffect(() => {
    if (searchEmp) fetchAllEmpAPI({ searchTerm: searchEmp })
    else {
      fetchAllEmpAPIReset()
      if (searchEmp === '' && searchEmp === undefined) fetchEmpAllAssetsreset()
    }
  }, [searchEmp])

  const handleEmpAllAssetsData = (employeeID: string) => {
    fetchEmpAllAssets({ employee_id: employeeID })
    if (Array.isArray(EmpAllAssetsData.data) && EmpAllAssetsData.data.length === 0) {
      toast.success('No Asset Assigned')
    }
  }

  const handleSearchEmp = (value: string) => {
    setSearchTerm(value)
  }

  const handleCheck = (checked: boolean, laptop_no: string) => {
    if (!checked) {
      setAssetsToUnallocate((prevList) => [...prevList, laptop_no])
    } else {
      setAssetsToUnallocate((prevList) => prevList.filter((item) => item !== laptop_no))
    }
  }

  const handleSave = () => {
    const requestData = {
      employee_id: employeeID,
      reason_for_change: reasonForUnallocation,
      assets_id: assetsToUnallocate,
      comments: comment,
    }

    updateUnallocateAssetAPI(requestData)
  }

  useEffect(() => {
    if (isUnallocateAssetSuccess) {
      toast.success(unallocateAssetMessage.message)
      updateUnallocateAssetAPIreset()
      handleUnallocateDialogClose()
    }
  }, [isUnallocateAssetLoader])

  return (
    <>
      <BootstrapDialog
        onClose={handleUnallocateDialogClose}
        aria-labelledby='customized-dialog-title'
        open={unallocatedialogopen}
      >
        <Loader state={isUnallocateAssetLoader} />

        <Heading>
          Unassign Asset
          <IconButton
            aria-label='close'
            onClick={handleUnallocateDialogClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
            <Grid item xs={6} sm={6} sx={{ fontFamily: styles.FONT_MEDIUM }}>
              <Autocomplete
                autoFocus={false}
                size='small'
                open={employeeAnchor}
                disablePortal
                id='select-employees'
                options={(AllEmpList ?? []).map((employee) => ({
                  label: `${employee.employee_id ?? ''}${employee.employee_id ? '-' : ''}${
                    employee.first_name
                  } ${employee.middle_name} ${employee.last_name}`.trim(),
                  id: employee.id,
                }))}
                inputValue={searchTerm}
                sx={stylesss.autoCompleteStyle}
                renderInput={(params) => (
                  <TextField {...params} label='Search Employee' variant='outlined' required />
                )}
                onInputChange={(e, value) => {
                  functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)
                  handleSearchEmp(value)
                  if (value !== '') {
                    setEmployeeAnchor(true)
                  } else {
                    setEmployeeAnchor(false)
                  }
                }}
                onChange={(event: any, newValue: { label: string; id: string } | null) => {
                  const empId = newValue?.id
                  setEmployeeID(newValue?.id ?? '')
                  setEmployeeAnchor(false)
                  if (empId) handleEmpAllAssetsData(empId ?? '')
                }}
                ListboxProps={{
                  style: {
                    maxHeight: '140px',
                  },
                }}
              />
            </Grid>

            <Grid item xs={6} sm={6} sx={{ fontFamily: styles.FONT_MEDIUM }}>
              <SelectField
                required
                id='outlined-required'
                label='Select RAM'
                size='small'
                select
                value={reasonForUnallocation}
                onChange={(e) => setReasonForUnallocation(e.target.value)}
                variant='outlined'
              >
                <StyledMenuItem value='Damaged'>Damaged</StyledMenuItem>
                <StyledMenuItem value='Technology Changed'>Technology Changed</StyledMenuItem>
                <StyledMenuItem value='Wrong Allocation'>Wrong Allocation</StyledMenuItem>
                <StyledMenuItem value='Relieving'>Relieving</StyledMenuItem>
              </SelectField>
            </Grid>

            <Grid item xs={12} sm={12} sx={{ fontFamily: styles.FONT_MEDIUM }}>
              {EmpAllAssetsData?.length > 0 ? (
                <>
                  <span style={{ color: style.PRIMARY_COLOR }}><i>* Uncheck the box to remove asset allocation.</i></span>
                  <FormGroup row>
                    {EmpAllAssetsData?.map((item: any, index: number) => (
                      <FormControlLabel
                        key={index}
                        control={
                          <Checkbox
                            defaultChecked
                            onChange={(_, checked) => handleCheck(checked, item.id)}
                          />
                        }
                        label={item.laptop_no}
                      />
                    ))}
                  </FormGroup>
                </>
              ) : (
                (isEmpAssetsDataSuccess && (
                  <span style={{ color: style.PRIMARY_COLOR }}>No Assets Assigned</span>
                )) ||
                ''
              )}
            </Grid>

            <Grid item xs={12} sm={12} sx={{ fontFamily: styles.FONT_MEDIUM }}>
              <InputField
                required
                id='outlined-required'
                label='Add Comments Here'
                size='small'
                value={comment}
                onChange={(e) => setComment(e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            sx={{
              color: '#fff !important',
              opacity:
                employeeID === '' ||
                reasonForUnallocation === '' ||
                comment === '' ||
                assetsToUnallocate.length === 0
                  ? 0.5
                  : 1,
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            disabled={
              employeeID === '' ||
              reasonForUnallocation === '' ||
              comment === '' ||
              assetsToUnallocate.length === 0
            }
            onClick={handleSave}
            autoFocus
          >
            SAVE
          </Button>

          <CancelButton autoFocus onClick={handleUnallocateDialogClose}>
            CANCEL
          </CancelButton>
        </DialogActions>
      </BootstrapDialog>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    AllEmpList: getAssetsPortal.getAssetsPortal(state).getEmpData,
    EmpAllAssetsData: getAssetsPortal.getAssetsPortal(state).getEmpAssetsData,
    isEmpAssetsDataSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isEmpAssetsDataSuccess,
    isUnallocateAssetLoader:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isUnallocateAssetLoader,
    isUnallocateAssetSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isUnallocateAssetSuccess,
    unallocateAssetMessage: getAssetsPortal.getAssetsPortal(state).getUnallocateAsset,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAllEmpAPI: (data?: {}) => dispatch(getEmpData.request({ data })),
    fetchAllEmpAPIReset: () => dispatch(getEmpData.reset()),
    fetchEmpAllAssets: (data?: {}) => dispatch(employeeAssets.request({ data })),
    fetchEmpAllAssetsreset: () => dispatch(employeeAssets.reset()),
    updateUnallocateAssetAPI: (data?: {}) => dispatch(updateUnallocateAsset.request({ data })),
    updateUnallocateAssetAPIreset: () => dispatch(updateUnallocateAsset.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(UnallocateAssetDialog)
