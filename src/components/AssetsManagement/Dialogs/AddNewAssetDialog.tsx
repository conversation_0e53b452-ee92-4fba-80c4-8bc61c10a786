import * as React from 'react'
import { styled } from '@mui/material/styles'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import styles from '../../../utils/styles.json'
import {
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import style from '../../../utils/styles.json'
import CloseIcon from '@mui/icons-material/Close'
import IconButton from '@mui/material/IconButton'
import { RootState } from '../../../configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { updateAddNewAsset } from '../../../actions'
import { Dayjs } from 'dayjs'
import { getAssetsManagementPortalUI, getAssetsPortal } from '../../../reducers'
import { toast } from 'react-toastify'
import Loader from '../../Common/Loader'

type DataType = {
  assetCode: string
  assetCodeNo: string
  assetLocation: number | null
  assetOS: string
  osSerialKey: string
  productKey: string
  assetMake: string
  assetModel: string
  serviseTagNo: string
  adapterNo: string
  macAddresWifi: string
  macAddresEthernet: string
  hardDiskSize: string
  ramSize: string
  comments: string
  assetPurchaseDate: Dayjs | null
  assetType: string
  warrentyPeriod: number | string | null
  warrentyExtended: number | null
  assetStatus: string
  processor: string
  warrentyEndDate: Dayjs | null
  assetTechnologies: string
  purchase_date: Dayjs | null
  license_expiry_date: Dayjs | null
  software_name: string
  license_key: string
  license_type: string
  version: string
  cost: number | null | string
  type: number
}

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    width: '70%',
    maxWidth: '1000px',
    maxHeight: '90vh',
    overflowY: 'auto',
  },
  '& .MuiDialogContent-root': {
    padding: '20px 30px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },

  '& .MuiButton-root:hover': {},
  '&.MuiButtonBase-root-MuiButton-root:hover': {},
}))

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '13px',
  fontFamily: styles.FONT_MEDIUM,
  maxHeight: '140px',
}))

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontFamily: styles.FONT_MEDIUM,
}))

const SelectField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '8px 12px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root': {
    fontSize: '15px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '26px',
  textAlign: 'center',
  fontFamily: styles.FONT_BOLD,
  letterSpacing: '0px',
  padding: '10px',
}))

export const DateField = styled(DatePicker)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '10px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root': {
    fontSize: '15px',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
}))

const InputField = styled(TextField)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '10px 12px',
    fontSize: '13px',
    fontFamily: styles.FONT_MEDIUM,
    height: '20px',
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    fontSize: '14px',
    lineHeight: '1.8em',
  },
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

const StyledMenuProps = {
  PaperProps: {
    style: {
      maxHeight: 140,
      borderRadius: 10,
    },
  },
}

const AddNewAssetDialog = (props: {
  addNewAssetdialogopen: boolean
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  addNewAsset: (data: {}) => { type: string }
  isAddNewAssetLoader: boolean
  isAddNewAssetSuccess: boolean
  addNewAssetreset: any
  assetAbiberation: any
  assetLocation: any
  assetMake: any
  assetStatus: any
  assetOS: any
  assetTechnologies: any
  addNewAssetMessage: any
}) => {
  const {
    addNewAssetdialogopen,
    onClose,
    addNewAsset,
    isAddNewAssetLoader,
    isAddNewAssetSuccess,
    addNewAssetreset,
    assetAbiberation,
    assetLocation,
    assetMake,
    assetStatus,
    assetOS,
    assetTechnologies,
    addNewAssetMessage,
  } = props

  const [btnCheck, setBtnCheck] = useState(true)

  const [data, setData] = useState<DataType>({
    type: 0,
    assetCode: '',
    assetCodeNo: '',
    assetLocation: null,
    assetOS: '',
    osSerialKey: '',
    productKey: '',
    assetMake: '',
    assetModel: '',
    serviseTagNo: '',
    adapterNo: '',
    macAddresWifi: '',
    macAddresEthernet: '',
    hardDiskSize: '',
    ramSize: '',
    comments: '',
    assetPurchaseDate: null,
    assetType: '',
    warrentyPeriod: 'NA',
    warrentyExtended: 0,
    assetStatus: '',
    processor: '',
    warrentyEndDate: null,
    assetTechnologies: '',
    purchase_date: null,
    license_expiry_date: null,
    software_name: '',
    license_key: '',
    license_type: '',
    version: '',
    cost: null,
  })

  const handleAddNewAssetDialogClose = () => {
    onClose(false)
    setData({
      type: 0,
      assetCode: '',
      assetCodeNo: '',
      assetLocation: null,
      assetOS: '',
      osSerialKey: '',
      productKey: '',
      assetMake: '',
      assetModel: '',
      serviseTagNo: '',
      adapterNo: '',
      macAddresWifi: '',
      macAddresEthernet: '',
      hardDiskSize: '',
      ramSize: '',
      comments: '',
      assetPurchaseDate: null,
      assetType: '',
      warrentyPeriod: 'NA',
      warrentyExtended: 0,
      assetStatus: '',
      processor: '',
      warrentyEndDate: null,
      assetTechnologies: '',
      purchase_date: null,
      license_expiry_date: null,
      software_name: '',
      license_key: '',
      license_type: '',
      version: '',
      cost: null,
    })
  }

  const handleSave = () => {
    const requestData =
      data.type === 0
        ? {
            asset_id: data.assetCode,
            laptop_number: data.assetCodeNo,
            id_company_location: data.assetLocation,
            laptop_os: data.assetOS,
            os_serial_key: data.osSerialKey,
            product_key: data.productKey,
            laptop_make: data.assetMake,
            laptop_model_no: data.assetModel,
            service_tag_no: data.serviseTagNo,
            adapter_no: data.adapterNo,
            mac_address_text: data.macAddresWifi,
            mac_address_ethernet: data.macAddresEthernet,
            hard_disk_size: data.hardDiskSize,
            ram_size: data.ramSize,
            comments: data.comments,
            laptop_purchase_date: data.assetPurchaseDate,
            laptop_type: data.assetType,
            warranty_period: data.warrentyPeriod === 'NA' ? 0 : data.warrentyPeriod,
            is_warranty_extended: data.warrentyExtended,
            asset_status: data.assetStatus,
            processor: data.processor,
            warrenty_end_date: data.warrentyEndDate,
            asset_technologies: data.assetTechnologies,
          }
        : {
            type: data.type ?? '1',
            asset_id: data.assetCode ?? '',
            laptop_make: data.assetMake ?? '',
            laptop_number: data.assetCodeNo ?? '',
            id_company_location: data.assetLocation ?? '',
            comments: data.comments ?? '',
            purchase_date: data.purchase_date ?? '',
            license_expiry_date: data.license_expiry_date ?? '',
            software_name: data.software_name ?? '',
            license_key: data.license_key ?? '',
            license_type: data.license_type ?? '',
            version: data.version ?? '',
            cost: data.cost ?? '',
          }
    addNewAsset(requestData)
  }

  useEffect(() => {
    if (isAddNewAssetSuccess) {
      toast.success(addNewAssetMessage.message)
      addNewAssetreset()
      handleAddNewAssetDialogClose()
      setBtnCheck(true)
    }
  }, [isAddNewAssetLoader])

  useEffect(() => {
    let check = false
    const excludekeys =
      data.type === 0
        ? [
            'assetLocation',
            'osSerialKey',
            'productKey',
            'macAddresEthernet',
            'comments',
            'assetPurchaseDate',
            'assetType',
            'warrentyEndDate',
            'warrentyExtended',
            'purchase_date',
            'license_expiry_date',
            'software_name',
            'license_key',
            'license_type',
            'version',
            'cost',
            'type',
          ]
        : [
            'assetLocation',
            'assetOS',
            'osSerialKey',
            'productKey',
            'assetMake',
            'assetModel',
            'serviseTagNo',
            'adapterNo',
            'macAddresWifi',
            'macAddresEthernet',
            'hardDiskSize',
            'ramSize',
            'comments',
            'assetPurchaseDate',
            'assetType',
            'warrentyPeriod',
            'warrentyExtended',
            'assetStatus',
            'processor',
            'warrentyEndDate',
            'assetTechnologies',
            'license_expiry_date',
          ]
    let key: keyof DataType
    for (key in data) {
      if (!excludekeys.includes(key) && !data[key]) {
        check = true
        break
      }
    }
    setBtnCheck(check)
  }, [data])

  const handlerAssetTechnology = (event: any) => {
    setData((prev) => ({
      ...prev,
      assetTechnologies: event.target.value ?? '',
    }))
  }

  const handlerWarrantyPeriod = (event: any) => {
    const newValue = event.target.value === 'NA' ? 'NA' : parseInt(event.target.value)
    setData((prev) => ({
      ...prev,
      warrentyPeriod: newValue,
    }))
  }

  const handleAmount = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
    if (!value?.trim()) {
      setData((prev) => ({
        ...prev,
        cost: '',
      }))
      return
    }
    const [beforeDecimal, afterDecimal] = value?.trim().split('.')
    const sanitizedValueBeforeDecimal = Number(beforeDecimal)
    const sanitizedValueAfterDecimal = Number(afterDecimal)
    if (!Number.isNaN(sanitizedValueBeforeDecimal)) {
      if (!Number.isNaN(sanitizedValueAfterDecimal))
        setData((prev) => ({
          ...prev,
          cost: `${beforeDecimal}.${afterDecimal?.toString().slice(0, 2)}`,
        }))
      else
        setData((prev) => ({
          ...prev,
          cost: `${beforeDecimal}${value?.includes('.') ? '.' : ''}`,
        }))
    }
  }

  return (
    <>
      <BootstrapDialog
        onClose={handleAddNewAssetDialogClose}
        aria-labelledby='customized-dialog-title'
        open={addNewAssetdialogopen}
      >
        <Loader state={isAddNewAssetLoader} />

        <Heading>
          Add New Asset
          <IconButton
            aria-label='close'
            onClick={handleAddNewAssetDialogClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers sx={{ '&': { padding: '0' }, paddingTop: '5px !important' }}>
          <Box
            display='flex'
            flexDirection='column'
            alignItems='center'
            justifyContent='flex-start'
          >
            <Box>
              <FormControl>
                <RadioGroup
                  row
                  value={data.type}
                  onChange={(_, val) => setData((prev) => ({ ...prev, type: Number(val) }))}
                >
                  <FormControlLabel value={0} control={<Radio />} label='Hardware' />
                  <FormControlLabel value={1} control={<Radio />} label='Software' />
                </RadioGroup>
              </FormControl>
            </Box>
            <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
              <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5%',
                  }}
                >
                  <Box sx={{ flex: '0 0 40%' }}>
                    <SelectField
                      required
                      label='Type'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.assetCode || ''}
                      onChange={(event) =>
                        setData((prev) => ({
                          ...prev,
                          assetCode: event.target.value,
                        }))
                      }
                      variant='outlined'
                    >
                      {assetAbiberation?.result?.map((data: { asset_id: string }) => (
                        <StyledMenuItem key={data.asset_id} value={data.asset_id}>
                          {data.asset_id}
                        </StyledMenuItem>
                      ))}
                    </SelectField>
                  </Box>

                  <Box sx={{ flex: '0 0 55%' }}>
                    <InputField
                      required
                      id='outlined-required'
                      size='small'
                      label='Asset Number'
                      type='number'
                      onChange={(e) =>
                        setData((prev) => ({ ...prev, assetCodeNo: e.target.value }))
                      }
                      onKeyDown={(e) => {
                        if (e.key === 'e' || e.key === 'E' || e.key === '+' || e.key === '-') {
                          e.preventDefault()
                        }
                      }}
                    />
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                <SelectField
                  label='Asset Location'
                  size='small'
                  select
                  SelectProps={{
                    MenuProps: StyledMenuProps,
                  }}
                  required={false}
                  value={data.assetLocation || ''}
                  onChange={(event) =>
                    setData((prev) => ({
                      ...prev,
                      assetLocation: Number(event.target.value),
                    }))
                  }
                  variant='outlined'
                >
                  {assetLocation?.result?.map((data: { location: string; id: number }) => (
                    <StyledMenuItem key={data.id} value={data.id}>
                      {data.location}
                    </StyledMenuItem>
                  ))}
                </SelectField>
              </Grid>

              {data.type === 0 ? (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <SelectField
                    required
                    label='Asset OS'
                    size='small'
                    select
                    SelectProps={{
                      MenuProps: StyledMenuProps,
                    }}
                    value={data.assetOS || ''}
                    onChange={(event) =>
                      setData((prev) => ({
                        ...prev,
                        assetOS: event.target.value,
                      }))
                    }
                    variant='outlined'
                  >
                    {assetOS?.result?.map((data: { name: string; id: string }) => (
                      <StyledMenuItem key={data.name} value={data.name}>
                        {data.name}
                      </StyledMenuItem>
                    ))}
                  </SelectField>
                </Grid>
              ) : (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required
                    label='Asset Name'
                    id='outlined-required'
                    size='small'
                    onChange={(e) =>
                      setData((prev) => ({ ...prev, software_name: e.target.value }))
                    }
                  />
                </Grid>
              )}

              {data.type === 0 ? (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required={false}
                    label='OS Serial Key'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, osSerialKey: e.target.value }))}
                  />
                </Grid>
              ) : (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required
                    label='Asset License Key'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, license_key: e.target.value }))}
                  />
                </Grid>
              )}

              {data.type === 0 ? (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required={false}
                    label='Product Key'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, productKey: e.target.value }))}
                  />
                </Grid>
              ) : (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required
                    label='Asset License Type'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, license_type: e.target.value }))}
                  />
                </Grid>
              )}

              <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                <SelectField
                  required
                  id='outlined-required'
                  label='Asset Make'
                  size='small'
                  select
                  SelectProps={{
                    MenuProps: StyledMenuProps,
                  }}
                  value={data.assetMake || ''}
                  onChange={(event) =>
                    setData((prev) => ({
                      ...prev,
                      assetMake: event.target.value,
                    }))
                  }
                  variant='outlined'
                >
                  {assetMake?.result?.map((data: { name: string }) => (
                    <StyledMenuItem key={data.name} value={data.name}>
                      {data.name}
                    </StyledMenuItem>
                  ))}
                </SelectField>
              </Grid>

              {data.type === 0 ? (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required
                    label='Asset Model'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, assetModel: e.target.value }))}
                  />
                </Grid>
              ) : (
                <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                  <InputField
                    required
                    label='Asset Version'
                    id='outlined-required'
                    size='small'
                    onChange={(e) => setData((prev) => ({ ...prev, version: e.target.value }))}
                  />
                </Grid>
              )}

              {data.type === 0 && (
                <>
                  {' '}
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='Service Tag No'
                      id='outlined-required'
                      size='small'
                      onChange={(e) =>
                        setData((prev) => ({ ...prev, serviseTagNo: e.target.value }))
                      }
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='Adapter No'
                      id='outlined-required'
                      size='small'
                      onChange={(e) => setData((prev) => ({ ...prev, adapterNo: e.target.value }))}
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='MAC Address Wifi'
                      id='outlined-required'
                      size='small'
                      onChange={(e) =>
                        setData((prev) => ({ ...prev, macAddresWifi: e.target.value }))
                      }
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required={false}
                      label='MAC Addres Ethernet'
                      id='outlined-required'
                      size='small'
                      onChange={(e) =>
                        setData((prev) => ({ ...prev, macAddresEthernet: e.target.value }))
                      }
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='Hard Disk Size'
                      id='outlined-required'
                      size='small'
                      onChange={(e) =>
                        setData((prev) => ({ ...prev, hardDiskSize: e.target.value }))
                      }
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <SelectField
                      required
                      id='outlined-required'
                      label='RAM Size'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.ramSize || ''}
                      onChange={(event) =>
                        setData((prev) => ({
                          ...prev,
                          ramSize: event.target.value,
                        }))
                      }
                      variant='outlined'
                    >
                      <StyledMenuItem value='NA'>NA</StyledMenuItem>
                      <StyledMenuItem value='512MB'>512MB</StyledMenuItem>
                      <StyledMenuItem value='1GB'>1GB</StyledMenuItem>
                      <StyledMenuItem value='2GB'>2GB</StyledMenuItem>
                      <StyledMenuItem value='3GB'>3GB</StyledMenuItem>
                      <StyledMenuItem value='4GB'>4GB</StyledMenuItem>
                      <StyledMenuItem value='6GB'>6GB</StyledMenuItem>
                      <StyledMenuItem value='8GB'>8GB</StyledMenuItem>
                      <StyledMenuItem value='10GB'>10GB</StyledMenuItem>
                      <StyledMenuItem value='12GB'>12GB</StyledMenuItem>
                      <StyledMenuItem value='16GB'>16GB</StyledMenuItem>
                    </SelectField>
                  </Grid>
                </>
              )}

              <Grid item xs={8} sm={8} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                <InputField
                  required={false}
                  id='outlined-required'
                  label='Add Comments Here'
                  size='small'
                  onChange={(e) => setData((prev) => ({ ...prev, comments: e.target.value }))}
                />
              </Grid>

              {data.type === 0 && (
                <>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DateField
                        label='Asset Purchase Date'
                        value={data.assetPurchaseDate}
                        format='MM-DD-YYYY'
                        sx={{
                          '& .MuiInputLabel-root': {
                            marginTop: '-6px',
                          },
                        }}
                        slotProps={{
                          textField: {
                            required: false,
                            error: false,
                          },
                        }}
                        onChange={(value) =>
                          setData((prev) => ({
                            ...prev,
                            assetPurchaseDate: value as Dayjs,
                          }))
                        }
                      />
                    </LocalizationProvider>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <SelectField
                      required={false}
                      id='outlined-required'
                      label='Asset Type'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.assetType || ''}
                      onChange={(event) =>
                        setData((prev) => ({
                          ...prev,
                          assetType: event.target.value,
                        }))
                      }
                      variant='outlined'
                    >
                      <StyledMenuItem value='Developement'>Developement</StyledMenuItem>
                      <StyledMenuItem value='Non Developemant'>Non Developemant</StyledMenuItem>
                    </SelectField>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <SelectField
                      required
                      id='outlined-required'
                      label='Warranty Period'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.warrentyPeriod}
                      onChange={handlerWarrantyPeriod}
                      variant='outlined'
                    >
                      <StyledMenuItem value='NA'>NA</StyledMenuItem>
                      <StyledMenuItem value='3'>3 Months</StyledMenuItem>
                      <StyledMenuItem value='6'>6 Months</StyledMenuItem>
                      <StyledMenuItem value='9'>9 Months</StyledMenuItem>
                      <StyledMenuItem value='12'>1 Year</StyledMenuItem>
                      <StyledMenuItem value='18'>1.5 Years</StyledMenuItem>
                      <StyledMenuItem value='24'>2 Years</StyledMenuItem>
                      <StyledMenuItem value='30'>2.5 Years</StyledMenuItem>
                      <StyledMenuItem value='36'>3 Years</StyledMenuItem>
                      <StyledMenuItem value='48'>4 Years</StyledMenuItem>
                      <StyledMenuItem value='60'>5 Years</StyledMenuItem>
                    </SelectField>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <SelectField
                      required
                      id='outlined-required'
                      label='Asset Status'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.assetStatus} // Bind to 'assetStatus' state
                      onChange={(event) =>
                        setData((prev) => ({
                          ...prev,
                          assetStatus: event.target.value, // Update with the selected value
                        }))
                      }
                      variant='outlined'
                    >
                      {assetStatus?.result?.map((data: { name: string }) => (
                        <StyledMenuItem key={data.name} value={data.name}>
                          {data.name}
                        </StyledMenuItem>
                      ))}
                    </SelectField>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <StyledTypography>
                      Warranty Extended
                      <Typography
                        component='span'
                        sx={{ color: 'red', marginLeft: '2px' }}
                        aria-label='required'
                      >
                        *
                      </Typography>
                    </StyledTypography>
                    <RadioGroup
                      row
                      value={data.warrentyExtended}
                      onChange={(event) =>
                        setData((prev) => ({
                          ...prev,
                          warrentyExtended: parseInt(event.target.value),
                        }))
                      }
                    >
                      <FormControlLabel value='1' control={<Radio />} label='Yes' />
                      <FormControlLabel value='0' control={<Radio />} label='No' />
                    </RadioGroup>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='Processor'
                      id='outlined-required'
                      size='small'
                      onChange={(e) => setData((prev) => ({ ...prev, processor: e.target.value }))}
                    />
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DateField
                        label='Warranty End Date'
                        value={data.warrentyEndDate}
                        format='MM-DD-YYYY'
                        slotProps={{
                          textField: {
                            required: false,
                            error: false,
                          },
                        }}
                        sx={{
                          '& .MuiInputLabel-root': {
                            marginTop: '-6px',
                          },
                        }}
                        onChange={(value) =>
                          setData((prev) => ({
                            ...prev,
                            warrentyEndDate: value as Dayjs,
                          }))
                        }
                      />
                    </LocalizationProvider>
                  </Grid>

                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <SelectField
                      required
                      id='outlined-required'
                      label='Asset Technologies'
                      size='small'
                      select
                      SelectProps={{
                        MenuProps: StyledMenuProps,
                      }}
                      value={data.assetTechnologies}
                      onChange={handlerAssetTechnology}
                      variant='outlined'
                    >
                      {assetTechnologies?.result?.map((data: { name: string; id: string }) => (
                        <StyledMenuItem key={data.name} value={data.id}>
                          {data.name}
                        </StyledMenuItem>
                      ))}
                    </SelectField>
                  </Grid>
                </>
              )}
              {data.type === 1 && (
                <>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DateField
                        label='Asset Purchase Date'
                        value={data.purchase_date}
                        format='MM-DD-YYYY'
                        onChange={(value) => {
                          const purchaseDate = value as Dayjs
                          if (data.license_expiry_date?.isBefore(purchaseDate)) {
                            setData((prev) => ({
                              ...prev,
                              purchase_date: value as Dayjs,
                              license_expiry_date: value as Dayjs,
                            }))
                          } else {
                            setData((prev) => ({
                              ...prev,
                              purchase_date: value as Dayjs,
                            }))
                          }
                        }}
                        sx={{
                          '& .MuiFormLabel-root': { fontSize: '15px' },
                          '& .MuiInputLabel-root': {
                            marginTop: '-6px',
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DateField
                        label='Asset Expiry Date'
                        value={data.license_expiry_date}
                        format='MM-DD-YYYY'
                        slotProps={{
                          textField: {
                            required: false,
                            error: false,
                          },
                        }}
                        minDate={data.purchase_date ?? ''}
                        onChange={(value) =>
                          setData((prev) => ({
                            ...prev,
                            license_expiry_date: value as Dayjs,
                          }))
                        }
                        sx={{
                          '& .MuiFormLabel-root': { fontSize: '15px' },
                          '& .MuiInputLabel-root': {
                            marginTop: '-6px',
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </Grid>
                  <Grid item xs={4} sm={4} sx={{ fontFamily: styles.FONT_MEDIUM }}>
                    <InputField
                      required
                      label='Asset Cost'
                      id='outlined-required'
                      size='small'
                      onChange={handleAmount}
                      value={data.cost ?? ''}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            sx={{
              cursor: !btnCheck ? 'pointer' : 'not-allowed',
              color: '#fff !important',
              opacity: btnCheck ? 0.5 : 1,
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            disabled={btnCheck}
            onClick={handleSave}
            autoFocus
          >
            SAVE
          </Button>

          <CancelButton autoFocus onClick={handleAddNewAssetDialogClose}>
            CANCEL
          </CancelButton>
        </DialogActions>
      </BootstrapDialog>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    isAddNewAssetLoader:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAddNewAssetLoader,
    isAddNewAssetSuccess:
      getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAddNewAssetSuccess,
    assetAbiberation: getAssetsPortal.getAssetsPortal(state).getAssetAbbeiration,
    assetLocation: getAssetsPortal.getAssetsPortal(state).getAssetLocation,
    assetMake: getAssetsPortal.getAssetsPortal(state).getAssetMake,
    assetStatus: getAssetsPortal.getAssetsPortal(state).getAssetStatus,
    assetOS: getAssetsPortal.getAssetsPortal(state).getAssetOS,
    assetTechnologies: getAssetsPortal.getAssetsPortal(state).getAssetTechnologies,
    addNewAssetMessage: getAssetsPortal.getAssetsPortal(state).getAddNewAsset,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    addNewAsset: (data: {}) => dispatch(updateAddNewAsset.request({ data })),
    addNewAssetreset: () => dispatch(updateAddNewAsset.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddNewAssetDialog)
