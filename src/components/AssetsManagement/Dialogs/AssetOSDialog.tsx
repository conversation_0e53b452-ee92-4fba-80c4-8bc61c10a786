import { Button, Dialog, DialogActions, DialogContent, Grid, IconButton, styled, TextField, Theme, Typography } from '@mui/material';
import * as React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import styles from '../../../utils/styles.json';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Dispatch } from 'redux';
import { RootState } from '../../../configureStore';
import { updateAssetOs } from '../../../actions';
import { StyledTypography } from '../../Common/ComponentCommonStyles';
import { toast } from 'react-toastify';
import { getAssetsManagementPortalUI } from '../../../reducers';


const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    width: '30%',
    overflowY: 'hidden',
  },
  '& .MuiDialogContent-root': {
    padding: '20px 30px',
  },
  '& .MuiDialogActions-root': {
    justifyContent: 'center',
    gap: '15px',
    padding: '20px 0',
  },

  '& .MuiButton-root:hover': {},
  '&.MuiButtonBase-root-MuiButton-root:hover': {},
}))

const CancelButton = styled(Button)(({ theme }) => ({
  background: '#E2E2E2',
  color: '#000000',
  fontSize: '13px',
  height: '42px',
  fontFamily: styles.FONT_BOLD,
  width: '20%',
  borderRadius: '20px',
  '&:hover': {
    background: '#E2E2E2',
    color: '#000000',
  },
}))

  const Heading = styled(Typography)(({ theme }) => ({
    fontSize: '26px',
    textAlign: 'center',
    fontFamily: styles.FONT_BOLD,
    letterSpacing: '0px',
    padding: '10px'
  }))

  const InputField = styled(TextField)(({ theme }) => ({
    marginTop: '5px',
    marginBottom: '5px',
    '& .MuiOutlinedInput-input': {
      padding: '13.5px 14px', // Adjust the padding here
      fontSize: '13px', // Adjust the font size here
      fontFamily: styles.FONT_MEDIUM,
    },
    '& .MuiFormLabel-asterisk': {
      color: 'red',
    },
    '& .MuiInputBase-root.MuiOutlinedInput-root': {
      borderRadius: '20px',
    },
    '& .MuiFormLabel-root.MuiInputLabel-root': {
      fontSize: '15px',
      lineHeight: '1.8em',
    },
  }))


const AssetOSDialog = (props: {
    assetOsDialogOpen: boolean
    assetOsClose: React.Dispatch<React.SetStateAction<boolean>>
    updateAssetOs: any
    addAssetOsSuccess: any
    updateAssetOsReset: any
}) => {
    const {
        assetOsDialogOpen,
        assetOsClose,
        updateAssetOs,
        addAssetOsSuccess,
        updateAssetOsReset,
    }= props

    const [name, setName] = useState("")

    const handleAssetOsDialogClose = () => {
        assetOsClose(false);
    }

    const handleSave = () => {
      const requestData = {
        name: name
      } 

      updateAssetOs(requestData);
      
    }

    useEffect (() => {
      if (addAssetOsSuccess) {
          toast.success("Asset OS Added");
          updateAssetOsReset();
          handleAssetOsDialogClose();
      }
  }, [addAssetOsSuccess]) 


    return (
     <>
      <BootstrapDialog onClose={handleAssetOsDialogClose} aria-labelledby='customized-dialog-title' open={assetOsDialogOpen}>
        <Heading>
          Add Asset OS
          <IconButton
            aria-label="close"
            onClick={handleAssetOsDialogClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
            <Grid item xs={12} sm={12}>
                <InputField
                  required
                  id='outlined-required'
                  label='Asset OS Name'
                  size='small'
                  onChange={(e) => {setName(e.target.value)}}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>

          <Button
            sx={{
              color: '#fff !important',
              opacity: (name === '') ? 0.5 : 1,
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            disabled={(name === '')}
            onClick={handleSave}
            autoFocus
          >
            SAVE
          </Button>

          <CancelButton autoFocus onClick={handleAssetOsDialogClose}>
            CANCEL
          </CancelButton>
        </DialogActions>



      </BootstrapDialog>



     </>
    );
}

const mapStateToProps = (state: RootState) => {
  return {
    addAssetOsSuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).addAssetOsSuccess,

    

    
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    updateAssetOs: (data: {}) => dispatch(updateAssetOs.request({ data })),
    updateAssetOsReset: () => dispatch(updateAssetOs.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssetOSDialog)