import { Button, Dialog, DialogActions, DialogContent, Grid, Icon<PERSON>utton, styled, TextField, Theme, Typography } from '@mui/material';
import * as React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { StyledTypography } from '../../Common/ComponentCommonStyles';
import styles from '../../../utils/styles.json';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Dispatch } from 'redux';
import { RootState } from '../../../configureStore';
import { updateAssetCategory, updateAssetOs } from '../../../actions';
import { toast } from 'react-toastify';
import { getAssetsManagementPortalUI } from '../../../reducers';


const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialog-paper': {
      width: '40%',
      overflowY: 'hidden',
    },
    '& .MuiDialogContent-root': {
      padding: '20px 30px',
    },
    '& .MuiDialogActions-root': {
      justifyContent: 'center',
      gap: '15px',
      padding: '20px 0',
    },
  
    '& .MuiButton-root:hover': {},
    '&.MuiButtonBase-root-MuiButton-root:hover': {},
  }))

  const Heading = styled(Typography)(({ theme }) => ({
    fontSize: '26px',
    textAlign: 'center',
    fontFamily: styles.FONT_BOLD,
    letterSpacing: '0px',
    padding: '10px'
  }))

  const InputField = styled(TextField)(({ theme }) => ({
    marginTop: '5px',
    marginBottom: '5px',
    '& .MuiOutlinedInput-input': {
      padding: '13.5px 14px', // Adjust the padding here
      fontSize: '13px', // Adjust the font size here
      fontFamily: styles.FONT_MEDIUM,
    },
    '& .MuiFormLabel-asterisk': {
      color: 'red',
    },
    '& .MuiInputBase-root.MuiOutlinedInput-root': {
      borderRadius: '20px',
    },
    '& .MuiFormLabel-root.MuiInputLabel-root': {
      fontSize: '15px',
      lineHeight: '1.8em',
    },
  }))

  const CancelButton = styled(Button)(({ theme }) => ({
    background: '#E2E2E2',
    color: '#000000',
    fontSize: '13px',
    height: '42px',
    fontFamily: styles.FONT_BOLD,
    width: '20%',
    borderRadius: '20px',
    '&:hover': {
      background: '#E2E2E2',
      color: '#000000',
    },
  }))


const AssetCategoryDialog = (props: {
    assetCategoreyDialogOpen: boolean
    assetCategoryClose: React.Dispatch<React.SetStateAction<boolean>>
    updateAssetCategory: any
    updateAssetCategorySuccess: any
    updateAssetCategoryReset: any
}) => {
    const {
        assetCategoreyDialogOpen,
        assetCategoryClose,
        updateAssetCategory,
        updateAssetCategorySuccess,
        updateAssetCategoryReset,
    }= props


    const [assetCategoryId, setAssetCategoryId] = useState("")
    const [name, setName] = useState("")
    const [description, setDiscription ] = useState("")

    const handleAssetCategoryDialogClose = () => {
        assetCategoryClose(false);
    }

    const handleSave = () => {
      const requestData = {
        asset_id: assetCategoryId,
        name: name,
        description: description
      } 

      updateAssetCategory(requestData);
    }

    useEffect (() =>{
      if(updateAssetCategorySuccess){
        toast.success("Asset Category Added")
        updateAssetCategoryReset()
        handleAssetCategoryDialogClose()
      }
    }) 


    return (
     <>
      <BootstrapDialog onClose={handleAssetCategoryDialogClose} aria-labelledby='customized-dialog-title' open={assetCategoreyDialogOpen}>
        <Heading>
          Add Asset Category
          <IconButton
            aria-label="close"
            onClick={handleAssetCategoryDialogClose}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <CloseIcon />
          </IconButton>
        </Heading>
        <DialogContent dividers>
          <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
            <Grid item xs={6} sm={6}>
                <InputField
                  required
                  id='outlined-required'
                  label='Asset Category ID'
                  size='small'
                  onChange={(e) => {setAssetCategoryId(e.target.value)}}
              />
            </Grid>

            <Grid item xs={6} sm={6}>
                <InputField
                  required
                  id='outlined-required'
                  label='Asset Category Name'
                  size='small'
                  onChange={(e) => {setName(e.target.value)}}
              />
            </Grid>

            <Grid item xs={12} sm={12}>
                <InputField
                  required
                  id='outlined-required'
                  label='Add Comments Here'
                  size='small'
                  onChange={(e) => {setDiscription(e.target.value)}}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>

          <Button
            sx={{
              color: '#fff !important',
              opacity: (assetCategoryId === '' || name === '' || description === '') ? 0.5 : 1,
              fontSize: '13px',
              height: '42px',
              fontFamily: styles.FONT_BOLD,
              width: '20%',
              borderRadius: '20px',
            }}
            disabled={(assetCategoryId === '' || name === '' || description === '')}
            onClick={handleSave}
            autoFocus
          >
            SAVE
          </Button>

          <CancelButton autoFocus onClick={handleAssetCategoryDialogClose}>
            CANCEL
          </CancelButton>
        </DialogActions>



      </BootstrapDialog>



     </>
    );
}

const mapStateToProps = (state: RootState) => {
  return {
    updateAssetCategorySuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).UpdateAssetCategorySuccess,

    
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    updateAssetCategory: (data: {}) => dispatch(updateAssetCategory.request({ data })),
    updateAssetCategoryReset: () => dispatch(updateAssetCategory.reset()),

  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssetCategoryDialog)