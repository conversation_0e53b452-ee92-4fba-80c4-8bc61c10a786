import { Box, Button, CircularProgress, Dialog, IconButton, InputAdornment, Pagination, Paper, styled, Table, TableBody, TableCell, tableCellClasses, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import style from '../../utils/styles.json'
import { connect } from "react-redux";
import { Dispatch } from "redux";
import { RootState } from "../../configureStore";
import { deleteAssetCategory, getAssetCategoreyData } from "../../actions";
import { getAssetsManagementPortalUI, getAssetsPortal } from "../../reducers";
import { ClearIcon } from "@mui/x-date-pickers";
import { SearchIconStyle } from "../Common/CommonStyles";
import styles from '../../utils/styles.json';
import AssetCategoryDialog from "./Dialogs/AssetCategoryDialog";
import { ReactComponent as DeleteIcon } from '../../assets/images/deleteIcon.svg'
import { toast } from "react-toastify";
import NoProjectRow from "./NoProjectRow";
import Loader from "../Common/Loader";




function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>;
  return function (this: unknown, ...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
}

const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: style.PRIMARY_COLOR,
    color: 'White',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '13px',
    letterSpacing: '0px',
    padding: '11px 0px',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
  },
}))

const ActionButton = styled(Button)(() => ({
  fontSize: "13px",
  height: "42px",
  borderRadius: "20px",
  padding: "5px 20px",
  fontFamily: "Arial, sans-serif",
}));

interface ISearchBoxCustom {
  width?: string
}

const SearchBoxCustom = styled(TextField)<ISearchBoxCustom>(({ width = '250px' }) => ({
  float: 'left',
  '& .MuiOutlinedInput-root': {
    width: `${width}`,
    borderRadius: '20px',
    '& fieldset': {
      borderColor: style.PRIMARY_COLOR,
    },
  },
}))

const StyledTableRow = styled(TableRow)(() => ({
  left: '160px',
  width: '1719px',
  height: '60px',
  boxShadow: '0px 10px 3px #6c6c6c10',
  opacity: '1',
}))

const loaderProps = {
  backgroundColor: 'transparent',
  boxShadow: 'none',
  height: '50px',
  width: '50px',
}



const AssetsCategory: React.FC = (props: any) =>  {
  const{
    fetchAssetCatregoryData,
    assetCategoryData,
    isAssetCategoreyLodder,
    updateAssetCategorySuccess,
    deleteAssetCategory,
    deleteAssetCategorySuccess,
    deleteAssetCategoryReset,
  }= props

  const [page, setPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [assetCategoreyDialogOpen, setAssetCategoreyDialogOpen] = useState(false)

  const clearSearch = () => {
    setSearchTerm("");
    setSearchQuery("");
  };

  const handleChangePage = (event: any, newPage: number) => {
    setPage(newPage)
    window.scrollTo(0, 0);
  }

  const functionDebounce = debounce(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setSearchQuery(e.target.value);
    },
    1200
  );


  useEffect (() => {
    fetchAssetCatregoryData({ page: page, search: searchQuery, });
  }, [page, searchQuery, updateAssetCategorySuccess, deleteAssetCategorySuccess])

  const handleAssetOsDialogOpen = ( ) => {
    setAssetCategoreyDialogOpen(true)
  }

  const handleDelete = (row: any) => {
    const deleteData = {
      id: row.id
    }

    deleteAssetCategory(deleteData)
  }

  useEffect (() => {
    if(deleteAssetCategorySuccess) {
      toast.success("Asset Category Deleted")
      deleteAssetCategoryReset()
    }
  }, [deleteAssetCategorySuccess])
  

  
  return (
    <Paper>
      <Loader state={isAssetCategoreyLodder} />
      <Box sx={{
        border: '1px solid', 
        borderRadius: '10px', 
        borderColor: 'grey.500'
      }}>
        <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '10px 20px 10px 20px'
          }}>

          <Box>
            <SearchBoxCustom
              id="outlined-basic"
              placeholder="Search Asset ID or Asset Name"
              variant="outlined"
              size="small"
              width="100%"
              value={searchTerm}
              onChange={(e) => {
                const value = e.target.value;
                setSearchTerm(value);
                functionDebounce(e as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>);
              }}
              InputProps={{
                startAdornment: <SearchIconStyle />,
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton aria-label="clear-icon" onClick={clearSearch}>
                      {searchTerm && <ClearIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>


          <Box>
            <ActionButton
              variant="outlined"
              onClick={handleAssetOsDialogOpen}
            >
              Add Asset Category
            </ActionButton>
          </Box>
          <AssetCategoryDialog  assetCategoreyDialogOpen={assetCategoreyDialogOpen} assetCategoryClose={setAssetCategoreyDialogOpen} />
          
        </Box>

        <Box sx={{ margin: '10px' }}>
          <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>
            <Table sx={{ minWidth: 1000 }} aria-label='customized table'>
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell>Asset ID</StyledTableCell>
                  <StyledTableCell>Asset Name</StyledTableCell>
                  <StyledTableCell>Description</StyledTableCell>
                  <StyledTableCell>Delete</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {assetCategoryData?.result?.map((row: any) => (
                <StyledTableRow key={row.id}>
                  <StyledTableCell>{row.asset_id}</StyledTableCell>
                  <StyledTableCell>{row.name}</StyledTableCell>
                  <StyledTableCell>{row.description}</StyledTableCell>
                  <StyledTableCell>
                    <Tooltip title="Delete" arrow>
                    <IconButton onClick={() => handleDelete(row)}>
                        <DeleteIcon />
                    </IconButton>
                    </Tooltip>
                  </StyledTableCell>
                </StyledTableRow>
                ))}
                {assetCategoryData?.result?.length === 0 && (
                  <StyledTableRow>
                    <StyledTableCell colSpan={10} align="center">
                      <NoProjectRow />
                    </StyledTableCell>
                  </StyledTableRow>
                )}
              </TableBody>
            </Table>
            <Pagination
              sx={{
                float: 'right',
                margin: '15px 0px 10px 0px',
              }}
              page={page}
              onChange={handleChangePage}
              count={
                Math.ceil(
                  +(assetCategoryData?.totalPages),
                )
              }
              color='primary'
            />
          </TableContainer> 
        </Box>
      </Box>



    </Paper>
  );
};

const mapStateToProps = (state: RootState) => {
  return {
    assetCategoryData: getAssetsPortal.getAssetsPortal(state).getAssetCategoryData,
    isAssetCategoreyLodder: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).isAssetCategoreyLoader,
    updateAssetCategorySuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).UpdateAssetCategorySuccess,
    deleteAssetCategorySuccess: getAssetsManagementPortalUI.getAllAssetsPortalUI(state).deleteAssetCategorySuccess,




  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAssetCatregoryData: (data: {}) => dispatch(getAssetCategoreyData.request({ data })),
    deleteAssetCategory: (data: {}) => dispatch(deleteAssetCategory.request({ data })),
    deleteAssetCategoryReset: () => dispatch(deleteAssetCategory.reset()),




  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AssetsCategory)

