import { Box, Button, styled } from "@mui/material";
import React from "react";
import { useNavigate } from "react-router-dom";

const ActionButton = styled(Button)(() => ({
  fontSize: "13px",
  height: "60px",
  borderRadius: "5px",
  padding: "5px 20px",
  fontFamily: "Arial, sans-serif",
  width: "100%",
}));

const Reports: React.FC = (props: any) => {
  const navigate = useNavigate();

  const handleNavigation = () => {
    navigate("/home/<USER>/assignedassetsreports")
  }

  return (
    <Box sx={{margin: '50px 300px 50px 300px'}}>
        <Box>
          <ActionButton
            sx={{ margin: "30px", }}
            variant="outlined"
            onClick={handleNavigation}
          >
            Assigned Asset Reports
          </ActionButton>
        </Box>

        <Box>
          <ActionButton
            sx={{ margin: "30px" }}
            variant="outlined"
          >
            Unassigned Asset Reports
          </ActionButton>
        </Box>

        <Box>
          <ActionButton
            sx={{ margin: "30px" }}
            variant="outlined"
          >
            Lost Asset Reports
          </ActionButton>
        </Box>

      </Box>
  );
};

export default Reports;