import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Badge, Box, IconButton, Popover, Typography, List, ListItem, ListItemAvatar, Avatar, ListItemText, Divider, CircularProgress, Button, Dialog, DialogTitle, DialogContent, DialogActions, Grid, Chip } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import CloseIcon from '@mui/icons-material/Close';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { ReactComponent as NoItemFoundIcon } from '../../assets/images/NoItemIcon.svg';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import styles from '../../utils/styles.json';
import { toast } from 'react-toastify';

// Use environment variable for API URL
const API_URL = process.env.REACT_APP_BASE_URL;
// const API_URL = 'http://localhost:3001'; // for local server

// Styled components for buttons
const CancelButton = styled(Button)(({ theme }) => ({
  background: 'transparent', // Transparent background
  color: '#f44336', // Red text
  fontSize: '13px',
  height: '35px',
  fontFamily: styles.FONT_BOLD,
  width: '25%',
  borderRadius: '20px',
  border: '1px solid #f44336', // Red border
  '&:hover': {
    background: 'rgba(244, 67, 54, 0.04)', // Very light red on hover
    color: '#d32f2f', // Slightly darker red text on hover
    borderColor: '#d32f2f', // Darker red border on hover
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  fontSize: '13px',
  height: '35px',
  fontFamily: styles.FONT_BOLD,
  width: '25%',
  borderRadius: '20px',
}));

const NotificationIcon: React.FC = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const token = localStorage.getItem('token');
  const notificationRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  
  // New state for service request details popup
  const [serviceRequestDetails, setServiceRequestDetails] = useState<any>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  
  // Get user ID from Redux store
  const userId = useSelector(
    (state: any) => state?.entities?.dashboard?.getUserDetails?.id
  );
  
  // Fetch notifications function
  const fetchNotifications = useCallback(async () => {
    if (!userId || !token) return;
    
    setLoading(true);
    try {
      const response = await axios.get(`${API_URL}/notifications`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data && Array.isArray(response.data)) {
        setNotifications(response.data as any[]);
        console.log('Fetched notifications:', response.data);
      } else {
        console.error('Invalid notification data format:', response.data);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, token]);
  
  // Initial fetch and polling setup
  useEffect(() => {
    if (!userId) return;
    
    fetchNotifications();
    
    // Set up polling for new notifications every 30 seconds
    const intervalId = setInterval(fetchNotifications, 30000);
    
    return () => clearInterval(intervalId);
  }, [userId, fetchNotifications]);
  
  // Fetch notifications when popover opens
  useEffect(() => {
    if (anchorEl) {
      fetchNotifications();
    }
  }, [anchorEl, fetchNotifications]);
  
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  // New function to fetch service request details
  const fetchServiceRequestDetails = async (serviceRequestId: number) => {
    if (!serviceRequestId || !token) return;
    
    setLoadingDetails(true);
    try {
      // Using the correct endpoint based on the controller code
      const response = await axios.get(`${API_URL}/service-request?id=${serviceRequestId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data && response.data.data) {
        setServiceRequestDetails(response.data.data);
        setDetailsDialogOpen(true);
      } else {
        console.error('Invalid service request data format:', response.data);
      }
    } catch (error) {
      console.error('Failed to fetch service request details:', error);
    } finally {
      setLoadingDetails(false);
    }
  };
  
  // Close details dialog
  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false);
  };
  
  const handleNotificationClick = async (notification: any) => {
    try {
      // Mark notification as read
      await axios.patch(`${API_URL}/notifications/${notification.id}/read`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Update local state to mark notification as read
      setNotifications((prevNotifications) => 
        prevNotifications.map((n) => 
          n.id === notification.id ? { ...n, is_read: true } : n
        )
      );
      
      // If it's a service request notification, show details popup
      if (notification.service_request_id) {
        // Remove the conditional check that was preventing the popup from showing
        fetchServiceRequestDetails(notification.service_request_id);
        handleClose(); // Only close the notification dropdown after showing details
      } else {
        handleClose();
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleServiceRequestAction = async (notification: any, isApproved: boolean, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent the notification click handler from firing
    
    try {
      // Update service request status
      if (notification.service_request_id) {
        // Use consistent status codes: 1 for approved, 0 for rejected
        const newStatus = isApproved ? 1 : 0;
        
        const response = await axios.patch(
          `${API_URL}/service-requests/${notification.service_request_id}/status`, 
          { status: newStatus },
          { headers: { Authorization: `Bearer ${token}` } }
        );
        
        console.log(`Service request ${notification.service_request_id} ${isApproved ? 'approved' : 'rejected'}`);
        
        // Delete the notification from the database after successful action
        await axios.delete(`${API_URL}/notifications/${notification.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        // Remove notification from local state immediately
        setNotifications((prevNotifications) => 
          prevNotifications.filter((n) => n.id !== notification.id)
        );
        
        // Show success message
        const actionText = isApproved ? 'approved' : 'rejected';
        toast.success(
          `Service request #${notification.service_request_id} ${actionText} successfully!`,
          {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
          }
        );
        
        // Also show a brief info about the notification sent to employee
        setTimeout(() => {
          toast.info(
            `Employee has been notified about the ${actionText} request.`,
            {
              position: "top-right",
              autoClose: 4000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
            }
          );
        }, 1000);
      }
      
    } catch (error: any) {
      console.error(`Failed to ${isApproved ? 'approve' : 'reject'} service request:`, error);
      
      // Show error message
      const actionText = isApproved ? 'approve' : 'reject';
      const errorMessage = error.response?.data?.message || `Failed to ${actionText} service request. Please try again.`;
      
      toast.error(
        `${errorMessage}`,
        {
          position: "top-right",
          autoClose: 6000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );
    }
  };
  
  const unreadCount = notifications.filter((notification: any) => !notification.is_read).length;
  const open = Boolean(anchorEl);
  
  // Format the notification time
  const formatTime = (timestamp: string) => {
    const now = moment();
    const notificationTime = moment(timestamp);
    
    if (now.diff(notificationTime, 'days') < 1) {
      return notificationTime.fromNow(); // e.g. "2 hours ago"
    } else if (now.diff(notificationTime, 'days') < 7) {
      return notificationTime.format('ddd [at] h:mm A'); // e.g. "Mon at 2:30 PM"
    } else {
      return notificationTime.format('MMM D, YYYY'); // e.g. "Jan 15, 2023"
    }
  };
  
  // Get the profile picture for the notification
  const getProfilePicture = (notification: any) => {
    // First try to use the creator's image if available
    if (notification.creator?.image_path) {
      return notification.creator.image_path;
    }
    
    // Then try to use the user_picture field
    if (notification.user_picture) {
      return notification.user_picture;
    }
    
    // Default avatar
    return '/assets/images/default-avatar.png';
  };

  // Function to determine if a notification needs approval buttons
  const needsApproval = (notification: any) => {
    // Check if this is a service request notification
    if (!notification.service_request_id) {
      return false;
    }
    
    // Check if the notification is for a new service request
    const isNewRequest = notification.message.toLowerCase().includes('created a new service request');
    
    // Only show approval buttons if it's a new request AND the service request is still open/pending
    if (!isNewRequest) {
      return false;
    }
    
    // Check the service request status - only show buttons for open (1) or pending (3) requests
    const serviceRequest = notification.service_request;
    if (serviceRequest) {
      const status = serviceRequest.status;
      // Status 1 = Open, Status 3 = Pending - these can be approved/rejected
      // Status 0 = Rejected, Status 5 = Closed - these are already processed
      return status === 1 || status === 3;
    }
    
    // If we can't determine the status, show the buttons (fallback for new requests)
    return true;
  };
  
  // Get status label
  const getStatusLabel = (statusValue: number) => {
    const statusMap: {[key: number]: string} = {
      0: 'Rejected',
      1: 'Open',
      2: 'In Progress',
      3: 'Pending',
      4: "Won't Fix",
      5: 'Closed',
      6: 'Re-opened'
    };
    return statusMap[statusValue] || 'Unknown';
  };
  
  // Get priority label
  const getPriorityLabel = (priorityValue: number) => {
    const priorityMap: {[key: number]: string} = {
      1: 'Low',
      2: 'Medium',
      3: 'High',
      4: 'Critical'
    };
    return priorityMap[priorityValue] || 'Unknown';
  };
  
  // Get status color
  const getStatusColor = (statusValue: number) => {
    const colorMap: {[key: number]: string} = {
      0: 'error',
      1: 'info',
      2: 'primary',
      3: 'warning',
      4: 'default',
      5: 'success',
      6: 'secondary'
    };
    return colorMap[statusValue] || 'default';
  };
  
  // Get priority color
  const getPriorityColor = (priorityValue: number) => {
    const colorMap: {[key: number]: string} = {
      1: 'success',
      2: 'info',
      3: 'warning',
      4: 'error'
    };
    return colorMap[priorityValue] || 'default';
  };

  // Limit notifications to 10 for the dropdown
  const limitedNotifications = notifications.slice(0, 10);

  // Function to navigate to all notifications page
  const handleShowAllClick = () => {
    handleClose(); // Close the popover
    navigate('/home/<USER>/notifications'); // Navigate to notifications page
  };

  return (
    <Box ref={notificationRef}>
      <IconButton 
        color="inherit" 
        onClick={handleClick}
        aria-label="notifications"
      >
        <Badge badgeContent={unreadCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>
      
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          style: {
            width: '380px',
            maxHeight: '450px',
          },
        }}
      >
        <Box sx={{ p: 1.5, borderBottom: '1px solid #eee', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" fontWeight="bold" fontSize="1rem">
            Notifications
          </Typography>
          <Box display="flex" alignItems="center">
            <Button 
              variant="contained"
              color="primary"
              size="small" 
              onClick={handleShowAllClick}
              sx={{ 
                mr: 1, 
                textTransform: 'none', 
                fontWeight: 'medium',
                borderRadius: '20px',
                padding: '4px 12px',
                fontSize: '0.85rem',
                boxShadow: 'none',
                '&:hover': {
                  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                }
              }}
            >
              Show All
            </Button>
            <IconButton
              aria-label="close"
              onClick={handleClose}
              size="small"
              sx={{ color: '#9e9e9e' }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
        
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" p={3}>
            <CircularProgress size={24} />
          </Box>
        ) : limitedNotifications.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            p={3}
          >
            <NoItemFoundIcon />
            <Typography
              fontFamily="Montserrat-Regular"
              fontSize="0.9rem"
              color="#444444"
              marginTop="0.5rem"
            >
              No notifications yet
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {limitedNotifications.map((notification: any) => (
              <React.Fragment key={notification.id}>
                <ListItem 
                  alignItems="flex-start" 
                  sx={{ 
                    cursor: 'pointer',
                    backgroundColor: notification.is_read ? 'white' : '#f0f7ff',
                    '&:hover': { backgroundColor: '#f5f5f5' },
                    padding: '10px 16px'
                  }}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <ListItemAvatar>
                    <Avatar 
                      src={getProfilePicture(notification)} 
                      alt={notification.creator?.name || "User"}
                      sx={{ width: 40, height: 40 }}
                    />
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box>
                        <Typography 
                          component="span"
                          variant="body2"
                          color="text.primary"
                          fontSize="0.8rem"
                          sx={{ 
                            display: 'block', 
                            mb: 0.5,
                            fontWeight: notification.is_read ? 'normal' : 'medium'
                          }}
                        >
                          {notification.message.length > 80 
                            ? `${notification.message.substring(0, 80)}...` 
                            : notification.message}
                        </Typography>
                        
                        {/* Priority and Project Group chips */}
                        {notification.service_request && (notification.service_request.priority || notification.service_request.project_group) && (
                          <Box sx={{ display: 'flex', gap: 0.5, mb: 0.5, flexWrap: 'wrap' }}>
                            {notification.service_request.priority && (
                              <Chip
                                label={`Priority: ${getPriorityLabel(notification.service_request.priority)}`}
                                color={getPriorityColor(notification.service_request.priority) as any}
                                size="small"
                                sx={{ fontSize: '0.6rem', height: '16px' }}
                              />
                            )}
                            {notification.service_request.project_group && (
                              <Chip
                                label={`Group: ${notification.service_request.project_group}`}
                                color="info"
                                variant="outlined"
                                size="small"
                                sx={{ fontSize: '0.6rem', height: '16px' }}
                              />
                            )}
                          </Box>
                        )}
                      </Box>
                    }
                    secondary={
                      <Box 
                        sx={{ 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                      >
                        <Typography
                          component="span"
                          variant="caption"
                          color="text.secondary"
                          fontSize="0.7rem"
                        >
                          {formatTime(notification.created_at)}
                        </Typography>
                        {notification.service_request_id && (
                          <Typography
                            component="span"
                            variant="caption"
                            color="primary"
                            fontWeight="medium"
                            fontSize="0.7rem"
                          >
                            TSR #{notification.service_request_id}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
            {notifications.length > 10 && (
              <ListItem 
                alignItems="center" 
                sx={{ 
                  justifyContent: 'center',
                  padding: '10px 16px',
                  borderTop: '1px solid #eee',
                  backgroundColor: '#f9f9f9'
                }}
              >
                <Button 
                  variant="outlined"
                  color="primary" 
                  size="small" 
                  onClick={handleShowAllClick}
                  sx={{ 
                    textTransform: 'none',
                    borderRadius: '20px',
                    padding: '6px 16px',
                    fontWeight: 'medium',
                    '&:hover': {
                      backgroundColor: 'rgba(25, 60, 109, 0.04)'
                    }
                  }}
                  endIcon={<ArrowForwardIcon fontSize="small" />}
                >
                  View all notifications
                </Button>
              </ListItem>
            )}
          </List>
        )}
      </Popover>
      
      {/* Service Request Details Dialog */}
      <Dialog 
        open={detailsDialogOpen} 
        onClose={handleCloseDetailsDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #eee',
          pb: 1
        }}>
          <Box>
            Service Request Details
            {serviceRequestDetails && (
              <Typography variant="subtitle1" color="primary">
                TSR #{serviceRequestDetails.id}
              </Typography>
            )}
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleCloseDetailsDialog}
            size="small"
            sx={{
              color: '#9e9e9e',
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {loadingDetails ? (
            <Box display="flex" justifyContent="center" alignItems="center" p={3}>
              <CircularProgress size={24} />
            </Box>
          ) : serviceRequestDetails ? (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {serviceRequestDetails.title}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Chip 
                  label={getStatusLabel(serviceRequestDetails.status)} 
                  color={getStatusColor(serviceRequestDetails.status) as any}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Priority
                </Typography>
                <Chip 
                  label={getPriorityLabel(serviceRequestDetails.priority)} 
                  color={getPriorityColor(serviceRequestDetails.priority) as any}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Department
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.department?.dept_name || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Issue Type
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.issues?.title || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created By
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.employeeName || 'N/A'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created At
                </Typography>
                <Typography variant="body2">
                  {serviceRequestDetails.created_at ? 
                    new Date(serviceRequestDetails.created_at).toLocaleString() : 
                    'N/A'}
                </Typography>
              </Grid>
              
              {serviceRequestDetails.project_group && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Project Group
                  </Typography>
                  <Chip 
                    label={serviceRequestDetails.project_group} 
                    color="info"
                    variant="outlined"
                    size="small"
                    sx={{ mt: 0.5 }}
                  />
                </Grid>
              )}
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Description
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, whiteSpace: 'pre-wrap' }}>
                  {serviceRequestDetails.description || 'No description provided.'}
                </Typography>
              </Grid>
              
              {serviceRequestDetails.due_by && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Due By
                  </Typography>
                  <Typography variant="body2">
                    {new Date(serviceRequestDetails.due_by).toLocaleDateString()}
                  </Typography>
                </Grid>
              )}
            </Grid>
          ) : (
            <Typography>No details available</Typography>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Button onClick={handleCloseDetailsDialog}>Close</Button>
          {serviceRequestDetails && (
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => {
                navigate(`/home/<USER>/service-request/${serviceRequestDetails.id}`);
                handleCloseDetailsDialog();
              }}
            >
              View Full Details
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NotificationIcon;