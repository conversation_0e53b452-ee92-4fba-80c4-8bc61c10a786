import { Dayjs } from 'dayjs'
import { IData } from './Dashboard/Types'
export interface DashboardProps {
  getUserData?: (data: IData) => void
}

export interface LoginProps {
  getUserAuthentication: (data: LoginAuthData) => void
  loginStatus?: boolean
  loginUserData?: any
}

export interface LanguageDropdownProps {
  width?: string
  height?: string
}

export interface LoginAuthData {
  token?: string
}

export interface UserDetails {
  name: string
  email: string
  designation: string
  password: string
  confirmPassword: string
}

export interface GetAccessToken {
  grant_type: string
  refresh_token?: string
}

interface IServiceRequest {
  id_department: number
  title: string
  description: string
  priority: number
  id_issue_type: number
  status: number
  project_group: string
}

interface IFutureLeave {
  leave_start_date: Date
  leave_end_date: Date
}

export interface IRequestData {
  serviceRequest: IServiceRequest
  futureLeave: IFutureLeave
}

export interface designationGraphData {
  designation: string
  startDate: string
  endDate: string
}

export interface DeleteData {
  id: number
}

export interface CollegeDriveData {
  college_id: number
  drive_date: string
  notes: string
}

export interface OrgData {
  id: number
  organisation: string
  city: string
  representative_type: string
  last_invited: string
  notes: string
}

export interface TemplatePayload {
  id: number
  name: string
  subject: string
  content: string
  description: string
  id_round: number
  rounds: number
}
type Option = number | string

export interface InterviewerWork {
  from_date: Dayjs | null
  interviewers: Option[]
  to_date: Dayjs | null
}

export interface CandidateCount {
  from_date: string
  to_date: string
}

export interface JoinedCandidates {
  from_date: Dayjs | null
  interviewers: Option[]
  recruiters: Option[]
  to_date: Dayjs | null
}

export interface CollegesPayload {
  representative_type: string;
  pageNumber?: number;
  limit?: number; 
  search_input?: string;
}
export interface RecruiterCall {
  from_date: Dayjs | null
  recruiter: string
  to_date: Dayjs | null
}

