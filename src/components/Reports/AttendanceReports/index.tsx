import React, { useEffect, useState, useCallback } from 'react'
import ProjectQAReportHeading from './Heading'
import style from '../../../utils/styles.json'
import styled from 'styled-components'
import { Box, Tabs, Tab, Paper, FormControl, Pagination, InputLabel, Typography, Backdrop, CircularProgress } from '@mui/material'
import DateRangePicker from './DateRangePicker'
import dayjs from 'dayjs'
import { connect } from 'react-redux'
import ui, * as UI from './ui'
import { Dispatch } from 'redux'
import {
    fetchLeaveReports,
    getAttendanceDetail,
    getAttendanceTimesheet,
    fetchLeaveReportsBalance,
    fetchLeaveReportsAllocated,
    fetchLeaveReportsEncashment,
    fetchLeaveReportsType,
    leaveFrequencies,
    getQuatre,
    fetchLeaveType,
    fetchAttendanceReport,
} from '../../../actions'
import ExportButton from './ExportButton'
import QATable from './QATable'
import { RootState } from '../../../configureStore'
import { ArrowBack, FormatAlignJustify } from '@mui/icons-material'
import moment from 'moment'
import { useNavigate } from 'react-router-dom'
import SearchBox from './SearchBox'
import { employeeDataRequest } from '../../../reducers/entities'
import { employeeDataSate, getAllEmpPortalUI } from '../../../reducers/ui'
import {
    attendanceEntity,
    dashboardEntity,
    employeeDataSateUI,
    employeePortalUI,
    fetchUserDetailsEntities,

} from '../../../reducers'
import { SelectField } from '../../Common/ComponentCommonStyles'
import { StyledMenuItem } from '../../Common/CommonStyles'
import Loader from '../../Common/Loader'
import { formatDate } from '../../../utils/dateUtils'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell, { tableCellClasses } from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import DebouncedSearchedBox from '../../Common/DebouncedSearchBox'

// const StyledPaper = styled(Paper)(() => ({
//     width: '94%',
//     padding: '25px 25px',
//     background: '#FFFFFF',
//     opacity: '1',
//     marginTop: '10px',
//     marginLeft: 'auto',
//     marginRight: 'auto',
//     marginBottom: '10px',
//     border: '1px solid #DDDDDD',
// }))

const StyledPaper = styled(Paper)(() => ({
    width: '93%',
    padding: '25px 25px',
    background: '#FFFFFF',
    opacity: '1',
    margin: '20px',
    border: '1px solid #DDDDDD',
}))

const MainContainer = {
    backgroundColor: 'rgb(231, 235, 240)',
    width: '100%',
}


const StyledTableCell = styled(TableCell)(() => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: style.PRIMARY_COLOR,
        color: 'White',
        fontFamily: style.FONT_MEDIUM,
        textAlign: 'center',
        fontSize: '13px',
        letterSpacing: '0px',
        padding: '11px 0px',
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 12,
        fontFamily: style.FONT_MEDIUM,
        textAlign: 'center',
        color: '#483f3f',
        letterSpacing: '0px',
    },
}))

const StyledTableRow = styled(TableRow)(() => ({
    left: '160px',
    width: '1719px',
    height: '60px',
    boxShadow: '0px 10px 3px #6c6c6c10',
    opacity: '1',
}))

const styles = {
    pageHead: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headingBar: {
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        justifyContent: 'center',
        width: 'auto',
    },
    primaryHeading: {
        fontSize: '1.5rem',
        fontWeight: 'bold',
        color: style.PRIMARY_COLOR,
        fontFamily: style.FONT_BOLD,
        textAlign: 'center',
    },
    headerHeading: {
        fontSize: '1.2rem',
        color: '#555',
    },
    backNavigave: {
        position: 'absolute',
        top: 90,
        right: 25,
        margin: '20px',
        cursor: 'pointer',
    }
}

const AttendanceReport = (props: any) => {
    const navigate = useNavigate();

    const {
        fetchSRData,
        SRData,
        loaderState,
        // fetchDepartmentList,
        employeeDataRequest,
        DepartmentList,
        isCreateServiceRequest,
        fetchAttendanceReport,
        AttendanceReportData,
        isAttendanceReportDataLoaded
    } = props
    const [page, setPage] = useState(1)
    const [pageSize, setPageSize] = useState(100)
    const [searchQuery, setSearchQuery] = useState('')
    const [startDate, setStartDate] = useState(dayjs())
    const [endDate, setEndDate] = useState(dayjs())
    const [showResults, setShowResults] = useState(true)
    useEffect(() => {
        fetchAttendanceReport({
            page: page,
            limit: pageSize,
            search: searchQuery,
            filterDate: startDate.format('YYYY-MM-DD').toString(),
        })
    }, [page, searchQuery])

    useEffect(() => {
    }, [AttendanceReportData])

    useEffect(() => {
        if (showResults) {
            console.log("showResults", showResults)
            fetchAttendanceReport({
                page: page,
                limit: pageSize,
                search: searchQuery,
                filterDate: startDate.format('YYYY-MM-DD').toString(),
            })
        }
        setShowResults(false)
    }, [showResults])

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, page: any) => {
        setPage(page)
    }

    // const fetchAttendanceReportData = [...fetchAttendanceReport]
    // console.log("fetchAttendanceReportData",fetchAttendanceReport)
    return (
        <>
            <div style={MainContainer}>
                <StyledPaper>
                    <Box sx={styles.pageHead}>
                        <Box
                            sx={styles.backNavigave}
                            onClick={() => navigate(-1)}
                        >
                            <ArrowBack />
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem', width: '97%' }}>
                            <DebouncedSearchedBox
                                placeHolder='Search by Name or Emp id'
                                setSearchQuery={setSearchQuery}
                                setPage={setPage}
                            />
                            <Box sx={styles.headingBar}>
                                <Typography width='fit-content' className='heading' sx={styles.primaryHeading}>
                                    Attendence Report
                                </Typography>
                            </Box>

                            <Box
                                display='flex'
                                alignItems='center'
                                justifyContent='center'
                                padding='0'
                            >
                                <DateRangePicker
                                    startDate={startDate}
                                    endDate={endDate}
                                    setStartDate={setStartDate}
                                    setEndDate={setEndDate}
                                    setShowResults={setShowResults}
                                />
                            </Box>
                        </Box>
                    </Box>
                    {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                        <DebouncedSearchedBox
                            placeHolder='Search by Name or Emp id'
                            setSearchQuery={setSearchQuery}
                            setPage={setPage}
                        />
                        <Box
                            display='flex'
                            alignItems='center'
                            justifyContent='center'
                            padding='0'
                        >
                            <DateRangePicker
                                startDate={startDate}
                                endDate={endDate}
                                setStartDate={setStartDate}
                                setEndDate={setEndDate}
                                setShowResults={setShowResults}
                            />
                        </Box>
                    </Box> */}
                    <Box>

                    </Box>

                    <TableContainer component={Paper} sx={{ position: 'relative', cursor: 'pointer' }}>
                        {/* Loader overlay */}
                        {!isAttendanceReportDataLoaded && <Loader state={true} />}

                        <Table sx={{ minWidth: 700 }} aria-label='customized table'>
                            <TableHead>
                                <TableRow>
                                    <StyledTableCell>Emp Id</StyledTableCell>
                                    <StyledTableCell>Name</StyledTableCell>
                                    <StyledTableCell>Date</StyledTableCell>
                                    <StyledTableCell>In Time</StyledTableCell>
                                    <StyledTableCell>Out Time</StyledTableCell>
                                    <StyledTableCell>Total Hours</StyledTableCell>
                                </TableRow>
                            </TableHead>

                            <TableBody>
                                {isAttendanceReportDataLoaded && AttendanceReportData?.data?.length ? (
                                    AttendanceReportData.data.map((request: any) => (
                                        <StyledTableRow key={`${request?.employee_id}${request?.id}`}>
                                            <StyledTableCell component='th' scope='row'>
                                                {request?.employee_id}
                                            </StyledTableCell>
                                            <StyledTableCell>{request?.name}</StyledTableCell>
                                            <StyledTableCell>
                                                {request?.date ? new Date(request.date).toLocaleDateString("en-US", {
                                                    month: "2-digit",
                                                    day: "2-digit",
                                                    year: "numeric"
                                                }) : "-"}
                                            </StyledTableCell>

                                            <StyledTableCell>
                                                {request?.in_time ? new Date(request.in_time).toLocaleTimeString("en-US", {
                                                    hour: "2-digit",
                                                    minute: "2-digit",
                                                    hour12: true
                                                }) : "-"}
                                            </StyledTableCell>

                                            <StyledTableCell>
                                                {request?.out_time ? new Date(request.out_time).toLocaleTimeString("en-US", {
                                                    hour: "2-digit",
                                                    minute: "2-digit",
                                                    hour12: true
                                                }) : "-"}
                                            </StyledTableCell>

                                            <StyledTableCell>{request?.total_hours}</StyledTableCell>
                                        </StyledTableRow>
                                    ))
                                ) : (
                                    <StyledTableRow>
                                        <StyledTableCell align='center' colSpan={10}>
                                            <Typography variant='subtitle1' sx={{ color: '#483f3f' }}>
                                                No matching records found.
                                            </Typography>
                                        </StyledTableCell>
                                    </StyledTableRow>
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
                        <Pagination
                            count={AttendanceReportData?.pagination?.totalPages || 1}
                            color='primary'
                            page={page}
                            onChange={(_, newPage) => handleChangePage(null, newPage - 1)}
                        />
                    </Box>
                </StyledPaper>
            </div>
        </>
    )
}

const mapStateToProp = (state: RootState) => {
    return {
        AttendanceReportData: employeeDataRequest.getAllEmployeesList(state).getAttendanceReports, // adjust path as per your state
        isAttendanceReportDataLoaded: employeeDataSateUI.getAllEmpList(state).isAttendanceReportloded
    }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
    return {
        fetchAttendanceReport: (data: any) => dispatch(fetchAttendanceReport.request({ data })),
    }
}
export default connect(mapStateToProp, mapDispatchToProp)(AttendanceReport)
