import { Box, Button, styled } from '@mui/material'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { Dayjs } from 'dayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { Dispatch, SetStateAction, useState } from 'react'
import styles from '../../../utils/styles.json'

type DateRangePickerPropType = {
  startDate: Dayjs
  endDate: Dayjs
  setStartDate: Dispatch<SetStateAction<Dayjs>>
  setEndDate: Dispatch<SetStateAction<Dayjs>>
  setShowResults: Dispatch<SetStateAction<boolean>>
}

const StyledDatePicker = styled(DatePicker)(({ theme }) => ({
  '*': {
    paddingLeft: '0',
    marginLeft: '0',
  },
  '&': {
    width: '10rem',
    padding: '0',
    marginLeft: '10px',
    '@media only screen and (max-width:460px)': {
      margin: '7px',
    },
  },
  '& .MuiInputBase-root': {
    height: '35px',
  },
  '& .MuiFormLabel-asterisk': {
    display: 'none',
  },
  '& .MuiDateCalendar-root': {
    width: '100px',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
  },
  input: {
    fontSize: '13px',
    padding: '10px 14px',
    color: 'primary',
    fontFamily: styles.FONT_MEDIUM,
  },
  '@media only screen and (max-width:650px)': {
    '&': {
      width: '6rem',
    },
    input: {
      fontSize: '12px',
      padding: '9px 10px',
    },
  },
}))

const style = {
  showResults: {
    padding: '3px 14px',
    borderRadius: '20px',
    fontFamily: styles.FONT_MEDIUM,
    fontSize: '13px',
    height: '35px',
    '@media only screen and (max-width:650px)': {
      width: '7rem',
      fontSize: '0.75rem',
    },
    '&.Mui-disabled': {
      color: '#BEBEBE',
      backgroundColor: 'transparent',
    },
  },
}

const DateRangePicker = (props: DateRangePickerPropType) => {
  const { setStartDate, setEndDate, startDate, endDate, setShowResults } = props
  const [dateOpen, setDateOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState(startDate)

  const handlerDateResult = () => {
    setStartDate(selectedDate)
    // Set end date to the same date for single date selection
    // setEndDate(selectedDate)
    setShowResults((prev: boolean) => !prev)
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box display="flex" alignItems="center" justifyContent="center" margin="0" padding="0">
        <StyledDatePicker
          label="Select Date"
          format='MM-DD-YYYY'
          value={selectedDate}
          onAccept={(newValue: any) => setSelectedDate(newValue as Dayjs)}
          disableFuture
          open={dateOpen}
          onClose={() => setDateOpen(false)}
          slotProps={{
            textField: {
              onClick: () => setDateOpen(true),
            },
          }}
        />
      </Box>
      <Box display="flex" alignItems="center" justifyContent="center" marginLeft="1.5rem">
        <Button onClick={handlerDateResult} variant="outlined" sx={style.showResults}>
          Show Result
        </Button>
      </Box>
    </LocalizationProvider>
  )
}

export default DateRangePicker
