import { StyledTableCell } from '../../Common/CommonStyles'
import { style } from './reportStyles'
import { Box, TableCell, TableRow, Typography, styled } from '@mui/material'
import styless from '../../../utils/styles.json'
import { formatDate } from '../../../utils/dateUtils'
const styles = {
  BgColor: {
    height: '15px',
    padding: '6px 16px',
    fontFamily: styless.FONT_MEDIUM,
  },
  typographyStyle: {
    fontSize: '13px',
    fontFamily: styless.FONT_MEDIUM,
  },
  dividerStyle: { padding: '5px 0' },
  resourcesStyle: {
    height: '15px',
    padding: '10px',
  },
  noEntriesRow: {
    fontFamily: styless.FONT_MEDIUM,
    Padding: '6px 16px',
    height: '15px',
  },
  tableCellsStyles: {
    height: '20px',
    padding: '6px 10px',
    fontFamily: styless.FONT_MEDIUM,
  },
}

const StyledTypographyDivider = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  padding: '1px 0',
  borderTop: '1px solid #EEEEEE',
  borderBottom: '1px solid #EEEEEE',
  backgroundColor: '#3B3C36',
  borderRadius: '5px',
}))
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  left: '160px',
  width: '1719px',
  height: '15px',
  boxShadow: '0px 5px 2px #6c6c6c10',
  opacity: '1',
}))

const TableBodyRow = ({ row, selectType, selectedTab, lastIndex, selectedQuarter }: any) => {
  const label = ['emp_name', 'is_billable', 'employement_type']

  const renderProjectDetails = (projectDetails: any[]) => {
    if (projectDetails?.length === 0) {
      return 'NA'
    }
    return projectDetails?.map((project) => (
      <Box key={`${project?.project_id}`}>
        {project.project_name ?? 'NA'} |{' '}
        {project.employement_type
          ? `${project.employement_type ?? 'NA'} (${
              project.time_count ? project.time_count : 'NA'
            })`
          : 'NA'}
      </Box>
    ))
  }

  const hasChildData = row?.result > 2

  return (
    <>
      {selectedTab === 0 && (
        <StyledTableRow id='body' sx={{ ...styles.tableCellsStyles, boxShadow: 'none' }}>
          <StyledTableCell sx={styles.BgColor}>{row.employee_id}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.name}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.designation}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{formatDate(row?.start_date)}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{formatDate(row?.end_date)}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.leave_type}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.description}</StyledTableCell>
          <StyledTableCell
            sx={{ ...styles.BgColor, borderRight: '1px solid rgba(224, 224, 224, 1)' }}
          >
            {row?.leave_count}
          </StyledTableCell>
          {row.firstIndex ? (
            <>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.deductibleLeavesSum}
              </StyledTableCell>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.nonDeductibleLeavesSum}
              </StyledTableCell>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.allocatedLeavesSum}
              </StyledTableCell>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.totalLeaveAvailed}
              </StyledTableCell>
            </>
          ) : (
            <></>
          )}
        </StyledTableRow>
      )}

      {selectedTab === 1 && (
        <StyledTableRow id='body' sx={styles.tableCellsStyles}>
          <StyledTableCell sx={styles.BgColor}>{row?.leaveEmployee?.employee_id}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.leaveEmployee?.name}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.totalLeaves}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.availedLeaves}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.leaveBalance}</StyledTableCell>
        </StyledTableRow>
      )}

      {selectedTab === 2 && (
        <StyledTableRow id='body' sx={styles.tableCellsStyles}>
          <StyledTableCell sx={styles.BgColor}>{row.employee_id}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.name}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.underProbation}</StyledTableCell>
          {!selectedQuarter ? (
            <>
              <StyledTableCell sx={styles.BgColor}>{row?.Quarter1}</StyledTableCell>
              <StyledTableCell sx={styles.BgColor}>{row?.Quarter2}</StyledTableCell>
              <StyledTableCell sx={styles.BgColor}>{row?.Quarter3}</StyledTableCell>
              <StyledTableCell sx={styles.BgColor}>{row?.Quarter4}</StyledTableCell>
            </>
          ) : (
            <></>
          )}
          <StyledTableCell sx={styles.BgColor}>{row?.totalLeavesAllocated}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.deductibleLeavesAvailed}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.nonDeductibleLeavesAvailed}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.totalLeaveBalance}</StyledTableCell>
        </StyledTableRow>
      )}
      {selectedTab === 3 && (
        <StyledTableRow id='body' sx={styles.tableCellsStyles}>
          <StyledTableCell sx={styles.BgColor}>{row?.leaveEmployee?.employee_id}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.leaveEmployee?.name}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.totalDeductibleLeaves}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.totalLeaveBalance}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.CalculatedBasicSalary}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.totalLeaveEncashmentAmount}</StyledTableCell>
        </StyledTableRow>
      )}

      {selectedTab === 4 && (
        <StyledTableRow id='body' sx={{ ...styles.tableCellsStyles, boxShadow: 'none' }}>
          <StyledTableCell sx={styles.BgColor}>{row?.employee_id}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.name}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{formatDate(row?.startDate)}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{formatDate(row?.endDate)}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.description}</StyledTableCell>
          <StyledTableCell
            sx={{ ...styles.BgColor, borderRight: '1px solid rgba(224, 224, 224, 1)' }}
          >
            {row?.leaveAvailed}
          </StyledTableCell>
          {row.firstIndex ? (
            <>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.deductibleLeavesAvailed}
              </StyledTableCell>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.nonDeductibleLeavesAvailed}
              </StyledTableCell>
              <StyledTableCell sx={styles.BgColor} rowSpan={row.length}>
                {row?.totalLeaveAvailed}
              </StyledTableCell>
            </>
          ) : (
            <></>
          )}
        </StyledTableRow>
      )}
      {selectedTab === 0 && lastIndex && (
        <StyledTableRow sx={styles.tableCellsStyles}>
          <StyledTableCell sx={styles.BgColor}>#</StyledTableCell>
          <StyledTableCell colSpan={7} sx={styles.BgColor}>
            Total Leaves
          </StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.grandDeductibleTotal || 0}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.grandNonDeductibleTotal || 0}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.grandAllocatedTotal || 0}</StyledTableCell>
          <StyledTableCell sx={styles.BgColor}>{row?.grandTotal || 0}</StyledTableCell>
        </StyledTableRow>
      )}

      {row?.members?.map((report: any, index: any) => (
        <StyledTableRow key={`report-${index}`} sx={style.border}>
          <StyledTableCell></StyledTableCell>
          <StyledTableCell></StyledTableCell>
          {label.map((labelData: any, idx: any) => (
            <StyledTableCell key={labelData}>
              {labelData === 'employement_type'
                ? `${report[labelData] ?? 'NA'}${
                    report.time_count ? ` (${report.time_count})` : ''
                  }`
                : report[labelData] || 'NA'}
            </StyledTableCell>
          ))}
        </StyledTableRow>
      ))}

      {/* Footer row */}

      {selectType !== 'project-wise' && (
        <StyledTableRow sx={style.border}>
          <StyledTableCell>{row?.emp_id}</StyledTableCell>
          <StyledTableCell>{row?.emp_name}</StyledTableCell>
          <StyledTableCell>{renderProjectDetails(row?.projects)}</StyledTableCell>
          <StyledTableCell>{row?.projects_count}</StyledTableCell>
        </StyledTableRow>
      )}
    </>
  )
}
export default TableBodyRow
