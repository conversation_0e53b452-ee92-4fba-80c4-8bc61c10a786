import React, { useEffect, useState, useCallback } from 'react'
import ProjectQAReportHeading from './Heading'
import { style } from './reportStyles'
import styled from 'styled-components'
import { Box, Tabs, Tab, Paper, FormControl, InputLabel } from '@mui/material'
import DateRangePicker from './DateRangePicker'
import dayjs from 'dayjs'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import {
  fetchLeaveReports,
  getAttendanceDetail,
  getAttendanceTimesheet,
  fetchLeaveReportsBalance,
  fetchLeaveReportsAllocated,
  fetchLeaveReportsEncashment,
  fetchLeaveReportsType,
  leaveFrequencies,
  getQuatre,
  fetchLeaveType,
} from '../../../actions'
import ExportButton from './ExportButton'
import QATable from './QATable'
import { RootState } from '../../../configureStore'
import { ArrowBack } from '@mui/icons-material'
import moment from 'moment'
import { useNavigate } from 'react-router-dom'
import SearchBox from './SearchBox'
import { employeeDataRequest } from '../../../reducers/entities'
import { employeeDataSate } from '../../../reducers/ui'
import {
  attendanceEntity,
  dashboardEntity,
  employeeDataSateUI,
  fetchUserDetailsEntities,
} from '../../../reducers'
import { SelectField } from '../../Common/ComponentCommonStyles'
import { StyledMenuItem } from '../../Common/CommonStyles'
import Loader from '../../Common/Loader'
import { formatDate } from '../../../utils/dateUtils'

const StyledPaper = styled(Paper)(() => ({
  width: '94%',
  padding: '25px 25px',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '10px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '10px',
  border: '1px solid #DDDDDD',
}))

const StyledFormControl = styled(FormControl)(({}) => ({}))

const CustomSelectField = (props: any) => {
  const { value, onChange, options, optionkeys, label, momentFlag, formatDateFlag } = props

  return (
    <Box>
      <StyledFormControl sx={{ margin: '16px 0px' }}>
        <InputLabel id={label}>{label}</InputLabel>
        <SelectField
          sx={{ borderRadius: '22px', width: '160px', height: '35px' }}
          variant='outlined'
          label={label}
          value={value}
          onChange={onChange}
        >
          {options.map((option: any) => {
            return (
              <StyledMenuItem key={option?.id} value={option?.id}>
                {momentFlag
                  ? moment(new Date(option?.[optionkeys?.[0]]).toISOString().split('T')[0]).format(
                      'MMMM YYYY',
                    )
                  : formatDateFlag
                  ? `${formatDate(option?.[optionkeys?.[0]])} to ${formatDate(
                      option?.[optionkeys?.[1]],
                    )}`
                  : option?.[optionkeys[0]]}
              </StyledMenuItem>
            )
          })}
        </SelectField>
      </StyledFormControl>
    </Box>
  )
}

const LeaveReport = ({
  fetchAttendanceTimesheet,
  fetchAttendanceData,
  leaveBalanceReportData,
  getUserById,
  AttendanceTimesheet,
  fetchLeaveReportAPI,
  fetchLeaveReportsBalance,
  fetchLeaveReportsType,
  leaveTypeReportData,
  leaveAllocatedReportData,
  fetchLeaveReportsAllocated,
  fetchLeaveReportsEncashment,
  leaveReportData,
  leaveEncashmentReportData,
  leaveReportLoader,
  fetchLeaveFrequencies,
  leaveFrequenciesData,
  getAllQuatre,
  allQuatre,
  getLeaveTypes,
  fetchLeaveType,
}: any) => {
  const navigate = useNavigate()
  const [selectType] = useState('project-wise')
  const [startDate, setStartDate] = useState(
    dayjs(new Date(new Date().getFullYear(), new Date().getMonth(), 1)),
  )
  const currentDate = dayjs()
  const [selectedMonth, setSelectedMonth] = useState('')
  const lastDateOfMonth = currentDate.endOf('month')
  const [endDate, setEndDate] = useState(lastDateOfMonth)
  const [page, setPage] = useState(1)
  const [rowPerPage, SetRowPerPage] = useState(100)
  const [showResult, setShowResult] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery)
  const [selectedTab, setSelectedTab] = useState<number>(() => {
    const savedTab = localStorage.getItem('selectedTab')
    return savedTab ? parseInt(savedTab, 10) : 0
  })
  const [selectedQuarter, setSelectedQuarter] = useState(0)
  const [selectedFinYear, setSelectedFinYear] = useState('')
  const [leaveType, setLeaveType] = useState('')

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 800)
    return () => clearTimeout(handler)
  }, [searchQuery])

  useEffect(() => {
    if (getUserById.id) {
      fetchAttendanceTimesheet(getUserById.id)
    }
  }, [getUserById.id])

  useEffect(() => {
    switch (selectedTab) {
      case 0:
        if (selectedMonth)
          fetchLeaveReportAPI({
            startDate: startDate.format('YYYY-MM-DD').toString(),
            endDate: endDate.format('YYYY-MM-DD').toString(),
            page: 1,
            pageSize: 500,
            tid: selectedMonth,
          })
        break
      case 1:
        fetchLeaveReportsBalance({
          page: page,
          limit: rowPerPage,
          search: debouncedSearchQuery,
        })
        break
      case 2:
        if (selectedFinYear)
          fetchLeaveReportsAllocated({
            yid: selectedFinYear,
            quarter: selectedQuarter,
          })
        break
      case 3:
        if (selectedFinYear)
          fetchLeaveReportsEncashment({
            yid: selectedFinYear,
          })
        break
      case 4:
        if (leaveType && selectedMonth)
          fetchLeaveReportsType({
            leaveType: leaveType,
            tid: selectedMonth,
          })
        break
      default:
        break
    }
  }, [
    showResult,
    selectedMonth,
    selectedFinYear,
    selectedTab,
    selectedQuarter,
    leaveType,
    page,
    rowPerPage,
  ])

  useEffect(() => {
    switch (selectedTab) {
      case 1:
        page === 1 &&
          fetchLeaveReportsBalance({
            page: page,
            limit: rowPerPage,
            search: debouncedSearchQuery,
          })
        setPage(() => 1)
        break
      default:
        break
    }
  }, [debouncedSearchQuery, selectedTab])

  useEffect(() => {
    fetchLeaveType()
    fetchLeaveFrequencies()
  }, [])

  const handleChangePage = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  useEffect(() => {
    if (AttendanceTimesheet.length && (selectedTab === 0 || selectedTab === 4)) {
      setSelectedMonth(AttendanceTimesheet[0].id)
    }
  }, [AttendanceTimesheet, selectedTab])

  useEffect(() => {
    if ((selectedTab === 2 || selectedTab === 3) && !!leaveFrequenciesData.length)
      setSelectedFinYear(() => leaveFrequenciesData[0].id)
  }, [selectedTab, leaveFrequenciesData])

  useEffect(() => {
    if (selectedTab === 2 && !!selectedFinYear) getAllQuatre({ yid: selectedFinYear })
  }, [selectedFinYear])

  useEffect(() => {
    if (getLeaveTypes?.length) {
      getLeaveTypes?.find((element: { leave_type_name: string; id: string }) => {
        if (element.leave_type_name.toLowerCase() === 'loss of pay') {
          setLeaveType(element.id)
          return true
        }
      })
    }
  }, [getLeaveTypes])

  const handleSelectMonth = useCallback(
    (event: any) => {
      const selectedMonthId = event.target.value
      setSelectedMonth(selectedMonthId)
    },
    [getUserById.id, fetchAttendanceData, AttendanceTimesheet],
  )

  const handleSelectLeaveType = useCallback(
    (event: any) => {
      setLeaveType(event.target.value)
    },
    [getLeaveTypes],
  )

  const handleSelectFinYear = useCallback(
    (event: any) => {
      setSelectedFinYear(event.target.value)
    },
    [leaveFrequenciesData],
  )

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue)
    localStorage.setItem('selectedTab', newValue.toString())
  }
  const handleSelectQuarter = (event: any): void => {
    setSelectedQuarter(event.target.value)
  }

  return (
    <StyledPaper>
      <Box
        sx={{
          display: 'flex',
          marginBottom: '20px',
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
        }}
      >
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label='Tabs for different tables'>
          <Tab label='Leave Report' />
          <Tab label='Leave Balance Report' />
          <Tab label='Leave Allocated Report' />
          <Tab label='Leaves Encashment Report' sx={{ display: 'none' }} />
          <Tab label='Leave Type Report' />
        </Tabs>
      </Box>
      <StyledPaper sx={{ padding: '0', margin: '0', width: '100%' }}>
        <Box sx={style.container}>
          <Box
            sx={{
              position: 'absolute',
              top: 80,
              right: 25,
              margin: '20px',
              cursor: 'pointer',
            }}
            onClick={() => navigate(-1)}
          >
            <ArrowBack />
          </Box>
          <Box sx={style.pageHead}>
            <ProjectQAReportHeading startDate={startDate} endDate={endDate} />
          </Box>
          <Box sx={style.actionBarConatiner}>
            <Box sx={style.actionBar}>
              {selectedTab === 1 && (
                <SearchBox
                  setSearchQuery={setSearchQuery}
                  selectType={selectType}
                  value={searchQuery}
                />
              )}
              {(selectedTab === 0 || selectedTab === 4) && (
                <CustomSelectField
                  label={'Payroll Period'}
                  value={selectedMonth}
                  onChange={handleSelectMonth}
                  options={AttendanceTimesheet}
                  optionkeys={['start_date']}
                  momentFlag={true}
                />
              )}
              {selectedTab === 4 && (
                <CustomSelectField
                  label={'Leave Type'}
                  value={leaveType}
                  onChange={handleSelectLeaveType}
                  options={getLeaveTypes}
                  optionkeys={['leave_type_name']}
                />
              )}
              {(selectedTab === 2 || selectedTab === 3) && (
                <CustomSelectField
                  label={'Financial Year'}
                  value={selectedFinYear}
                  onChange={handleSelectFinYear}
                  options={leaveFrequenciesData}
                  optionkeys={['current_year_beg_date', 'current_year_end_date']}
                  formatDateFlag={true}
                />
              )}
              {selectedTab === 2 && (
                <CustomSelectField
                  label={'Quarter'}
                  value={selectedQuarter}
                  onChange={handleSelectQuarter}
                  options={[{ id: 0, quarter_name: 'All' }, ...allQuatre]}
                  optionkeys={['quarter_name']}
                />
              )}
              {selectedTab === 0 && (
                <Box sx={style.datePickerBoxStyle}>
                  <DateRangePicker
                    startDate={startDate}
                    endDate={endDate}
                    setStartDate={setStartDate}
                    setEndDate={setEndDate}
                    setShowResult={setShowResult}
                    height={'35px'}
                  />
                </Box>
              )}
              {/* <Box sx={style.actionBarDownloadbtn}>
                <ExportButton selectType={selectType} rowsToDisplay={leaveReportData} />
              </Box> */}
            </Box>
            {/* <Collapse orientation='vertical' in={expand}>
              <CollapseData projectQAReportData={leaveReportData} />
            </Collapse> */}
          </Box>
          {leaveReportLoader ? (
            <Loader state={leaveReportLoader} />
          ) : (
            <QATable
              selectedTab={selectedTab}
              selectType={selectType}
              projectQAReportData={leaveReportData}
              projectLeaveBalanceReportData={leaveBalanceReportData}
              projectLeaveAllocatedReportData={leaveAllocatedReportData}
              projectLeaveEncashmentReportData={leaveEncashmentReportData}
              projectLeaveTypeReportData={leaveTypeReportData}
              page={page}
              setPage={setPage}
              handleChangePage={handleChangePage}
              SetRowPerPage={SetRowPerPage}
              rowPerPage={rowPerPage}
              selectedQuarter={selectedQuarter}
            />
          )}
        </Box>
      </StyledPaper>
    </StyledPaper>
  )
}

const mapStateToProp = (state: RootState) => {
  return {
    leaveReportData: employeeDataRequest.getAllEmployeesList(state).getLeavesReports,
    leaveBalanceReportData: employeeDataRequest.getAllEmployeesList(state).getLeavesReportsBalance,
    leaveTypeReportData: employeeDataRequest.getAllEmployeesList(state).getLeavesReportsType,
    leaveAllocatedReportData:
      employeeDataRequest.getAllEmployeesList(state).getLeavesReportsAllocated,
    leaveEncashmentReportData:
      employeeDataRequest.getAllEmployeesList(state).getLeavesReportsEncashment,
    isLeaveReportDone: employeeDataSate.getAllEmpList(state).isGetLeavesReportDone,
    isLeaveReportLoader: employeeDataSate.getAllEmpList(state).isGetLeavesLoader,
    AttendanceTimesheet: attendanceEntity.getAllAttendance(state).getAttendanceTimesheet,
    getUserById: dashboardEntity.getDashboard(state).getUserDetails,
    leaveReportLoader: employeeDataSateUI.getAllEmpList(state).leaveReportLoader,
    leaveFrequenciesData: employeeDataRequest.getAllEmployeesList(state).getLeavesFrequencies,
    allQuatre: employeeDataRequest.getAllEmployeesList(state).getQuatre,
    getLeaveTypes: fetchUserDetailsEntities.fetchUserData(state).getLeaveTypes,
  }
}
const mapDispatchToProp = (dispatch: Dispatch) => {
  return {
    fetchLeaveReportAPI: (data: any) => dispatch(fetchLeaveReports.request({ data })),
    fetchAttendanceData: (data: any) => dispatch(getAttendanceDetail.request(data)),
    fetchAttendanceTimesheet: (userId: any) => dispatch(getAttendanceTimesheet.request({ userId })),
    fetchLeaveReportsBalance: (data: any) => dispatch(fetchLeaveReportsBalance.request({ data })),
    fetchLeaveReportsType: (data: any) => dispatch(fetchLeaveReportsType.request({ data })),
    fetchLeaveReportsAllocated: (data: any) =>
      dispatch(fetchLeaveReportsAllocated.request({ data })),
    fetchLeaveReportsEncashment: (data: any) =>
      dispatch(fetchLeaveReportsEncashment.request({ data })),
    fetchLeaveFrequencies: () => dispatch(leaveFrequencies.request()),
    getAllQuatre: (data: { [key: string]: string }) => dispatch(getQuatre.request(data)),
    fetchLeaveType: (data: any) => dispatch(fetchLeaveType.request(data)),
  }
}
export default connect(mapStateToProp, mapDispatchToProp)(LeaveReport)
