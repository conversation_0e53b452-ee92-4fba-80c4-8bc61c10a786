import {
  Box,
  TableContainer,
  Paper,
  TableBody,
  Pagination,
  Table,
  FormControl,
  MenuItem,
  Select,
  Typography,
  SelectChangeEvent,
  SxProps,
  Theme,
} from '@mui/material'
import { style } from './reportStyles'
import NoQAProjectRow from './NoQAProjectRow'
import TableBodyRow from './TableBodyRow'
import TableHeadColumns from './TableHeadColumns'

interface ColumnType {
  key: string
  label: string
  sx?: SxProps<Theme>
}

export type QATablePropType = {
  projectQAReportData: any
  projectLeaveBalanceReportData: any
  projectLeaveAllocatedReportData: any
  projectLeaveEncashmentReportData: any
  projectLeaveTypeReportData: any
  selectType: string
  count: number
  page: number
  handleChangePage: any
  SetRowPerPage: React.Dispatch<React.SetStateAction<number>>
  rowPerPage: number
  selectedTab: any
}

const QATable = ({
  projectQAReportData,
  projectLeaveBalanceReportData,
  projectLeaveAllocatedReportData,
  projectLeaveEncashmentReportData,
  projectLeaveTypeReportData,
  selectType,
  page,
  handleChangePage,
  SetRowPerPage,
  rowPerPage,
  setPage,
  selectedTab,
  selectedQuarter,
}: any) => {
  const handleSelectEntry = (event: SelectChangeEvent<string>) => {
    const value = parseInt(event.target.value, 10)
    SetRowPerPage(value)
    setPage(1)
  }

  const getLeaveTypeReportArray = (): any[] => {
    return projectLeaveTypeReportData?.finalResult
      ?.map?.((element: any) => {
        const lastIndex = element?.leaves.length - 1
        return element?.leaves?.map((leaveObj: any, i: number) => {
          return {
            employee_id: leaveObj.employee_id,
            name: leaveObj.name,
            description: leaveObj.description,
            startDate: leaveObj.startDate,
            endDate: leaveObj.endDate,
            leaveAvailed: leaveObj.leaveAvailed,
            //commom values
            deductibleLeavesAvailed: element.deductibleLeavesAvailed,
            nonDeductibleLeavesAvailed: element.nonDeductibleLeavesAvailed,
            totalLeaveAvailed: element.totalLeaveAvailed,
            firstIndex: i === 0,
            length: lastIndex + 1,
          }
        })
      })
      .flat(Infinity)
  }

  const getLeaveReport = (): any[] => {
    return projectQAReportData?.employeeDetails
      ?.map((element: any) => {
        const lastIndex = element?.emp.length - 1
        return element?.emp?.map((emp: any, i: number) => {
          return {
            name: emp.name,
            employee_id: emp.employee_id,
            designation: emp.designation,
            start_date: emp.start_date,
            end_date: emp.end_date,
            description: emp.description,
            leave_count: emp.leave_count,
            leave_type: emp.leave_type,
            //common keys
            deductibleLeavesSum: element.deductibleLeavesSum,
            nonDeductibleLeavesSum: element.nonDeductibleLeavesSum,
            allocatedLeavesSum: element.allocatedLeavesSum,
            totalLeaveAvailed: element.totalLeaveAvailed,
            firstIndex: i === 0,
            length: lastIndex + 1,
            //All Table data
            grandTotal: projectQAReportData?.grandTotal,
            grandDeductibleTotal: projectQAReportData?.grandDeductibleTotal,
            grandNonDeductibleTotal: projectQAReportData?.grandNonDeductibleTotal,
            grandAllocatedTotal: projectQAReportData?.grandAllocatedTotal,
          }
        })
      })
      .flat(Infinity)
  }

  const dataMapping: Record<number, any[]> = {
    0: getLeaveReport() || [],
    1: projectLeaveBalanceReportData?.reports || [],
    2: projectLeaveAllocatedReportData || [],
    3: projectLeaveEncashmentReportData || [],
    4: getLeaveTypeReportArray() || [],
  }

  const selectedData = dataMapping[selectedTab] || []

  const leaveReportGroupColumnSx = { minWidth: '150px', textWrap: 'unset !important' }

  const leaveTypeReportGroupColumnSx = { ...leaveReportGroupColumnSx }

  const leaveReportColumns = [
    [
      { key: 'employee_id', label: 'Emp Id' },
      { key: 'name', label: 'Name' },
      { key: 'designation', label: 'Job Title' },
      { key: 'start_date', label: 'Start Date' },
      { key: 'end_date', label: 'End Date' },
      { key: 'leave_type', label: 'Leave Type            ' },
      { key: 'description', label: 'Leave Description' },
      { key: 'leave_count', label: 'Leaves Availed' },
      {
        key: 'deductibleLeavesSum',
        label: 'Total Deductible Leaves Availed in the Date Range',
        sx: leaveReportGroupColumnSx,
      },
      {
        key: 'nonDeductibleLeavesSum',
        label: 'Total Non Deductible Leaves Availed in the Date Range',
        sx: leaveReportGroupColumnSx,
      },
      {
        key: 'allocatedLeavesSum',
        label: 'Total Leaves Allocated in the Quarter',
        sx: leaveReportGroupColumnSx,
      },
      {
        key: 'totalLeaveAvailed',
        label: 'Total Leaves Availed in the Date Range',
        sx: leaveReportGroupColumnSx,
      },
    ],
    [
      { key: 'empId', label: 'Emp Id' },
      { key: 'name', label: 'Name' },
      { key: 'Leaves Allocated', label: 'Leaves Allocated' },
      { key: 'Leaves Availed', label: 'Leaves Availed' },
      { key: 'Leaves Balance', label: 'Leaves Balance' },
    ],
    [
      { key: 'empId', label: 'Emp Id' },
      { key: 'name', label: 'Name' },
      { key: 'underProbation', label: 'Under Probation' },
      ...(!selectedQuarter
        ? [
            { key: 'Quarter1', label: 'Quarter 1' },
            { key: 'Quarter2', label: 'Quarter 2' },
            { key: 'Quarter3', label: 'Quarter 3' },
            { key: 'Quarter4', label: 'Quarter 4' },
          ]
        : []),
      { key: 'TotalLeaveAllocated', label: 'Total Leave Allocated' },
      { key: 'TotalDeductibleLeaves', label: 'Total Deductible Leaves' },
      { key: 'TotalNONDeductibleLeaves', label: 'Total Non Deductible Leaves' },
      { key: 'TotalLeavesBalance', label: 'Total Leaves Balance' },
    ],
    [
      { key: 'empId', label: 'Emp Id' },
      { key: 'name', label: 'Name' },
      { key: 'TotalLeaveAllocated', label: 'Total Leave Allocated' },
      { key: 'TotalDeductibleLeaves', label: 'Total Deductible Leaves' },
      { key: 'TotalLeavesBalance', label: 'Total Leaves Balance' },
      { key: 'CalculatedBasicSalary', label: 'Calculated Basic Salary' },
      { key: 'LeaveEncashment', label: 'Leave Encashment' },
    ],
    [
      { key: 'employee_id', label: 'Emp Id' },
      { key: 'name', label: 'Name' },
      { key: 'startDate', label: 'Start Date' },
      { key: 'endDate', label: 'End Date' },
      { key: 'description', label: 'Description' },
      { key: 'leaveAvailed', label: 'Leave Availed' },
      {
        key: 'deductibleLeavesAvailed',
        label: 'Total Deductible Leaves',
        sx: leaveTypeReportGroupColumnSx,
      },
      {
        key: 'nonDeductibleLeavesAvailed',
        label: 'Total Non Deductible Leaves',
        sx: leaveTypeReportGroupColumnSx,
      },
      {
        key: 'totalLeaveAvailed',
        label: 'Total Leaves availed in The Date Range',
        sx: leaveTypeReportGroupColumnSx,
      },
    ],
    ``,
  ]

  return (
    <Box id='export' sx={style.tableBoxStyle}>
      <Box sx={style.tableContainer}>
        <TableContainer component={Paper}>
          <Table sx={style.styleOfTableBox}>
            <TableHeadColumns columns={leaveReportColumns[selectedTab] as ColumnType[]} />
            <TableBody id='body'>
              {selectedData.length > 0 ? (
                selectedData.map((row: any, i: number) => (
                  <TableBodyRow
                    row={row}
                    selectType={selectType}
                    key={`${i}`}
                    lastIndex={selectedData?.length - 1 === i}
                    selectedTab={selectedTab}
                    selectedQuarter={selectedQuarter}
                  />
                ))
              ) : (
                <NoQAProjectRow selectType={selectType} />
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {selectedTab === 1 ? (
          <Box sx={style.selectAndPaginationBox}>
            <Box sx={style.BoxSecondCustomStyle}>
              <Typography sx={style.typographyEntriesNameSecondStyle}>Show Entries : </Typography>
              <FormControl sx={style.FormSecondControlStyle}>
                <Select
                  value={rowPerPage.toString()}
                  onChange={handleSelectEntry}
                  sx={style.selectSecondTypeStyle}
                >
                  <MenuItem value={100}>100</MenuItem>
                  <MenuItem value={200}>200</MenuItem>
                  <MenuItem value={300}>300</MenuItem>
                  <MenuItem value={400}>400</MenuItem>
                  <MenuItem value={500}>500</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Pagination
              count={Math.ceil(projectLeaveBalanceReportData?.totalEmployees / rowPerPage)}
              page={page}
              onChange={handleChangePage}
              color='primary'
            />
          </Box>
        ) : (
          ''
        )}
      </Box>
    </Box>
  )
}
export default QATable
