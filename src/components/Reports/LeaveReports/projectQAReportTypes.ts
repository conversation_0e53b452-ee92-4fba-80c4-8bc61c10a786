import { Dayjs } from 'dayjs'
import { Dispatch, SetStateAction } from 'react'

export type ProjectQAReportPropType = {
  getProjectQAReport: (data: {
    data: {
      data: {
        startDate: string
        endDate: string
        page:number
        limit:number
        type:string
      }
    }
  }) => void
  projectQAReportData: {
    projectCount: number
    countWithQa: number
    countWithoutQa: number
    projectWiseData: []
    employeeWiseData: []
    data: ProjectQAReportDataType
  }
  isGettingingProjectQAReport: boolean
}
export type CollaspePropType = {
  projectQAReportData: {
    projectCount: number
    countWithQa: number
    countWithoutQa: number
    projectWiseData: []
    employeeWiseData: []
  }
}

export type ProjectQAReportDataType = {
  projectCount: number
  countWithQa: number
  countWithoutQa: number
  projectWiseData: {
    project_name: string
    project_status: string
    qa: string
    employmentType: string
    isBillable: string
  }
  employeeWiseData: {
    employeeId: number
    full_name: string
    projectname: string
    projectAssigned: string
  }
}

export type TableDataType = {
  project_name: string
  project_status: string
  qa: string
  employmentType: string
  isBillable: string
  employeeId: number
  full_name: string
  projectname: string
  projectAssigned: string
}

export type ProjectQARportPropType = {
  selectType: string
}

export type TableBodyRowPropsType = {
  row: TableDataType
  selectType: string
  key: number
}

export type SelectReportTypePropType = {
  selectType: string
  setSelectType: React.Dispatch<React.SetStateAction<string>>
}

export type DateRangePickerPropType = {
  startDate: Dayjs
  endDate: Dayjs
  setStartDate: Dispatch<SetStateAction<Dayjs>>
  setEndDate: Dispatch<SetStateAction<Dayjs>>
  setShowResult: (value: boolean | ((prev: boolean) => boolean)) => void;
  height?:string
}

export type TableHeadColumnsPropType = {
  selectType: string
}

export type ExportButtonPropType={
  selectType: string
  rowsToDisplay:any
}

export type HeadingPropType={
  startDate: Dayjs
  endDate: Dayjs
}