import { SxProps, TableHead, TableRow, Theme } from '@mui/material'
import { StyledTableCell } from '../../Common/CommonStyles'

export const style = {
  typographyStyle: {
    textAlign: 'center',
    width: '100%',
  },
  border: {
    borderLeft: '1px solid #E9E9E9',
    borderRight: '1px solid #E9E9E9',
  },
}

interface ColumnType {
  key: string
  label: string
  sx?: SxProps<Theme>
}

interface TableHeadColumnsPropType {
  columns: ColumnType[] // Array of objects with `key` and `label`
}

const TableHeadColumns = ({ columns }: TableHeadColumnsPropType) => {
  const calculateWidth = (label: string) => {
    const baseWidth = 50 // Minimum width in pixels
    const charWidth = 8 // Approximate width per character
    return `${baseWidth + (label.length / 2) * charWidth}px`
  }

  return (
    <TableHead id='head'>
      <TableRow>
        {columns.map((column) => (
          <StyledTableCell key={column.key} sx={{ ...{ textWrap: 'nowrap' }, ...column.sx }}>
            {column.label} {/* Render the column label */}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}

export default TableHeadColumns
