import { Box, IconButton, InputAdornment, styled } from '@mui/material'
import { Dispatch, SetStateAction, useState } from 'react'
import { SearchBoxCustom, SearchIconStyle } from '../../Common/CommonStyles'
import ClearIcon from '@mui/icons-material/Clear'
import { style } from '../../../components/ProjectAnalytics/SheetStyles'

type SearchBoxType = {
  setSearchQuery: Dispatch<SetStateAction<string>>
  selectType: string
  value: string
}

const CustomSearchBox = styled(SearchBoxCustom)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '20px',
    '& fieldset': {
      borderColor: '#777777',
    },
  },
}))

const SearchBox = (props: SearchBoxType) => {
  const { setSearchQuery, selectType, value } = props

  const handleClear = () => {
    setSearchQuery('')
  }

  return (
    <Box width='250px' sx={style.serachBoxContainer} style={{ marginRight: '10px' }}>
      <CustomSearchBox
        id='outlined-basic'
        placeholder={`Search ${selectType === 'project-wise' ? 'Project' : 'Name'}`}
        variant='outlined'
        size='small'
        value={value}
        onChange={(e) => {
          setSearchQuery(e.target.value?.trim())
        }}
        width='100%'
        InputProps={{
          startAdornment: <SearchIconStyle />,
          endAdornment: (
            <InputAdornment position='end'>
              <IconButton aria-label='clear-icon' onClick={handleClear} edge='end'>
                {value && <ClearIcon />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  )
}

export default SearchBox
