import styled from '@emotion/styled'
import {
  Box,
  Button,
  Grid,
  Paper,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  Typography,
  tableCellClasses,
  RadioGroup,
  FormControl,
  FormLabel,
  FormControlLabel,
  Radio,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import data from './Loan.json'
import style from '../../utils/styles.json'

const StyledPaper = styled(Paper)(({ theme }) => ({
  width: '93%',
  background: '#FFFFFF',
  opacity: '1',
  marginTop: '25px',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginBottom: '20px',
}))

const MainContainer = {
  backgroundColor: 'rgb(231, 235, 240)',
  width: '100%',
  height: '100%',
}

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  fontSize: '13px',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
}))

const StyledFormLabel = styled(FormLabel)(({ theme }) => ({
  textAlign: 'center',
  fontSize: '13px',
}))

const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  '.MuiFormControlLabel-label': {
    fontSize: '13px',
  },
}))

const Heading = styled(Typography)(({ theme }) => ({
  fontSize: '25px',
  textAlign: 'center',
  fontFamily: style.FONT_BOLD,
  letterSpacing: '0px',
  background: style.PRIMARY_COLOR,
  color: 'white',
  lineHeight: 2.3,
}))

const DateField = styled(DatePicker)(({ theme }) => ({
  marginTop: '5px',
  marginBottom: '5px',
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px',
    fontSize: '13px',
    fontFamily: style.FONT_MEDIUM,
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiFormLabel-root, & .MuiInputLabel-root': {
    lineHeight: '1em',
  },
}))

const InnerTabStyle: React.CSSProperties = {
  padding: '10px 70px',
}

const SearchBox: React.CSSProperties = {
  width: '250px',
  float: 'right',
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: '#f2f2f2f2',
    color: '#787070',
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    fontSize: '12px',
    letterSpacing: '0px',
    width: '33%',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    color: '#483f3f',
    letterSpacing: '0px',
    width: '33%',
  },
}))

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
  },
  '&:last-child td, &:last-child th': {
    fontSize: 12,
    fontFamily: style.FONT_MEDIUM,
    textAlign: 'center',
    borderBottom: 'none',
    letterSpacing: '0px',
  },
}))

const RepaymentHistoryeTable = ({ data }: { data: any[] }) => {
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(5)
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredRows, setFilteredRows] = useState<any[]>([])

  const handleChangePage = (event: any, newPage: number) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  useEffect(() => {
    const filtered = data.filter((row) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    )
    setFilteredRows(filtered)
  }, [searchQuery, data])

  const rows = data.map((row) => createData(row.loanAmount, row.loanDate, row.status))

  function createData(loanAmount: string, loanDate: string, status: string) {
    return {
      loanAmount,
      loanDate,
      status,
    }
  }

  return (
    <>
      <TextField
        id='outlined-basic'
        placeholder='Search'
        variant='outlined'
        size='small'
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        style={SearchBox}
      />

      <TableContainer component={Paper}>
        <Table aria-label='customized table'>
          <TableHead>
            <TableRow>
              <StyledTableCell>LOAN AMOUNT</StyledTableCell>
              <StyledTableCell>LOAN DATE</StyledTableCell>
              <StyledTableCell>STATUS</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {(rowsPerPage > 0
              ? searchQuery
                ? filteredRows
                : rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              : searchQuery
                ? filteredRows
                : rows
            ).map((row) => (
              <StyledTableRow key={row.loanAmount}>
                <StyledTableCell>{row.loanAmount}</StyledTableCell>
                <StyledTableCell>
                  {row.loanDate
                    ? new Date(row.loanDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                    : "NA"}
                </StyledTableCell>

                <StyledTableCell>{row.status}</StyledTableCell>
              </StyledTableRow>
            ))}
            {rows.length === 0 && (
              <StyledTableRow>
                <StyledTableCell colSpan={3}>No data available</StyledTableCell>
              </StyledTableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component='div'
        count={searchQuery ? filteredRows.length : rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </>
  )
}

const Food = () => {
  const [value, setValue] = React.useState(0)
  const [orderType, setOrderType] = useState('')

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const handleOrderType = (event: any) => {
    setOrderType(event.target.value)
  }

  const renderTabContent = () => {
    switch (value) {
      case 0:
        return (
          <div style={InnerTabStyle}>
            <Grid container rowSpacing={1.5} columnSpacing={{ xs: 1, sm: 2, md: 4 }}>
              <Grid item xs={4} sm={6}>
                <StyledFormControl>
                  <StyledFormLabel>Order Type</StyledFormLabel>
                  <RadioGroup row>
                    <StyledFormControlLabel
                      value='Lunch'
                      control={<Radio />}
                      label='Lunch'
                      checked={orderType === 'Lunch'}
                      onChange={handleOrderType}
                    />
                    <StyledFormControlLabel
                      value='Dinner'
                      control={<Radio />}
                      label='Dinner'
                      checked={orderType === 'Dinner'}
                      onChange={handleOrderType}
                    />
                  </RadioGroup>
                </StyledFormControl>
              </Grid>
              <Grid item xs={6} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateField label='Order Date' format='MM-DD-YYYY' />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={6} sm={6} justifyContent='flex-end'>
                <Box sx={{ textAlign: 'center' }}>
                  <Button
                    variant='outlined'
                    sx={{
                      fontSize: '13px',
                      height: '42px',
                      width: '121px',
                      marginTop: '15px',
                    }}
                  >
                    PLACE ORDER
                  </Button>
                </Box>
              </Grid>
              <Grid item xs={6} sm={6} justifyContent='flex-start'>
                <Box sx={{ textAlign: 'center' }}>
                  <Button
                    variant='outlined'
                    sx={{
                      fontSize: '13px',
                      height: '42px',
                      width: '121px',
                      marginTop: '15px',
                    }}
                  >
                    CANCEL
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </div>
        )
      case 1:
        return (
          <div>
            <RepaymentHistoryeTable data={data} />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div style={MainContainer}>
      <StyledPaper>
        <Heading>Food</Heading>
        <Box sx={{ width: '100%', bgcolor: 'background.paper' }}>
          <Tabs value={value} onChange={handleChange} centered>
            <Tab label='Food Order' />
            <Tab label='Order History' />
          </Tabs>
          <Box sx={{ p: 3 }}>{renderTabContent()}</Box>
        </Box>
      </StyledPaper>
    </div>
  )
}

export default Food
