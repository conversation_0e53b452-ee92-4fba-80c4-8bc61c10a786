{"name": "kuber-frontend", "version": "1.1.0", "private": true, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@ckeditor/ckeditor5-build-classic": "^41.1.0", "@ckeditor/ckeditor5-react": "^6.2.0", "@date-io/date-fns": "^1.3.13", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^4.5.8", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.6", "@mui/styled-engine-sc": "^5.12.0", "@mui/styles": "^5.14.7", "@mui/x-charts": "^7.23.2", "@mui/x-date-pickers": "^6.3.1", "@react-oauth/google": "^0.11.0", "@react-pdf/renderer": "^3.1.12", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.34", "@types/react-dom": "^18.0.11", "apexcharts": "^4.1.0", "axios": "^1.3.5", "date-fns": "^2.30.0", "dayjs": "^1.11.8", "echarts": "^5.5.0", "font-awesome": "^4.7.0", "formBuilder": "^3.16.5", "formik": "^2.4.6", "html2pdf.js": "^0.10.1", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^7.0.1", "jquery": "^3.7.1", "jquery-ui-sortable": "^1.0.0", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "moment": "^2.29.4", "multiselect-react-dropdown": "^2.0.25", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-markdown": "^9.0.1", "react-number-format": "^5.2.2", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-rte": "^0.16.5", "react-scripts": "^5.0.1", "react-toastify": "^9.1.2", "react-use": "^17.4.0", "redux": "^4.2.1", "redux-saga": "^1.2.3", "rehype-raw": "^7.0.0", "sass": "^1.69.7", "styled-components": "^6.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yup": "^1.4.0"}, "scripts": {"start": "./node_modules/react-scripts/bin/react-scripts.js start --port 5000", "build": "./node_modules/react-scripts/bin/react-scripts.js build && mv build dist", "build-prod": "cp .env.main .env && ./node_modules/react-scripts/bin/react-scripts.js build && mv build dist", "test": "./node_modules/react-scripts/bin/react-scripts.js test", "eject": "./node_modules/react-scripts/bin/react-scripts.js eject", "format": "prettier -v && prettier --write \"src/**/*.t{s,sx}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"prettier": "2.8.7"}}